// Here you can add other styles

// custom .chartjs-tooltip-body-item padding
@import "charts";

// custom tweaks for scrollbar styling (wip)
@import "scrollbar";

.sidebar-nav .nav-link:hover.nav-group-toggle::after {
  background-color: #1061AC !important;
}

.sidebar-nav .nav-group.show > .nav-group-toggle::after {
  background-color: #1061AC !important;
}

 // custom calendar today cell color
.calendar-cell.today {
  --cui-calendar-cell-today-color: var(--cui-info) !important;
}

// custom select week cursor pointer
.select-week .calendar-row.current {
  cursor: pointer;
}

.sidebar-logo {
  max-width: 100% !important;
  height: 32px !important;
}

.sidebar-brand-full {
  display: block;
}

.sidebar-brand-narrow {
  display: none;
}

.sidebar-narrow .sidebar-brand-full {
  display: none;
}

.sidebar-narrow .sidebar-brand-narrow {
  display: block;
}

.sidebar-header.custom-header {
  background: white !important;
  min-height: calc(4rem + 1px); /* Match CoreUI's default header height */
  display: flex; /* Enable flexbox for positioning children */
  align-items: center; /* Vertically center children */

  // Push the toggle button (now a span) to the far right and remove its default styling
  .sidebar-toggler {
    all: unset !important; /* Extremely aggressive reset */
    margin-inline-start: auto !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer; /* Ensure it's still clickable */
    width: 24px; /* Set explicit width and height for consistency with the image */
    height: 24px;
    
    // Ensure no visual styles on the toggler itself
    background: transparent !important;
    background-color: transparent !important;
    border: none !important;
    border-color: transparent !important;
    padding: 0 !important;
    outline: none !important;
    box-shadow: none !important;
    user-select: none !important; /* Prevent selection that might show a box */
    color: transparent !important; /* Hide any potential text/dots */
    font-size: 0 !important; /* Ensure no text is visible */

    img {
      display: block; /* Ensure image takes up space */
      width: 100%;    /* Make image fill its container */
      height: 100%;
      border: none !important;
      outline: none !important;
      pointer-events: auto; /* Ensure image is clickable */
    }

    &:hover,
    &:focus,
    &:active {
      background: transparent !important;
      background-color: transparent !important;
      border: none !important;
      border-color: transparent !important;
      outline: none !important;
      box-shadow: none !important;

      img {
        outline: 0 !important;
        box-shadow: none !important;
      }
    }

    &::before,
    &::after {
      content: none !important; /* Remove any pseudo-element content */
      background-image: none !important; /* Remove any background images */
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important; /* Ensure pseudo elements are also transparent */
      border: none !important;
    }
  }
}

// Ensure sidebar nav items show only icons in narrow mode, no text overflow
.sidebar-narrow {
  .c-sidebar-nav {
    .nav-link,
    .nav-group-toggle {
      // Aggressively hide all text that's not an icon or badge within the nav link
      & > *:not(.nav-icon):not(.badge):not(i), // Covers text spans, divs, etc. excluding icons and badges, and any <i> elements (which might be icons)
      .nav-link-text { // Specific to CoreUI's nav-link-text class
        display: none !important;
      }
    }

    .nav-icon {
      margin-right: 0 !important;
      width: 100% !important; /* Ensure icon takes full width in narrow mode */
      text-align: center; /* Center the icon */
    }
  }
}

// Remove all custom content shifting rules to allow CoreUI's native behavior to function
// .wrapper, app-default-header .container-fluid will be managed by CoreUI

// Fix for button focus outline/border
/* Removed: c-sidebar-header .btn:focus, c-sidebar-header .btn:active { outline: none !important; box-shadow: none !important; } */

// Header styles
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem; // Adjust padding as needed
}

.search-container {
  .input-group {
    border-radius: 20px !important; // Apply full rounded corners to the group
    overflow: hidden; // Hide any overflow from children that might break the rounded corners
    background-color: #f0f2f5; // Background color for the entire search field
    border: 1px solid #e0e4eb; // Border for the entire search field
    max-width: 300px; // Maintain max-width from HTML

    .form-control {
      border-radius: 0 !important; // Remove individual border-radius
      border: none !important; // Remove individual border
      background-color: transparent !important; // Ensure input background is transparent within the group
      color: #333366; // Dark blue/grey text color for placeholder and input
      height: calc(2.5rem + 2px); /* Standard form-control height for symmetry */

      &::placeholder {
        color: #333366; // Dark blue/grey color for placeholder
        opacity: 1; /* Firefox fix */
      }
    }

    .btn {
      border-radius: 0 !important; // No individual border-radius for the button
      background: transparent !important; // Remove blue gradient background
      border: none !important;
      padding: 0 !important;             // Remove all padding
      display: flex;
      align-items: center;
      justify-content: center;
      width: auto; /* Define explicit width for the button area */
      height: auto; /* Define explicit height for the button area */

      img {
        width: 100% !important;    // Make image fill its container
        height: 100% !important;
        filter: none !important; // Ensure icon is in its original color
      }
    }
  }
}

.notification-icon-wrapper,
.logout-icon-wrapper {
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  padding: 0.5rem !important;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    display: block;
    width: 24px;
    height: 24px;
  }
}

.logout-icon-wrapper img {
    width: 20px;
    height: 20px;
}

.user-profile-group {
  .user-name,
  .pharmacy-name {
    font-size: 0.875rem; // Adjust font size as needed
    color: #333; // Adjust color as needed
    white-space: nowrap;
  }

  .user-name {
    margin-right: 0.5rem;
  }

  .pharmacy-name {
    margin-left: 0.5rem;
  }
}

// Adjust separator for better alignment
c-header-nav .vr {
  height: 1.5rem !important; // Ensure separator is visible and aligned
}

/* Custom Sidebar Styling */




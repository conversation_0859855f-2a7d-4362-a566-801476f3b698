// Medicine interface for prescription
export interface Medicine {
  medicine: string;
  dosage?: string;
  frequency?: string;
  duration?: string;
  timing?: string;
}

// Prescription Status enum (matching your backend exactly)
export enum PrescriptionStatus {
  UPLOADED = 'UPLOADED',
  IN_REVIEW = 'IN_REVIEW',
  NEEDS_CLARIFICATION = 'NEEDS_CLARIFICATION',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
  ARCHIVED = 'ARCHIVED'
}

// Main Prescription interface matching your backend entity
export interface Prescription {
  _id?: string; // MongoDB ID
  id: string;
  patientId: string;
  pharmacyId: string;
  storagePath: string[]; // Array of file paths
  issueDate: Date | string;
  date: Date | string;
  prescriptionStatus: PrescriptionStatus | string; // Allow string for "UPLOADED" etc.
  approved: boolean;
  note?: string;
  uploadedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  rejectionReason?: string;
  expiresAt?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field
  medicines?: Medicine[];

  // Optional display properties (can be populated from other API calls)
  patientName?: string;
  patientInitials?: string;
  patientPhone?: string;
  pharmacyName?: string;
  householdName?: string;
}

// File upload response
export interface FileUploadResponse {
  success: boolean;
  message: string;
  filePath?: string;
  fileSize?: number;
}

// Create prescription request (matching your backend command)
export interface CreatePrescriptionRequest {
  issueDate: Date;
  storagePaths: string[]; // File paths from upload
  note?: string;
  medicines?: Medicine[];
}

// Create prescription by admin request (matching your PrescriptionCreateForPatientCommand)
export interface CreatePrescriptionByAdminRequest {
  patientId: string;
  pharmacyId: string;
  issueDate?: string; // ISO date string
  storagePaths: string[]; // File paths from upload
  note?: string;
  medicines?: {
    medicine: string;
    dosage?: string;
    frequency?: string;
    duration?: string;
    timing?: string;
  }[];
}

// Update prescription request (matching your backend command)
export interface UpdatePrescriptionRequest {
  note?: string;
  storagePaths?: string[];
  medicines?: Medicine[];
}

// Prescription filters for the /filter endpoint (matching backend PrescriptionFilterCommand)
export interface PrescriptionFilters {
  date?: string; // Format: YYYY-MM-DD
  status?: PrescriptionStatus; // Single status filter using backend enum
  hasHousehold?: string; // 'true', 'false', or undefined
  page?: number; // Page number for pagination (1-based)
  limit?: number; // Items per page (odd numbers: 3, 5, 7, 9, etc.)
}

// API Response structure matching your backend
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  total?: number;
  prescriptions?: Prescription[];
  prescription?: Prescription;
}

// Get all prescriptions response (matching your backend exactly)
export interface PrescriptionListResponse {
  success: boolean;
  total: number;
  prescriptions: Prescription[];
}

// Get single prescription response
export interface PrescriptionDetailResponse {
  success: boolean;
  prescription: Prescription;
}

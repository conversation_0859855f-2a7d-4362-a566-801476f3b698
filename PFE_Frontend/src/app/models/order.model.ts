// Order Status enum (matching your backend exactly)
export enum OrderStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED'
}

// Main Order interface matching your backend entity
export interface Order {
  _id?: string; // MongoDB ID
  id: string;
  patientId: string;
  status: OrderStatus | string; // Allow string for "PENDING" etc.
  orderDate: Date | string;
  totalAmount: number;
  productIds: string[]; // IDs of the products in the order/basket
  flag: boolean; // false = in basket, true = ordered
  quantity: number;
  deliveryManNote?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field

  // Optional display properties (can be populated from other API calls)
  patientName?: string;
  patientInitials?: string;
  patientPhone?: string;
  householdName?: string;
}

// Order filters for the /filter endpoint (matching backend OrderFilterCommand)
export interface OrderFilters {
  date?: string; // Format: YYYY-MM-DD
  status?: OrderStatus; // Single status filter using backend enum
  hasHousehold?: string; // 'true', 'false', or undefined
  page?: number; // Page number for pagination (1-based)
  limit?: number; // Items per page (odd numbers: 3, 5, 7, 9, etc.)
}

// Order pharmacist confirm command (matching your backend OrderPharmacistConfirmCommand)
export interface OrderPharmacistConfirmCommand {
  patientId: string; // Required - will be set from selected order
  quantity: number; // Required - from modal input
  deliveryManNote?: string; // Optional - from modal input
}

// API Response structure matching your backend
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  total?: number;
  orders?: Order[];
  order?: Order;
}

// Order list response
export interface OrderListResponse {
  success: boolean;
  total: number;
  orders: Order[];
}

// Order detail response
export interface OrderDetailResponse {
  success: boolean;
  order: Order;
}

// Order confirm response
export interface OrderConfirmResponse {
  success: boolean;
  message: string;
  order: Order;
}

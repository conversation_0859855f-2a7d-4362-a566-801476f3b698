// Main Category interface matching your backend entity
export interface Category {
  _id?: string; // MongoDB ID
  id: string;
  name: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field
}

// Category creation request (matching your backend command)
export interface CreateCategoryRequest {
  name: string;
}

// Category update request (matching your backend command)
export interface UpdateCategoryRequest {
  name?: string;
}

// API Response structure matching your backend
export interface CategoryApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  total?: number;
  categories?: Category[];
  category?: Category;
}

// Get all categories response (matching your backend exactly)
export interface CategoryListResponse {
  success: boolean;
  categories: Category[];
}

// Get single category response
export interface CategoryDetailResponse {
  success: boolean;
  category: Category;
}

// Category creation response
export interface CategoryCreateResponse {
  success: boolean;
  message: string;
  category: Category;
}

// Category update response
export interface CategoryUpdateResponse {
  success: boolean;
  message: string;
  category: Category;
}

// Category deletion response
export interface CategoryDeleteResponse {
  success: boolean;
  message: string;
}

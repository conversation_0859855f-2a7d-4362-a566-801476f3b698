// Patient Payment Type enum
export enum PatientPaymentType {
  PAYONDELEVERY = 'Paiement à la livraison',
  ACCOUNTCHARGE = 'Charge au compte',
  CREDITCARD = 'Carte de credit'
}

// Signature Type enum
export enum SignatureType {
  DIGITAL = 'DIGITAL',
  PHYSICAL = 'PHYSICAL',
  NONE = 'NONE'
}

// Address interface
export interface PatientAddress {
  address: string;
  lat: number;
  long: number;
  apartmentNumber?: string;
}

// Authorized Person interface
export interface AuthorizedPerson {
  name: string;
  type: string;
  phone: string;
}

// Vacation Period interface
export interface VacationPeriod {
  start: Date | string;
  end: Date | string;
}

// Recurrence Option interface
export interface RecurrenceOption {
  note: string;
  frequency: {
    standardFrequency?: string;
    each?: number;
    period?: string;
  };
  from: Date | string;
  to: Date | string;
}

// Main Patient interface matching your backend entity
export interface Patient {
  _id?: string; // MongoDB ID
  id: string;
  pharmacyId: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  post?: number;
  officePhone?: string;
  address: PatientAddress[];
  authorizedPerson?: AuthorizedPerson;
  paymentType?: PatientPaymentType;
  deliveryManNote?: string;
  pharmacyNote?: string;
  householdNote?: string;
  spot?: string;
  signatureType?: SignatureType;
  hospitalizedPatient?: boolean;
  vacationPeriod?: VacationPeriod;
  recurrenceOption?: RecurrenceOption;
  deliveryType: string[];
  houseHoldId?: string;
  roomNumber?: number;
  createdBy?: string;
  deletedAt?: Date | string;
  deletedBy?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field
  
  // Computed properties for display
  fullName?: string;
  initials?: string;
  primaryAddress?: string;
}

// API Response for patient
export interface PatientResponse {
  success: boolean;
  message?: string;
  data?: Patient;
}

// Patient list response (for future use)
export interface PatientListResponse {
  success: boolean;
  message?: string;
  data?: Patient[];
  total?: number;
}

// Patient search response (for search endpoint)
export interface PatientSearchResponse {
  success: boolean;
  total: number;
  patients: Patient[];
}

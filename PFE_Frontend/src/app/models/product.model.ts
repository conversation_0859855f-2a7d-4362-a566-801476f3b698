// Stock Status enum (matching your backend exactly)
export enum StockStatus {
  IN_STOCK = 'IN_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK'
}

// Main Product interface matching your backend entity
export interface Product {
  _id?: string; // MongoDB ID
  id: string;
  pharmacyId: string;
  name: string;
  description?: string;
  quantity: number;
  price: number;
  discount?: number;
  storagePath?: string[]; // Array of image paths
  stockStatus?: StockStatus;
  categoryId: string; // Single category ID
  expiryDate?: Date | string;
  supplier?: string;
  barcode?: string;
  isTaxable?: boolean;
  brand?: string;
  actif: boolean; // Active/Inactive status
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field

  // Optional display properties (can be populated from other API calls)
  categoryName?: string;
  imageUrl?: string | null; // First image from storagePath for display
}

// Product creation request (matching your backend command)
export interface CreateProductRequest {
  name: string;
  description?: string;
  quantity: number;
  price: number;
  discount?: number;
  storagePath?: string[]; // File paths from upload
  stockStatus?: StockStatus;
  categoryId: string;
  expiryDate?: Date | string;
  supplier?: string;
  barcode?: string;
  isTaxable?: boolean;
  brand?: string;
  actif: boolean;
}

// Product update request (matching your backend command)
export interface UpdateProductRequest {
  name?: string;
  description?: string;
  quantity?: number;
  price?: number;
  discount?: number;
  storagePath?: string[];
  stockStatus?: StockStatus;
  categoryId?: string;
  expiryDate?: Date | string;
  supplier?: string;
  barcode?: string;
  isTaxable?: boolean;
  brand?: string;
  actif?: boolean;
}

// Product filters for the getAllForPharmacy endpoint (matching backend)
export interface ProductFilters {
  search?: string; // Search by product name
  stockStatus?: StockStatus; // Filter by stock status
  actif?: boolean; // Filter by active/inactive status
  page?: number; // Page number for pagination (1-based)
  limit?: number; // Items per page
}

// File upload response for product images
export interface ProductFileUploadResponse {
  success: boolean;
  message: string;
  filePath?: string;
  fileSize?: number;
}

// API Response structure matching your backend
export interface ProductApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  total?: number;
  products?: Product[];
  product?: Product;
}

// Get all products response (matching your backend exactly)
export interface ProductListResponse {
  success: boolean;
  total: number;
  products: Product[];
}

// Get single product response
export interface ProductDetailResponse {
  success: boolean;
  product: Product;
}

// Product creation response
export interface ProductCreateResponse {
  success: boolean;
  message: string;
  product: Product;
}

// Product update response
export interface ProductUpdateResponse {
  success: boolean;
  message: string;
  product: Product;
}

// Product deletion response
export interface ProductDeleteResponse {
  success: boolean;
  message: string;
}

// Helper function to get stock status display text
export function getStockStatusText(status: StockStatus): string {
  switch (status) {
    case StockStatus.IN_STOCK:
      return 'En stock';
    case StockStatus.OUT_OF_STOCK:
      return 'Rupture de stock';
    default:
      return 'Inconnu';
  }
}

// Helper function to get stock status badge class
export function getStockStatusBadgeClass(status: StockStatus): string {
  switch (status) {
    case StockStatus.IN_STOCK:
      return 'badge-in-stock';
    case StockStatus.OUT_OF_STOCK:
      return 'badge-out-of-stock';
    default:
      return 'badge-unknown';
  }
}

// Helper function to get active status text
export function getActiveStatusText(actif: boolean): string {
  return actif ? 'Actif' : 'Inactif';
}

// Helper function to get active status badge class
export function getActiveStatusBadgeClass(actif: boolean): string {
  return actif ? 'badge-active' : 'badge-inactive';
}

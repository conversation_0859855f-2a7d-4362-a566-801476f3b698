import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { Router } from '@angular/router';
import { AuthService } from './auth.service';
import { LoginRequest, LoginResponse, Role } from '../models/user.model';
import { environment } from '../../environments/environment';

describe('AuthService', () => {
  let service: AuthService;
  let httpMock: HttpTestingController;
  let routerSpy: jasmine.SpyObj<Router>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AuthService,
        { provide: Router, useValue: spy }
      ]
    });

    service = TestBed.inject(AuthService);
    httpMock = TestBed.inject(HttpTestingController);
    routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Clear localStorage before each test
    localStorage.clear();
  });

  afterEach(() => {
    httpMock.verify();
    localStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should login successfully', () => {
    const mockCredentials: LoginRequest = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const mockResponse: LoginResponse = {
      token: 'mock-jwt-token',
      message: 'Login successful'
    };

    service.login(mockCredentials).subscribe(response => {
      expect(response).toEqual(mockResponse);
      expect(service.isAuthenticated()).toBeTruthy();
    });

    const req = httpMock.expectOne(`${environment.apiUrl}/auth/login`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(mockCredentials);
    req.flush(mockResponse);
  });

  it('should logout successfully', () => {
    // Set up authenticated state
    localStorage.setItem('authToken', 'mock-token');
    
    service.logout();

    expect(localStorage.getItem('authToken')).toBeNull();
    expect(service.isAuthenticated()).toBeFalsy();
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/login']);
  });

  it('should check if user is authenticated', () => {
    expect(service.isAuthenticated()).toBeFalsy();
    
    // Mock authenticated state
    const authState = service.getCurrentAuthState();
    authState.isAuthenticated = true;
    
    expect(service.isAuthenticated()).toBeFalsy(); // Still false because we didn't properly set the state
  });

  it('should get current user', () => {
    const user = service.getCurrentUser();
    expect(user).toBeNull();
  });

  it('should check user role', () => {
    expect(service.hasRole(Role.PHARMACIST)).toBeFalsy();
  });

  it('should get auth headers', () => {
    localStorage.setItem('authToken', 'test-token');
    const headers = service.getAuthHeaders();
    
    expect(headers.get('Authorization')).toBe('Bearer test-token');
    expect(headers.get('Content-Type')).toBe('application/json');
  });
});

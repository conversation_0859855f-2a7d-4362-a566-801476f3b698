import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryApiResponse,
  CategoryListResponse,
  CategoryDetailResponse,
  CategoryCreateResponse,
  CategoryUpdateResponse,
  CategoryDeleteResponse
} from '../models/category.model';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class CategoryService {
  private apiUrl = `${environment.apiUrl}/categories`;
  
  // State management
  private categoriesSubject = new BehaviorSubject<Category[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string | null>(null);

  // Public observables
  categories$ = this.categoriesSubject.asObservable();
  loading$ = this.loadingSubject.asObservable();
  error$ = this.errorSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  // Get all categories
  getAllCategories(): Observable<CategoryListResponse> {
    this.loadingSubject.next(true);
    this.errorSubject.next(null);

    return this.http.get<CategoryListResponse>(`${this.apiUrl}/getAll`)
      .pipe(
        map(response => {
          console.log('✅ Categories fetched successfully:', response);
          
          if (response.success && response.categories) {
            // Update state
            this.categoriesSubject.next(response.categories);
            this.loadingSubject.next(false);
            
            return response;
          }
          
          this.loadingSubject.next(false);
          return response;
        }),
        catchError(error => {
          console.error('❌ Error fetching categories:', error);
          this.errorSubject.next('Erreur lors du chargement des catégories');
          this.loadingSubject.next(false);
          return throwError(() => error);
        })
      );
  }

  // Get category by ID
  getCategoryById(id: string): Observable<CategoryDetailResponse> {
    return this.http.get<CategoryDetailResponse>(`${this.apiUrl}/get/${id}`)
      .pipe(
        map(response => {
          console.log('✅ Category fetched by ID:', response);
          return response;
        }),
        catchError(error => {
          console.error('❌ Error fetching category by ID:', error);
          return throwError(() => error);
        })
      );
  }

  // Create new category
  createCategory(categoryData: CreateCategoryRequest): Observable<CategoryCreateResponse> {
    const headers = this.getAuthHeaders();

    return this.http.post<CategoryCreateResponse>(`${this.apiUrl}/create`, categoryData, { headers })
      .pipe(
        map(response => {
          console.log('✅ Category created successfully:', response);
          
          if (response.success && response.category) {
            // Add to local state
            this.addCategoryToState(response.category);
          }
          
          return response;
        }),
        catchError(error => {
          console.error('❌ Error creating category:', error);
          return throwError(() => error);
        })
      );
  }

  // Update category
  updateCategory(id: string, categoryData: UpdateCategoryRequest): Observable<CategoryUpdateResponse> {
    const headers = this.getAuthHeaders();

    return this.http.put<CategoryUpdateResponse>(`${this.apiUrl}/update/${id}`, categoryData, { headers })
      .pipe(
        map(response => {
          console.log('✅ Category updated successfully:', response);
          
          if (response.success && response.category) {
            // Update local state
            this.updateCategoryInState(response.category);
          }
          
          return response;
        }),
        catchError(error => {
          console.error('❌ Error updating category:', error);
          return throwError(() => error);
        })
      );
  }

  // Delete category
  deleteCategory(id: string): Observable<CategoryDeleteResponse> {
    const headers = this.getAuthHeaders();

    return this.http.delete<CategoryDeleteResponse>(`${this.apiUrl}/delete/${id}`, { headers })
      .pipe(
        map(response => {
          console.log('✅ Category deleted successfully:', response);
          
          if (response.success) {
            // Remove from local state
            this.removeCategoryFromState(id);
          }
          
          return response;
        }),
        catchError(error => {
          console.error('❌ Error deleting category:', error);
          return throwError(() => error);
        })
      );
  }

  // Helper methods for state management
  private addCategoryToState(category: Category): void {
    const currentCategories = this.categoriesSubject.value;
    this.categoriesSubject.next([...currentCategories, category]);
    console.log('🔄 Category added to local state');
  }

  private updateCategoryInState(updatedCategory: Category): void {
    const currentCategories = this.categoriesSubject.value;
    const updatedCategories = currentCategories.map(c => 
      c.id === updatedCategory.id ? updatedCategory : c
    );
    this.categoriesSubject.next(updatedCategories);
    console.log('🔄 Category updated in local state');
  }

  private removeCategoryFromState(categoryId: string): void {
    const currentCategories = this.categoriesSubject.value;
    const updatedCategories = currentCategories.filter(c => c.id !== categoryId);
    this.categoriesSubject.next(updatedCategories);
    console.log('🔄 Category removed from local state');
  }

  // Helper method to get authentication headers
  private getAuthHeaders(): HttpHeaders {
    return this.authService.getAuthHeaders();
  }

  // Get current categories from state (useful for dropdowns)
  getCurrentCategories(): Category[] {
    return this.categoriesSubject.value;
  }
}

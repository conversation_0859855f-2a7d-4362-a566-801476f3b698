import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, timer } from 'rxjs';
import { map, catchError, tap, switchMap, takeUntil } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  Notification,
  NotificationType,
  NotificationStatus,
  GetUserNotificationsCommand,
  MarkNotificationAsReadCommand,
  UpdateFcmTokenCommand,
  SendNotificationCommand,
  GetNotificationsResponse,
  MarkAsReadResponse,
  UpdateFcmTokenResponse,
  SendNotificationResponse
} from '../models/notification.model';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiUrl = `${environment.apiUrl}/notifications`;
  
  // State management
  private notificationsSubject = new BehaviorSubject<Notification[]>([]);
  private unreadCountSubject = new BehaviorSubject<number>(0);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private pollingSubject = new BehaviorSubject<boolean>(false);

  // Public observables
  notifications$ = this.notificationsSubject.asObservable();
  unreadCount$ = this.unreadCountSubject.asObservable();
  loading$ = this.loadingSubject.asObservable();
  isPolling$ = this.pollingSubject.asObservable();

  // Polling configuration
  private pollingInterval = 30000; // 30 seconds
  private pollingTimer: any;

  constructor(private http: HttpClient) {}

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('authToken');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Get user notifications
  getUserNotifications(params?: Partial<GetUserNotificationsCommand>): Observable<GetNotificationsResponse> {
    this.loadingSubject.next(true);
    
    let httpParams = new HttpParams();
    if (params?.status) httpParams = httpParams.set('status', params.status);
    if (params?.type) httpParams = httpParams.set('type', params.type);
    if (params?.page) httpParams = httpParams.set('page', params.page.toString());
    if (params?.limit) httpParams = httpParams.set('limit', params.limit.toString());

    const headers = this.getAuthHeaders();

    return this.http.get<GetNotificationsResponse>(this.apiUrl, { headers, params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.notificationsSubject.next(response.data.notifications);
            this.unreadCountSubject.next(response.data.unreadCount);
          }
          this.loadingSubject.next(false);
        }),
        catchError(error => {
          console.error('❌ Error fetching notifications:', error);
          this.loadingSubject.next(false);
          return throwError(() => error);
        })
      );
  }

  // Mark notification as read
  markAsRead(notificationId: string): Observable<MarkAsReadResponse> {
    const headers = this.getAuthHeaders();
    
    return this.http.put<MarkAsReadResponse>(`${this.apiUrl}/${notificationId}/read`, {}, { headers })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            // Update local state
            const currentNotifications = this.notificationsSubject.value;
            const updatedNotifications = currentNotifications.map(notification =>
              notification.id === notificationId
                ? { ...notification, status: NotificationStatus.READ, readAt: new Date().toISOString() }
                : notification
            );
            this.notificationsSubject.next(updatedNotifications);
            
            // Update unread count
            const currentUnreadCount = this.unreadCountSubject.value;
            this.unreadCountSubject.next(Math.max(0, currentUnreadCount - 1));
          }
        }),
        catchError(error => {
          console.error('❌ Error marking notification as read:', error);
          return throwError(() => error);
        })
      );
  }

  // Update FCM token
  updateFcmToken(fcmToken: string): Observable<UpdateFcmTokenResponse> {
    const headers = this.getAuthHeaders();
    const body: Omit<UpdateFcmTokenCommand, 'userId'> = { fcmToken };
    
    return this.http.post<UpdateFcmTokenResponse>(`${this.apiUrl}/fcm-token`, body, { headers })
      .pipe(
        tap(response => {
          if (response.success) {
            console.log('✅ FCM token updated successfully:', response.data);
          }
        }),
        catchError(error => {
          console.error('❌ Error updating FCM token:', error);
          return throwError(() => error);
        })
      );
  }

  // Send notification (admin only)
  sendNotification(command: Omit<SendNotificationCommand, 'senderId'>): Observable<SendNotificationResponse> {
    const headers = this.getAuthHeaders();
    
    return this.http.post<SendNotificationResponse>(`${this.apiUrl}/send`, command, { headers })
      .pipe(
        catchError(error => {
          console.error('❌ Error sending notification:', error);
          return throwError(() => error);
        })
      );
  }

  // Start polling for new notifications
  startPolling(): void {
    if (this.pollingTimer) {
      this.stopPolling();
    }

    this.pollingSubject.next(true);
    console.log('🔄 Starting notification polling...');

    // Initial fetch
    this.getUserNotifications({ limit: 20 }).subscribe();

    // Set up polling timer
    this.pollingTimer = timer(this.pollingInterval, this.pollingInterval)
      .pipe(
        switchMap(() => this.getUserNotifications({ limit: 20 })),
        takeUntil(this.pollingSubject.pipe(map(isPolling => !isPolling)))
      )
      .subscribe({
        next: (response) => {
          console.log('🔄 Polling update received:', response.data?.unreadCount, 'unread notifications');
        },
        error: (error) => {
          console.error('❌ Polling error:', error);
          // Continue polling even if there's an error
        }
      });
  }

  // Stop polling
  stopPolling(): void {
    if (this.pollingTimer) {
      this.pollingTimer.unsubscribe();
      this.pollingTimer = null;
    }
    this.pollingSubject.next(false);
    console.log('⏹️ Notification polling stopped');
  }

  // Get current notifications
  getCurrentNotifications(): Notification[] {
    return this.notificationsSubject.value;
  }

  // Get current unread count
  getCurrentUnreadCount(): number {
    return this.unreadCountSubject.value;
  }

  // Mark all notifications as read
  markAllAsRead(): Observable<any> {
    const unreadNotifications = this.getCurrentNotifications()
      .filter(notification => notification.status === NotificationStatus.UNREAD);

    if (unreadNotifications.length === 0) {
      return new Observable(observer => {
        observer.next({ success: true, message: 'No unread notifications' });
        observer.complete();
      });
    }

    // Mark each unread notification as read
    const markAsReadRequests = unreadNotifications.map(notification =>
      this.markAsRead(notification.id)
    );

    // Execute all requests
    return new Observable(observer => {
      let completed = 0;
      const total = markAsReadRequests.length;

      markAsReadRequests.forEach(request => {
        request.subscribe({
          next: () => {
            completed++;
            if (completed === total) {
              observer.next({ success: true, message: 'All notifications marked as read' });
              observer.complete();
            }
          },
          error: (error) => {
            observer.error(error);
          }
        });
      });
    });
  }

  // Refresh notifications
  refreshNotifications(): void {
    this.getUserNotifications({ limit: 20 }).subscribe();
  }

  // Clear all notifications from local state
  clearNotifications(): void {
    this.notificationsSubject.next([]);
    this.unreadCountSubject.next(0);
  }
}

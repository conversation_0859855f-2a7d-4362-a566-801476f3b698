import { Injectable } from '@angular/core';
import { initializeApp, FirebaseApp } from 'firebase/app';
import { getMessaging, getToken, onMessage, Messaging, MessagePayload } from 'firebase/messaging';
import { environment } from '../../environments/environment';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {
  private app: FirebaseApp | null = null;
  private messaging: Messaging | null = null;
  private fcmTokenSubject = new BehaviorSubject<string | null>(null);
  private messageSubject = new BehaviorSubject<MessagePayload | null>(null);

  // Public observables
  fcmToken$ = this.fcmTokenSubject.asObservable();
  message$ = this.messageSubject.asObservable();

  constructor() {
    this.initializeFirebase();
  }

  private initializeFirebase(): void {
    try {
      // Initialize Firebase
      this.app = initializeApp(environment.firebase);
      
      // Initialize Firebase Cloud Messaging
      if (this.isMessagingSupported()) {
        this.messaging = getMessaging(this.app);
        this.setupMessageListener();
      } else {
        console.warn('🔥 Firebase Messaging is not supported in this browser');
      }
    } catch (error) {
      console.error('🔥 Error initializing Firebase:', error);
    }
  }

  private isMessagingSupported(): boolean {
    return 'serviceWorker' in navigator && 'PushManager' in window;
  }

  private setupMessageListener(): void {
    if (!this.messaging) return;

    // Listen for foreground messages
    onMessage(this.messaging, (payload) => {
      console.log('🔥 Foreground message received:', payload);
      this.messageSubject.next(payload);
      
      // Show browser notification if permission is granted
      this.showBrowserNotification(payload);
    });
  }

  async requestPermission(): Promise<boolean> {
    try {
      if (!('Notification' in window)) {
        console.warn('🔥 This browser does not support notifications');
        return false;
      }

      const permission = await Notification.requestPermission();
      console.log('🔥 Notification permission:', permission);
      
      return permission === 'granted';
    } catch (error) {
      console.error('🔥 Error requesting notification permission:', error);
      return false;
    }
  }

  async getFcmToken(): Promise<string | null> {
    try {
      if (!this.messaging) {
        console.warn('🔥 Firebase Messaging not initialized');
        return null;
      }

      // Request permission first
      const hasPermission = await this.requestPermission();
      if (!hasPermission) {
        console.warn('🔥 Notification permission denied');
        return null;
      }

      // Get FCM token
      const token = await getToken(this.messaging, {
        vapidKey: environment.firebase.vapidKey
      });

      if (token) {
        console.log('🔥 FCM Token generated:', token);
        this.fcmTokenSubject.next(token);
        return token;
      } else {
        console.warn('🔥 No registration token available');
        return null;
      }
    } catch (error) {
      console.error('🔥 Error getting FCM token:', error);
      return null;
    }
  }

  private showBrowserNotification(payload: MessagePayload): void {
    if (Notification.permission !== 'granted') return;

    const notificationTitle = payload.notification?.title || 'Med4 Solutions';
    const notificationOptions: NotificationOptions = {
      body: payload.notification?.body || '',
      icon: payload.notification?.icon || '/assets/images/med4-logo-login.png',
      badge: '/assets/images/med4-logo-login.png',
      tag: payload.data?.['notificationId'] || 'med4-notification',
      data: payload.data,
      requireInteraction: true
    };

    const notification = new Notification(notificationTitle, notificationOptions);

    notification.onclick = (event) => {
      event.preventDefault();
      window.focus();
      notification.close();
      
      // Handle notification click - you can navigate to specific routes here
      if (payload.data?.['type']) {
        this.handleNotificationClick(payload.data);
      }
    };
  }

  private handleNotificationClick(data: any): void {
    // Handle different notification types
    switch (data['type']) {
      case 'PRESCRIPTION_ADDED':
        // Navigate to prescriptions page
        window.location.href = '#/prescriptions';
        break;
      case 'PACKAGE_READY':
        // Navigate to orders page
        window.location.href = '#/orders';
        break;
      case 'DELIVERY_ASSIGNED':
      case 'DELIVERY_COMPLETED':
        // Navigate to deliveries page
        window.location.href = '#/orders';
        break;
      default:
        // Navigate to dashboard
        window.location.href = '#/dashboard';
        break;
    }
  }



  // Get current FCM token
  getCurrentToken(): string | null {
    return this.fcmTokenSubject.value;
  }

  // Check if notifications are supported
  isNotificationSupported(): boolean {
    return 'Notification' in window && this.isMessagingSupported();
  }

  // Get notification permission status
  getNotificationPermission(): NotificationPermission {
    return Notification.permission;
  }

  // Show a test notification (for testing purposes)
  showTestNotification(title: string, body: string, data?: any): void {
    if (Notification.permission === 'granted') {
      const notification = new Notification(title, {
        body,
        icon: '/assets/images/med4-logo-login.png',
        badge: '/assets/images/med4-logo-login.png',
        tag: 'test-notification',
        data,
        requireInteraction: true
      });

      notification.onclick = () => {
        console.log('🔔 Test notification clicked');
        notification.close();
        window.focus();
      };

      // Auto close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);
    } else {
      console.warn('🔔 Notification permission not granted');
    }
  }
}

import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, Subject, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import {
  Product,
  CreateProductRequest,
  UpdateProductRequest,
  ProductFilters,
  ProductApiResponse,
  ProductFileUploadResponse,
  ProductListResponse,
  ProductDetailResponse,
  ProductCreateResponse,
  ProductUpdateResponse,
  ProductDeleteResponse,
  StockStatus
} from '../models/product.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private apiUrl = `${environment.apiUrl}/products`;
  
  // State management
  private productsSubject = new BehaviorSubject<Product[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string | null>(null);
  
  // Listener pattern for component communication
  private _listeners = new Subject<any>();

  // Public observables
  products$ = this.productsSubject.asObservable();
  loading$ = this.loadingSubject.asObservable();
  error$ = this.errorSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Get filtered products for pharmacy (using the getAllForPharmacy endpoint)
  getProductsForPharmacy(filters?: ProductFilters): Observable<ProductListResponse> {
    this.loadingSubject.next(true);
    this.errorSubject.next(null);

    let params = new HttpParams();

    // Add search filter if provided
    if (filters?.search) {
      params = params.set('search', filters.search);
    }

    // Add stock status filter if provided
    if (filters?.stockStatus) {
      params = params.set('stockStatus', filters.stockStatus);
    }

    // Add active status filter if provided
    if (filters?.actif !== undefined) {
      params = params.set('actif', filters.actif.toString());
    }

    // Add pagination if provided
    if (filters?.page) {
      params = params.set('page', filters.page.toString());
    }

    if (filters?.limit) {
      params = params.set('limit', filters.limit.toString());
    }

    const headers = this.getAuthHeaders();

    return this.http.get<ProductListResponse>(`${this.apiUrl}/getAllForPharmacy`, { headers, params })
      .pipe(
        map(response => {
          console.log('✅ Products fetched successfully:', response);
          
          if (response.success && response.products) {
            // Process products for display
            const processedProducts = response.products.map(product => ({
              ...product,
              imageUrl: product.storagePath && product.storagePath.length > 0
                ? `${environment.fileBaseUrl}${product.storagePath[0]}`
                : null
            }));

            // Update state
            this.productsSubject.next(processedProducts);
            this.loadingSubject.next(false);
            
            return {
              ...response,
              products: processedProducts
            };
          }
          
          this.loadingSubject.next(false);
          return response;
        }),
        catchError(error => {
          console.error('❌ Error fetching products:', error);
          this.errorSubject.next('Erreur lors du chargement des produits');
          this.loadingSubject.next(false);
          return throwError(() => error);
        })
      );
  }

  // Upload product image file
  uploadProductImage(file: File): Observable<ProductFileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const headers = this.getAuthHeaders();
    // Remove Content-Type header to let browser set it with boundary for FormData
    delete headers['Content-Type'];

    return this.http.post<ProductFileUploadResponse>(`${this.apiUrl}/uploadFile`, formData, { headers })
      .pipe(
        map(response => {
          console.log('✅ File uploaded successfully:', response);
          return response;
        }),
        catchError(error => {
          console.error('❌ Error uploading file:', error);
          return throwError(() => error);
        })
      );
  }

  // Create new product
  createProduct(productData: CreateProductRequest): Observable<ProductCreateResponse> {
    const headers = this.getAuthHeaders();

    return this.http.post<ProductCreateResponse>(`${this.apiUrl}/create`, productData, { headers })
      .pipe(
        map(response => {
          console.log('✅ Product created successfully:', response);
          
          if (response.success && response.product) {
            // Add to local state for lazy loading
            this.addProductToState(response.product);
            
            // Trigger listener for component refresh
            this.filter('ProductCreated');
          }
          
          return response;
        }),
        catchError(error => {
          console.error('❌ Error creating product:', error);
          return throwError(() => error);
        })
      );
  }

  // Upload image and create product workflow
  uploadImageAndCreateProduct(file: File, productData: CreateProductRequest): Observable<ProductCreateResponse> {
    // First upload the file
    return this.uploadProductImage(file).pipe(
      switchMap(uploadResponse => {
        console.log('File uploaded successfully:', uploadResponse);

        if (!uploadResponse.filePath) {
          throw new Error('File upload failed: No file path returned');
        }

        // Then create the product with the uploaded file path
        const createRequest: CreateProductRequest = {
          ...productData,
          storagePath: [uploadResponse.filePath]
        };

        return this.createProduct(createRequest);
      }),
      catchError(error => {
        console.error('Upload and create workflow error:', error);
        return throwError(() => error);
      })
    );
  }

  // Get product by ID
  getProductById(id: string): Observable<ProductDetailResponse> {
    const headers = this.getAuthHeaders();

    return this.http.get<ProductDetailResponse>(`${this.apiUrl}/get/${id}`, { headers })
      .pipe(
        map(response => {
          console.log('✅ Product fetched by ID:', response);
          
          if (response.success && response.product) {
            // Process product for display
            const processedProduct = {
              ...response.product,
              imageUrl: response.product.storagePath && response.product.storagePath.length > 0
                ? `${environment.fileBaseUrl}${response.product.storagePath[0]}`
                : null
            };

            return {
              ...response,
              product: processedProduct
            };
          }
          
          return response;
        }),
        catchError(error => {
          console.error('❌ Error fetching product by ID:', error);
          return throwError(() => error);
        })
      );
  }

  // Update product
  updateProduct(id: string, productData: UpdateProductRequest): Observable<ProductUpdateResponse> {
    const headers = this.getAuthHeaders();

    return this.http.put<ProductUpdateResponse>(`${this.apiUrl}/update/${id}`, productData, { headers })
      .pipe(
        map(response => {
          console.log('✅ Product updated successfully:', response);
          
          if (response.success && response.product) {
            // Update local state for lazy loading
            this.updateProductInState(response.product);
            
            // Trigger listener for component refresh
            this.filter('ProductUpdated');
          }
          
          return response;
        }),
        catchError(error => {
          console.error('❌ Error updating product:', error);
          return throwError(() => error);
        })
      );
  }

  // Delete product
  deleteProduct(id: string): Observable<ProductDeleteResponse> {
    const headers = this.getAuthHeaders();

    return this.http.delete<ProductDeleteResponse>(`${this.apiUrl}/delete/${id}`, { headers })
      .pipe(
        map(response => {
          console.log('✅ Product deleted successfully:', response);
          
          if (response.success) {
            // Remove from local state for lazy loading
            this.removeProductFromState(id);
            
            // Trigger listener for component refresh
            this.filter('ProductDeleted');
          }
          
          return response;
        }),
        catchError(error => {
          console.error('❌ Error deleting product:', error);
          return throwError(() => error);
        })
      );
  }

  // Helper methods for state management
  private addProductToState(product: Product): void {
    const currentProducts = this.productsSubject.value;
    const processedProduct = {
      ...product,
      imageUrl: product.storagePath && product.storagePath.length > 0
        ? `${environment.fileBaseUrl}${product.storagePath[0]}`
        : null
    };
    this.productsSubject.next([processedProduct, ...currentProducts]);
    console.log('🔄 Product added to local state - lazy loading update');
  }

  private updateProductInState(updatedProduct: Product): void {
    const currentProducts = this.productsSubject.value;
    const processedProduct = {
      ...updatedProduct,
      imageUrl: updatedProduct.storagePath && updatedProduct.storagePath.length > 0
        ? `${environment.fileBaseUrl}${updatedProduct.storagePath[0]}`
        : null
    };
    const updatedProducts = currentProducts.map(p => 
      p.id === updatedProduct.id ? processedProduct : p
    );
    this.productsSubject.next(updatedProducts);
    console.log('🔄 Product updated in local state - lazy loading update');
  }

  private removeProductFromState(productId: string): void {
    const currentProducts = this.productsSubject.value;
    const updatedProducts = currentProducts.filter(p => p.id !== productId);
    this.productsSubject.next(updatedProducts);
    console.log('🔄 Product removed from local state - lazy loading update');
  }

  // Listener pattern methods
  listen(): Observable<any> {
    return this._listeners.asObservable();
  }

  filter(filterBy: string): void {
    console.log('🔔 Broadcasting event:', filterBy);
    this._listeners.next(filterBy);
  }

  // Helper method to get authentication headers
  private getAuthHeaders(): any {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  // Helper method to get stock status badge class
  getStockStatusBadgeClass(status: StockStatus): string {
    switch (status) {
      case StockStatus.IN_STOCK:
        return 'badge-in-stock';
      case StockStatus.OUT_OF_STOCK:
        return 'badge-out-of-stock';
      default:
        return 'badge-unknown';
    }
  }

  // Helper method to get active status badge class
  getActiveStatusBadgeClass(actif: boolean): string {
    return actif ? 'badge-active' : 'badge-inactive';
  }
}

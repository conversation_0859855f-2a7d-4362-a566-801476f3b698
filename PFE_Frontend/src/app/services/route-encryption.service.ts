import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class RouteEncryptionService {
  private readonly secretKey = 'Med4Solutions2024!@#$%^&*()_+{}|:<>?[]\\;\'\",./-=`~';
  
  constructor() {}

  /**
   * Encrypts an ID for use in route parameters
   * @param id The ID to encrypt
   * @returns Encrypted string safe for URL use
   */
  encryptId(id: string): string {
    try {
      // Convert ID to base64 first
      const base64Id = btoa(id);
      
      // Simple XOR encryption with the secret key
      let encrypted = '';
      for (let i = 0; i < base64Id.length; i++) {
        const charCode = base64Id.charCodeAt(i);
        const keyChar = this.secretKey.charCodeAt(i % this.secretKey.length);
        encrypted += String.fromCharCode(charCode ^ keyChar);
      }
      
      // Convert to base64 and make URL safe
      const encryptedBase64 = btoa(encrypted);
      return this.makeUrlSafe(encryptedBase64);
    } catch (error) {
      console.error('Error encrypting ID:', error);
      // Fallback to original ID if encryption fails
      return id;
    }
  }

  /**
   * Decrypts an encrypted ID from route parameters
   * @param encryptedId The encrypted ID from the route
   * @returns Original ID or null if decryption fails
   */
  decryptId(encryptedId: string): string | null {
    try {
      // Restore from URL safe format
      const base64Encrypted = this.restoreFromUrlSafe(encryptedId);
      
      // Decode from base64
      const encrypted = atob(base64Encrypted);
      
      // XOR decrypt with the secret key
      let decrypted = '';
      for (let i = 0; i < encrypted.length; i++) {
        const charCode = encrypted.charCodeAt(i);
        const keyChar = this.secretKey.charCodeAt(i % this.secretKey.length);
        decrypted += String.fromCharCode(charCode ^ keyChar);
      }
      
      // Decode from base64 to get original ID
      return atob(decrypted);
    } catch (error) {
      console.error('Error decrypting ID:', error);
      // Return the encrypted ID as fallback (in case it's not encrypted)
      return encryptedId;
    }
  }

  /**
   * Makes a base64 string URL safe by replacing problematic characters
   * @param base64String The base64 string to make URL safe
   * @returns URL safe string
   */
  private makeUrlSafe(base64String: string): string {
    return base64String
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * Restores a URL safe string back to base64 format
   * @param urlSafeString The URL safe string to restore
   * @returns Base64 string
   */
  private restoreFromUrlSafe(urlSafeString: string): string {
    let base64 = urlSafeString
      .replace(/-/g, '+')
      .replace(/_/g, '/');
    
    // Add padding if needed
    while (base64.length % 4) {
      base64 += '=';
    }
    
    return base64;
  }

  /**
   * Validates if a string appears to be an encrypted ID
   * @param value The value to check
   * @returns True if it appears to be encrypted
   */
  isEncrypted(value: string): boolean {
    // Check if the value contains URL-safe base64 characters
    const urlSafeBase64Pattern = /^[A-Za-z0-9\-_]+$/;
    return urlSafeBase64Pattern.test(value) && value.length > 10;
  }
}

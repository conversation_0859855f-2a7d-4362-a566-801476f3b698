import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
  HouseHold,
  HouseHoldResponse,
  HouseHoldSearchResponse
} from '../models/household.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class HouseHoldService {
  private readonly apiUrl = `${environment.apiUrl}/household`;

  // State management
  private householdsSubject = new BehaviorSubject<HouseHold[]>([]);
  public households$ = this.householdsSubject.asObservable();

  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Get authentication headers
  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('authToken');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Get household by ID (matching your existing endpoint)
  getHouseHoldById(householdId: string): Observable<HouseHold> {
    const headers = this.getAuthHeaders();
    const url = `${this.apiUrl}/getHouseHoldDetails/${householdId}`;

    console.log('Fetching household:', householdId);

    return this.http.get<HouseHoldResponse>(url, { headers })
      .pipe(
        map(response => {
          console.log('Household response:', response);

          if (response.success && response.data) {
            const household = this.enrichHouseHoldData(response.data);
            return household;
          }
          throw new Error('Failed to fetch household');
        }),
        catchError(error => {
          console.error('Get household error:', error);
          return throwError(() => error);
        })
      );
  }

  // Search households by name (using the new /search endpoint)
  searchHouseholdsByName(query: string): Observable<HouseHold[]> {
    if (!query || query.trim().length < 2) {
      return new Observable(observer => {
        observer.next([]);
        observer.complete();
      });
    }

    const headers = this.getAuthHeaders();
    const params = new HttpParams().set('query', query.trim());
    const url = `${this.apiUrl}/search`;

    console.log('Searching households with query:', query);

    return this.http.get<HouseHoldSearchResponse>(url, { headers, params })
      .pipe(
        map(response => {
          console.log('Household search response:', response);

          if (response.success && response.households) {
            // Enrich each household with computed properties
            const enrichedHouseholds = response.households.map(household => this.enrichHouseHoldData(household));
            return enrichedHouseholds;
          }
          return [];
        }),
        catchError(error => {
          console.error('Search households error:', error);
          return throwError(() => error);
        })
      );
  }

  // Enrich household data with computed properties
  private enrichHouseHoldData(household: HouseHold): HouseHold {
    return {
      ...household,
      displayName: household.name || `Foyer #${household.id}`,
      displayAddress: this.formatAddress(household.address),
      contactInfo: this.formatContactInfo(household)
    };
  }

  // Format household address for display
  private formatAddress(address: any): string {
    if (!address || !address.address) {
      return 'Adresse non disponible';
    }
    return address.address;
  }

  // Format contact information
  private formatContactInfo(household: HouseHold): string {
    const parts = [];
    if (household.responsibleName) parts.push(household.responsibleName);
    if (household.phoneNumber) parts.push(household.phoneNumber);
    if (household.email) parts.push(household.email);
    
    return parts.length > 0 ? parts.join(' • ') : 'Contact non disponible';
  }

  // Get household display name
  getHouseHoldDisplayName(household: HouseHold): string {
    return household.displayName || household.name || `Foyer #${household.id}`;
  }

  // Get responsible person name
  getResponsiblePersonName(household: HouseHold): string {
    return household.responsibleName || 'Non spécifié';
  }

  // Format household phone number
  formatHouseHoldPhone(phone: string): string {
    if (!phone) return '';
    
    // Basic phone formatting - adjust based on your needs
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  }

  // Check if household has delivery notes
  hasDeliveryNotes(household: HouseHold): boolean {
    return !!(household.deliveryManNote || household.pharmacyNote || household.note);
  }

  // Get all delivery notes combined
  getAllNotes(household: HouseHold): string {
    const notes = [];
    if (household.note) notes.push(`Note: ${household.note}`);
    if (household.deliveryManNote) notes.push(`Livreur: ${household.deliveryManNote}`);
    if (household.pharmacyNote) notes.push(`Pharmacie: ${household.pharmacyNote}`);
    
    return notes.length > 0 ? notes.join(' | ') : 'Aucune note';
  }

  // Check if household has recurrence option
  hasRecurrenceOption(household: HouseHold): boolean {
    return !!household.recurrenceOption;
  }

  // Get recurrence frequency text
  getRecurrenceFrequencyText(household: HouseHold): string {
    if (!household.recurrenceOption) {
      return 'Aucune récurrence';
    }
    return household.recurrenceOption.frequency || 'Fréquence non spécifiée';
  }

  // Format household email
  formatHouseHoldEmail(email?: string): string {
    return email || 'Email non disponible';
  }

  // Get household status (active/deleted)
  getHouseHoldStatus(household: HouseHold): string {
    return household.deletedAt ? 'Supprimé' : 'Actif';
  }

  // Get household status badge class
  getHouseHoldStatusBadgeClass(household: HouseHold): string {
    return household.deletedAt ? 'badge-deleted' : 'badge-active';
  }
}

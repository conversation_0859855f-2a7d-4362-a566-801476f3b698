import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
  Patient,
  PatientResponse,
  PatientSearchResponse
} from '../models/patient.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PatientService {
  private readonly apiUrl = `${environment.apiUrl}/patient`; // Adjust based on your endpoint

  // State management
  private patientsSubject = new BehaviorSubject<Patient[]>([]);
  public patients$ = this.patientsSubject.asObservable();

  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Get authentication headers
  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('authToken');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Get patient by ID (you'll need to implement this endpoint)
  getPatientById(patientId: string): Observable<Patient> {
    const headers = this.getAuthHeaders();
    const url = `${this.apiUrl}/getPatientInfo/${patientId}`; // Adjust endpoint name

    console.log('Fetching patient:', patientId);

    return this.http.get<PatientResponse>(url, { headers })
      .pipe(
        map(response => {
          console.log('Patient response:', response);

          if (response.success && response.data) {
            const patient = this.enrichPatientData(response.data);
            return patient;
          }
          throw new Error('Failed to fetch patient');
        }),
        catchError(error => {
          console.error('Get patient error:', error);
          return throwError(() => error);
        })
      );
  }

  // Search patients by name (using the new /search endpoint)
  searchPatientsByName(query: string): Observable<Patient[]> {
    if (!query || query.trim().length < 2) {
      return new Observable(observer => {
        observer.next([]);
        observer.complete();
      });
    }

    const headers = this.getAuthHeaders();
    const params = new HttpParams().set('query', query.trim());
    const url = `${this.apiUrl}/search`;

    console.log('Searching patients with query:', query);

    return this.http.get<PatientSearchResponse>(url, { headers, params })
      .pipe(
        map(response => {
          console.log('Patient search response:', response);

          if (response.success && response.patients) {
            // Enrich each patient with computed properties
            const enrichedPatients = response.patients.map(patient => this.enrichPatientData(patient));
            return enrichedPatients;
          }
          return [];
        }),
        catchError(error => {
          console.error('Search patients error:', error);
          return throwError(() => error);
        })
      );
  }

  // Enrich patient data with computed properties
  private enrichPatientData(patient: Patient): Patient {
    return {
      ...patient,
      fullName: `${patient.firstName} ${patient.lastName}`,
      initials: this.generateInitials(patient.firstName, patient.lastName),
      primaryAddress: this.getPrimaryAddress(patient.address)
    };
  }

  // Generate initials from first and last name
  generateInitials(firstName: string, lastName: string): string {
    const firstInitial = firstName?.charAt(0)?.toUpperCase() || '';
    const lastInitial = lastName?.charAt(0)?.toUpperCase() || '';
    return `${firstInitial}${lastInitial}`;
  }

  // Get primary address string
  private getPrimaryAddress(addresses: any[]): string {
    if (!addresses || addresses.length === 0) {
      return 'Adresse non disponible';
    }
    
    const primaryAddress = addresses[0];
    return primaryAddress.address || 'Adresse non disponible';
  }

  // Format patient display name
  getPatientDisplayName(patient: Patient): string {
    if (patient.fullName) {
      return patient.fullName;
    }
    return `${patient.firstName} ${patient.lastName}`;
  }

  // Get patient initials for avatar
  getPatientInitials(patient: Patient): string {
    if (patient.initials) {
      return patient.initials;
    }
    return this.generateInitials(patient.firstName, patient.lastName);
  }

  // Check if patient is hospitalized
  isPatientHospitalized(patient: Patient): boolean {
    return patient.hospitalizedPatient || false;
  }

  // Check if patient is on vacation
  isPatientOnVacation(patient: Patient): boolean {
    if (!patient.vacationPeriod) {
      return false;
    }
    
    const now = new Date();
    const start = new Date(patient.vacationPeriod.start);
    const end = new Date(patient.vacationPeriod.end);
    
    return now >= start && now <= end;
  }

  // Get patient status text
  getPatientStatusText(patient: Patient): string {
    if (this.isPatientHospitalized(patient)) {
      return 'Hospitalisé';
    }
    if (this.isPatientOnVacation(patient)) {
      return 'En vacances';
    }
    return 'Actif';
  }

  // Get patient status badge class
  getPatientStatusBadgeClass(patient: Patient): string {
    if (this.isPatientHospitalized(patient)) {
      return 'badge-hospitalized';
    }
    if (this.isPatientOnVacation(patient)) {
      return 'badge-vacation';
    }
    return 'badge-active';
  }

  // Format patient phone number
  formatPhoneNumber(phone: string): string {
    if (!phone) return '';
    
    // Basic phone formatting - adjust based on your needs
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  }

  // Get delivery types as formatted string
  getDeliveryTypesText(deliveryTypes: string[]): string {
    if (!deliveryTypes || deliveryTypes.length === 0) {
      return 'Non spécifié';
    }
    return deliveryTypes.join(', ');
  }
}

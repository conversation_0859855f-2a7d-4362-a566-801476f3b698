import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';
import { Router } from '@angular/router';
import {
  User,
  UserProperties,
  LoginRequest,
  LoginResponse,
  JwtPayload,
  AuthState,
  PasswordResetRequest,
  PasswordResetConfirm,
  ChangePasswordRequest,
  ApiErrorResponse,
  Role
} from '../models/user.model';
import { environment } from '../../environments/environment';
import { FirebaseService } from './firebase.service';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = `${environment.apiUrl}/user`;
  
  // State management
  private authStateSubject = new BehaviorSubject<AuthState>({
    isAuthenticated: false,
    user: null,
    userProperties: null,
    token: null,
    requiresPasswordReset: false
  });

  // Public observables
  authState$ = this.authStateSubject.asObservable();
  isAuthenticated$ = this.authState$.pipe(map(state => state.isAuthenticated));
  user$ = this.authState$.pipe(map(state => state.user));
  requiresPasswordReset$ = this.authState$.pipe(map(state => state.requiresPasswordReset));

  constructor(
    private http: HttpClient,
    private router: Router,
    private firebaseService: FirebaseService,
    private notificationService: NotificationService
  ) {
    // Initialize auth state from localStorage on service creation
    this.initializeAuthState();
  }

  // Initialize authentication state from localStorage
  private initializeAuthState(): void {
    const token = this.getStoredToken();
    if (token && this.isTokenValid(token)) {
      const payload = this.decodeToken(token);
      if (payload) {
        // Convert role to number if it's a string
        const roleNumber = typeof payload.role === 'string' ? parseInt(payload.role, 10) : payload.role;

        const user: User = {
          id: payload.id,
          email: payload.email,
          role: roleNumber,
          phone: payload.phoneNumber
        };

        this.authStateSubject.next({
          isAuthenticated: true,
          user,
          userProperties: null, // Will be loaded separately if needed
          token,
          requiresPasswordReset: false
        });
      }
    }
  }

  // Login method
  login(credentials: LoginRequest): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${this.apiUrl}/login`, credentials)
      .pipe(
        tap(response => {
          console.log('✅ Login successful:', response);

          // Extract token string from response
          let tokenString: string;
          if (typeof response.token === 'string') {
            tokenString = response.token;
          } else if (typeof response.token === 'object' && response.token !== null) {
            // If token is an object, try to extract the actual token string
            tokenString = (response.token as any).token || (response.token as any).accessToken || JSON.stringify(response.token);
          } else {
            throw new Error('Invalid token format received from server');
          }

          console.log('🔑 Token extracted:', tokenString);

          // Store token
          this.storeToken(tokenString);

          // Decode token to get user info
          const payload = this.decodeToken(tokenString);
          if (payload) {
            console.log('🔍 JWT Payload:', payload);
            console.log('🔍 User role from payload:', payload.role, 'Type:', typeof payload.role);

            // Convert role to number if it's a string
            const roleNumber = typeof payload.role === 'string' ? parseInt(payload.role, 10) : payload.role;
            console.log('🔄 Role converted to:', roleNumber, 'Type:', typeof roleNumber);

            const user: User = {
              id: payload.id,
              email: payload.email,
              role: roleNumber,
              phone: payload.phoneNumber
            };

            console.log('👤 User object created:', user);
            console.log('🔍 User role in user object:', user.role, 'Type:', typeof user.role);

            // Update auth state
            this.authStateSubject.next({
              isAuthenticated: true,
              user,
              userProperties: null,
              token: tokenString,
              requiresPasswordReset: response.requiresPasswordReset || false
            });

            // Initialize FCM and notifications after successful login
            this.initializeFcmAndNotifications();
          }
        }),
        catchError(error => {
          console.error('❌ Login failed:', error);
          return throwError(() => error);
        })
      );
  }

  // Initialize FCM and notifications after login
  private async initializeFcmAndNotifications(): Promise<void> {
    try {
      console.log('🔥 Initializing FCM and notifications...');

      // Get FCM token
      const fcmToken = await this.firebaseService.getFcmToken();

      if (fcmToken) {
        // Update FCM token on backend
        this.notificationService.updateFcmToken(fcmToken).subscribe({
          next: (response) => {
            console.log('✅ FCM token registered successfully:', response);

            // Start polling for notifications
            this.notificationService.startPolling();
          },
          error: (error) => {
            console.error('❌ Failed to register FCM token:', error);
            // Still start polling even if FCM registration fails
            this.notificationService.startPolling();
          }
        });
      } else {
        console.warn('🔥 No FCM token available, starting polling without FCM');
        // Start polling even without FCM token
        this.notificationService.startPolling();
      }
    } catch (error) {
      console.error('🔥 Error initializing FCM and notifications:', error);
      // Start polling as fallback
      this.notificationService.startPolling();
    }
  }

  // Logout method
  logout(): void {
    // Stop notification polling
    this.notificationService.stopPolling();

    // Clear notifications
    this.notificationService.clearNotifications();

    // Clear stored token
    this.clearStoredToken();

    // Reset auth state
    this.authStateSubject.next({
      isAuthenticated: false,
      user: null,
      userProperties: null,
      token: null,
      requiresPasswordReset: false
    });

    // Navigate to login page
    this.router.navigate(['/login']);
    
    console.log('✅ User logged out successfully');
  }

  // Get current auth state
  getCurrentAuthState(): AuthState {
    return this.authStateSubject.value;
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.authStateSubject.value.user;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.authStateSubject.value.isAuthenticated;
  }

  // Check if user has specific role
  hasRole(role: Role): boolean {
    const user = this.getCurrentUser();
    return user?.role === role;
  }

  // Check if user has any of the allowed roles
  hasAnyRole(roles: Role[]): boolean {
    const user = this.getCurrentUser();
    console.log('🔍 hasAnyRole check - User:', user?.email, 'Role:', user?.role);
    console.log('🔍 hasAnyRole check - Allowed roles:', roles);
    console.log('🔍 hasAnyRole check - Includes check:', user ? roles.includes(user.role) : false);
    return user ? roles.includes(user.role) : false;
  }

  // Get user-friendly role name for display
  getRoleDisplayName(role: Role): string {
    switch (role) {
      case Role.PATIENT:
        return 'Patient';
      case Role.INTERNAL_DELIVERY_MAN:
        return 'Livreur Interne';
      case Role.EXTERNAL_DELIVERY_MAN:
        return 'Livreur Externe';
      case Role.INTERNAL_PHARMACY:
        return 'Pharmacie Interne';
      case Role.EXTERNAL_PHARMACY:
        return 'Pharmacie Externe';
      case Role.ADMIN:
        return 'Administrateur';
      default:
        return 'Utilisateur';
    }
  }

  // Check if user can access the application (only roles 4 and 6)
  canAccessApplication(): boolean {
    const user = this.getCurrentUser();
    console.log('🔍 Checking application access for user:', user);
    console.log('🔍 User role:', user?.role, 'Type:', typeof user?.role);
    console.log('🔍 Role.INTERNAL_PHARMACY:', Role.INTERNAL_PHARMACY, 'Type:', typeof Role.INTERNAL_PHARMACY);
    console.log('🔍 Role.ADMIN:', Role.ADMIN, 'Type:', typeof Role.ADMIN);

    const hasAccess = this.hasAnyRole([Role.INTERNAL_PHARMACY, Role.ADMIN]);
    console.log('🔍 Has access:', hasAccess);

    return hasAccess;
  }

  // Get authentication headers for API calls
  getAuthHeaders(): HttpHeaders {
    const token = this.getStoredToken();
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });
  }

  // Token management methods
  private storeToken(token: string): void {
    localStorage.setItem('authToken', token);
  }

  private getStoredToken(): string | null {
    return localStorage.getItem('authToken');
  }

  private clearStoredToken(): void {
    localStorage.removeItem('authToken');
  }

  // Token validation and decoding
  private isTokenValid(token: string): boolean {
    try {
      const payload = this.decodeToken(token);
      if (!payload || !payload.exp) return false;
      
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp > currentTime;
    } catch (error) {
      return false;
    }
  }

  private decodeToken(token: string): JwtPayload | null {
    try {
      // Validate token format
      if (!token || typeof token !== 'string') {
        console.error('❌ Invalid token: not a string');
        return null;
      }

      const parts = token.split('.');
      if (parts.length !== 3) {
        console.error('❌ Invalid JWT format: expected 3 parts, got', parts.length);
        return null;
      }

      const base64Url = parts[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );

      const payload = JSON.parse(jsonPayload);
      console.log('🔓 Token decoded successfully:', payload);
      return payload;
    } catch (error) {
      console.error('❌ Error decoding token:', error);
      console.error('❌ Token that failed to decode:', token);
      return null;
    }
  }

  // Password reset methods (for future implementation)
  requestPasswordReset(request: PasswordResetRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/request-password-reset`, request);
  }

  confirmPasswordReset(request: PasswordResetConfirm): Observable<any> {
    return this.http.post(`${this.apiUrl}/confirm-password-reset`, request);
  }

  changePassword(request: ChangePasswordRequest): Observable<any> {
    const headers = this.getAuthHeaders();
    return this.http.post(`${this.apiUrl}/change-password`, request, { headers });
  }
}

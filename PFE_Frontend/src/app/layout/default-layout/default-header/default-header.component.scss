// Notification styles
.notification-wrapper {
  .notification-icon-wrapper {
    border: none !important;
    background: none !important;
    box-shadow: none !important;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05) !important;
      border-radius: 8px;
    }

    &:focus {
      box-shadow: none !important;
      outline: none !important;
    }
  }

  .notification-panel {
    animation: slideDown 0.2s ease-out;
    border: 1px solid #C7DAEC;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 0;

    .notification-item {
      transition: background-color 0.2s ease;
      border: none !important;
      border-bottom: 1px solid #E6EDF5 !important;

      &:hover {
        background-color: #F5FFF9;
      }

      &.unread {
        background-color: #F8F9FA;
        border-left: 3px solid #1061AC !important;
      }

      &:last-child {
        border-bottom: none !important;
      }

      .patient-avatar {
        .avatar-circle {
          transition: all 0.2s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }

      &:hover .patient-avatar .avatar-circle {
        background-color: #57B6B1 !important;
        color: white !important;
        border-color: #1061AC !important;
        transform: scale(1.05);
      }

      .notification-content {
        .notification-title {
          font-weight: 600;
          color: #163659;
          margin-bottom: 4px;
        }

        .notification-message {
          color: #728A9B;
          font-size: 0.85rem;
          line-height: 1.4;
          margin-bottom: 6px;
        }

        .notification-meta {
          .notification-type {
            color: #1061AC;
            font-weight: 500;
          }
        }
      }

      .unread-indicator {
        .rounded-circle {
          background-color: #1061AC !important;
          animation: pulse 2s infinite;
        }
      }
    }
  }
}

// Slide down animation
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Pulse animation for unread indicator
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// Custom dropdown header styles
c-dropdown-header {
  background-color: #F8F9FA;
  border-bottom: 1px solid #E6EDF5;
  font-weight: 600;
  color: #163659;
}

// Custom button styles in dropdown
.btn-link {
  &:hover {
    color: #57B6B1 !important;
  }
}
<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Spinner</strong> <small>Border</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use the border spinners for a lightweight loading indicator.
        </p>
        <app-docs-example href="components/spinner">
          <c-spinner></c-spinner>
        </app-docs-example>
        <p class="text-body-secondary small">
          The border spinner uses <code>currentColor</code> for its <code>border-color</code>.
          You can use any of our text color utilities on the standard spinner.
        </p>
        <app-docs-example href="components/spinner#colors">
          <c-spinner color="primary"></c-spinner>
          <c-spinner color="secondary"></c-spinner>
          <c-spinner color="success"></c-spinner>
          <c-spinner color="danger"></c-spinner>
          <c-spinner color="warning"></c-spinner>
          <c-spinner color="info"></c-spinner>
          <c-spinner color="light"></c-spinner>
          <c-spinner color="dark"></c-spinner>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Spinner</strong> <small>Growing</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          If you don&#39;tfancy a border spinner, switch to the grow spinner. While it
          doesn&#39;t technically spin, it does repeatedly grow!
        </p>
        <app-docs-example href="components/spinner#growing-spinner">
          <c-spinner variant="grow"></c-spinner>
        </app-docs-example>
        <p class="text-body-secondary small">
          Once again, this spinner is built with <code>currentColor</code>, so you can easily
          change its appearance. Here it is in blue, along with the supported variants.
        </p>
        <app-docs-example href="components/spinner#growing-spinner">
          <c-spinner color="primary" variant="grow"></c-spinner>
          <c-spinner color="secondary" variant="grow"></c-spinner>
          <c-spinner color="success" variant="grow"></c-spinner>
          <c-spinner color="danger" variant="grow"></c-spinner>
          <c-spinner color="warning" variant="grow"></c-spinner>
          <c-spinner color="info" variant="grow"></c-spinner>
          <c-spinner color="light" variant="grow"></c-spinner>
          <c-spinner color="dark" variant="grow"></c-spinner>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Spinner</strong> <small>Size</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>size=&#34;sm&#34;</code> property to make a smaller spinner that can quickly
          be used within other components.
        </p>
        <app-docs-example href="components/spinner#size">
          <c-spinner size="sm"></c-spinner>
          <c-spinner size="sm" variant="grow"></c-spinner>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Spinner</strong> <small>Buttons</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use spinners within buttons to indicate an action is currently processing or taking
          place. You may also swap the text out of the spinner element and utilize button text
          as needed.
        </p>
        <app-docs-example href="components/spinner#buttons">
          <button cButton class="m-1" disabled>
            <c-spinner aria-hidden="true" size="sm"></c-spinner>
          </button>
          <button cButton class="m-1" disabled>
            <c-spinner aria-hidden="true" size="sm"></c-spinner>
            Loading...
          </button>
        </app-docs-example>
        <app-docs-example href="components/spinner#buttons">
          <br>
          <button cButton class="m-1" disabled>
            <c-spinner aria-hidden="true" size="sm" variant="grow"></c-spinner>
          </button>
          <button cButton class="m-1" disabled>
            <c-spinner aria-hidden="true" size="sm" variant="grow"></c-spinner>
            Loading...
          </button>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>

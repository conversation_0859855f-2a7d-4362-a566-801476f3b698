.prescription-details-container {
  padding: 1.5rem;
  background-color: #f9fbfd;
  min-height: 100vh;
}

/* Page Header */
.page-header {
  margin-bottom: 2rem;

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    .header-navigation {
      .back-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: none;
        border: none;
        color: #1061AC;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        padding: 0;
        transition: color 0.2s ease;

        &:hover {
          color: #0d4f8c;
        }

        .back-arrow {
          font-size: 1rem;
          font-weight: bold;
        }
      }
    }

    /* Action Buttons */
    .action-buttons {
      display: flex;
      gap: 1rem;

      .action-btn {
        background: #1061AC;
        color: #FFFFFF;
        border: none;
        border-radius: 4px;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #0d4f8c;
          transform: translateY(-1px);
        }

        &.primary {
          box-shadow: 0 2px 4px rgba(16, 97, 172, 0.2);

          &:hover {
            box-shadow: 0 4px 8px rgba(16, 97, 172, 0.3);
          }
        }
      }
    }
  }

  .page-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #163659;
    margin: 0;
  }
}

/* Panels Container */
.panels-container {
  display: grid;
  grid-template-columns: 550px 1fr;
  gap: 1.5rem;
  align-items: start;
}

/* Left Column */
.left-column {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Right Column */
.right-column {
  height: 100%;
}

/* Panel Base Styles */
.panel {
  background: #FFFFFF;
  border: 1px solid #C7DAEC;
  border-radius: 10px;
  padding: 1.5rem;
  position: relative;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    .panel-title {
      color: #1061AC;
      font-size: 0.875rem;
      font-weight: 600;
      margin: 0;
    }

    .panel-action-btn {
      background: none;
      border: 1px solid #C7DAEC;
      border-radius: 6px;
      padding: 0.5rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: #f8f9fa;
        border-color: #1061AC;
      }

      svg {
        width: 16px;
        height: 16px;
        color: #666;
      }

      &:hover svg {
        color: #1061AC;
      }

      .action-icon {
        width: 16px;
        height: 16px;
      }
    }
  }
}

/* Specific Panel Styles */
.info-panel {
  height: 276px;
  box-sizing: border-box;
}

.patient-panel {
  height: 444px;
  box-sizing: border-box;
}

.ordonnances-panel {
  height: calc(276px + 444px + 1rem); /* Height of both left panels + gap */
  box-sizing: border-box;
  background: #FFFFFF;
}

/* Panel 1: Information de l'ordonnance */
.fields-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;

  .fields-row {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;

    .field-item {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      flex: 1;
      min-width: 100px;

      .field-label {
        color: #163659;
        opacity: 0.63;
        font-weight: 500;
        font-size: 0.6875rem;
        white-space: nowrap;
      }

      .field-value {
        color: #333;
        font-size: 0.6875rem;
        word-break: break-word;
      }
    }
  }
}

.badges-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;

  .badge-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;

    .badge-label {
      color: #1061AC;
      font-weight: 500;
      font-size: 0.75rem;
      text-align: left;
      align-self: flex-start;
    }

    .badge {
      padding: 0.375rem 0.75rem !important;
      border-radius: 20px !important;
      font-size: 0.75rem !important;
      font-weight: 500 !important;
      text-transform: uppercase !important;
      width: fit-content !important;
      text-align: center !important;
      align-self: center !important;
      white-space: nowrap !important;
      display: inline-block !important;

      // Default fallback styling
      background-color: #E3F2FD !important;
      color: #1976D2 !important;

      // Legacy badge styles
      &.badge-priority {
        background-color: #FFEBEE !important;
        color: #C62828 !important;
      }

      &.badge-cancelled {
        background-color: #FFCDD2 !important;
        color: #D32F2F !important;
      }

      &.badge-in-progress {
        background-color: #FFF3E0 !important;
        color: #F57C00 !important;
      }

      // New prescription status badges (matching prescriptions component)
      &.badge-uploaded {
        background-color: #E8F5E8 !important;
        color: #2E7D32 !important;
      }

      &.badge-in-review {
        background-color: #E3F2FD !important;
        color: #1976D2 !important;
      }

      &.badge-needs-clarification {
        background-color: #FFF3E0 !important;
        color: #F57C00 !important;
      }

      &.badge-approved {
        background-color: #E8F5E8 !important;
        color: #2E7D32 !important;
      }

      &.badge-rejected {
        background-color: #FFCDD2 !important;
        color: #D32F2F !important;
      }

      &.badge-expired {
        background-color: #F5F5F5 !important;
        color: #757575 !important;
      }

      &.badge-archived {
        background-color: #F5F5F5 !important;
        color: #757575 !important;
      }

      &.badge-default {
        background-color: #E3F2FD !important;
        color: #1976D2 !important;
      }
    }
  }
}

/* Note Section */
.note-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #E8EDF5;

  .note-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .note-label {
      color: #1061AC;
      font-weight: 500;
      font-size: 0.75rem;
    }

    .note-value {
      color: #333333;
      font-size: 0.75rem;
      line-height: 1.4;
      word-wrap: break-word;
      overflow-wrap: break-word;
      white-space: normal;
      max-width: 100%;
      box-sizing: border-box;
    }
  }
}

/* Loading and Error States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #E8EDF5;
    border-top: 4px solid #1061AC;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  p {
    color: #728A9B;
    font-size: 1rem;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  .error-message {
    color: #D32F2F;
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  .retry-btn {
    padding: 0.5rem 1rem;
    background: #1061AC;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;

    &:hover {
      background: #0D4E8C;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* No Image Placeholder */
.no-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #F8F9FA;
  border: 2px dashed #C7DAEC;
  border-radius: 8px;
  color: #728A9B;
  font-size: 0.875rem;
}

/* Embedded PDF Container Styles */
.pdf-embedded-container {
  width: 100%;
  background: #FFFFFF;
  border: 2px solid #C7DAEC;
  border-radius: 8px;
  overflow: hidden;

  .pdf-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: #FFFFFF;
    border-bottom: 1px solid #C7DAEC;

    .pdf-title {
      font-size: 0.875rem;
      font-weight: 600;
      color: #163659;
    }

    .pdf-fullscreen-btn {
      background: none;
      border: 1px solid #C7DAEC;
      border-radius: 4px;
      padding: 6px;
      cursor: pointer;
      color: #1061AC;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #1061AC;
        color: white;
        border-color: #1061AC;
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .pdf-content-wrapper {
    position: relative;
    height: 450px;
    overflow: hidden;
    background: #FFFFFF;
    border-radius: 4px;

    pdf-viewer {
      width: 100% !important;
      height: 100% !important;
      display: block !important;

      ::ng-deep {
        .ng2-pdf-viewer-container {
          width: 100% !important;
          height: 100% !important;
          overflow: auto !important;
          background: #FFFFFF !important;
        }

        .pdfViewer {
          padding: 10px !important;

          .page {
            margin: 0 auto 15px auto !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
            background: white !important;
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
            max-width: calc(100% - 20px) !important;
          }
        }

        canvas {
          width: 100% !important;
          height: auto !important;
          max-width: 100% !important;
        }

        .textLayer {
          user-select: text !important;
        }

        .annotationLayer {
          pointer-events: auto !important;
        }
      }
    }
  }

  &:hover {
    border-color: #1061AC;
  }
}

// PDF Preview Styles
.pdf-loading-preview,
.pdf-error-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  border: 1px solid #C7DAEC;
  border-radius: 8px;
  background: #FFFFFF;
  color: #163659;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #C7DAEC;
    border-top: 4px solid #1061AC;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .retry-btn {
    margin-top: 16px;
    padding: 8px 16px;
    background: #1061AC;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Nautica Rounded', sans-serif;

    &:hover {
      background: #57B6B1;
    }
  }
}

.pdf-preview-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;

  .pdf-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;

    .pdf-overlay-text {
      background: rgba(16, 97, 172, 0.9);
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-family: 'Nautica Rounded', sans-serif;
      font-weight: 600;

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }

  &:hover .pdf-overlay {
    opacity: 1;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* PDF Loading and Error States */
.pdf-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #FFFFFF;
  border-radius: 8px;
  margin: 20px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #E8EDF5;
    border-top: 4px solid #1061AC;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  p {
    color: #728A9B;
    font-size: 0.875rem;
    margin: 0;
  }
}

.pdf-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  margin: 20px;
  padding: 20px;
  text-align: center;

  p {
    color: #e53e3e;
    font-size: 0.875rem;
    margin: 0 0 0.5rem 0;
  }

  .pdf-url-debug {
    color: #718096;
    font-size: 0.75rem;
    word-break: break-all;
    margin-top: 0.5rem;
  }

  .retry-btn {
    margin-top: 16px;
    padding: 8px 16px;
    background: #1061AC;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Nautica Rounded', sans-serif;

    &:hover {
      background: #57B6B1;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Panel 2: Informations du patient */
.patient-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;

  .patient-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #E3F2FD;
    color: #1976D2;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1rem;
    flex-shrink: 0;
  }

  .patient-name {
    color: #163659;
    font-weight: 600;
    font-size: 1.1rem;
  }
}

.divider {
  border-top: 1px solid #C7DAEC;
  margin: 1rem 0;
}

.patient-details {
  .details-row {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;

    .detail-column {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      flex: 1;
      min-width: 150px;

      .detail-label {
        color: #163659;
        opacity: 0.63;
        font-weight: 500;
        font-size: 0.75rem;
        white-space: nowrap;
      }

      .detail-value {
        color: #163659;
        font-size: 0.75rem;
        word-break: break-word;
      }
    }
  }

  .footer-row {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;

    .footer-item {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      flex: 1;
      min-width: 150px;

      .footer-label {
        color: #163659;
        opacity: 0.63;
        font-weight: 500;
        font-size: 0.75rem;
        flex-shrink: 0;
        white-space: nowrap;
      }

      .footer-value {
        color: #163659;
        font-size: 0.75rem;
      }
    }
  }
}

/* Panel 3: Ordonnances */
.prescription-viewer {
  height: calc(100% - 3rem); /* Full height minus title and padding */
  overflow: auto;
  border: 1px solid #C7DAEC;
  border-radius: 4px;
  background: #FFFFFF;

  .prescription-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.02);
    }
  }

  /* PDF Embedded Container Styles */
  .pdf-embedded-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .pdf-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem;
      background: #FFFFFF;
      border-bottom: 1px solid #C7DAEC;

      .pdf-title {
        color: #163659;
        font-weight: 600;
        font-size: 0.875rem;
      }

      .pdf-fullscreen-btn {
        background: none;
        border: 1px solid #C7DAEC;
        border-radius: 4px;
        padding: 0.5rem;
        cursor: pointer;
        color: #728A9B;
        transition: all 0.2s ease;

        &:hover {
          background: #1061AC;
          border-color: #1061AC;
          color: white;
        }

        svg {
          width: 16px;
          height: 16px;
        }
      }
    }

    .pdf-content-wrapper {
      flex: 1;
      overflow: auto;
      background: #FFFFFF;

      .pdf-preview-container {
        position: relative;
        height: 100%;

        pdf-viewer {
          ::ng-deep {
            .ng2-pdf-viewer-container {
              overflow: auto !important;
              background: #FFFFFF !important;
            }

            .pdfViewer {
              padding: 10px !important;

              .page {
                margin: 0 auto 10px auto !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                background: white !important;
                border: 1px solid #C7DAEC !important;
                border-radius: 4px !important;
              }
            }
          }
        }

        .pdf-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.2s ease;
          cursor: pointer;

          &:hover {
            opacity: 1;
          }

          .pdf-overlay-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 0.75rem 1rem;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #163659;
            font-size: 0.875rem;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

            svg {
              width: 16px;
              height: 16px;
            }
          }
        }
      }

      .pdf-loading-preview, .pdf-error-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        text-align: center;

        .loading-spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #C7DAEC;
          border-top: 3px solid #1061AC;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 1rem;
        }

        p {
          color: #728A9B;
          margin: 0;
          font-size: 0.875rem;
        }

        .retry-btn {
          margin-top: 0.75rem;
          padding: 0.5rem 1rem;
          background: #1061AC;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 0.875rem;

          &:hover {
            background: #0D4E8C;
          }
        }
      }
    }
  }
}

/* Image Viewer Modal */
.image-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;

  .image-viewer-container {
    position: relative;
    width: 500px;
    height: 650px;
    background: #FFFFFF;
    border: 1px solid #C7DAEC;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    overflow: hidden;

    .modal-controls {
      position: absolute;
      top: 15px;
      right: 15px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .close-viewer {
        width: 24px;
        height: 24px;
        border: 2px solid #57B6B1;
        border-radius: 50%;
        background: transparent;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        position: relative;

        &:hover {
          background-color: #57B6B1;
        }

        &::before,
        &::after {
          content: '';
          position: absolute;
          width: 12px;
          height: 2px;
          background-color: #57B6B1;
          transition: background-color 0.2s ease;
        }

        &:hover::before,
        &:hover::after {
          background-color: white;
        }

        &::before {
          transform: rotate(45deg);
        }

        &::after {
          transform: rotate(-45deg);
        }
      }

      .download-viewer {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 0.5rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f0f0f0;
          border-color: #ccc;
        }

        svg {
          width: 14px;
          height: 14px;
          color: #666;
        }

        &:hover svg {
          color: #1061AC;
        }
      }
    }

    .modal-image-container {
      width: calc(100% - 30px);
      height: calc(100% - 70px);
      margin: 55px 15px 15px 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;

      .full-size-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 4px;
      }
    }

    .modal-pdf-container {
      width: calc(100% - 30px);
      height: calc(100% - 70px);
      margin: 55px 15px 15px 15px;
      overflow: auto;
      border-radius: 4px;
      background: #FFFFFF;

      .pdf-loading, .pdf-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        text-align: center;

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #C7DAEC;
          border-top: 4px solid #1061AC;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 1rem;
        }

        p {
          color: #728A9B;
          margin: 0.5rem 0;
        }

        .retry-btn {
          padding: 0.5rem 1rem;
          background: #1061AC;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 0.875rem;

          &:hover {
            background: #0D4E8C;
          }
        }
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .panels-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .left-column {
    gap: 1rem;
  }

  .info-panel,
  .patient-panel,
  .ordonnances-panel {
    height: auto;
    min-height: 200px;
  }

  .patient-details .details-row,
  .patient-details .footer-row {
    flex-direction: column;
    gap: 1rem;
  }

  .fields-grid .fields-row {
    flex-direction: column;
    gap: 1rem;
  }

  .badges-section {
    flex-direction: column;
    gap: 1rem;
  }

  .prescription-viewer {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .prescription-details-container {
    padding: 1rem;
  }

  .page-header .header-top {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;

    .action-buttons {
      justify-content: center;
      flex-wrap: wrap;

      .action-btn {
        flex: 1;
        min-width: 120px;
        text-align: center;
      }
    }
  }

  .panels-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .fields-grid .fields-row {
    flex-direction: column;
    gap: 1rem;
  }

  .patient-details .details-row,
  .patient-details .footer-row {
    flex-direction: column;
    gap: 1rem;
  }

  .badges-section {
    flex-direction: column;
    gap: 1rem;
  }

  .patient-header {
    .patient-avatar {
      width: 40px;
      height: 40px;
      font-size: 0.875rem;
    }

    .patient-name {
      font-size: 1rem;
    }
  }

  .prescription-viewer {
    height: 300px;
  }
}

/* PDF Modal Styles - Now using image modal design */

/* Debug info styles */
.pdf-debug-info {
  background: #f0f8ff;
  border: 1px solid #1061AC;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  font-size: 0.75rem;

  p {
    margin: 0 0 5px 0;
    word-break: break-all;
  }

  strong {
    color: #163659;
  }
}

.error-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;

  button {
    padding: 6px 12px;
    border: 1px solid #1061AC;
    background: white;
    color: #1061AC;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75rem;

    &:hover {
      background: #1061AC;
      color: white;
    }
  }
}

.iframe-test, .blob-test {
  margin: 15px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;

  h4 {
    margin: 0 0 10px 0;
    color: #163659;
    font-size: 0.875rem;
  }
}

/* Custom Package Modal Styles */
::ng-deep .custom-package-modal {
  z-index: 1000 !important;
}
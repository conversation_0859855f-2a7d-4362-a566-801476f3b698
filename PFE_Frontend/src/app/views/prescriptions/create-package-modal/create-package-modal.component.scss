/* Modal Container */
.modal-container {
  width: 800px;
  max-width: 90vw;
  max-height: 90vh;
  background: #FFFFFF !important;
  border: 3px solid #1061AC !important;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5) !important;
  overflow-y: auto;
  box-sizing: border-box;
  display: flex !important;
  flex-direction: column;
  position: relative;
  z-index: 10003 !important;
  margin: auto;
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.modal-title {
  font: normal normal 800 16px/20px 'Nautica Rounded', sans-serif;
  letter-spacing: 1.4px;
  color: #1061AC;
  text-align: left;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #728A9B;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background: #F5FFF9;
    color: #163659;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

/* Error Message */
.error-message {
  background: #FFF5F5;
  border: 1px solid #FEB2B2;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
  color: #C53030;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Success Message */
.success-message {
  background: #F0FFF4;
  border: 1px solid #57B6B1;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
  color: #57B6B1;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* Modal Content */
.modal-content {
  flex: 1;
  overflow-y: auto;
}

/* Patient Info Section */
.patient-info-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #C7DAEC;
}

.section-title {
  font: normal normal 800 14px/17px 'Nautica Rounded', sans-serif;
  color: #163659;
  margin: 0 0 15px 0;
  letter-spacing: 0.5px;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.patient-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #1061AC;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  .initials {
    color: #FFFFFF;
    font: normal normal 800 16px/20px 'Nautica Rounded', sans-serif;
    letter-spacing: 1px;
  }
}

.patient-details {
  flex: 1;
}

.patient-name {
  font: normal normal 800 16px/20px 'Nautica Rounded', sans-serif;
  color: #163659;
  margin-bottom: 4px;
}

.patient-address {
  font: normal normal normal 14px/17px 'Nautica Rounded', sans-serif;
  color: #728A9B;
}

/* Form Section */
.form-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;

  &.full-width {
    flex: 1 1 100%;
  }
}

.form-label {
  font: normal normal 800 14px/17px 'Nautica Rounded', sans-serif;
  color: #163659;
  margin: 0;
}

.form-input,
.form-select,
.form-textarea {
  padding: 12px 16px;
  border: 1px solid #8AA3D5;
  border-radius: 6px;
  font: normal normal normal 14px/17px 'Nautica Rounded', sans-serif;
  color: #163659;
  background: #FFFFFF;
  transition: all 0.2s ease;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: #1061AC;
    box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
  }

  &:hover {
    border-color: #57B6B1;
    background-color: #F5FFF9;
  }

  &:disabled {
    background: #F5FFF9;
    color: #728A9B;
    cursor: not-allowed;
  }

  &::placeholder {
    color: #8AA3D5;
  }
}

/* Custom Dropdown Styles */
.custom-dropdown {
  position: relative;
  min-width: 150px;

  .dropdown-toggle {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #8AA3D5;
    border-radius: 6px;
    background: #FFFFFF;
    font: normal normal normal 14px/17px 'Nautica Rounded', sans-serif;
    color: #163659;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: #1061AC;
      box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
    }

    &:hover:not(:disabled) {
      border-color: #57B6B1;
      background-color: #F5FFF9;
    }

    &:disabled {
      background: #F5FFF9;
      color: #728A9B;
      cursor: not-allowed;
    }
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #FFFFFF;
    border: 2px solid #C7DAEC;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
    z-index: 99999;
    margin-top: 4px;
    min-width: 200px;
    max-height: 350px;
    overflow-y: auto;
    display: block;
    visibility: visible;

    .dropdown-item {
      display: flex;
      align-items: center;
      padding: 0.75rem 1rem;
      cursor: pointer;
      transition: all 0.2s ease;
      border-bottom: 1px solid #e0e0e0;
      min-height: 40px;
      background-color: #FFFFFF;

      &:hover {
        background-color: #F0F8FF;
      }

      &.selected {
        background-color: #E8F4FD;
        border-left: 3px solid #1061AC;
        padding-left: 0.75rem;

        .option-text {
          color: #1061AC;
          font-weight: 600;
        }
      }

      .option-text {
        font-size: 0.875rem;
        color: #163659;
        font-weight: 500;
        flex: 1;
        line-height: 1.4;
      }

      &:first-child {
        border-radius: 8px 8px 0 0;
      }

      &:last-child {
        border-radius: 0 0 8px 8px;
        border-bottom: none;
      }
    }
  }
}

.form-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23163659' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 40px;
}

.form-textarea {
  resize: vertical;
  min-height: 60px;
  font-family: 'Nautica Rounded', sans-serif;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #C7DAEC;
  flex-shrink: 0;
}

.cancel-button {
  background: transparent;
  color: #163659;
  border: 1px solid #C7DAEC;
  border-radius: 6px;
  padding: 12px 24px;
  font: normal normal 800 14px/17px 'Nautica Rounded', sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;

  &:hover {
    background: #F5FFF9;
    border-color: #8AA3D5;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.submit-button {
  background: linear-gradient(135deg, #1061AC 0%, #57B6B1 100%);
  color: #FFFFFF;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font: normal normal 800 14px/17px 'Nautica Rounded', sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 97, 172, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  &.loading {
    pointer-events: none;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #FFFFFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 900px) {
  .modal-container {
    width: 95vw;
    max-width: 750px;
    margin: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .modal-footer {
    flex-direction: column-reverse;
    gap: 10px;

    .cancel-button,
    .submit-button {
      width: 100%;
    }
  }
}

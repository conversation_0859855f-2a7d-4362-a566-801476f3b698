import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { CreatePackageModalComponent, CreatePackageModalData } from './create-package-modal.component';
import { PackageService } from '../../../services/package.service';
import { AuthService } from '../../../services/auth.service';
import { PatientPaymentType, PreferenceType } from '../../../models/package.model';
import { Patient } from '../../../models/patient.model';
import { Prescription } from '../../../models/prescription.model';

describe('CreatePackageModalComponent', () => {
  let component: CreatePackageModalComponent;
  let fixture: ComponentFixture<CreatePackageModalComponent>;
  let mockPackageService: jasmine.SpyObj<PackageService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<CreatePackageModalComponent>>;

  const mockPatient: Patient = {
    id: 'patient-1',
    pharmacyId: 'pharmacy-1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    phone: '**********',
    address: [{
      address: '123 Test Street',
      lat: 45.5017,
      long: -73.5673
    }],
    deliveryType: [],
    fullName: 'John Doe',
    initials: 'JD',
    primaryAddress: '123 Test Street'
  };

  const mockPrescription: Prescription = {
    id: 'prescription-1',
    patientId: 'patient-1',
    pharmacyId: 'pharmacy-1',
    storagePath: ['test-path.pdf'],
    issueDate: new Date(),
    date: new Date(),
    prescriptionStatus: 'APPROVED',
    approved: true,
    uploadedAt: new Date().toISOString()
  };

  const mockDialogData: CreatePackageModalData = {
    patient: mockPatient,
    prescription: mockPrescription,
    packageType: 'colis'
  };

  beforeEach(async () => {
    const packageServiceSpy = jasmine.createSpyObj('PackageService', ['createPackage']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [
        CreatePackageModalComponent,
        FormsModule,
        HttpClientTestingModule
      ],
      providers: [
        { provide: PackageService, useValue: packageServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CreatePackageModalComponent);
    component = fixture.componentInstance;
    mockPackageService = TestBed.inject(PackageService) as jasmine.SpyObj<PackageService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockDialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<CreatePackageModalComponent>>;

    // Setup default auth service response
    mockAuthService.getCurrentUser.and.returnValue({
      id: 'user-1',
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      role: 4
    });

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default form data', () => {
    expect(component.formData.packagePrice).toBe(0);
    expect(component.formData.paymentType).toBe(PatientPaymentType.PAYONDELEVERY);
    expect(component.formData.preference.type).toBe(PreferenceType.ANYTIME);
    expect(component.formData.deliveryTime).toBe('L\'après-midi (PM)');
  });

  it('should pre-fill form data from patient information', () => {
    const patientWithData: Patient = {
      ...mockPatient,
      paymentType: PatientPaymentType.CREDITCARD,
      deliveryManNote: 'Test delivery note',
      pharmacyNote: 'Test pharmacy note',
      spot: 'Test spot',
      authorizedPerson: {
        name: 'Jane Doe',
        type: 'Famille',
        phone: '**********'
      }
    };

    component.data.patient = patientWithData;
    component.ngOnInit();

    expect(component.formData.paymentType).toBe(PatientPaymentType.CREDITCARD);
    expect(component.formData.deliveryManNote).toBe('Test delivery note');
    expect(component.formData.pharmacyNote).toBe('Test pharmacy note');
    expect(component.formData.spot).toBe('Test spot');
    expect(component.formData.authorizedPersonName).toBe('Jane Doe');
    expect(component.formData.authorizedPersonType).toBe('Famille');
  });

  it('should return correct modal title based on package type', () => {
    component.data.packageType = 'colis';
    expect(component.getModalTitle()).toBe('Créer un colis');

    component.data.packageType = 'cueillette';
    expect(component.getModalTitle()).toBe('Créer une cueillette');

    component.data.packageType = 'colis-cueillette';
    expect(component.getModalTitle()).toBe('Créer un colis + cueillette');
  });

  it('should validate form correctly', () => {
    // Initially invalid (no package price)
    expect(component.isFormValid()).toBeFalse();

    // Set required fields
    component.formData.packagePrice = 25.50;
    component.formData.authorizedPersonName = 'John Doe';
    expect(component.isFormValid()).toBeTrue();

    // Test preference-specific validation
    component.formData.preference.type = PreferenceType.BEFORE;
    expect(component.isFormValid()).toBeFalse(); // Missing time

    component.formData.preference.time = '14:00';
    expect(component.isFormValid()).toBeTrue();

    // Test BETWEEN preference
    component.formData.preference.type = PreferenceType.BETWEEN;
    component.formData.preference.time = '';
    expect(component.isFormValid()).toBeFalse(); // Missing from/to

    component.formData.preference.from = '09:00';
    component.formData.preference.to = '17:00';
    expect(component.isFormValid()).toBeTrue();
  });

  it('should reset preference fields when preference type changes', () => {
    component.formData.preference.time = '14:00';
    component.formData.preference.from = '09:00';
    component.formData.preference.to = '17:00';

    component.onPreferenceTypeChange();

    expect(component.formData.preference.time).toBe('');
    expect(component.formData.preference.from).toBe('');
    expect(component.formData.preference.to).toBe('');
  });

  it('should create package successfully', (done) => {
    const mockCreatedPackage = { id: 'package-1', status: 'PENDING' };
    mockPackageService.createPackage.and.returnValue(of(mockCreatedPackage));

    // Set up valid form data
    component.formData.packagePrice = 25.50;
    component.formData.authorizedPersonName = 'John Doe';

    component.onSubmit();

    expect(mockPackageService.createPackage).toHaveBeenCalled();
    expect(component.successMessage).toBe('Colis créé avec succès');

    // Wait for the timeout to complete
    setTimeout(() => {
      expect(mockDialogRef.close).toHaveBeenCalledWith({
        success: true,
        package: mockCreatedPackage
      });
      done();
    }, 1100);
  });

  it('should handle package creation error', () => {
    const errorResponse = { error: { message: 'Creation failed' } };
    mockPackageService.createPackage.and.returnValue(throwError(() => errorResponse));

    // Set up valid form data
    component.formData.packagePrice = 25.50;
    component.formData.authorizedPersonName = 'John Doe';

    component.onSubmit();

    expect(component.error).toBe('Creation failed');
    expect(component.isSubmitting).toBeFalse();
  });

  it('should close dialog when close is called', () => {
    component.close();
    expect(mockDialogRef.close).toHaveBeenCalledWith({ success: false });
  });

  it('should not submit if form is invalid', () => {
    component.formData.packagePrice = 0; // Invalid
    component.onSubmit();

    expect(mockPackageService.createPackage).not.toHaveBeenCalled();
  });

  it('should not submit if already submitting', () => {
    component.isSubmitting = true;
    component.formData.packagePrice = 25.50;
    component.formData.authorizedPersonName = 'John Doe';

    component.onSubmit();

    expect(mockPackageService.createPackage).not.toHaveBeenCalled();
  });
});

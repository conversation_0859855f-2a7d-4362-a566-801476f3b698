/* Modal Container */
.modal-container {
  width: 900px;
  height: 400px;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border: 2px solid #C7DAEC;
  border-radius: 10px;
  opacity: 1;
  padding: 25px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10003;
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.modal-title {
  font: normal normal 800 14px/17px 'Nautica Rounded', sans-serif;
  letter-spacing: 1.4px;
  color: #1061AC;
  text-align: left;
}

/* Close Button */
.close-button {
  width: 24px;
  height: 24px;
  border: 2px solid #57B6B1;
  border-radius: 50%;
  opacity: 1;
  background: transparent;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: #57B6B1;
}

.close-button::before,
.close-button::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 2px;
  background-color: #57B6B1;
  transition: background-color 0.2s ease;
}

.close-button:hover::before,
.close-button:hover::after {
  background-color: white;
}

.close-button::before {
  transform: rotate(45deg);
}

.close-button::after {
  transform: rotate(-45deg);
}

/* Section Title */
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #163659;
  margin-bottom: 12px;
  font-family: 'Nautica Rounded', sans-serif;
  flex-shrink: 0;
}

/* File Input Section */
.file-input-container {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 25px;
  flex-shrink: 0;
}

.file-input-wrapper {
  position: relative;
  width: 650px;
  height: 48px;
}

.file-input {
  width: 100%;
  height: 100%;
  background: #F5FFF9 0% 0% no-repeat padding-box;
  border: 1px dashed #6D9ECC;
  border-radius: 10px;
  opacity: 1;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}

.file-input:hover {
  border-color: #57B6B1;
  background: #F0FFF8;
}

.file-input-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.file-input-label {
  color: #6D9ECC;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.file-input-hint {
  color: #8AA3D5;
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
  text-align: center;
}

.import-button {
  width: 200px;
  height: 45px;
  background: transparent linear-gradient(180deg, #1061AC 0%, #57B6B1 100%) 0% 0% no-repeat padding-box;
  border-radius: 10px;
  opacity: 1;
  color: white;
  border: none;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(16, 97, 172, 0.2);
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.import-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 97, 172, 0.3);
}

.button-icon {
  font-size: 16px;
}

/* Form Inputs */
.input-row {
  display: flex;
  gap: 25px;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.patient-search-container,
.household-display-container {
  position: relative;
  width: 400px;
}

.input-field {
  width: 400px;
  height: 48px;
  border-radius: 10px;
  opacity: 1;
  border: 1px solid #8AA3D5;
  padding: 0 15px;
  font-size: 14px;
  font-family: 'Nautica Rounded', sans-serif;
  transition: all 0.2s ease;
}

.input-field:focus {
  outline: none;
  border-color: #57B6B1;
  box-shadow: 0 0 0 3px rgba(87, 182, 177, 0.1);
}

.input-field::placeholder {
  color: #8AA3D5;
  font-weight: 400;
}

// Disabled state for household field
.input-field:disabled {
  background: #F8F9FA !important;
  color: #728A9B !important;
  cursor: not-allowed;
  border-color: #C7DAEC;
}

// Loading state
.input-field.loading {
  background: #F8F9FA;
  color: #728A9B;
}

// No household state
.input-field.no-household {
  color: #F80D38 !important;
  font-style: italic;
}

// Loading indicator for household field
.household-display-container {
  .loading-indicator {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;

    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #57B6B1;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.patient-name {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  width: 100%;
}

.foyer-name {
  background: #EEF4F9 0% 0% no-repeat padding-box;
}

/* Patient & Household Search Dropdowns */
.patient-dropdown,
.household-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border: 1px solid #C7DAEC;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.patient-item,
.household-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;

  &:hover {
    background-color: #F0F8FF;
  }

  &:last-child {
    border-bottom: none;
  }
}

.patient-avatar,
.household-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #C7DAEC;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  color: #1061AC;
  flex-shrink: 0;
}

.patient-info,
.household-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.patient-name-text,
.household-name-text {
  font-size: 14px;
  font-weight: 600;
  color: #163659;
  line-height: 1.2;
}

.patient-details,
.household-details {
  font-size: 12px;
  color: #8AA3D5;
  line-height: 1.2;
}

.no-results {
  padding: 16px;
  text-align: center;
  color: #8AA3D5;
  font-size: 14px;
  font-style: italic;
}

/* Button Row */
.button-row {
  position: absolute;
  right: 25px;
  bottom: 25px;
  display: flex;
  gap: 15px;
  align-items: center;
}

/* Cancel Button */
.cancel-button {
  width: 120px;
  height: 32px;
  background: transparent;
  border: 1px solid #163659;
  border-radius: 10px;
  color: #163659;
  cursor: pointer;
  font-weight: 600;
  font-size: 13px;
  transition: all 0.2s ease;
  font-family: 'Nautica Rounded', sans-serif;
}

.cancel-button:hover {
  background: #163659;
  color: white;
}

/* Submit Button */
.submit-button {
  width: 120px;
  height: 32px;
  background: #57B6B1;
  border: none;
  border-radius: 10px;
  opacity: 1;
  color: white;
  cursor: pointer;
  font-weight: 600;
  font-size: 13px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(87, 182, 177, 0.2);
  font-family: 'Nautica Rounded', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(87, 182, 177, 0.3);
  background: #4AA5A0;
}

.submit-button:disabled {
  //background: #95A5A6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-button.loading {
  width: 160px; // Wider for loading text
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* File Preview */
.file-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 12px;
}

.file-preview-name {
  color: #163659;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  line-height: 1.2;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.delete-file {
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: red;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.delete-file:hover {
  // background: #FF3742;
  transform: scale(1.1);
}

.delete-file svg {
  width: 16px;
  height: 16px;
}
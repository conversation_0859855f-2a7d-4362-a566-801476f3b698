<div class="modal-container">
  <div class="modal-header">
    <div class="modal-title">Ajouter une ordonnance</div>
    <button class="close-button" (click)="close()"></button>
  </div>

  <div class="section-title">Pièces jointes</div>

  <div class="file-input-container">
    <div class="file-input-wrapper">
      <div class="file-input" (click)="triggerFileInput()">
        @if (!uploadedFile) {
          <div class="file-input-content">
            <span class="file-input-label">Choisir une ordonnance (PDF ou Image)</span>
            <!-- <span class="file-input-hint">Formats acceptés: PDF, JPG, PNG, JPEG</span> -->
          </div>
        }
        @if (uploadedFile) {
          <div class="file-preview">
            <span class="file-preview-name">{{ uploadedFile.name }}</span>
            <button class="delete-file" (click)="removeFile($event)" title="Supprimer le fichier">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M10 11V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M14 11V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        }
      </div>
      <input
        #fileInput
        type="file"
        accept=".pdf,.jpg,.jpeg,.png,.gif,.bmp,.webp"
        (change)="onFileSelected($event)"
        style="display: none;"
      >
    </div>
    <button class="import-button" (click)="triggerFileInput()">
      <!-- <span class="button-icon">📁</span> -->
      Importer une ordonnance
    </button>
  </div>

  <div class="input-row">
    <div class="patient-search-container">
      <input
        type="text"
        class="input-field patient-name"
        placeholder="Nom du Patient"
        [(ngModel)]="patientSearchQuery"
        (input)="onPatientSearchInput($event)"
        (focus)="onPatientSearchFocus()"
        (blur)="onPatientSearchBlur()"
        [value]="selectedPatient ? selectedPatient.fullName : patientSearchQuery"
      >

      @if (showPatientDropdown && searchResults.length > 0) {
        <div class="patient-dropdown">
          @for (patient of searchResults; track patient.id) {
            <div class="patient-item" (click)="selectPatient(patient)">
              <div class="patient-avatar">{{ patient.initials }}</div>
              <div class="patient-info">
                <span class="patient-name-text">{{ patient.fullName }}</span>
                <span class="patient-details">{{ patient.phone }} • {{ patient.primaryAddress }}</span>
              </div>
            </div>
          }
        </div>
      }

      @if (showPatientDropdown && searchResults.length === 0 && patientSearchQuery.length >= 2) {
        <div class="patient-dropdown">
          <div class="no-results">
            <span>Aucun patient trouvé</span>
          </div>
        </div>
      }
    </div>

    <div class="household-display-container">
      <input
        type="text"
        class="input-field foyer-name"
        placeholder="Nom du foyer"
        [value]="householdDisplayText"
        readonly
        disabled
        [class.loading]="isLoadingHousehold"
        [class.no-household]="householdDisplayText === 'Aucun foyer trouvé'"
      >

      @if (isLoadingHousehold) {
        <div class="loading-indicator">
          <div class="loading-spinner"></div>
        </div>
      }
    </div>
  </div>

  <div class="button-row">
    <button class="cancel-button" (click)="close()" [disabled]="isSubmitting">
      Annuler
    </button>
    <button
      class="submit-button"
      (click)="onSubmit()"
      [disabled]="!isFormValid() || isSubmitting"
      [class.loading]="isSubmitting"
    >
      @if (isSubmitting) {
        <span class="loading-spinner"></span>
        Création en cours...
      } @else {
        Valider
      }
    </button>
  </div>
</div>
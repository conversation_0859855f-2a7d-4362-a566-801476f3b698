import { Component, OnInit, OnD<PERSON>roy, HostListener, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import {
  ButtonDirective,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { MatDialog } from '@angular/material/dialog';
import { AddPrescriptionComponent } from './add-prescription/add-prescription.component';
import { PrescriptionService } from '../../services/prescription.service';
import { PatientService } from '../../services/patient.service';
import { PharmacyService } from '../../services/pharmacy.service';
import { HouseHoldService } from '../../services/household.service';
import { RouteEncryptionService } from '../../services/route-encryption.service';
import { Prescription, PrescriptionFilters } from '../../models/prescription.model';


@Component({
  selector: 'app-prescriptions',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonDirective,
    IconDirective
  ],
  templateUrl: './prescriptions.component.html',
  styleUrls: ['./prescriptions.component.scss']
})
export class PrescriptionsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  viewMode: 'table' | 'cards' = 'table';
  activeCardMenu: string | null = null;
  prescriptions: Prescription[] = [];
  loading = false;
  error: string | null = null;

  // Filter properties - using signals for reactivity
  showFilter = signal(false);
  selectedType = signal('all'); // Default to 'all' for household filter
  selectedStatus = signal(''); // No default status (user must select one)
  showTypeDropdown = signal(false);
  showStatusDropdown = signal(false);

  // Filters
  filters: PrescriptionFilters = {};

  // Pagination
  totalPrescriptions = 0;

  dateRange: Date[] = [];
  selectedDate: Date = new Date();

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 3; // Default to 3 (first odd number)
  totalPages: number = 0;
  availablePageSizes: number[] = [3, 5, 7, 9, 11, 13, 15]; // Odd numbers only

  // Frontend pagination - store all prescriptions and paginated view
  allPrescriptions: Prescription[] = []; // All prescriptions from backend
  paginatedPrescriptions: Prescription[] = []; // Current page prescriptions

  constructor(
    private dialog: MatDialog,
    private router: Router,
    private prescriptionService: PrescriptionService,
    private patientService: PatientService,
    private pharmacyService: PharmacyService,
    private householdService: HouseHoldService,
    private routeEncryptionService: RouteEncryptionService
  ) {
    this.generateDateRange();
    // Initialize arrays to prevent null errors
    this.allPrescriptions = [];
    this.paginatedPrescriptions = [];
  }

  ngOnInit() {
    console.log('PrescriptionsComponent initialized');
    this.loadPrescriptions();
    this.subscribeToServiceState();
    this.subscribeToServiceEvents();
  }

  // Subscribe to prescription service state for lazy loading
  private subscribeToServiceState() {
    console.log('🔄 Setting up lazy loading subscription...');
    this.prescriptionService.prescriptions$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (prescriptions) => {
          console.log('🔄 Lazy loading update - received:', prescriptions.length, 'prescriptions');
          console.log('🔄 Previous count:', this.prescriptions.length);

          // Pure lazy loading - only update the component data, no API calls
          this.prescriptions = [...prescriptions]; // Create new array reference for change detection
          this.totalPrescriptions = prescriptions.length;
          this.loading = false; // Ensure loading state is cleared

          console.log('🔄 Lazy loading complete - new count:', this.prescriptions.length);
          console.log('🔄 Component updated without page refresh');
        },
        error: (error) => {
          console.error('❌ Error in lazy loading subscription:', error);
          this.loading = false;
        }
      });
  }

  // Subscribe to service events for component refresh
  private subscribeToServiceEvents() {
    console.log('🔔 Setting up service event listener...');
    this.prescriptionService.listen()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (event) => {
          console.log('🔔 Received service event:', event);

          if (event === 'CreationClick') {
            console.log('🔄 Handling CreationClick event - refreshing component...');
            // Reset to first page and reload data properly
            this.currentPage = 1;
            this.refreshAfterCreation();
          }
        },
        error: (error) => {
          console.error('❌ Error in service event subscription:', error);
        }
      });
  }

  // Special refresh method for after prescription creation
  private refreshAfterCreation() {
    console.log('🔄 Refreshing after prescription creation...');

    // Set loading state immediately to prevent showing stale data
    this.loading = true;

    // Clear current data to prevent showing stale data
    this.prescriptions = [];
    this.allPrescriptions = [];
    this.paginatedPrescriptions = [];

    // Reset pagination state
    this.currentPage = 1;
    this.totalPrescriptions = 0;
    this.totalPages = 0;

    // Load fresh data
    this.loadPrescriptions();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadPrescriptions() {
    // Build filters from current selections
    this.buildFilters();

    console.log('Loading prescriptions with filters:', this.filters);
    this.loading = true;

    this.prescriptionService.getPrescriptions(this.filters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Component received response:', response);

          // Store all prescriptions for frontend pagination
          this.allPrescriptions = response.prescriptions;
          this.totalPrescriptions = response.total;

          // Apply frontend pagination
          this.applyFrontendPagination();

          console.log('All prescriptions loaded:', this.allPrescriptions.length);
          console.log('Total prescriptions:', this.totalPrescriptions);
          console.log('Total pages:', this.totalPages);
          console.log('Current page:', this.currentPage);
          console.log('Paginated prescriptions:', this.paginatedPrescriptions.length);

          // ✅ Component handles data enrichment on paginated data ONLY
          this.enrichPaginatedPrescriptionsWithPatientData();

          // Don't set loading = false here, let the enrichment method handle it
          this.error = null;
        },
        error: (error) => {
          console.error('Error loading prescriptions:', error);
          this.error = 'Failed to load prescriptions';
          this.loading = false;
          this.allPrescriptions = [];
          this.prescriptions = [];
          this.paginatedPrescriptions = [];
          this.totalPrescriptions = 0;
        }
      });
  }

  // Build filters object from current selections
  private buildFilters() {
    this.filters = {};

    // Add date filter (from selected date)
    if (this.selectedDate) {
      this.filters.date = this.selectedDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    }

    // Add household filter (using hasHousehold parameter)
    if (this.selectedType()) {
      if (this.selectedType() === 'foyer') {
        this.filters.hasHousehold = 'true'; // Only patients with household
      } else if (this.selectedType() === 'all') {
        // Don't set hasHousehold parameter to get all data
        this.filters.hasHousehold = undefined;
      }
    }

    // Add status filter (only if a status is selected)
    if (this.selectedStatus() && this.selectedStatus() !== '') {
      this.filters.status = this.selectedStatus() as any; // Cast to PrescriptionStatus
    }

    // Note: Pagination handled on frontend, not sent to backend
    // this.filters.page = this.currentPage;
    // this.filters.limit = this.itemsPerPage;

    console.log('Built filters:', this.filters);
  }

  // Apply frontend pagination to all prescriptions
  private applyFrontendPagination() {
    // Handle empty state
    if (!this.allPrescriptions || this.allPrescriptions.length === 0) {
      this.totalPages = 0;
      this.currentPage = 1;
      this.paginatedPrescriptions = [];
      this.prescriptions = [];
      return;
    }

    // Calculate total pages based on all prescriptions
    this.totalPages = Math.ceil(this.allPrescriptions.length / this.itemsPerPage);

    // Ensure current page is valid
    if (this.currentPage > this.totalPages && this.totalPages > 0) {
      this.currentPage = this.totalPages;
    }
    if (this.currentPage < 1) {
      this.currentPage = 1;
    }

    // Calculate start and end indices for current page
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;

    // Get prescriptions for current page
    this.paginatedPrescriptions = this.allPrescriptions.slice(startIndex, endIndex);

    // DON'T update prescriptions here - let enrichment method handle it
    // this.prescriptions = [...this.paginatedPrescriptions];

    console.log('Frontend pagination applied:', {
      totalPrescriptions: this.allPrescriptions.length,
      currentPage: this.currentPage,
      itemsPerPage: this.itemsPerPage,
      totalPages: this.totalPages,
      startIndex,
      endIndex,
      paginatedCount: this.paginatedPrescriptions.length,
      paginatedPrescriptionIds: this.paginatedPrescriptions.map(p => p.id)
    });

    console.log('📋 Current page prescriptions:', this.prescriptions.map(p => ({
      id: p.id,
      patientId: p.patientId,
      patientName: p.patientName,
      pharmacyId: p.pharmacyId,
      pharmacyName: p.pharmacyName
    })));

    // Re-enrich the new page data
    this.enrichPaginatedPrescriptionsWithPatientData();
  }

  // ✅ New method to enrich only paginated prescriptions
  private enrichPaginatedPrescriptionsWithPatientData() {
    console.log('🔄 Starting enrichment for paginated prescriptions:', this.paginatedPrescriptions.length);

    // Handle empty prescriptions case
    if (!this.paginatedPrescriptions || this.paginatedPrescriptions.length === 0) {
      this.loading = false;
      this.prescriptions = [];
      console.log('✅ No prescriptions to enrich');
      return;
    }

    // Set loading state to prevent showing incomplete data
    this.loading = true;

    // DON'T show prescriptions until enrichment is complete
    this.prescriptions = [];

    console.log('🔄 Enrichment initialized with prescriptions:', {
      paginatedCount: this.paginatedPrescriptions.length,
      prescriptionIds: this.paginatedPrescriptions.map(p => p.id)
    });

    // Create a copy of paginated prescriptions to work with
    const prescriptionsToEnrich = [...this.paginatedPrescriptions];

    // Track completion of all async operations
    let completedOperations = 0;
    let totalOperations = 0;

    // Calculate total operations dynamically
    const calculateTotalOperations = () => {
      // Each prescription needs: patient + pharmacy + household (if patient has houseHoldId)
      // We'll start with patient + pharmacy for each, then add household operations as needed
      totalOperations = prescriptionsToEnrich.length * 2; // patient + pharmacy for each prescription
      console.log(`🔄 Initial total operations: ${totalOperations}`);
    };

    const checkCompletion = () => {
      completedOperations++;
      console.log(`🔄 Enrichment progress: ${completedOperations}/${totalOperations}`);
      if (completedOperations >= totalOperations) {
        // Only show prescriptions when ALL enrichment is complete
        this.prescriptions = [...prescriptionsToEnrich];
        this.loading = false;
        console.log('✅ All prescription data loaded and displayed');
      }
    };

    // Initialize total operations count
    calculateTotalOperations();

    prescriptionsToEnrich.forEach((prescription, index) => {
      console.log('🔄 Enriching prescription:', prescription.id, 'at index:', index);

      // Fetch patient data
      this.patientService.getPatientById(prescription.patientId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (patient) => {
            console.log('✅ Patient data loaded:', patient.fullName, 'for prescription:', prescription.id);

            // Update the prescription in the working array
            prescriptionsToEnrich[index] = {
              ...prescriptionsToEnrich[index],
              patientName: patient.fullName,
              patientInitials: patient.initials,
              patientPhone: patient.phone
            };

            // Also update in allPrescriptions array to maintain data
            const allIndex = this.allPrescriptions.findIndex(p => p.id === prescription.id);
            if (allIndex !== -1) {
              this.allPrescriptions[allIndex] = {
                ...this.allPrescriptions[allIndex],
                patientName: patient.fullName,
                patientInitials: patient.initials,
                patientPhone: patient.phone
              };
            }

            // ✅ Fetch household data if patient has houseHoldId
            if (patient.houseHoldId) {
              // Increment total operations for household loading
              totalOperations++;
              console.log(`🔄 Added household operation, new total: ${totalOperations}`);

              this.householdService.getHouseHoldById(patient.houseHoldId)
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                  next: (household) => {
                    console.log('✅ Household data loaded:', household.displayName, 'for prescription:', prescription.id);

                    // Update working array
                    prescriptionsToEnrich[index] = {
                      ...prescriptionsToEnrich[index],
                      householdName: household.displayName || household.name
                    };

                    // Update all prescriptions
                    if (allIndex !== -1) {
                      this.allPrescriptions[allIndex] = {
                        ...this.allPrescriptions[allIndex],
                        householdName: household.displayName || household.name
                      };
                    }

                    checkCompletion();
                  },
                  error: (error) => {
                    console.error('❌ Error loading household data:', error);
                    prescriptionsToEnrich[index] = {
                      ...prescriptionsToEnrich[index],
                      householdName: 'Foyer non trouvé'
                    };
                    checkCompletion();
                  }
                });
            } else {
              // No household ID - set fallback
              prescriptionsToEnrich[index] = {
                ...prescriptionsToEnrich[index],
                householdName: undefined
              };
            }

            checkCompletion();
          },
          error: (error) => {
            console.error('❌ Error loading patient data:', error);
            prescriptionsToEnrich[index] = {
              ...prescriptionsToEnrich[index],
              patientName: `Patient #${prescription.patientId}`,
              patientInitials: 'PA',
              patientPhone: 'N/A',
              householdName: undefined
            };
            checkCompletion();
          }
        });

      // Fetch pharmacy data
      this.pharmacyService.getPharmacyById(prescription.pharmacyId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (pharmacy) => {
            console.log('✅ Pharmacy data loaded:', pharmacy.pharmacyName, 'for prescription:', prescription.id);

            // Update working array
            prescriptionsToEnrich[index] = {
              ...prescriptionsToEnrich[index],
              pharmacyName: pharmacy.pharmacyName
            };

            // Update all prescriptions
            const allIndex = this.allPrescriptions.findIndex(p => p.id === prescription.id);
            if (allIndex !== -1) {
              this.allPrescriptions[allIndex] = {
                ...this.allPrescriptions[allIndex],
                pharmacyName: pharmacy.pharmacyName
              };
            }

            checkCompletion();
          },
          error: (error) => {
            console.error('❌ Error loading pharmacy data:', error);
            prescriptionsToEnrich[index] = {
              ...prescriptionsToEnrich[index],
              pharmacyName: `Pharmacie #${prescription.pharmacyId}`
            };
            checkCompletion();
          }
        });
    });
  }



  generateDateRange() {
    const today = new Date();
    for (let i = -3; i <= 3; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      this.dateRange.push(date);
    }
  }

  selectDate(date: Date) {
    this.selectedDate = date;
    // Reset to first page when changing filters
    this.currentPage = 1;
    this.loadPrescriptions(); // This will reload data and apply pagination
  }

  // Frontend pagination methods
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      console.log('🔄 Going to page:', page);
      this.currentPage = page;
      this.applyFrontendPagination();
      // Re-enrich the new page data
      this.enrichPaginatedPrescriptionsWithPatientData();
    }
  }

  goToFirstPage() {
    this.goToPage(1);
  }

  goToLastPage() {
    this.goToPage(this.totalPages);
  }

  goToPreviousPage() {
    this.goToPage(this.currentPage - 1);
  }

  goToNextPage() {
    this.goToPage(this.currentPage + 1);
  }

  changeItemsPerPage(newSize: number) {
    console.log('🔄 Changing page size from', this.itemsPerPage, 'to', newSize);
    this.itemsPerPage = newSize;
    this.currentPage = 1; // Reset to first page
    this.applyFrontendPagination();
    // Re-enrich the new page data
    this.enrichPaginatedPrescriptionsWithPatientData();
  }

  // Type-safe event handler for page size change
  onPageSizeChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    if (target && target.value) {
      console.log('🔄 Page size change event:', target.value);
      this.changeItemsPerPage(+target.value);
    }
  }

  // Get page numbers for pagination display
  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;

    if (this.totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      const start = Math.max(1, this.currentPage - 2);
      const end = Math.min(this.totalPages, start + maxVisiblePages - 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }

    return pages;
  }

  // Get the end item number for pagination display
  getEndItemNumber(): number {
    if (!this.allPrescriptions || this.allPrescriptions.length === 0) {
      return 0;
    }
    return Math.min(this.currentPage * this.itemsPerPage, this.allPrescriptions.length);
  }

  // Get total count for pagination display
  getTotalCount(): number {
    return this.allPrescriptions ? this.allPrescriptions.length : 0;
  }

  navigateDate(direction: number) {
    if (direction > 0) {
      this.dateRange.shift();
      const last = this.dateRange[this.dateRange.length - 1];
      const next = new Date(last);
      next.setDate(last.getDate() + 1);
      this.dateRange.push(next);
    } else {
      this.dateRange.pop();
      const first = this.dateRange[0];
      const prev = new Date(first);
      prev.setDate(first.getDate() - 1);
      this.dateRange.unshift(prev);
    }
  }

  getBadgeClass(status: string): string {
    return this.prescriptionService.getStatusBadgeClass(status);
  }

  getBadgeText(status: string): string {
    return this.prescriptionService.getStatusText(status);
  }

  formatDate(date: Date | string): string {
    return this.prescriptionService.formatDate(date);
  }

  getDayName(date: Date): string {
    return date.toLocaleDateString('fr-FR', { weekday: 'short' });
  }

  getDayNumber(date: Date): string {
    return date.getDate().toString();
  }

  toggleView() {
    this.viewMode = this.viewMode === 'table' ? 'cards' : 'table';
  }

  toggleFilter() {
    this.showFilter.set(!this.showFilter());
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return date1.toDateString() === date2.toDateString();
  }

  // Custom dropdown methods
  toggleTypeDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showTypeDropdown.set(!this.showTypeDropdown());
    this.showStatusDropdown.set(false); // Close other dropdown
  }

  toggleStatusDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showStatusDropdown.set(!this.showStatusDropdown());
    this.showTypeDropdown.set(false); // Close other dropdown
  }

  // Close dropdowns when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.custom-dropdown')) {
      this.showTypeDropdown.set(false);
      this.showStatusDropdown.set(false);
    }
  }

  selectTypeOption(value: string) {
    this.selectedType.set(value);
    this.showTypeDropdown.set(false);
    // Reset to first page when changing filters
    this.currentPage = 1;
    this.loadPrescriptions();
  }

  selectStatusOption(value: string) {
    this.selectedStatus.set(value);
    this.showStatusDropdown.set(false);
    // Reset to first page when changing filters
    this.currentPage = 1;
    this.loadPrescriptions();
  }

  getSelectedTypeText(): string {
    switch (this.selectedType()) {
      case 'foyer': return 'Foyer';
      case 'all': return 'Tous';
      default: return 'Tous';
    }
  }

  getSelectedStatusText(): string {
    switch (this.selectedStatus()) {
      case 'UPLOADED': return 'Téléchargée';
      case 'IN_REVIEW': return 'En révision';
      case 'NEEDS_CLARIFICATION': return 'Clarification requise';
      case 'APPROVED': return 'Approuvée';
      case 'REJECTED': return 'Rejetée';
      case 'EXPIRED': return 'Expirée';
      case 'ARCHIVED': return 'Archivée';
      default: return 'Sélectionner un statut';
    }
  }

  toggleCardMenu(prescriptionId: string) {
    this.activeCardMenu = this.activeCardMenu === prescriptionId ? null : prescriptionId;
  }

  viewDetails(prescription: Prescription) {
    this.activeCardMenu = null;
    console.log('Viewing details for prescription:', prescription.id);

    // Encrypt the prescription ID for security
    const encryptedId = this.routeEncryptionService.encryptId(prescription.id);
    console.log('Encrypted prescription ID:', encryptedId);

    // Navigate to details page with encrypted prescription ID as route parameter
    this.router.navigate(['/prescriptions/details', encryptedId]);
  }

  downloadPrescription(prescription: Prescription) {
    this.activeCardMenu = null;

    if (prescription.storagePath && prescription.storagePath.length > 0) {
      // Download the first file (you can modify this to download all files)
      const filePath = prescription.storagePath[0];
      this.prescriptionService.downloadPrescriptionFile(filePath)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (blob) => {
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `prescription-${prescription.id}.pdf`;
            link.click();
            window.URL.revokeObjectURL(url);
          },
          error: (error) => {
            console.error('Error downloading prescription:', error);
          }
        });
    }
  }

  openAddPrescriptionModal() {
    const dialogRef = this.dialog.open(AddPrescriptionComponent, {
      panelClass: 'custom-modal',
      width: '900px',
      height: '400px',
      maxWidth: '900px',
      maxHeight: '400px',
      disableClose: false,
      hasBackdrop: true,
      backdropClass: 'custom-backdrop',
      position: {
        left: 'calc(256px + (100vw - 256px - 900px) / 2)'
      }
    });

    // Handle modal close event
    dialogRef.afterClosed().pipe(takeUntil(this.destroy$)).subscribe(result => {
      if (result && result.success) {
        console.log('✅ Prescription created successfully:', result.prescription);
        console.log('🔄 Lazy loading will handle the update automatically');

        // No manual refresh needed - lazy loading handles everything
        // The service has already updated the state, component will auto-update

        // Optional: Show a toast notification instead of alert
        // You can implement a toast service here if needed
      }
    });
  }
}

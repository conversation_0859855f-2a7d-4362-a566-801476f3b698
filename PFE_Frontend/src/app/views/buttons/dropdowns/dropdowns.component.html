<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Dropdown</strong> <small>Single button</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Here&#39;s how you can put them to work with either <code>&lt;button&gt;</code>
          elements:
        </p>
        <app-docs-example href="components/dropdown#single-button">
          <c-dropdown>
            <button cButton cDropdownToggle color="secondary">
              Dropdown button
            </button>
            <ul cDropdownMenu>
              <li><h6 cDropdownHeader>Header</h6></li>
              <li><a [routerLink]="[]" cDropdownItem>Action</a></li>
              <li><a [routerLink]="[]" cDropdownItem>Another action</a></li>
              <li><a [routerLink]="[]" cDropdownItem>Something else here</a></li>
              <li>
                <hr cDropdownDivider>
              </li>
              <li>
                <button [routerLink]="'/buttons'" cDropdownItem>Separated link</button>
              </li>
            </ul>
          </c-dropdown>
        </app-docs-example>
        <p class="text-body-secondary small">
          The best part is you can do this with any button variant, too:
        </p>
        <app-docs-example href="components/dropdown#single-button">
          @for (color of colors; track color; let i = $index) {
            <c-dropdown variant="btn-group">
              <button [color]="color" cButton cDropdownToggle>
                {{ color }}
              </button>
              <ul cDropdownMenu>
                <li>
                  <button [routerLink]="[]" cDropdownItem>Action</button>
                </li>
                <li>
                  <button [routerLink]="[]" cDropdownItem>Another action</button>
                </li>
                <li>
                  <button [routerLink]="[]" cDropdownItem>Something else here</button>
                </li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li>
                  <button cDropdownItem>Separated link</button>
                </li>
              </ul>
            </c-dropdown>
          }
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Dropdown</strong> <small>Split button</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Similarly, create split button dropdowns with virtually the same markup as single
          button dropdowns, but with the addition of boolean prop <code>split</code> for proper
          spacing around the dropdown caret.
        </p>
        <p class="text-body-secondary small">
          We use this extra class to reduce the horizontal <code>padding</code> on either side
          of the caret by 25% and remove the <code>margin-left</code> that&#39;s attached for
          normal button dropdowns. Those additional changes hold the caret centered in the split
          button and implement a more properly sized hit area next to the main button.
        </p>
        <app-docs-example href="components/dropdown#split-button">
          @for (color of colors; track color; let i = $index) {
            <c-dropdown placement="bottom-start" variant="btn-group">
              <button [color]="color" cButton>
                {{ color }}
              </button>
              <button [color]="color" cButton cDropdownToggle split>
                <span class="visually-hidden">Toggle Dropdown</span>
              </button>
              <ul cDropdownMenu>
                <li>
                  <button [routerLink]="[]" cDropdownItem>Action</button>
                </li>
                <li>
                  <button cDropdownItem>Another action</button>
                </li>
                <li>
                  <button cDropdownItem>Something else here</button>
                </li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li>
                  <button cDropdownItem>Separated link</button>
                </li>
              </ul>
            </c-dropdown>
          }
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Dropdown</strong> <small>Sizing</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Button dropdowns work with buttons of all sizes, including default and split dropdown
          buttons.
        </p>
        <app-docs-example href="components/dropdown#sizing">
          <c-dropdown variant="btn-group">
            <button cButton cDropdownToggle color="secondary" size="lg">
              Large button
            </button>
            <ul cDropdownMenu>
              <li>
                <button cDropdownItem>Action</button>
              </li>
              <li>
                <button cDropdownItem>Another action</button>
              </li>
              <li>
                <button cDropdownItem>Something else here</button>
              </li>
              <li>
                <hr cDropdownDivider>
              </li>
              <li>
                <button cDropdownItem>Separated link</button>
              </li>
            </ul>
          </c-dropdown>
          <c-dropdown variant="btn-group">
            <button cButton color="secondary" size="lg">
              Large split button
            </button>
            <button cButton cDropdownToggle color="secondary" size="lg" split>
              <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <ul cDropdownMenu>
              <li>
                <button [routerLink]="[]" cDropdownItem>Action</button>
              </li>
              <li>
                <button cDropdownItem>Another action</button>
              </li>
              <li>
                <button cDropdownItem>Something else here</button>
              </li>
              <li>
                <hr cDropdownDivider>
              </li>
              <li>
                <button cDropdownItem>Separated link</button>
              </li>
            </ul>
          </c-dropdown>
        </app-docs-example>
        <app-docs-example href="components/dropdown#sizing">
          <c-dropdown variant="btn-group">
            <button cButton cDropdownToggle color="secondary" size="sm">
              Small button
            </button>
            <ul cDropdownMenu>
              <li>
                <button cDropdownItem>Action</button>
              </li>
              <li>
                <button cDropdownItem>Another action</button>
              </li>
              <li>
                <button cDropdownItem>Something else here</button>
              </li>
              <li>
                <hr cDropdownDivider>
              </li>
              <li>
                <button cDropdownItem>Separated link</button>
              </li>
            </ul>
          </c-dropdown>
          <c-dropdown variant="btn-group">
            <button cButton color="secondary" size="sm">
              Small split button
            </button>
            <button cButton cDropdownToggle color="secondary" size="sm" split>
              <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <ul cDropdownMenu>
              <li>
                <button [routerLink]="[]" cDropdownItem>Action</button>
              </li>
              <li>
                <button cDropdownItem>Another action</button>
              </li>
              <li>
                <button cDropdownItem>Something else here</button>
              </li>
              <li>
                <hr cDropdownDivider>
              </li>
              <li>
                <button cDropdownItem>Separated link</button>
              </li>
            </ul>
          </c-dropdown>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Dropdown</strong> <small>dark</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Opt into darker dropdowns to match a dark navbar or custom style by set
          <code>dark</code> property. No changes are required to the dropdown items.
        </p>
        <app-docs-example href="components/dropdown#dark-dropdowns">
          <c-dropdown dark>
            <button cButton cDropdownToggle color="secondary">
              Dropdown button
            </button>
            <ul cDropdownMenu cdkTrapFocus>
              <li>
                <button cDropdownItem>Action</button>
              </li>
              <li>
                <button cDropdownItem>Another action</button>
              </li>
              <li>
                <button cDropdownItem>Something else here</button>
              </li>
              <li>
                <hr cDropdownDivider>
              </li>
              <li>
                <button cDropdownItem>Separated link</button>
              </li>
            </ul>
          </c-dropdown>
        </app-docs-example>
        <p class="text-body-secondary small">And putting it to use in a navbar:</p>
        <app-docs-example href="components/dropdown#dark-dropdowns">
          <c-navbar class="bg-dark" colorScheme="dark" expand="lg">
            <c-container [fluid]="true">
              <a cNavbarBrand href="https://coreui.io/angular/" target="_blank">
                Navbar
              </a>
              <button [cNavbarToggler]="collapseRef"></button>
              <div #collapseRef="cCollapse" [navbar]="true" cCollapse>
                <c-navbar-nav class="me-auto mb-2 mb-lg-0">
                  <c-nav-item>
                    <a [active]="true" [routerLink]="[]" cNavLink>Home</a>
                  </c-nav-item>
                  <c-nav-item>
                    <a [routerLink]="[]" cNavLink>Link</a>
                  </c-nav-item>
                  <c-nav-item>
                    <c-dropdown variant="nav-item" [popper]="false">
                      <a cNavLink cDropdownToggle>
                        Dropdown
                      </a>
                      <ul cDropdownMenu dark>
                        <li>
                          <button cDropdownItem>Action</button>
                        </li>
                        <li>
                          <button cDropdownItem>Another action</button>
                        </li>
                        <li>
                          <button cDropdownItem>Something else here</button>
                        </li>
                        <li>
                          <hr cDropdownDivider>
                        </li>
                        <li>
                          <button cDropdownItem>Separated link</button>
                        </li>
                      </ul>
                    </c-dropdown>
                  </c-nav-item>
                  <c-nav-item>
                    <a cNavLink disabled>Disabled</a>
                  </c-nav-item>
                </c-navbar-nav>
                <form cForm class="d-flex">
                  <input aria-label="Search" cFormControl class="me-2" placeholder="Search" type="search">
                  <button cButton color="success" type="submit" variant="outline">Search</button>
                </form>
              </div>
            </c-container>
          </c-navbar>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Dropdown</strong> <small>Dropup</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Trigger dropdown menus above elements by adding
          <code>direction=&#34;dropup&#34;</code> to the <code>&lt;c-dropdown&gt;</code>
          component.
        </p>
        <app-docs-example href="components/dropdown#dropup">
          <c-dropdown direction="dropup" variant="btn-group">
            <button cButton cDropdownToggle color="secondary">
              Dropup
            </button>
            <ul cDropdownMenu>
              <li>
                <button cDropdownItem>Action</button>
              </li>
              <li>
                <button cDropdownItem>Another action</button>
              </li>
              <li>
                <button cDropdownItem>Something else here</button>
              </li>
              <li>
                <hr cDropdownDivider>
              </li>
              <li>
                <button cDropdownItem>Separated link</button>
              </li>
            </ul>
          </c-dropdown>
          <c-dropdown direction="dropup" variant="btn-group">
            <button cButton color="secondary">
              Split Dropup
            </button>
            <button [split]="true" cButton cDropdownToggle color="secondary">
              <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <ul cDropdownMenu>
              <li>
                <button cDropdownItem>Action</button>
              </li>
              <li>
                <button cDropdownItem>Another action</button>
              </li>
              <li>
                <button cDropdownItem>Something else here</button>
              </li>
              <li>
                <hr cDropdownDivider>
              </li>
              <li>
                <button cDropdownItem>Separated link</button>
              </li>
            </ul>
          </c-dropdown>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Dropdown</strong> <small>Dropright</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Trigger dropdown menus at the right of the elements by adding
          <code>direction=&#34;dropend&#34;</code> to the <code>&lt;c-dropdown&gt;</code>
          component.
        </p>
        <app-docs-example href="components/dropdown#dropright">
          <c-dropdown direction="dropend" variant="btn-group">
            <button cButton cDropdownToggle color="secondary">
              Dropend
            </button>
            <ul cDropdownMenu>
              <li>
                <button cDropdownItem>Action</button>
              </li>
              <li>
                <button cDropdownItem>Another action</button>
              </li>
              <li>
                <button cDropdownItem>Something else here</button>
              </li>
              <li>
                <hr cDropdownDivider>
              </li>
              <li>
                <button cDropdownItem>Separated link</button>
              </li>
            </ul>
          </c-dropdown>
          <c-dropdown direction="dropend" variant="btn-group">
            <button cButton color="secondary">
              Split Right
            </button>
            <button [split]="true" cButton cDropdownToggle color="secondary">
              <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <ul cDropdownMenu>
              <li>
                <button cDropdownItem>Action</button>
              </li>
              <li>
                <button cDropdownItem>Another action</button>
              </li>
              <li>
                <button cDropdownItem>Something else here</button>
              </li>
              <li>
                <hr cDropdownDivider>
              </li>
              <li>
                <button cDropdownItem>Separated link</button>
              </li>
            </ul>
          </c-dropdown>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Dropdown</strong> <small>Dropleft</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Trigger dropdown menus at the left of the elements by adding
          <code>direction=&#34;dropstart&#34;</code> to the <code>&lt;c-dropdown&gt;</code>
          component.
        </p>
        <app-docs-example href="components/dropdown#dropleft">
          <c-button-group>
            <c-dropdown direction="dropstart" variant="input-group">
              <button [split]="true" cButton cDropdownToggle color="secondary">
                <span class="visually-hidden">Toggle Dropdown</span>
              </button>
              <ul cDropdownMenu>
                <li>
                  <button cDropdownItem>Action</button>
                </li>
                <li>
                  <button cDropdownItem>Another action</button>
                </li>
                <li>
                  <button cDropdownItem>Something else here</button>
                </li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li>
                  <button cDropdownItem>Separated link</button>
                </li>
              </ul>
              <button cButton color="secondary">
                Split Left
              </button>
            </c-dropdown>
          </c-button-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Dropdown</strong> <small>Centered</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Trigger dropdown menus centered below the toggle by adding <code>direction="center"</code> to the <code>c-dropdown</code>
          component.
        </p>
        <app-docs-example href="components/dropdown#centered">
          <c-dropdown direction="center" variant="btn-group">
            <button cButton cDropdownToggle color="secondary">Centered dropdown</button>
            <ul cDropdownMenu>
              <li>
                <button cDropdownItem>Action one</button>
              </li>
              <li>
                <button cDropdownItem>Action two</button>
              </li>
              <li>
                <button cDropdownItem>Action three</button>
              </li>
            </ul>
          </c-dropdown>
          <c-dropdown direction="dropup-center" class="dropup" variant="btn-group">
            <button cButton cDropdownToggle color="secondary">Centered dropdup</button>
            <ul cDropdownMenu>
              <li>
                <button cDropdownItem>Action one</button>
              </li>
              <li>
                <button cDropdownItem>Action two</button>
              </li>
              <li>
                <button cDropdownItem>Action three</button>
              </li>
            </ul>
          </c-dropdown>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>

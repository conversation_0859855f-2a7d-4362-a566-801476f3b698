import { Component, OnInit, OnD<PERSON>roy, signal, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ButtonDirective } from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { OrderService } from '../../services/order.service';
import { PatientService } from '../../services/patient.service';
import { HouseHoldService } from '../../services/household.service';
import { ProductService } from '../../services/product.service';
import { RouteEncryptionService } from '../../services/route-encryption.service';
import { Order, OrderFilters, OrderStatus, OrderPharmacistConfirmCommand } from '../../models/order.model';

@Component({
  selector: 'app-order',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonDirective,
    IconDirective
  ],
  templateUrl: './order.component.html',
  styleUrls: ['./order.component.scss']
})
export class OrderComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  viewMode: 'table' | 'cards' = 'table';
  activeCardMenu: string | null = null;
  orders: Order[] = [];
  loading = false;
  error: string | null = null;

  // Filter properties - using signals for reactivity
  showFilter = signal(false);
  selectedType = signal('all'); // Default to 'all' for household filter
  selectedStatus = signal(''); // No default status (user must select one)
  showTypeDropdown = signal(false);
  showStatusDropdown = signal(false);

  // Filters
  filters: OrderFilters = {};

  // Pagination - matching prescription component
  totalOrders = 0;
  allOrders: Order[] = [];
  paginatedOrders: Order[] = [];
  currentPage = 1;
  itemsPerPage = 3; // Initialize with first option (3)
  totalPages = 0;
  availablePageSizes = [3, 5, 7, 9];

  dateRange: Date[] = [];
  selectedDate: Date = new Date();

  // Confirmation modal properties
  showConfirmModal = false;
  selectedOrderForConfirm: Order | null = null;
  confirmQuantity = 1;
  confirmDeliveryNote = '';
  availableProductQuantity = 0;
  loadingProductInfo = false;
  productValidationError = '';

  constructor(
    private orderService: OrderService,
    private patientService: PatientService,
    private householdService: HouseHoldService,
    private productService: ProductService,
    private router: Router,
    private routeEncryptionService: RouteEncryptionService
  ) {}

  ngOnInit() {
    this.initializeDateRange();
    this.loadOrders();

    // Subscribe to service refresh events
    this.orderService.refresh$
      .pipe(takeUntil(this.destroy$))
      .subscribe((source) => {
        console.log('🔄 Order component received refresh signal from:', source);
        this.loadOrders();
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Close dropdowns when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.custom-dropdown')) {
      this.showTypeDropdown.set(false);
      this.showStatusDropdown.set(false);
    }
    if (!target.closest('.card-menu')) {
      this.activeCardMenu = null;
    }
  }

  // Initialize date range (7 days)
  initializeDateRange() {
    const today = new Date();
    this.selectedDate = today;
    this.dateRange = [];

    // Create 7-day range centered on today
    for (let i = -3; i <= 3; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      this.dateRange.push(date);
    }
  }

  // Load orders from service
  loadOrders() {
    this.loading = true;
    this.error = null;

    // Set date filter to selected date
    this.filters.date = this.formatDateForAPI(this.selectedDate);

    // Set status filter if selected
    if (this.selectedStatus() && this.selectedStatus() !== '') {
      this.filters.status = this.selectedStatus() as OrderStatus;
    } else {
      delete this.filters.status;
    }

    // Set household filter based on type selection
    if (this.selectedType() === 'foyer') {
      this.filters.hasHousehold = 'true';
    } else if (this.selectedType() === 'patient') {
      this.filters.hasHousehold = 'false';
    } else {
      delete this.filters.hasHousehold;
    }

    this.orderService.getOrders(this.filters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Component received response:', response);

          // Store all orders for frontend pagination
          this.allOrders = response.orders;
          this.totalOrders = response.total;

          // Start enrichment for ALL orders (not just paginated ones)
          this.allOrders.forEach((order, index) => {
            this.enrichOrderData(order, index);
          });

          // Apply frontend pagination AFTER starting enrichment
          this.applyFrontendPagination();

          console.log('All orders loaded:', this.allOrders.length);
          console.log('Total orders:', this.totalOrders);
          console.log('Total pages:', this.totalPages);
          console.log('Current page:', this.currentPage);
          console.log('Paginated orders:', this.paginatedOrders.length);

          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading orders:', error);
          this.error = 'Erreur lors du chargement des commandes';
          this.loading = false;
        }
      });
  }

  // Apply frontend pagination
  applyFrontendPagination() {
    // Calculate total pages
    this.totalPages = Math.ceil(this.allOrders.length / this.itemsPerPage);

    // Ensure current page is valid
    if (this.currentPage > this.totalPages && this.totalPages > 0) {
      this.currentPage = this.totalPages;
    }
    if (this.currentPage < 1) {
      this.currentPage = 1;
    }

    // Calculate start and end indices for current page
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;

    // Get orders for current page
    this.paginatedOrders = this.allOrders.slice(startIndex, endIndex);

    // Update orders to show paginated data
    this.orders = [...this.paginatedOrders];

    console.log('Frontend pagination applied:', {
      totalOrders: this.allOrders.length,
      currentPage: this.currentPage,
      itemsPerPage: this.itemsPerPage,
      totalPages: this.totalPages,
      startIndex,
      endIndex,
      paginatedCount: this.paginatedOrders.length
    });
  }

  // Format date for API (YYYY-MM-DD)
  formatDateForAPI(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  // Format date for display
  formatDate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  // Format amount for display
  formatAmount(amount: number): string {
    return `${amount.toFixed(2)} €`;
  }

  // Get badge class for order status
  getBadgeClass(status: string): string {
    switch (status) {
      case 'PENDING':
        return 'badge-pending';
      case 'APPROVED':
        return 'badge-approved';
      case 'IN_TRANSIT':
        return 'badge-in-transit';
      case 'DELIVERED':
        return 'badge-delivered';
      case 'CANCELLED':
        return 'badge-cancelled';
      default:
        return 'badge-default';
    }
  }

  // Get badge text for order status
  getBadgeText(status: string): string {
    switch (status) {
      case 'PENDING':
        return 'En attente';
      case 'APPROVED':
        return 'Approuvée';
      case 'IN_TRANSIT':
        return 'En transit';
      case 'DELIVERED':
        return 'Livrée';
      case 'CANCELLED':
        return 'Annulée';
      default:
        return status;
    }
  }

  // Toggle filter visibility
  toggleFilter() {
    this.showFilter.set(!this.showFilter());
    // Close dropdowns when toggling filter
    this.showTypeDropdown.set(false);
    this.showStatusDropdown.set(false);
  }

  // Toggle view mode
  toggleView() {
    this.viewMode = this.viewMode === 'table' ? 'cards' : 'table';
    this.activeCardMenu = null; // Close any open card menus
  }

  // Toggle type dropdown
  toggleTypeDropdown(event: Event) {
    event.stopPropagation();
    this.showTypeDropdown.set(!this.showTypeDropdown());
    this.showStatusDropdown.set(false);
  }

  // Toggle status dropdown
  toggleStatusDropdown(event: Event) {
    event.stopPropagation();
    this.showStatusDropdown.set(!this.showStatusDropdown());
    this.showTypeDropdown.set(false);
  }

  // Select type option
  selectTypeOption(type: string) {
    this.selectedType.set(type);
    this.showTypeDropdown.set(false);
    this.currentPage = 1; // Reset to first page
    this.loadOrders();
  }

  // Select status option
  selectStatusOption(status: string) {
    this.selectedStatus.set(status);
    this.showStatusDropdown.set(false);
    this.currentPage = 1; // Reset to first page
    this.loadOrders();
  }

  // Get selected type text
  getSelectedTypeText(): string {
    switch (this.selectedType()) {
      case 'all':
        return 'Tous';
      case 'patient':
        return 'Patient';
      case 'foyer':
        return 'Foyer';
      default:
        return 'Tous';
    }
  }

  // Get selected status text
  getSelectedStatusText(): string {
    switch (this.selectedStatus()) {
      case '':
        return 'Tous les statuts';
      case 'PENDING':
        return 'En attente';
      case 'APPROVED':
        return 'Approuvée';
      case 'IN_TRANSIT':
        return 'En transit';
      case 'DELIVERED':
        return 'Livrée';
      case 'CANCELLED':
        return 'Annulée';
      default:
        return 'Tous les statuts';
    }
  }

  // Date navigation methods - matching prescription component
  navigateDate(direction: number) {
    if (direction > 0) {
      this.dateRange.shift();
      const last = this.dateRange[this.dateRange.length - 1];
      const next = new Date(last);
      next.setDate(last.getDate() + 1);
      this.dateRange.push(next);
    } else {
      this.dateRange.pop();
      const first = this.dateRange[0];
      const prev = new Date(first);
      prev.setDate(first.getDate() - 1);
      this.dateRange.unshift(prev);
    }

    // Don't auto-load orders when navigating dates
    // User needs to click on a specific date
  }

  selectDate(date: Date) {
    this.selectedDate = date;
    this.currentPage = 1; // Reset to first page
    this.loadOrders();
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return date1.toDateString() === date2.toDateString();
  }

  getDayName(date: Date): string {
    return date.toLocaleDateString('fr-FR', { weekday: 'short' });
  }

  // Card menu methods
  toggleCardMenu(orderId: string) {
    this.activeCardMenu = this.activeCardMenu === orderId ? null : orderId;
  }

  // Navigation methods
  viewDetails(order: Order) {
    this.activeCardMenu = null;

    // Encrypt the order ID for security
    const encryptedId = this.routeEncryptionService.encryptId(order.id);
    console.log('Encrypted order ID:', encryptedId);

    // Navigate to details page with encrypted order ID as route parameter
    this.router.navigate(['/orders/order-details', encryptedId]);
  }

  // Open confirmation modal
  confirmOrder(order: Order) {
    if (order.status !== 'PENDING') {
      return;
    }

    this.selectedOrderForConfirm = order;
    this.confirmQuantity = order.quantity || 1; // Default to existing quantity or 1
    this.confirmDeliveryNote = order.deliveryManNote || '';
    this.availableProductQuantity = 0;
    this.loadingProductInfo = true;
    this.productValidationError = '';
    this.showConfirmModal = true;

    // Fetch product information to get available quantity
    if (order.productIds && order.productIds.length > 0) {
      const productId = order.productIds[0]; // Get first product ID
      this.productService.getProductById(productId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.success && response.product) {
              this.availableProductQuantity = response.product.quantity || 0;
              this.loadingProductInfo = false;
              console.log('✅ Product info loaded:', response.product.name, 'Available quantity:', this.availableProductQuantity);

              // Validate current quantity
              this.validateQuantity();
            } else {
              this.loadingProductInfo = false;
              this.productValidationError = 'Produit non trouvé';
            }
          },
          error: (error) => {
            console.error('❌ Error loading product info:', error);
            this.loadingProductInfo = false;
            this.productValidationError = 'Impossible de charger les informations du produit';
          }
        });
    } else {
      this.loadingProductInfo = false;
      this.productValidationError = 'Aucun produit trouvé dans la commande';
    }
  }

  // Close confirmation modal
  closeConfirmModal() {
    this.showConfirmModal = false;
    this.selectedOrderForConfirm = null;
    this.confirmQuantity = 1;
    this.confirmDeliveryNote = '';
    this.availableProductQuantity = 0;
    this.loadingProductInfo = false;
    this.productValidationError = '';
  }

  // Validate quantity against available stock
  validateQuantity() {
    if (this.confirmQuantity > this.availableProductQuantity) {
      this.productValidationError = `Quantité demandée (${this.confirmQuantity}) dépasse le stock disponible (${this.availableProductQuantity})`;
    } else if (this.confirmQuantity <= 0) {
      this.productValidationError = 'La quantité doit être supérieure à 0';
    } else {
      this.productValidationError = '';
    }
  }

  // Check if confirmation is valid
  isConfirmationValid(): boolean {
    return this.confirmQuantity > 0 &&
           this.confirmQuantity <= this.availableProductQuantity &&
           !this.loadingProductInfo &&
           this.productValidationError === '';
  }

  // Submit pharmacist confirmation
  submitPharmacistConfirmation() {
    if (!this.selectedOrderForConfirm) {
      return;
    }

    const command: OrderPharmacistConfirmCommand = {
      patientId: this.selectedOrderForConfirm.patientId,
      quantity: this.confirmQuantity,
      deliveryManNote: this.confirmDeliveryNote || undefined
    };

    this.orderService.pharmacistConfirmOrder(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('✅ Order confirmed by pharmacist successfully:', response);
          this.closeConfirmModal();
          // Refresh orders to show updated status
          this.loadOrders();
        },
        error: (error) => {
          console.error('❌ Error confirming order by pharmacist:', error);
          this.error = 'Erreur lors de la confirmation de la commande';
        }
      });
  }

  // Pagination methods
  updatePagination() {
    this.applyFrontendPagination();
  }

  onItemsPerPageChange() {
    this.currentPage = 1; // Reset to first page
    this.applyFrontendPagination();
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.applyFrontendPagination();
    }
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;

    if (this.totalPages <= maxVisiblePages) {
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      const startPage = Math.max(1, this.currentPage - 2);
      const endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  }

  // Pagination methods - matching prescription component
  getTotalCount(): number {
    return this.totalOrders;
  }

  getEndItemNumber(): number {
    return Math.min(this.currentPage * this.itemsPerPage, this.totalOrders);
  }

  onPageSizeChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.itemsPerPage = parseInt(target.value, 10);
    this.currentPage = 1; // Reset to first page
    this.applyFrontendPagination();
  }

  goToFirstPage(): void {
    if (this.currentPage !== 1) {
      this.currentPage = 1;
      this.applyFrontendPagination();
    }
  }

  goToPreviousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.applyFrontendPagination();
    }
  }

  goToNextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.applyFrontendPagination();
    }
  }

  goToLastPage(): void {
    if (this.currentPage !== this.totalPages) {
      this.currentPage = this.totalPages;
      this.applyFrontendPagination();
    }
  }

  // Enrich order data with patient and household information (for ALL orders)
  private enrichOrderData(order: Order, allOrdersIndex: number) {
    console.log('🔄 Starting enrichment for order:', order.id, 'at allOrders index:', allOrdersIndex);

    // Fetch patient data
    this.patientService.getPatientById(order.patientId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (patient) => {
          console.log('✅ Patient data loaded:', patient.fullName, 'for order:', order.id);

          // Update the order in allOrders array
          this.allOrders[allOrdersIndex] = {
            ...this.allOrders[allOrdersIndex],
            patientName: patient.fullName,
            patientInitials: patient.initials,
            patientPhone: patient.phone
          };

          // ✅ Fetch household data if patient has houseHoldId
          if (patient.houseHoldId) {
            this.householdService.getHouseHoldById(patient.houseHoldId)
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: (household) => {
                  console.log('✅ Household data loaded:', household.displayName, 'for order:', order.id);

                  // Update the order in allOrders array
                  this.allOrders[allOrdersIndex] = {
                    ...this.allOrders[allOrdersIndex],
                    householdName: household.displayName || household.name
                  };

                  // Re-apply pagination to update the current view
                  this.applyFrontendPagination();
                },
                error: (error) => {
                  console.error('❌ Error loading household data:', error);
                  this.allOrders[allOrdersIndex] = {
                    ...this.allOrders[allOrdersIndex],
                    householdName: 'Foyer non trouvé'
                  };
                  // Re-apply pagination to update the current view
                  this.applyFrontendPagination();
                }
              });
          } else {
            // No household ID - set fallback
            this.allOrders[allOrdersIndex] = {
              ...this.allOrders[allOrdersIndex],
              householdName: undefined
            };
            // Re-apply pagination to update the current view
            this.applyFrontendPagination();
          }
        },
        error: (error) => {
          console.error('❌ Error loading patient data:', error);
          this.allOrders[allOrdersIndex] = {
            ...this.allOrders[allOrdersIndex],
            patientName: `Patient #${order.patientId}`,
            patientInitials: 'PA',
            patientPhone: 'N/A',
            householdName: undefined
          };
          // Re-apply pagination to update the current view
          this.applyFrontendPagination();
        }
      });
  }
}

import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'Orders'
    },
    children: [
      {
        path: '',
        loadComponent: () => import('./order.component').then(m => m.OrderComponent),
        data: {
          title: 'Orders'
        }
      },
      {
        path: 'stock',
        loadComponent: () => import('./stock/stock.component').then(m => m.StockComponent),
        data: {
          title: 'Gestion du Stock'
        }
      },
      {
        path: 'stock/add-product',
        loadComponent: () => import('./stock/add-product/add-product.component').then(m => m.AddProductComponent),
        data: {
          title: 'Ajouter un article'
        }
      },
      {
        path: 'stock/categories',
        loadComponent: () => import('./stock/category/category.component').then(m => m.CategoryComponent),
        data: {
          title: 'Catégories'
        }
      },
      {
        path: 'order-details/:encryptedId',
        loadComponent: () => import('./order-details/order-details.component').then(m => m.OrderDetailsComponent),
        data: {
          title: 'Détails de la commande'
        }
      }
    ]
  }
];

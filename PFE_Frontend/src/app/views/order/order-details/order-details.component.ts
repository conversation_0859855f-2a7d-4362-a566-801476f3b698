import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil, forkJoin } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { OrderService } from '../../../services/order.service';
import { ProductService } from '../../../services/product.service';
import { PatientService } from '../../../services/patient.service';
import { HouseHoldService } from '../../../services/household.service';
import { RouteEncryptionService } from '../../../services/route-encryption.service';
import { Order, OrderPharmacistConfirmCommand } from '../../../models/order.model';
import { Product } from '../../../models/product.model';
import { Patient } from '../../../models/patient.model';
import { HouseHold } from '../../../models/household.model';
import { environment } from '../../../../environments/environment';

// Interface for product with order details
interface OrderProduct extends Product {
  orderQuantity: number;
}

@Component({
  selector: 'app-order-details',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './order-details.component.html',
  styleUrls: ['./order-details.component.scss']
})
export class OrderDetailsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  order: Order | null = null;
  patient: Patient | null = null;
  household: HouseHold | null = null;
  products: Product[] = [];
  loading = false;
  loadingPatient = false;
  loadingProducts = false;
  error: string | null = null;
  orderId: string;
  orderProducts: OrderProduct[] = [];
  isNarrowMode = false;

  // Confirmation modal properties
  showConfirmModal = false;
  confirmQuantity = 1;
  confirmDeliveryNote = '';
  availableProductQuantity = 0;
  loadingProductInfo = false;
  productValidationError = '';

  // Image navigation for products with multiple images
  productImageIndexes: { [productId: string]: number } = {};

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private orderService: OrderService,
    private productService: ProductService,
    private patientService: PatientService,
    private houseHoldService: HouseHoldService,
    private routeEncryptionService: RouteEncryptionService
  ) {
    // Get encrypted ID from route and decrypt it
    const encryptedId = this.route.snapshot.params['encryptedId'];
    this.orderId = this.routeEncryptionService.decryptId(encryptedId) || '';
    console.log('Decrypted order ID:', this.orderId);
  }

  ngOnInit() {
    this.loadOrderDetails();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadOrderDetails() {
    this.loading = true;
    this.error = null;

    this.orderService.getOrderById(this.orderId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (order) => {
          this.order = order;
          this.loading = false;

          // Load patient details
          this.loadPatientDetails(order.patientId);

          // Load product details
          this.loadProductDetails(order.productIds);
        },
        error: (error) => {
          console.error('Error loading order details:', error);
          this.error = 'Erreur lors du chargement des détails de la commande';
          this.loading = false;
        }
      });
  }

  loadPatientDetails(patientId: string) {
    this.loadingPatient = true;

    this.patientService.getPatientById(patientId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (patient) => {
          this.patient = patient;
          this.loadingPatient = false;

          // Load household details if patient has one
          if (patient.houseHoldId) {
            this.loadHouseholdDetails(patient.houseHoldId);
          }
        },
        error: (error) => {
          console.error('Error loading patient details:', error);
          this.loadingPatient = false;
        }
      });
  }

  loadHouseholdDetails(householdId: string) {
    this.houseHoldService.getHouseHoldById(householdId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (household) => {
          this.household = household;
        },
        error: (error) => {
          console.error('Error loading household details:', error);
        }
      });
  }

  loadProductDetails(productIds: string[]) {
    this.loadingProducts = true;

    // Create array of product fetch observables
    const productObservables = productIds.map(id =>
      this.productService.getProductById(id)
    );

    forkJoin(productObservables)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (responses) => {
          this.orderProducts = responses.map((response) => ({
            ...response.product,
            orderQuantity: this.order?.quantity || 1 // For now, use order quantity
          }));
          this.loadingProducts = false;
        },
        error: (error) => {
          console.error('Error loading product details:', error);
          this.loadingProducts = false;
        }
      });
  }

  goBack() {
    this.router.navigate(['/orders']);
  }

  // Mark order as delivered
  markAsDelivered() {
    if (!this.order || (this.order.status !== 'APPROVED' && this.order.status !== 'IN_TRANSIT')) {
      return;
    }

    this.orderService.markOrderAsDelivered(this.order.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('✅ Order marked as delivered successfully:', response);
          // Reload order details to reflect the status change
          this.loadOrderDetails();
        },
        error: (error) => {
          console.error('❌ Error marking order as delivered:', error);
          // You could add a toast notification here to show the error to the user
        }
      });
  }

  formatDate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  formatAmount(amount: number): string {
    return `${amount.toFixed(2)} €`;
  }

  getBadgeClass(status: string): string {
    switch (status) {
      case 'PENDING':
        return 'badge-pending';
      case 'APPROVED':
        return 'badge-approved';
      case 'IN_TRANSIT':
        return 'badge-in-transit';
      case 'DELIVERED':
        return 'badge-delivered';
      case 'CANCELLED':
        return 'badge-cancelled';
      default:
        return 'badge-default';
    }
  }

  getBadgeText(status: string): string {
    switch (status) {
      case 'PENDING':
        return 'En attente';
      case 'APPROVED':
        return 'Approuvée';
      case 'IN_TRANSIT':
        return 'En transit';
      case 'DELIVERED':
        return 'Livrée';
      case 'CANCELLED':
        return 'Annulée';
      default:
        return status;
    }
  }

  // Helper methods for patient information
  getPatientAddress(): string {
    if (!this.patient?.address || this.patient.address.length === 0) {
      return 'Adresse non disponible';
    }
    return this.patient.address[0].address || 'Adresse non disponible';
  }

  getHospitalizationStatus(): string {
    return this.patient?.hospitalizedPatient ? 'Oui' : 'Non';
  }

  getVacationStatus(): string {
    if (!this.patient?.vacationPeriod) {
      return 'Non';
    }
    const start = new Date(this.patient.vacationPeriod.start);
    const end = new Date(this.patient.vacationPeriod.end);
    return `Oui (${this.formatDate(start)} - ${this.formatDate(end)})`;
  }

  getHouseholdName(): string {
    return this.household?.name || 'Aucun foyer';
  }

  getPaymentTypeText(): string {
    if (!this.patient?.paymentType) {
      return 'Non spécifié';
    }

    // The enum values are already in French, so we can return them directly
    return this.patient.paymentType;
  }



  // Calculate total for display
  calculateTotal(): number {
    return this.orderProducts.reduce((total, product) => {
      return total + (product.price * product.orderQuantity);
    }, 0);
  }

  // Toggle narrow mode
  toggleNarrowMode(): void {
    this.isNarrowMode = !this.isNarrowMode;
  }

  // Open confirmation modal
  confirmOrder(order: Order): void {
    if (order.status !== 'PENDING') {
      return;
    }

    this.confirmQuantity = order.quantity || 1;
    this.confirmDeliveryNote = order.deliveryManNote || '';
    this.availableProductQuantity = 0;
    this.loadingProductInfo = true;
    this.productValidationError = '';
    this.showConfirmModal = true;

    // Load product stock information
    if (order.productIds && order.productIds.length > 0) {
      this.productService.getProductById(order.productIds[0])
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.success && response.product) {
              this.availableProductQuantity = response.product.quantity || 0;
              this.loadingProductInfo = false;
              this.validateQuantity();
            }
          },
          error: (error) => {
            console.error('❌ Error loading product info:', error);
            this.loadingProductInfo = false;
            this.productValidationError = 'Impossible de charger les informations du produit';
          }
        });
    } else {
      this.loadingProductInfo = false;
      this.productValidationError = 'Aucun produit trouvé dans la commande';
    }
  }

  // Close confirmation modal
  closeConfirmModal(): void {
    this.showConfirmModal = false;
    this.confirmQuantity = 1;
    this.confirmDeliveryNote = '';
    this.availableProductQuantity = 0;
    this.loadingProductInfo = false;
    this.productValidationError = '';
  }

  // Validate quantity
  validateQuantity(): void {
    this.productValidationError = '';

    if (this.confirmQuantity <= 0) {
      this.productValidationError = 'La quantité doit être supérieure à 0';
      return;
    }

    if (this.confirmQuantity > this.availableProductQuantity) {
      this.productValidationError = `Quantité disponible: ${this.availableProductQuantity}`;
      return;
    }
  }

  // Check if confirmation is valid
  isConfirmationValid(): boolean {
    return this.confirmQuantity > 0 &&
           this.confirmQuantity <= this.availableProductQuantity &&
           !this.loadingProductInfo &&
           !this.productValidationError;
  }

  // Submit pharmacist confirmation
  submitPharmacistConfirmation(): void {
    if (!this.order) {
      return;
    }

    const command: OrderPharmacistConfirmCommand = {
      patientId: this.order.patientId,
      quantity: this.confirmQuantity,
      deliveryManNote: this.confirmDeliveryNote || undefined
    };

    this.orderService.pharmacistConfirmOrder(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('✅ Order confirmed by pharmacist successfully:', response);
          this.closeConfirmModal();
          // Refresh order details to show updated status
          this.loadOrderDetails();
        },
        error: (error) => {
          console.error('❌ Error confirming order by pharmacist:', error);
          this.error = 'Erreur lors de la confirmation de la commande';
        }
      });
  }

  // Image navigation methods
  getProductImageUrl(product: Product): string {
    if (product.storagePath && product.storagePath.length > 0) {
      const currentIndex = this.productImageIndexes[product.id] || 0;
      const imageIndex = Math.min(currentIndex, product.storagePath.length - 1);
      return `${environment.fileBaseUrl}${product.storagePath[imageIndex]}`;
    }
    return 'assets/images/product-placeholder.svg';
  }

  hasMultipleImages(product: Product): boolean {
    return !!(product.storagePath && product.storagePath.length > 1);
  }

  getCurrentImageIndex(product: Product): number {
    return this.productImageIndexes[product.id] || 0;
  }

  canNavigatePrevious(product: Product): boolean {
    return this.getCurrentImageIndex(product) > 0;
  }

  canNavigateNext(product: Product): boolean {
    const currentIndex = this.getCurrentImageIndex(product);
    return !!(product.storagePath && currentIndex < product.storagePath.length - 1);
  }

  previousImage(product: Product, event?: Event): void {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    if (this.canNavigatePrevious(product)) {
      const currentIndex = this.getCurrentImageIndex(product);
      this.productImageIndexes[product.id] = currentIndex - 1;
    }
  }

  nextImage(product: Product, event?: Event): void {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    if (this.canNavigateNext(product)) {
      const currentIndex = this.getCurrentImageIndex(product);
      this.productImageIndexes[product.id] = currentIndex + 1;
    }
  }

  getImageCounter(product: Product): string {
    if (!this.hasMultipleImages(product)) return '';
    const current = this.getCurrentImageIndex(product) + 1;
    const total = product.storagePath?.length || 0;
    return `${current}/${total}`;
  }

  // Reset all image navigation indexes
  resetImageIndexes(): void {
    this.productImageIndexes = {};
  }
}

.order-details-container {
  padding: 1.5rem;
  background-color: #FFFFFF;
  min-height: 100vh;



  // Main content layout
  .details-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
    align-items: stretch; // Make both containers same height
    transition: all 0.3s ease;

    &.narrow-mode {
      grid-template-columns: 1fr 200px;
    }

    // First Container - Order Summary (Single Cohesive Container)
    .order-summary-container {
      background: #FFFFFF;
      border: 1px solid #C7DAEC;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      min-height: 100%; // Ensure it takes full height

      // Navigation Header
      .navigation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem 2rem 1rem 2rem;
        background: #F8F9FA;
        //border-bottom: 1px solid #C7DAEC;

        .nav-left {
          .back-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: none;
            border: none;
            color: #1061AC;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            padding: 0;
            transition: all 0.2s ease;

            &:hover {
              color: #0D4E8C;
            }

            .back-arrow {
              font-size: 1rem;
              font-weight: bold;
            }
          }
        }

        .nav-right {
          display: flex;
          gap: 1rem;
          align-items: center;

          .confirm-button {
            background: #57B6B1;
            color: #FFFFFF;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: #4A9B96;
              transform: translateY(-1px);
            }
          }

          .action-btn {
            background: #1061AC;
            color: #FFFFFF;
            border: none;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: #0d4f8c;
              transform: translateY(-1px);
            }

            &.primary {
              box-shadow: 0 2px 4px rgba(16, 97, 172, 0.2);

              &:hover {
                box-shadow: 0 4px 8px rgba(16, 97, 172, 0.3);
              }
            }
          }
        }
      }

      .page-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #163659;
        margin: 0;
        padding: 0 2rem 1.5rem 2rem;
        background: #F8F9FA;
      }



      // Prix Total Box
      .prix-total-box {
        padding: 2rem;
        background: #F6F8FA;
        border-bottom: 1px solid #C7DAEC;

        .price-row {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;

          .price-left {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;

            .price-label {
              font-size: 1.125rem;
              font-weight: 800;
              color: #163659;
              margin: 0;
            }

            .patient-address {
              display: flex;
              flex-direction: column;
              gap: 0.25rem;
              max-width: 250px; // Limit width to force wrapping

              .address-item {
                display: flex;
                align-items: flex-start;
                gap: 0.5rem;

                .address-icon {
                  width: 12px;
                  height: 12px;
                  color: #728A9B;
                  flex-shrink: 0;
                  margin-top: 0.125rem; // Align with first line of text
                }

                span {
                  font-size: 0.875rem;
                  color: #163659;
                  font-weight: 500;
                  line-height: 1.3;
                  word-wrap: break-word;
                  overflow-wrap: break-word;
                }
              }
            }
          }

          .price-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.75rem;

            .price-amount {
              font-size: 1.5rem;
              font-weight: 800;
              color: #1061AC;
              margin: 0;
            }

            .payment-type {
              font-size: 0.875rem;
              color: #8AA3D5;
              font-weight: 500;
              margin: 0;
            }
          }
        }
      }

      // Articles Section
      .articles-section {
        padding: 2rem;
        flex: 1; // Expand to fill remaining space

        .articles-title {
          font-size: 1.125rem;
          font-weight: 800;
          color: #163659;
          margin: 0 0 1.5rem 0;
        }

        .loading-products {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.75rem;
          padding: 2rem;
          color: #728A9B;

          .loading-spinner-small {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1061AC;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
        }

        .articles-table {
          width: 100%;

          table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #C7DAEC;
            border-radius: 8px;
            overflow: hidden;

            thead {
              th {
                padding: 1rem 0.75rem;
                text-align: left;
                font-weight: 800;
                color: #163659;
                background: #F8F9FA;
                border-bottom: 1px solid #C7DAEC;
                font-size: 0.875rem;

                &:first-child {
                  border-top-left-radius: 8px;
                }

                &:last-child {
                  border-top-right-radius: 8px;
                }
              }
            }

            tbody {
              tr {
                border-bottom: 1px solid #E0E0E0;

                &:hover {
                  background-color: #F9F9F9;
                }

                &:last-child {
                  border-bottom: none;
                }

                td {
                  padding: 1rem 0.75rem;
                  vertical-align: middle;
                  font-size: 0.875rem;

                  &.product-image-cell {
                    width: 60px;

                    .table-image-container {
                      position: relative;
                      width: 50px;
                      height: 50px;

                      .product-image {
                        width: 50px;
                        height: 50px;
                        object-fit: cover;
                        border-radius: 6px;
                        border: 1px solid #E0E0E0;
                      }

                      // Navigation arrows for table view
                      .table-nav-arrow {
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        background: rgba(0, 0, 0, 0.7);
                        color: white;
                        border: none;
                        border-radius: 50%;
                        width: 20px;
                        height: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        opacity: 0;
                        transition: opacity 0.2s ease;
                        z-index: 2;

                        &:hover {
                          background: rgba(0, 0, 0, 0.9);
                        }

                        &:disabled {
                          opacity: 0.3;
                          cursor: not-allowed;
                        }

                        svg {
                          width: 12px;
                          height: 12px;
                        }

                        &.table-nav-arrow-left {
                          left: 2px;
                        }

                        &.table-nav-arrow-right {
                          right: 2px;
                        }
                      }

                      // Image counter for table view
                      .table-image-counter {
                        position: absolute;
                        bottom: 2px;
                        right: 2px;
                        background: rgba(0, 0, 0, 0.7);
                        color: white;
                        font-size: 0.6rem;
                        padding: 1px 4px;
                        border-radius: 2px;
                        opacity: 0;
                        transition: opacity 0.2s ease;
                      }

                      &:hover {
                        .table-nav-arrow,
                        .table-image-counter {
                          opacity: 1;
                        }
                      }
                    }
                  }

                  &.product-name {
                    font-weight: 600;
                    color: #163659;
                  }

                  &.product-quantity {
                    text-align: center;
                    font-weight: 500;
                    color: #333;
                  }

                  &.product-price {
                    font-weight: 600;
                    color: #1061AC;
                    text-align: right;
                  }
                }
              }
            }
          }
        }

        .no-products {
          text-align: center;
          padding: 2rem;
          color: #728A9B;
          font-style: italic;
        }
      }
    }

    // Second Container - Patient Information
    .patient-info-container {
      .patient-card {
        background: #FFFFFF;
        border: 1px solid #C7DAEC;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        height: 100%; // Match height of left container
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;

        .card-header {
          background: #FFFFFF;
          padding: 1rem 2rem;
          border-bottom: 1px solid #C7DAEC;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .card-title {
            font-size: 1.125rem;
            font-weight: 800;
            color: #1061AC;
            margin: 0;
          }

          .narrow-toggle {
            background: none;
            border: none;
            padding: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              opacity: 0.7;
            }

            .narrow-icon {
              width: 25px;
              height: 25px;
            }
          }
        }

        .card-content {
          padding: 2rem;
          flex: 1; // Fill remaining space

          .loading-patient {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            padding: 2rem;
            color: #728A9B;

            .loading-spinner-small {
              width: 20px;
              height: 20px;
              border: 2px solid #f3f3f3;
              border-top: 2px solid #1061AC;
              border-radius: 50%;
              animation: spin 1s linear infinite;
            }
          }

          .patient-info {
            display: flex;
            flex-direction: column;

            // Identity Section
            .identity-section {
              display: flex;
              flex-direction: column;
              align-items: center;
              text-align: center;
              margin-bottom: 2rem;

              .patient-avatar {
                margin-bottom: 1rem;

                .initials-badge {
                  width: 80px;
                  height: 80px;
                  border-radius: 50%;
                  background-color: #E3F2FD;
                  color: #1976D2;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-weight: 600;
                  font-size: 1.5rem;
                  flex-shrink: 0;
                }
              }

              .patient-name {
                font-size: 1.125rem;
                font-weight: 800;
                color: #163659;
                margin: 0 0 1rem 0;
              }

              .patient-separator {
                width: 100%;
                height: 1px;
                background-color: #6D9ECC;
                margin: 0;
              }
            }

            // Patient Data Grid
            .patient-data-grid {
              display: flex;
              flex-direction: column;
              gap: 2rem;

              .data-row {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 2rem;

                .data-column {
                  display: flex;
                  flex-direction: column;
                  gap: 0.5rem;

                  .data-label {
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: #728A9B;
                  }

                  .data-value {
                    font-size: 0.75rem;
                    color: #163659;
                    font-weight: 500;
                    word-wrap: break-word;

                    &.address-value {
                      .address-item {
                        display: flex;
                        align-items: flex-start;
                        gap: 0.5rem;
                        margin-bottom: 0.25rem;

                        &:last-child {
                          margin-bottom: 0;
                        }

                        .address-icon {
                          width: 12px;
                          height: 12px;
                          color: #728A9B;
                          flex-shrink: 0;
                          margin-top: 0.125rem; // Align with first line of text
                        }

                        span {
                          font-size: 0.75rem;
                          color: #163659;
                          font-weight: 500;
                          line-height: 1.2;
                        }
                      }
                    }
                  }
                }
              }
            }
          }

          .no-patient {
            text-align: center;
            padding: 2rem;
            color: #728A9B;
            font-style: italic;
          }
        }
      }
    }

    // Narrow mode adjustments
    &.narrow-mode {
      // Ensure both containers maintain equal height in narrow mode
      align-items: stretch;

      .order-summary-container {
        display: flex;
        flex-direction: column;
        min-height: 100%;

        .articles-section {
          flex: 1; // Fill remaining space to match patient container height
        }
      }

      .patient-info-container {
        .patient-card {
          height: 100%;

          .card-header {
            padding: 1rem 1.5rem;

            .card-title {
              font-size: 1rem;
            }
          }

          .card-content {
            padding: 1.5rem;
            flex: 1;

            .patient-info {
              .identity-section {
                margin-bottom: 1.5rem;

                .patient-avatar {
                  margin-bottom: 0.75rem;

                  .initials-badge {
                    width: 60px;
                    height: 60px;
                    font-size: 1.25rem;
                  }
                }

                .patient-name {
                  font-size: 1rem;
                  margin-bottom: 0.75rem;
                }
              }

              .patient-data-grid {
                gap: 1.5rem;

                .data-row {
                  grid-template-columns: 1fr;
                  gap: 1rem;

                  .data-column {
                    .data-label {
                      font-size: 0.75rem;
                    }

                    .data-value {
                      font-size: 0.625rem;
                      color: #163659;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #728A9B;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #1061AC;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #D32F2F;

    .retry-button {
      margin-top: 1rem;
      padding: 0.5rem 1rem;
      background: #1061AC;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #0D4E8C;
      }
    }
  }

  .details-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
    align-items: start;

    // Left Container - Order Summary
    .left-container {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      .total-section {
        background: #FFFFFF;
        border: 1px solid #C7DAEC;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        .total-header {
          margin-bottom: 1rem;

          .total-title {
            font-size: 1.25rem;
            font-weight: 800;
            color: #163659;
            margin: 0 0 0.25rem 0;
          }

          .total-subtitle {
            font-size: 0.875rem;
            color: #728A9B;
            margin: 0;
            font-style: italic;
          }
        }

        .total-amount {
          font-size: 2rem;
          font-weight: 800;
          color: #1061AC;
          text-align: center;
          padding: 1rem;
          background: #F5FFF9;
          border-radius: 8px;
          border: 1px solid #C7DAEC;
        }
      }

      .products-section {
        background: #FFFFFF;
        border: 1px solid #C7DAEC;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        .section-title {
          font-size: 1.125rem;
          font-weight: 800;
          color: #163659;
          margin: 0 0 1.5rem 0;
        }

        .loading-products {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.75rem;
          padding: 2rem;
          color: #728A9B;

          .loading-spinner-small {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1061AC;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
        }

        .products-table {
          width: 100%;

          table {
            width: 100%;
            border-collapse: collapse;

            thead {
              th {
                padding: 1rem 0.75rem;
                text-align: left;
                font-weight: 600;
                color: #163659;
                border-bottom: 2px solid #C7DAEC;
                font-size: 0.875rem;
                background: #F8F9FA;

                &:first-child {
                  border-top-left-radius: 8px;
                }

                &:last-child {
                  border-top-right-radius: 8px;
                }
              }
            }

            tbody {
              tr {
                border-bottom: 1px solid #E0E0E0;

                &:hover {
                  background-color: #F9F9F9;
                }

                td {
                  padding: 1rem 0.75rem;
                  vertical-align: middle;
                  font-size: 0.875rem;

                  &.product-image-cell {
                    width: 60px;

                    .product-image {
                      width: 50px;
                      height: 50px;
                      object-fit: cover;
                      border-radius: 6px;
                      border: 1px solid #E0E0E0;
                    }

                    .product-placeholder {
                      width: 50px;
                      height: 50px;
                      background: #E6EDF5;
                      border: 1px solid #6D9ECC;
                      border-radius: 6px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      color: #728A9B;

                      svg {
                        width: 24px;
                        height: 24px;
                      }
                    }
                  }

                  &.product-name {
                    font-weight: 600;
                    color: #163659;
                  }

                  &.product-quantity {
                    text-align: center;
                    font-weight: 500;
                    color: #333;
                  }

                  &.product-price {
                    font-weight: 600;
                    color: #1061AC;
                    text-align: right;
                  }
                }
              }
            }
          }
        }

        .no-products {
          text-align: center;
          padding: 2rem;
          color: #728A9B;
          font-style: italic;
        }
      }
    }

    // Right Container - Patient Information
    .right-container {
      .patient-card {
        background: #FFFFFF;
        border: 1px solid #C7DAEC;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        height: fit-content;

        .card-header {
          background: #F8F9FA;
          padding: 1rem 1.5rem;
          border-bottom: 1px solid #C7DAEC;

          .card-title {
            font-size: 1.125rem;
            font-weight: 800;
            color: #163659;
            margin: 0;
          }
        }

        .card-content {
          padding: 1.5rem;

          .loading-patient {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            padding: 2rem;
            color: #728A9B;

            .loading-spinner-small {
              width: 20px;
              height: 20px;
              border: 2px solid #f3f3f3;
              border-top: 2px solid #1061AC;
              border-radius: 50%;
              animation: spin 1s linear infinite;
            }
          }

          .patient-info {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;

            .patient-avatar {
              display: flex;
              justify-content: center;
              margin-bottom: 0.5rem;

              .initials-badge {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                background-color: #E3F2FD;
                color: #1976D2;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                font-size: 1.5rem;
                flex-shrink: 0;
              }
            }

            .patient-details {
              display: flex;
              flex-direction: column;
              gap: 1rem;

              .detail-item {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;

                .detail-label {
                  font-size: 0.875rem;
                  font-weight: 500;
                  color: #728A9B;
                }

                .detail-value {
                  font-size: 0.875rem;
                  color: #333;
                  font-weight: 500;
                  word-wrap: break-word;
                }
              }
            }
          }

          .no-patient {
            text-align: center;
            padding: 2rem;
            color: #728A9B;
            font-style: italic;
          }
        }
      }
    }

    .details-card {
      background: #FFFFFF;
      border: 1px solid #C7DAEC;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      overflow: hidden;

      .card-header {
        background: #F8F9FA;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #C7DAEC;

        .card-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: #163659;
          margin: 0;
        }
      }

      .card-content {
        padding: 1.5rem;

        .details-grid {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .detail-row {
            display: flex;
            gap: 2rem;

            &.full-width {
              .detail-item {
                flex: 1;
              }
            }

            .detail-item {
              display: flex;
              flex-direction: column;
              gap: 0.25rem;
              flex: 1;

              .detail-label {
                font-size: 0.875rem;
                font-weight: 500;
                color: #728A9B;
              }

              .detail-value {
                font-size: 0.875rem;
                color: #333;
                font-weight: 500;

                &.amount {
                  color: #1061AC;
                  font-weight: 600;
                  font-size: 1rem;
                }
              }

              .badge {
                padding: 0.375rem 0.75rem;
                border-radius: 20px;
                font-size: 0.75rem;
                font-weight: 500;
                text-transform: uppercase;
                width: fit-content;

                &.badge-pending {
                  background-color: #FFF3E0;
                  color: #F57C00;
                }

                &.badge-approved {
                  background-color: #E8F5E8;
                  color: #2E7D32;
                }

                &.badge-in-transit {
                  background-color: #E3F2FD;
                  color: #1976D2;
                }

                &.badge-delivered {
                  background-color: #E8F5E8;
                  color: #2E7D32;
                }

                &.badge-cancelled {
                  background-color: #FFCDD2;
                  color: #D32F2F;
                }

                &.badge-default {
                  background-color: #F5F5F5;
                  color: #757575;
                }
              }
            }
          }
        }

        .patient-info {
          display: flex;
          align-items: flex-start;
          gap: 1rem;

          .patient-avatar {
            .initials-badge {
              width: 60px;
              height: 60px;
              border-radius: 50%;
              background-color: #E3F2FD;
              color: #1976D2;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: 600;
              font-size: 1.25rem;
              flex-shrink: 0;
            }
          }

          .patient-details {
            flex: 1;

            .detail-row {
              display: flex;
              gap: 2rem;
              margin-bottom: 1rem;

              &:last-child {
                margin-bottom: 0;
              }

              .detail-item {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
                flex: 1;

                .detail-label {
                  font-size: 0.875rem;
                  font-weight: 500;
                  color: #728A9B;
                }

                .detail-value {
                  font-size: 0.875rem;
                  color: #333;
                  font-weight: 500;
                }
              }
            }
          }
        }

        .products-info {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;

            .detail-label {
              font-size: 0.875rem;
              font-weight: 500;
              color: #728A9B;
            }

            .detail-value {
              font-size: 0.875rem;
              color: #333;
              font-weight: 500;
            }
          }

          .product-ids {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            .detail-label {
              font-size: 0.875rem;
              font-weight: 500;
              color: #728A9B;
            }

            .product-list {
              display: flex;
              flex-wrap: wrap;
              gap: 0.5rem;

              .product-id-badge {
                background: #E3F2FD;
                color: #1976D2;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.75rem;
                font-weight: 500;
                font-family: monospace;
              }
            }
          }
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 1024px) {
    .details-content {
      grid-template-columns: 1fr;
      gap: 1.5rem;

      &.narrow-mode {
        grid-template-columns: 1fr;
      }

      .patient-info-container {
        order: -1; // Move patient info to top on tablet/mobile
      }
    }
  }

  @media (max-width: 768px) {
    padding: 1rem;

    .details-content {
      gap: 1rem;

      .order-summary-container {
        .navigation-header {
          padding: 1rem 1.5rem 0.75rem 1.5rem;
          flex-direction: column;
          align-items: stretch;
          gap: 1rem;

          .nav-right {
            align-self: center;
            flex-direction: column;
            gap: 0.5rem;

            .confirm-button {
              padding: 0.5rem 1rem;
              font-size: 0.75rem;
            }

            .action-btn {
              padding: 0.5rem 1rem;
              font-size: 0.75rem;
            }
          }
        }

        .page-title {
          padding: 0 1.5rem 1rem 1.5rem;
          font-size: 0.75rem;
        }

        .prix-total-box {
          padding: 1.5rem;

          .price-row {
            flex-direction: column;
            gap: 1rem;

            .price-left {
              gap: 0.5rem;

              .price-label {
                font-size: 1rem;
              }

              .patient-address {
                span {
                  font-size: 0.75rem;
                }
              }
            }

            .price-right {
              align-items: flex-start;
              gap: 0.5rem;

              .price-amount {
                font-size: 1.25rem;
              }

              .payment-type {
                font-size: 0.75rem;
              }
            }
          }
        }

        .articles-section {
          padding: 1.5rem;

          .articles-title {
            font-size: 1rem;
          }

          .articles-table {
            table {
              thead th {
                padding: 0.75rem 0.5rem;
                font-size: 0.75rem;
              }

              tbody td {
                padding: 0.75rem 0.5rem;
                font-size: 0.75rem;

                &.product-image-cell {
                  width: 40px;

                  .product-image,
                  .product-placeholder {
                    width: 35px;
                    height: 35px;
                  }

                  .product-placeholder svg {
                    width: 18px;
                    height: 18px;
                  }
                }
              }
            }
          }
        }
      }

      .patient-info-container {
        .patient-card {
          .card-header {
            padding: 0.75rem 1.5rem;

            .card-title {
              font-size: 1rem;
            }

            .narrow-toggle {
              display: none; // Hide narrow toggle on mobile
            }
          }

          .card-content {
            padding: 1.5rem;

            .patient-info {
              .identity-section {
                margin-bottom: 1.5rem;

                .patient-avatar {
                  margin-bottom: 0.75rem;

                  .initials-badge {
                    width: 60px;
                    height: 60px;
                    font-size: 1.25rem;
                  }
                }

                .patient-name {
                  font-size: 1rem;
                  margin-bottom: 0.75rem;
                }
              }

              .patient-data-grid {
                gap: 1.5rem;

                .data-row {
                  grid-template-columns: 1fr;
                  gap: 1rem;

                  .data-column {
                    .data-label {
                      font-size: 0.75rem;
                    }

                    .data-value {
                      font-size: 0.625rem;
                      color: #163659;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // Confirmation Modal Styles
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 1rem;

    .modal-container {
      background: #FFFFFF;
      border: 1px solid #C7DAEC;
      border-radius: 10px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      max-width: 500px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid #C7DAEC;

        .modal-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: #163659;
          margin: 0;
        }

        .close-button {
          background: none;
          border: none;
          font-size: 1.5rem;
          color: #728A9B;
          cursor: pointer;
          padding: 0;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: #163659;
          }

          &::before {
            content: '×';
          }
        }
      }

      .modal-content {
        padding: 1.5rem;

        .order-info {
          margin-bottom: 1.5rem;
          padding: 1rem;
          background: #F8F9FA;
          border-radius: 6px;

          h3 {
            font-size: 1rem;
            font-weight: 600;
            color: #163659;
            margin: 0 0 0.5rem 0;
          }

          .household-info {
            font-size: 0.875rem;
            color: #728A9B;
            margin: 0;
          }
        }

        .product-loading {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem;
          background: #F8F9FA;
          border-radius: 6px;
          margin-bottom: 1rem;

          .loading-spinner-small {
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1061AC;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }

          span {
            font-size: 0.875rem;
            color: #728A9B;
          }
        }

        .product-stock-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem;
          background: #E8F5E8;
          border-radius: 6px;
          margin-bottom: 1rem;

          .stock-label {
            font-size: 0.875rem;
            color: #2E7D32;
            font-weight: 500;
          }

          .stock-value {
            font-size: 0.875rem;
            color: #2E7D32;
            font-weight: 600;
          }
        }

        .form-group {
          margin-bottom: 1rem;

          .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #163659;
            margin-bottom: 0.5rem;
          }

          .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #8AA3D5;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;

            &:focus {
              outline: none;
              border-color: #1061AC;
            }

            &.error {
              border-color: #F80D38;
            }
          }

          .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #8AA3D5;
            border-radius: 6px;
            font-size: 0.875rem;
            resize: vertical;
            min-height: 80px;
            transition: border-color 0.2s ease;

            &:focus {
              outline: none;
              border-color: #1061AC;
            }
          }

          .error-message {
            font-size: 0.75rem;
            color: #F80D38;
            margin-top: 0.25rem;
          }
        }
      }

      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        padding: 1.5rem;
        border-top: 1px solid #C7DAEC;

        .btn-cancel {
          background: transparent;
          color: #163659;
          border: 1px solid #C7DAEC;
          border-radius: 6px;
          padding: 0.75rem 1.5rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #F8F9FA;
          }
        }

        .btn-confirm {
          background: #57B6B1;
          color: #FFFFFF;
          border: none;
          border-radius: 6px;
          padding: 0.75rem 1.5rem;
          font-size: 0.875rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover:not(:disabled) {
            background: #4A9B96;
          }

          &:disabled {
            background: #C7DAEC;
            cursor: not-allowed;
          }
        }
      }
    }
  }

  // Responsive modal
  @media (max-width: 768px) {
    .modal-overlay {
      padding: 0.5rem;

      .modal-container {
        max-width: 100%;
        max-height: 95vh;

        .modal-header,
        .modal-content,
        .modal-footer {
          padding: 1rem;
        }

        .modal-footer {
          flex-direction: column;

          .btn-cancel,
          .btn-confirm {
            width: 100%;
            justify-content: center;
          }
        }
      }
    }
  }
}
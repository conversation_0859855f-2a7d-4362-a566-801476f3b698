.orders-container {
  padding: 1.5rem;
  background-color: #FFFFFF;
  min-height: 100vh;
  overflow: visible !important; // Ensure dropdowns can overflow

  // 🔷 New Header Layout matching mockup
  .orders-header {
    margin-bottom: 2rem;
    overflow: visible !important; // Ensure dropdowns can overflow

    // Top Section: Title (left) and Icons (max right)
    .title-and-icons {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #163659;
        margin: 0;
      }

      .header-icons {
        display: flex;
        gap: 0.5rem;

        .icon-button {
          width: 40px;
          height: 40px;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          background: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f0f0f0;
            border-color: #ccc;
          }

          .icon {
            width: 18px;
            height: 18px;
            color: #666;
          }

          &:hover .icon {
            color: #1061AC;
          }
        }
      }
    }

    // Filter Section: Background #ECEFF9
    .filter-container {
      background-color: #ECEFF9;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 1rem;
      overflow: visible !important; // Ensure dropdowns can overflow

      .filter-row {
        display: flex;
        gap: 2rem;
        align-items: end;
        overflow: visible !important; // Ensure dropdowns can overflow

        .filter-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          overflow: visible !important; // Ensure dropdowns can overflow

          .filter-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #163659;
          }

          // ✅ Custom dropdown with white background and square checkboxes
          .custom-dropdown {
            position: relative;
            min-width: 150px;

            .dropdown-toggle {
              width: 100%;
              padding: 0.5rem 0.75rem;
              border: 1px solid #C7DAEC;
              border-radius: 6px;
              background: white;
              font-size: 0.875rem;
              color: #163659;
              cursor: pointer;
              display: flex;
              justify-content: space-between;
              align-items: center;
              text-align: left;

              &:focus {
                outline: none;
                border-color: #1061AC;
                box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
              }
            }

            // ✅ White background dropdown menu
            .dropdown-menu {
              position: absolute;
              top: 100%;
              left: 0;
              right: 0;
              background: #FFFFFF !important;
              border: 2px solid #C7DAEC !important;
              border-radius: 8px;
              box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25) !important;
              z-index: 99999 !important;
              margin-top: 4px;
              min-width: 200px !important;
              max-height: 350px !important;
              overflow-y: auto;
              display: block !important;
              visibility: visible !important;

              .dropdown-item {
                display: flex !important;
                align-items: center;
                padding: 1rem 1.25rem !important;
                cursor: pointer;
                transition: all 0.2s ease;
                gap: 0.75rem;
                border-bottom: 1px solid #e0e0e0 !important;
                min-height: 48px !important;
                background-color: #FFFFFF !important;

                &:hover {
                  background-color: #F0F8FF !important;
                  border-left: 3px solid #1061AC !important;
                  padding-left: 1rem !important;
                }

                &:first-child {
                  border-radius: 8px 8px 0 0;
                }

                &:last-child {
                  border-radius: 0 0 8px 8px;
                  border-bottom: none !important;
                }

                // ✅ Custom rectangular checkbox - smaller and wider than tall
                .checkbox-square {
                  width: 18px !important;
                  height: 14px !important;
                  border: 2px solid #1061AC !important;
                  border-radius: 3px;
                  background: white !important;
                  position: relative;
                  flex-shrink: 0;
                  transition: all 0.2s ease;
                  display: inline-block !important;
                  margin-right: 0.75rem;

                  // ✅ Checkmark when selected - adjusted for smaller rectangular checkbox
                  &::after {
                    content: '';
                    position: absolute;
                    top: 1px;
                    left: 4px;
                    width: 4px;
                    height: 8px;
                    border: solid white;
                    border-width: 0 2px 2px 0;
                    transform: rotate(45deg);
                    opacity: 0;
                    transition: opacity 0.2s ease;
                  }

                  // ✅ When checked - Make it very visible
                  &.checked {
                    background-color: #1061AC !important;
                    border-color: #1061AC !important;
                    box-shadow: 0 0 0 1px #1061AC !important;

                    &::after {
                      opacity: 1 !important;
                    }
                  }
                }

                .option-text {
                  font-size: 1rem !important;
                  color: #000000 !important;
                  font-weight: 500 !important;
                  flex: 1;
                  line-height: 1.4;
                }
              }
            }
          }
        }
      }
    }

    // Second Row: Calendar (right) - matching prescription component
    .action-row {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 2rem;

      .calendar-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .nav-arrow {
          width: 32px;
          height: 32px;
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          background: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f0f0f0;
            border-color: #ccc;
          }

          svg {
            width: 16px;
            height: 16px;
            color: #666;
          }
        }

        .date-selector {
          display: flex;
          gap: 0.25rem;
          padding: 0.25rem;
          background: white;
          border-radius: 8px;
          border: 1px solid #e0e0e0;

          .date-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.5rem 0.75rem;
            border: none;
            border-radius: 6px;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 50px;

            &:hover {
              background-color: #f0f0f0;
            }

            &.active {
              background-color: #1061AC;
              color: white;
            }

            .day-name {
              font-size: 0.75rem;
              font-weight: 500;
              margin-bottom: 0.125rem;
            }

            .day-number {
              font-size: 0.875rem;
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  // Loading State
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #728A9B;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #1061AC;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }

  // Error State
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #D32F2F;

    .retry-button {
      margin-top: 1rem;
      padding: 0.5rem 1rem;
      background: #1061AC;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #0D4E8C;
      }
    }
  }

  // Empty State
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    color: #728A9B;

    .empty-icon {
      margin-bottom: 1.5rem;

      .empty-svg {
        width: 64px;
        height: 64px;
        color: #C7DAEC;
      }
    }

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #163659;
      margin: 0 0 0.5rem 0;
    }

    p {
      font-size: 0.875rem;
      margin: 0;
      max-width: 400px;
    }
  }

  // Table View
  .table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .orders-table {
      width: 100%;
      border-collapse: collapse;

      thead {
        background-color: #f8f9fa;

        th {
          padding: 1rem;
          text-align: left;
          font-weight: 600;
          color: #333;
          border-bottom: 1px solid #e0e0e0;
          font-size: 0.875rem;
        }
      }

      tbody {
        tr {
          border-bottom: 1px solid #f0f0f0;

          &:hover {
            background-color: #f9f9f9;
          }

          td {
            padding: 1rem;
            vertical-align: middle;
            font-size: 0.875rem;

            &.patient-cell {
              .patient-info {
                display: flex;
                align-items: center;
                gap: 0.75rem;

                .initials-badge {
                  width: 40px;
                  height: 40px;
                  border-radius: 50%;
                  background-color: #E3F2FD;
                  color: #1976D2;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-weight: 600;
                  font-size: 0.875rem;
                }
              }
            }

            &.amount-cell {
              font-weight: 600;
              color: #1061AC;
            }

            &.confirmation-cell {
              .confirm-button {
                background: #57B6B1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 0.5rem 1rem;
                font-size: 0.75rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                  background: #4A9B96;
                  transform: translateY(-1px);
                }
              }

              .confirmed-text {
                color: #2E7D32;
                font-weight: 500;
                font-size: 0.75rem;
              }
            }

            .empty-state {
              background-color: #F5FFF9;
              color: #4CAF50;
              padding: 0.25rem 0.5rem;
              border-radius: 4px;
              font-size: 0.75rem;
              font-weight: 500;
            }

            .badge {
              padding: 0.375rem 0.75rem;
              border-radius: 20px;
              font-size: 0.75rem;
              font-weight: 500;
              text-transform: uppercase;

              &.badge-pending {
                background-color: #FFF3E0;
                color: #F57C00;
              }

              &.badge-approved {
                background-color: #E8F5E8;
                color: #2E7D32;
              }

              &.badge-in-transit {
                background-color: #E3F2FD;
                color: #1976D2;
              }

              &.badge-delivered {
                background-color: #E8F5E8;
                color: #2E7D32;
              }

              &.badge-cancelled {
                background-color: #FFCDD2;
                color: #D32F2F;
              }

              &.badge-default {
                background-color: #F5F5F5;
                color: #757575;
              }
            }

            &.actions-cell {
              button {
                background: none;
                border: none;
                padding: 0.5rem;
                margin: 0 0.25rem;
                cursor: pointer;
                border-radius: 4px;
                display: inline-flex;
                align-items: center;
                justify-content: center;

                &:hover {
                  background-color: #f0f0f0;
                }

                .eye-icon {
                  width: 18px;
                  height: 18px;
                }

                svg {
                  width: 18px;
                  height: 18px;
                  color: #666;
                }
              }
            }
          }
        }
      }
    }
  }

  // Cards View
  .cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 0;
  }

  .order-card {
    background: #FFFFFF;
    border: 1px solid #C7DAEC;
    border-radius: 10px;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    position: relative;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;

      .patient-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;

        .initials-badge {
          width: 45px;
          height: 45px;
          border-radius: 50%;
          background-color: #E3F2FD;
          color: #1976D2;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 0.875rem;
          flex-shrink: 0;
        }

        .patient-details {
          flex: 1;
          display: flex;
          flex-direction: column;

          .patient-name {
            font-size: 1rem;
            font-weight: 700;
            color: #333;
            margin: 0 0 0.25rem 0;
            line-height: 1.2;
          }

          .household-name {
            font-size: 0.875rem;
            color: #666;
            margin: 0 0 0.5rem 0;
            font-weight: 400;

            &.empty {
              color: #999;
              font-style: italic;
            }
          }

          .household-separator {
            width: 100%;
            height: 1px;
            background-color: #E0E0E0;
            margin: 0;
          }
        }
      }

      .card-menu {
        position: relative;

        .menu-button {
          background: transparent;
          border: none;
          padding: 0.5rem;
          cursor: pointer;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &:hover {
            background: #f0f0f0;
          }

          .menu-icon {
            width: 18px;
            height: 18px;
            color: #666;
            transform: rotate(90deg);
          }
        }

        .menu-dropdown {
          position: absolute;
          top: 100%;
          right: 0;
          background: white;
          border: 1px solid #C7DAEC;
          border-radius: 6px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          z-index: 1000;
          min-width: 140px;
          overflow: hidden;

          .menu-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            width: 100%;
            padding: 0.75rem 1rem;
            background: none;
            border: none;
            text-align: left;
            font-size: 0.875rem;
            color: #333;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
              background-color: #f8f9fa;
            }

            .menu-item-icon {
              width: 16px;
              height: 16px;
              color: #666;
            }
          }
        }
      }
    }

    .card-content {
      .order-info {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 1rem;

        .info-row {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          .label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #728A9B;
            flex-shrink: 0;
          }

          .value {
            font-size: 0.875rem;
            color: #333;
            font-weight: 500;

            &.amount {
              color: #1061AC;
              font-weight: 600;
            }
          }
        }
      }

      .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid #f0f0f0;

        .badge {
          padding: 0.375rem 0.75rem;
          border-radius: 20px;
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: uppercase;

          &.badge-pending {
            background-color: #FFF3E0;
            color: #F57C00;
          }

          &.badge-approved {
            background-color: #E8F5E8;
            color: #2E7D32;
          }

          &.badge-in-transit {
            background-color: #E3F2FD;
            color: #1976D2;
          }

          &.badge-delivered {
            background-color: #E8F5E8;
            color: #2E7D32;
          }

          &.badge-cancelled {
            background-color: #FFCDD2;
            color: #D32F2F;
          }

          &.badge-default {
            background-color: #F5F5F5;
            color: #757575;
          }
        }

        .confirm-button-small {
          background: #57B6B1;
          color: white;
          border: none;
          border-radius: 6px;
          padding: 0.375rem 0.75rem;
          font-size: 0.75rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #4A9B96;
            transform: translateY(-1px);
          }
        }
      }
    }
  }

  // Pagination Styles - matching prescription component
  .pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 0;
    margin-top: 1.5rem;
    border-top: 1px solid #E8EDF5;
    gap: 1rem;
    flex-wrap: wrap;

    .items-per-page {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .pagination-label {
        font-size: 0.875rem;
        color: #728A9B;
        font-weight: 500;
      }

      .page-size-selector {
        padding: 0.375rem 0.75rem;
        border: 1px solid #C7DAEC;
        border-radius: 6px;
        background: white;
        color: #163659;
        font-size: 0.875rem;
        cursor: pointer;
        min-width: 60px;

        &:focus {
          outline: none;
          border-color: #1061AC;
          box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
        }
      }
    }

    .pagination-info {
      flex: 1;
      text-align: center;

      .pagination-text {
        font-size: 0.875rem;
        color: #728A9B;
        font-weight: 500;
      }
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: 0.25rem;

      .pagination-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border: 1px solid #C7DAEC;
        background: white;
        color: #163659;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.875rem;
        font-weight: 500;

        &:hover:not(:disabled) {
          background: #F5F7FA;
          border-color: #1061AC;
          color: #1061AC;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          background: #F8F9FA;
        }

        &.active {
          background: #1061AC;
          border-color: #1061AC;
          color: white;

          &:hover {
            background: #0D4E8C;
            border-color: #0D4E8C;
          }
        }

        &.page-number {
          min-width: 36px;
        }

        svg {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    padding: 1rem;

    .orders-header {
      .title-and-icons {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;

        .header-icons {
          align-self: flex-end;
        }
      }

      .filter-container .filter-row {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;

        .filter-group .custom-dropdown {
          min-width: 100%;
        }
      }

      .action-row {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;

        .calendar-container {
          justify-content: center;

          .date-selector {
            flex-wrap: wrap;
            justify-content: center;
          }
        }
      }
    }

    .table-container {
      overflow-x: auto;

      .orders-table {
        min-width: 800px;
      }
    }

    .cards-container {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1rem;

      .order-card {
        padding: 1rem;

        .card-header {
          .patient-info {
            .initials-badge {
              width: 40px;
              height: 40px;
              font-size: 0.75rem;
            }

            .patient-details {
              .patient-name {
                font-size: 0.875rem;
                margin: 0 0 0.2rem 0;
              }

              .household-name {
                font-size: 0.75rem;
                margin: 0 0 0.375rem 0;
              }
            }
          }
        }
      }
    }

    .pagination-container {
      flex-direction: column;
      gap: 1rem;
      align-items: center;

      .items-per-page {
        order: 1;
      }

      .pagination-info {
        order: 2;
        text-align: center;
      }

      .pagination-controls {
        order: 3;
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.25rem;
      }
    }
  }

  // Confirmation Modal Styles
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 1rem;

    .modal-container {
      background: #FFFFFF;
      border: 1px solid #C7DAEC;
      border-radius: 10px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      max-width: 500px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid #C7DAEC;

        .modal-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: #163659;
          margin: 0;
        }

        // Close Button (matching other modals)
        .close-button {
          width: 24px;
          height: 24px;
          border: 2px solid #57B6B1;
          border-radius: 50%;
          opacity: 1;
          background: transparent;
          cursor: pointer;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &:hover {
            background-color: #57B6B1;
          }

          &::before,
          &::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 2px;
            background-color: #57B6B1;
            transition: background-color 0.2s ease;
          }

          &:hover::before,
          &:hover::after {
            background-color: white;
          }

          &::before {
            transform: rotate(45deg);
          }

          &::after {
            transform: rotate(-45deg);
          }
        }
      }

      .modal-content {
        padding: 1.5rem;

        .order-info {
          margin-bottom: 1.5rem;
          padding: 1rem;
          background: #F8F9FA;
          border-radius: 6px;

          h3 {
            font-size: 1rem;
            font-weight: 600;
            color: #163659;
            margin: 0 0 0.5rem 0;
          }

          .household-info {
            font-size: 0.875rem;
            color: #728A9B;
            margin: 0;
          }
        }

        .product-loading {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem;
          background: #F8F9FA;
          border-radius: 6px;
          margin-bottom: 1rem;

          .loading-spinner-small {
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1061AC;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }

          span {
            font-size: 0.875rem;
            color: #728A9B;
          }
        }

        .product-stock-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem;
          background: #E8F5E8;
          border: 1px solid #4CAF50;
          border-radius: 6px;
          margin-bottom: 1rem;

          .stock-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #2E7D32;
          }

          .stock-value {
            font-size: 0.875rem;
            font-weight: 600;
            color: #2E7D32;
          }
        }

        .form-group {
          margin-bottom: 1.5rem;

          .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #163659;
            margin-bottom: 0.5rem;
          }

          .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #8AA3D5;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: all 0.2s ease;

            &:focus {
              outline: none;
              border-color: #1061AC;
              box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
            }

            &.error {
              border-color: #F80D38;
              background-color: #FFF5F5;

              &:focus {
                border-color: #F80D38;
                box-shadow: 0 0 0 2px rgba(248, 13, 56, 0.1);
              }
            }
          }

          .error-message {
            margin-top: 0.5rem;
            font-size: 0.75rem;
            color: #F80D38;
            font-weight: 500;
          }

          .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #8AA3D5;
            border-radius: 6px;
            font-size: 0.875rem;
            resize: vertical;
            min-height: 80px;
            transition: all 0.2s ease;

            &:focus {
              outline: none;
              border-color: #1061AC;
              box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
            }
          }
        }
      }

      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        padding: 1.5rem;
        border-top: 1px solid #C7DAEC;

        .btn-cancel {
          padding: 0.75rem 1.5rem;
          background: transparent;
          border: none;
          color: #163659;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          border-radius: 6px;
          transition: all 0.2s ease;

          &:hover {
            background: #f0f0f0;
          }
        }

        .btn-confirm {
          padding: 0.75rem 1.5rem;
          background: #57B6B1;
          border: none;
          color: white;
          font-size: 0.875rem;
          font-weight: 600;
          cursor: pointer;
          border-radius: 6px;
          transition: all 0.2s ease;
          min-width: 120px;

          &:hover:not(:disabled) {
            background: #4A9B96;
            transform: translateY(-1px);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
          }
        }
      }
    }
  }

  // Responsive modal
  @media (max-width: 768px) {
    .modal-overlay {
      padding: 0.5rem;

      .modal-container {
        max-width: 100%;
        max-height: 95vh;

        .modal-header,
        .modal-content,
        .modal-footer {
          padding: 1rem;
        }

        .modal-footer {
          flex-direction: column;

          .btn-cancel,
          .btn-confirm {
            width: 100%;
            justify-content: center;
          }
        }
      }
    }
  }
}
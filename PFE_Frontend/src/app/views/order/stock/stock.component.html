<div class="prescriptions-container">
  <!-- 🔷 Top Section: Title (left) and Icons (max right) -->
  <div class="prescriptions-header">
    <div class="title-and-icons">
      <h1 class="page-title">Gestion de stock</h1>

      <div class="header-icons">
        <button class="icon-button" (click)="toggleFilter()" title="Filtrer">
          <svg cIcon name="cil-filter" class="icon"></svg>
        </button>
        <button class="icon-button" (click)="toggleView()" title="Grid">
          @if (viewMode === 'table') {
            <svg cIcon name="cil-grid" class="icon"></svg>
          } @else {
            <svg cIcon name="cil-list" class="icon"></svg>
          }
        </button>
      </div>
    </div>

    <!-- 🔷 Filter Section: Appears below icons when filter is clicked -->
    @if (showFilter()) {
      <div class="filter-container">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">Stock</label>
            <div class="custom-dropdown">
              <button class="dropdown-toggle" (click)="toggleStockStatusDropdown($event)">
                <span>{{ getSelectedStockStatusLabel() }}</span>
              </button>
              @if (showStockStatusDropdown()) {
                <div class="dropdown-menu">
                  @for (option of stockStatusOptions; track option.value) {
                    <div class="dropdown-item" (click)="selectStockStatus(option.value)">
                      <span class="checkbox-square" [class.checked]="selectedStockStatus() === option.value"></span>
                      <span class="option-text">{{ option.label }}</span>
                    </div>
                  }
                </div>
              }
            </div>
          </div>

          <div class="filter-group">
            <label class="filter-label">État</label>
            <div class="custom-dropdown">
              <button class="dropdown-toggle" (click)="toggleActiveStatusDropdown($event)">
                <span>{{ getSelectedActiveStatusLabel() }}</span>
              </button>
              @if (showActiveStatusDropdown()) {
                <div class="dropdown-menu">
                  @for (option of activeStatusOptions; track option.value) {
                    <div class="dropdown-item" (click)="selectActiveStatus(option.value)">
                      <span class="checkbox-square" [class.checked]="selectedActiveStatus() === option.value"></span>
                      <span class="option-text">{{ option.label }}</span>
                    </div>
                  }
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    }

    <!-- 🔷 Second Row: Add Button and Search (when filter is active) -->
    <div class="action-row">
      <button class="primary-button" (click)="navigateToAddProduct()">
        <svg cIcon name="cil-plus" class="button-icon"></svg>
        Ajouter un article
      </button>

      <!-- Search Field: Only visible when filter is active -->
      @if (showFilter()) {
        <div class="search-container">
          <input
            type="text"
            placeholder="Rechercher un produit..."
            [(ngModel)]="searchQuery"
            (ngModelChange)="onSearchChange()"
            class="search-input"
          >
          <svg cIcon name="cil-search" class="search-icon"></svg>
        </div>
      }
    </div>
  </div>

  <!-- Table View -->
  @if (viewMode === 'table') {
    <div class="table-container">
      <table class="prescriptions-table">
        <thead>
          <tr>
            <th>Produit</th>
            <th>Nom du produit</th>
            <th>Stock</th>
            <th>Prix Unitaire</th>
            <th>État</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          @for (product of products; track product.id) {
            <tr>
              <td class="product-image-cell">
                <div class="table-image-container">
                  <img
                    [src]="getProductImageUrl(product)"
                    [alt]="product.name"
                    class="product-image"
                    (error)="onImageError($event)"
                  >

                  <!-- Navigation arrows for multiple images -->
                  @if (hasMultipleImages(product)) {
                    <button
                      class="table-nav-arrow table-nav-arrow-left"
                      (click)="previousImage(product, $event)"
                      [disabled]="!canNavigatePrevious(product)"
                      type="button"
                      title="Image précédente">
                      <svg cIcon name="cil-chevron-left" class="arrow-icon"></svg>
                    </button>
                    <button
                      class="table-nav-arrow table-nav-arrow-right"
                      (click)="nextImage(product, $event)"
                      [disabled]="!canNavigateNext(product)"
                      type="button"
                      title="Image suivante">
                      <svg cIcon name="cil-chevron-right" class="arrow-icon"></svg>
                    </button>

                    <!-- Image counter -->
                    <div class="table-image-counter">{{ getImageCounter(product) }}</div>
                  }
                </div>
              </td>
              <td>
                <div class="product-name-cell">
                  <div class="product-name">{{ product.name }}</div>
                  <div class="product-category">{{ getCategoryName(product.categoryId) }}</div>
                </div>
              </td>
              <td>
                <span class="badge" [ngClass]="getStockStatusBadgeClass(product.stockStatus)">
                  {{ getStockStatusText(product.stockStatus) }}
                </span>
                <div class="quantity">{{ product.quantity }} unités</div>
              </td>
              <td>
                <div class="price-cell">
                  <div class="unit-price">{{ formatPrice(product.price) }}</div>
                  @if (product.discount && product.discount > 0) {
                    <div class="discount">-{{ product.discount }}%</div>
                  }
                </div>
              </td>
              <td>
                <span class="badge" [ngClass]="getActiveStatusBadgeClass(product.actif)">
                  {{ getActiveStatusText(product.actif) }}
                </span>
              </td>
              <td class="actions-cell">
                <button title="Voir" (click)="viewProduct(product)">
                  <img src="assets/images/EyeIcon.svg" alt="Voir" class="eye-icon" />
                </button>
                <button title="Modifier" (click)="editProduct(product)">
                  <svg cIcon name="cil-pencil"></svg>
                </button>
                <button title="Supprimer" (click)="deleteProduct(product)">
                  <svg cIcon name="cil-trash"></svg>
                </button>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  } @else {
    <!-- Cards View (compact vertical layout) -->
    <div class="cards-container">
      @for (product of products; track product.id) {
        <div class="product-card">
          <!-- Out of stock ribbon -->
          @if (product.stockStatus === 'OUT_OF_STOCK') {
            <div class="out-of-stock-ribbon">
              <img src="assets/images/tag.svg" alt="Rupture de stock" class="ribbon-icon">
            </div>
          }

          <!-- Product image on top -->
          <div class="product-image-container">
            <img
              [src]="getProductImageUrl(product)"
              [alt]="product.name"
              class="product-image"
              (error)="onImageError($event)"
            >

            <!-- Navigation arrows for multiple images -->
            @if (hasMultipleImages(product)) {
              <button
                class="card-nav-arrow card-nav-arrow-left"
                (click)="previousImage(product, $event)"
                [disabled]="!canNavigatePrevious(product)"
                type="button"
                title="Image précédente">
                <svg cIcon name="cil-chevron-left" class="arrow-icon"></svg>
              </button>
              <button
                class="card-nav-arrow card-nav-arrow-right"
                (click)="nextImage(product, $event)"
                [disabled]="!canNavigateNext(product)"
                type="button"
                title="Image suivante">
                <svg cIcon name="cil-chevron-right" class="arrow-icon"></svg>
              </button>

              <!-- Image counter -->
              <div class="card-image-counter">{{ getImageCounter(product) }}</div>
            }
          </div>

          <!-- Product name with toggle switch and 3-dot menu -->
          <div class="product-header">
            <h3 class="product-name">{{ product.name }}</h3>
            <div class="header-actions">
              <div class="toggle-switch" [class.active]="product.actif" [class.inactive]="!product.actif">
                <div class="toggle-slider"></div>
              </div>

              <!-- 3-dot menu button -->
              <div class="menu-container">
                <button class="menu-button" (click)="toggleProductMenu(product.id, $event)">
                  <svg width="16" height="4" viewBox="0 0 16 4" fill="none">
                    <circle cx="2" cy="2" r="2" fill="#728A9B"/>
                    <circle cx="8" cy="2" r="2" fill="#728A9B"/>
                    <circle cx="14" cy="2" r="2" fill="#728A9B"/>
                  </svg>
                </button>

                <!-- Dropdown menu -->
                @if (showProductMenu() === product.id) {
                  <div class="menu-dropdown">
                    <button class="menu-item" (click)="editProduct(product)">
                      <svg cIcon name="cil-pencil" class="menu-icon"></svg>
                      <span>Modifier</span>
                    </button>
                    <button class="menu-item delete" (click)="deleteProduct(product)">
                      <svg cIcon name="cil-trash" class="menu-icon"></svg>
                      <span>Supprimer</span>
                    </button>
                  </div>
                }
              </div>
            </div>
          </div>

          <!-- Product type/category -->
          <div class="product-type">
            {{ getCategoryName(product.categoryId) }}
          </div>

          <!-- Stock information -->
          <div class="stock-info">
            @if (product.stockStatus === 'OUT_OF_STOCK') {
              <span class="stock-text out-of-stock">Stock: Rupture de stock</span>
            } @else {
              <span class="stock-text in-stock">Stock: {{ product.quantity.toString().padStart(2, '0') }}</span>
            }
          </div>

          <!-- Price -->
          <div class="product-price">
            {{ formatPrice(product.price) }}
          </div>
        </div>
      }
    </div>
  }

  <!-- Pagination Section -->
  @if (getTotalCount() > 0) {
    <div class="pagination-container">
      <!-- Items per page selector -->
      <div class="items-per-page">
        <label class="pagination-label">Éléments par page :</label>
        <select class="page-size-selector" [value]="itemsPerPage" (change)="onPageSizeChange($event)">
          @for (size of availablePageSizes; track size) {
            <option [value]="size">{{ size }}</option>
          }
        </select>
      </div>

      <!-- Pagination info -->
      <div class="pagination-info">
        <span class="pagination-text">
          {{ (currentPage - 1) * itemsPerPage + 1 }} -
          {{ getEndItemNumber() }}
          sur {{ getTotalCount() }} produits
        </span>
      </div>

      <!-- Pagination controls -->
      <div class="pagination-controls">
        <!-- First page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === 1"
          (click)="goToFirstPage()"
          title="Première page">
          <svg cIcon name="cil-media-skip-backward"></svg>
        </button>

        <!-- Previous page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === 1"
          (click)="goToPreviousPage()"
          title="Page précédente">
          <svg cIcon name="cil-chevron-left"></svg>
        </button>

        <!-- Page numbers -->
        @for (page of getPageNumbers(); track page) {
          <button
            class="pagination-btn page-number"
            [class.active]="page === currentPage"
            (click)="goToPage(page)">
            {{ page }}
          </button>
        }

        <!-- Next page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === totalPages"
          (click)="goToNextPage()"
          title="Page suivante">
          <svg cIcon name="cil-chevron-right"></svg>
        </button>

        <!-- Last page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === totalPages"
          (click)="goToLastPage()"
          title="Dernière page">
          <svg cIcon name="cil-media-skip-forward"></svg>
        </button>
      </div>
    </div>
  }
</div>

// Product Details Modal styles (matches edit modal)
.edit-product-modal {
  width: 950px;
  height: 550px;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border: 2px solid #C7DAEC;
  border-radius: 10px;
  opacity: 1;
  padding: 25px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10003;
  font-family: 'Nautica Rounded', sans-serif;

  // Modal header
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;

    .modal-title {
      font: normal normal 800 14px/17px 'Nautica Rounded', sans-serif;
      letter-spacing: 1.4px;
      color: #1061AC;
      text-align: left;
      margin: 0;
    }

    // Close Button
    .close-button {
      width: 24px;
      height: 24px;
      border: 2px solid #57B6B1;
      border-radius: 50%;
      opacity: 1;
      background: transparent;
      cursor: pointer;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background-color: #57B6B1;
      }

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 12px;
        height: 2px;
        background-color: #57B6B1;
        transition: background-color 0.2s ease;
      }

      &:hover::before,
      &:hover::after {
        background-color: white;
      }

      &::before {
        transform: rotate(45deg);
      }

      &::after {
        transform: rotate(-45deg);
      }
    }
  }

  // Modal content
  .modal-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    // Status toggle (non-clickable)
    .status-toggle {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 12px;
      margin-bottom: 20px;

      .toggle-label {
        font: normal normal 600 14px/17px 'Nautica Rounded', sans-serif;
        letter-spacing: 0px;
        color: #163659;
        opacity: 1;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 20px;
        background: #F8F9FA;
        border: 1px solid #E0E0E0;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #DC3545; // Red for inactive
        }

        .status-text {
          font-size: 12px;
          font-weight: 600;
          color: #163659;
        }

        &.active {
          .status-dot {
            background: #28A745; // Green for active
          }
        }
      }
    }

    // Form content
    .form-content {
      display: flex;
      gap: 30px;
      flex: 1;

      // Image section (left side)
      .image-section {
        flex: 0 0 300px;

        .image-placeholder {
          width: 300px;
          height: 300px;
          background: #E6EDF5;
          border: 1px solid #6D9ECC;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          overflow: hidden;

          &.has-image {
            background: transparent;
            border: none;
          }

          .image-container {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            .preview-image {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
              border-radius: 8px;
            }

            // Navigation arrows
            .nav-arrow {
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              background: rgba(0, 0, 0, 0.7);
              color: white;
              border: none;
              border-radius: 50%;
              width: 32px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              opacity: 0;
              transition: opacity 0.2s ease;
              z-index: 2;

              &:hover {
                background: rgba(0, 0, 0, 0.9);
              }

              &:disabled {
                opacity: 0.3;
                cursor: not-allowed;
              }

              svg {
                width: 16px;
                height: 16px;
              }

              &.nav-arrow-left {
                left: 10px;
              }

              &.nav-arrow-right {
                right: 10px;
              }
            }

            // Image counter
            .image-counter {
              position: absolute;
              bottom: 10px;
              right: 10px;
              background: rgba(0, 0, 0, 0.7);
              color: white;
              font-size: 12px;
              padding: 4px 8px;
              border-radius: 4px;
              opacity: 0;
              transition: opacity 0.2s ease;
            }

            &:hover {
              .nav-arrow,
              .image-counter {
                opacity: 1;
              }
            }
          }

          .no-image-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            color: #728A9B;

            svg {
              opacity: 0.5;
            }

            span {
              font-size: 14px;
              font-weight: 500;
            }
          }
        }
      }

      // Form section (right side)
      .form-section {
        flex: 1;

        .product-form {
          display: flex;
          flex-direction: column;
          gap: 20px;

          .form-row {
            display: flex;
            gap: 20px;

            &.full-width {
              .form-group {
                flex: 1;
              }
            }

            .form-group {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 8px;

              label {
                font: normal normal 600 14px/17px 'Nautica Rounded', sans-serif;
                letter-spacing: 0px;
                color: #163659;
                opacity: 1;
              }

              .form-input,
              .form-textarea {
                width: 100%;
                height: 40px;
                background: #F8F9FA; // Light background for read-only
                border: 1px solid #8AA3D5;
                border-radius: 6px;
                opacity: 1;
                padding: 0 12px;
                font: normal normal normal 14px/17px 'Nautica Rounded', sans-serif;
                letter-spacing: 0px;
                color: #495057;
                box-sizing: border-box;
                cursor: default;

                &:focus {
                  outline: none;
                  border-color: #8AA3D5;
                }
              }

              .form-textarea {
                height: auto;
                min-height: 80px;
                padding: 12px;
                resize: none;
                font-family: 'Nautica Rounded', sans-serif;
              }
            }
          }
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 1024px) {
    width: 90vw;
    height: 80vh;
    max-width: 800px;

    .modal-content {
      .form-content {
        flex-direction: column;
        gap: 20px;

        .image-section {
          flex: none;
          align-self: center;

          .image-placeholder {
            width: 250px;
            height: 250px;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    width: 95vw;
    height: 85vh;
    padding: 15px;

    .modal-header {
      margin-bottom: 15px;

      .modal-title {
        font-size: 12px;
      }
    }

    .modal-content {
      .status-toggle {
        margin-bottom: 15px;
        justify-content: center;

        .toggle-label {
          font-size: 12px;
        }
      }

      .form-content {
        gap: 15px;

        .image-section {
          .image-placeholder {
            width: 200px;
            height: 200px;
          }
        }

        .form-section {
          .product-form {
            gap: 15px;

            .form-row {
              gap: 15px;

              .form-group {
                label {
                  font-size: 12px;
                }

                .form-input,
                .form-textarea {
                  padding: 10px;
                  font-size: 12px;
                  height: 35px;
                }

                .form-textarea {
                  height: auto;
                  min-height: 70px;
                }
              }
            }
          }
        }
      }
    }
  }
}
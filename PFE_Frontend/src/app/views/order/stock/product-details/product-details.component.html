<div class="edit-product-modal">
  <!-- <PERSON><PERSON> Header -->
  <div class="modal-header">
    <h2 class="modal-title">Détails du produit</h2>
    <button class="close-button" (click)="onClose()" type="button"></button>
  </div>

  <!-- Modal Content -->
  <div class="modal-content">
    <!-- Active Status Display (Non-clickable) -->
    <div class="status-toggle">
      <label class="toggle-label"><PERSON>tat (Actif/Inactif)</label>
      <div class="status-indicator" [class.active]="product.actif">
        <div class="status-dot"></div>
        <span class="status-text">{{ getActiveStatusText() }}</span>
      </div>
    </div>

    <!-- Form Content -->
    <div class="form-content">
      <!-- Image Section -->
      <div class="image-section">
        <!-- Image Preview or Placeholder -->
        <div class="image-placeholder" [class.has-image]="hasImages()">
          @if (hasImages()) {
            <!-- Image Container with Navigation -->
            <div class="image-container">
              <img [src]="getCurrentImageUrl()" [alt]="product.name" class="preview-image">

              <!-- Navigation Arrows -->
              @if (getTotalImages() > 1) {
                <button
                  class="nav-arrow nav-arrow-left"
                  (click)="previousImage()"
                  [disabled]="!canNavigatePrevious()"
                  type="button">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="15,18 9,12 15,6"></polyline>
                  </svg>
                </button>
                <button
                  class="nav-arrow nav-arrow-right"
                  (click)="nextImage()"
                  [disabled]="!canNavigateNext()"
                  type="button">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9,18 15,12 9,6"></polyline>
                  </svg>
                </button>

                <!-- Image Counter -->
                <div class="image-counter">{{ getImageCounter() }}</div>
              }
            </div>
          } @else {
            <!-- No Image Placeholder -->
            <div class="no-image-placeholder">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <circle cx="8.5" cy="8.5" r="1.5"/>
                <polyline points="21,15 16,10 5,21"/>
              </svg>
              <span>Aucune image</span>
            </div>
          }
        </div>
      </div>

      <!-- Form Fields -->
      <div class="form-section">
        <form class="product-form">
          <!-- First Row -->
          <div class="form-row">
            <div class="form-group">
              <label for="productName">Nom du produit</label>
              <input
                id="productName"
                type="text"
                [value]="product.name"
                class="form-input"
                readonly>
            </div>

            <div class="form-group">
              <label for="category">Catégorie</label>
              <input
                id="category"
                type="text"
                [value]="getCategoryName()"
                class="form-input"
                readonly>
            </div>
          </div>

          <!-- Second Row -->
          <div class="form-row">
            <div class="form-group">
              <label for="quantity">Quantité</label>
              <input
                id="quantity"
                type="number"
                [value]="product.quantity"
                class="form-input"
                readonly>
            </div>

            <div class="form-group">
              <label for="price">Prix</label>
              <input
                id="price"
                type="number"
                [value]="product.price"
                class="form-input"
                readonly>
            </div>
          </div>

          <!-- Description Row -->
          <div class="form-row full-width">
            <div class="form-group">
              <label for="description">Description</label>
              <textarea
                id="description"
                [value]="product.description || ''"
                class="form-textarea"
                rows="3"
                readonly></textarea>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

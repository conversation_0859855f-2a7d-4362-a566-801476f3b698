<div class="edit-product-modal">
  <!-- <PERSON><PERSON> Header -->
  <div class="modal-header">
    <h2 class="modal-title">Modifier le produit</h2>
    <button class="close-button" (click)="onCancel()" type="button"></button>
  </div>

  <!-- Modal Content -->
  <div class="modal-content">
    <!-- Active Status Toggle -->
    <div class="status-toggle">
      <label class="toggle-label">État (Actif/Inactif)</label>
      <div class="toggle-switch" [class.active]="productData.actif" (click)="toggleActiveStatus()">
        <div class="toggle-slider"></div>
      </div>
    </div>

    <!-- Form Content -->
    <div class="form-content">
      <!-- Image Section -->
      <div class="image-section">
        <!-- Image Preview or Placeholder -->
        <div class="image-placeholder" [class.has-image]="hasImages()">
          @if (hasImages()) {
            <!-- Image Container with Navigation -->
            <div class="image-container">
              <img [src]="getCurrentImageUrl()" [alt]="productData.name" class="preview-image">

              <!-- Navigation Arrows -->
              @if (getTotalImages() > 1) {
                <button
                  class="nav-arrow nav-arrow-left"
                  (click)="previousImage()"
                  [disabled]="!canNavigatePrevious()"
                  type="button">
                  <svg cIcon name="cil-chevron-left" class="arrow-icon"></svg>
                </button>
                <button
                  class="nav-arrow nav-arrow-right"
                  (click)="nextImage()"
                  [disabled]="!canNavigateNext()"
                  type="button">
                  <svg cIcon name="cil-chevron-right" class="arrow-icon"></svg>
                </button>

                <!-- Image Counter -->
                <div class="image-counter">{{ getImageCounter() }}</div>
              }

              <!-- Remove Current Image Button -->
              <button class="remove-image-btn" (click)="removeCurrentImage()" type="button">
                <svg cIcon name="cil-x" class="remove-icon"></svg>
              </button>
            </div>
          } @else {
            <svg cIcon name="cil-cloud-upload" class="upload-icon"></svg>
            <span class="upload-text">Ajouter des images</span>
          }
        </div>

        <!-- Upload Button -->
        <button class="upload-button" type="button" (click)="fileInput.click()">
          <svg cIcon name="cil-plus" class="button-icon"></svg>
          Ajouter des images
        </button>

        <!-- Hidden File Input (Multiple) -->
        <input
          #fileInput
          id="imageInput"
          type="file"
          accept="image/*"
          multiple
          (change)="onImageSelected($event)"
          style="display: none;"
        >
      </div>

      <!-- Form Fields -->
      <div class="form-section">
        <form class="product-form" (ngSubmit)="onSubmit()">
          <!-- First Row -->
          <div class="form-row">
            <div class="form-group">
              <label for="productName">Nom du produit</label>
              <input
                id="productName"
                type="text"
                [(ngModel)]="productData.name"
                name="productName"
                placeholder="Nom du produit"
                class="form-input"
                required
              >
            </div>

            <div class="form-group">
              <label for="category">Catégorie</label>
              <div class="custom-dropdown">
                <button type="button" class="dropdown-toggle" (click)="toggleCategoryDropdown($event)">
                  <span>{{ getSelectedCategoryText() }}</span>
                </button>
                @if (showCategoryDropdown()) {
                  <div class="dropdown-menu">
                    @for (category of categories; track category.id) {
                      <div class="dropdown-item" (click)="selectCategory(category.id)" [class.selected]="productData.categoryId === category.id">
                        <span class="option-text">{{ category.name }}</span>
                      </div>
                    }
                  </div>
                }
              </div>
            </div>
          </div>

          <!-- Second Row -->
          <div class="form-row">
            <div class="form-group">
              <label for="quantity">Quantité</label>
              <input
                id="quantity"
                type="number"
                [(ngModel)]="productData.quantity"
                name="quantity"
                placeholder="Quantité"
                class="form-input"
                min="0"
                required
              >
            </div>

            <div class="form-group">
              <label for="price">Prix</label>
              <input
                id="price"
                type="number"
                [(ngModel)]="productData.price"
                name="price"
                placeholder="Prix"
                class="form-input"
                min="0"
                step="0.01"
                required
              >
            </div>
          </div>

          <!-- Description Row -->
          <div class="form-row full-width">
            <div class="form-group">
              <label for="description">Description</label>
              <textarea
                id="description"
                [(ngModel)]="productData.description"
                name="description"
                placeholder="Description du produit"
                class="form-textarea"
                rows="3"
              ></textarea>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Messages -->
    @if (error()) {
      <div class="message error-message">
        <svg cIcon name="cil-warning" class="message-icon"></svg>
        {{ error() }}
      </div>
    }

    @if (success()) {
      <div class="message success-message">
        <svg cIcon name="cil-check-circle" class="message-icon"></svg>
        {{ success() }}
      </div>
    }
  </div>

  <!-- Modal Footer -->
  <div class="button-row">
    <button class="cancel-button" (click)="onCancel()" [disabled]="isSubmitting()">
      Annuler
    </button>
    <button
      class="submit-button"
      (click)="onSubmit()"
      [disabled]="isSubmitting()"
      [class.loading]="isSubmitting()"
    >
      @if (isSubmitting()) {
        <span class="loading-spinner"></span>
        Mise à jour...
      } @else {
        Valider
      }
    </button>
  </div>
</div>

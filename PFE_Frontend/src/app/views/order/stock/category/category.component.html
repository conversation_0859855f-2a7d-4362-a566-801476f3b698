<!-- Category Management Container -->
<div class="category-container">
  <!-- Header -->
  <div class="category-header">
    <h1 class="page-title">Gestion des catégories</h1>
  </div>

  <!-- Header with Add Button and Search Field -->
  <div class="header-controls">
    <!-- Add Category Button -->
    <div class="add-button-container">
      <button class="add-button" (click)="addCategory()">
        <svg cIcon name="cil-plus" class="add-icon"></svg>
        Ajouter une catégorie
      </button>
    </div>

    <!-- Search Field -->
    <div class="search-container">
      <div class="search-field">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onSearchChange()"
          placeholder="Rechercher une catégorie..."
          class="search-input"
        >
        @if (searchTerm) {
          <button class="clear-search" (click)="clearSearch()">
            <svg cIcon name="cil-x"></svg>
          </button>
        }
        <svg cIcon name="cil-search" class="search-icon"></svg>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  @if (loading) {
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <span>Chargement des catégories...</span>
    </div>
  }

  <!-- Error State -->
  @if (error && !loading) {
    <div class="error-container">
      <svg cIcon name="cil-warning" class="error-icon"></svg>
      <span>{{ error }}</span>
      <button class="retry-button" (click)="loadCategories()">Réessayer</button>
    </div>
  }

  <!-- Content -->
  @if (!loading && !error) {
    <!-- Table View -->
    <div class="table-container">
      <table class="categories-table">
        <thead>
          <tr>
            <th>Nom de la catégorie</th>
            <th>Date de création</th>
            <th>Date de modification</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          @for (category of getPaginatedCategories(); track category.id) {
            <tr>
              <td>
                <span class="category-name">{{ category.name }}</span>
              </td>
              <td>
                <span class="date-text">{{ formatDate(category.createdAt) }}</span>
              </td>
              <td>
                <span class="date-text">{{ formatDate(category.updatedAt) }}</span>
              </td>
              <td class="actions-cell">
                <button title="Modifier" (click)="editCategory(category)">
                  <svg cIcon name="cil-pencil"></svg>
                </button>
                <button title="Supprimer" (click)="deleteCategory(category)">
                  <svg cIcon name="cil-trash"></svg>
                </button>
              </td>
            </tr>
          } @empty {
            <tr>
              <td colspan="4" class="no-data">
                @if (searchTerm) {
                  Aucune catégorie trouvée pour "{{ searchTerm }}"
                } @else {
                  Aucune catégorie disponible
                }
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>



    <!-- Pagination Section -->
    @if (getTotalCount() > 0) {
      <div class="pagination-container">
        <!-- Items per page selector -->
        <div class="items-per-page">
          <label class="pagination-label">Éléments par page :</label>
          <select class="page-size-selector" [value]="itemsPerPage" (change)="onPageSizeChange($event)">
            @for (size of availablePageSizes; track size) {
              <option [value]="size">{{ size }}</option>
            }
          </select>
        </div>

        <!-- Pagination info -->
        <div class="pagination-info">
          <span class="pagination-text">
            {{ (currentPage - 1) * itemsPerPage + 1 }} -
            {{ getEndItemNumber() }}
            sur {{ getTotalCount() }} catégories
          </span>
        </div>

        <!-- Pagination controls -->
        <div class="pagination-controls">
          <!-- First page -->
          <button
            class="pagination-btn"
            [disabled]="currentPage === 1"
            (click)="goToFirstPage()"
            title="Première page">
            <svg cIcon name="cil-media-skip-backward"></svg>
          </button>

          <!-- Previous page -->
          <button
            class="pagination-btn"
            [disabled]="currentPage === 1"
            (click)="goToPreviousPage()"
            title="Page précédente">
            <svg cIcon name="cil-chevron-left"></svg>
          </button>

          <!-- Page numbers -->
          @for (page of getPageNumbers(); track page) {
            <button
              class="pagination-btn page-number"
              [class.active]="page === currentPage"
              (click)="goToPage(page)">
              {{ page }}
            </button>
          }

          <!-- Next page -->
          <button
            class="pagination-btn"
            [disabled]="currentPage === totalPages"
            (click)="goToNextPage()"
            title="Page suivante">
            <svg cIcon name="cil-chevron-right"></svg>
          </button>

          <!-- Last page -->
          <button
            class="pagination-btn"
            [disabled]="currentPage === totalPages"
            (click)="goToLastPage()"
            title="Dernière page">
            <svg cIcon name="cil-media-skip-forward"></svg>
          </button>
        </div>
      </div>
    }
  }
</div>

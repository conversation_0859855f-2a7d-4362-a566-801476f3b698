import { Component, OnDestroy, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IconDirective } from '@coreui/icons-angular';
import { CategoryService } from '../../../../../services/category.service';
import { Category, UpdateCategoryRequest } from '../../../../../models/category.model';

@Component({
  selector: 'app-edit-category',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconDirective
  ],
  templateUrl: './edit-category.component.html',
  styleUrl: './edit-category.component.scss'
})
export class EditCategoryComponent implements OnDestroy {
  private destroy$ = new Subject<void>();

  // Category data
  category: Category;
  categoryData: UpdateCategoryRequest = {
    name: ''
  };

  // UI state
  isSubmitting = signal(false);
  error = signal<string | null>(null);
  success = signal<string | null>(null);

  constructor(
    private dialogRef: MatDialogRef<EditCategoryComponent>,
    private categoryService: CategoryService
  ) {
    const data = inject(MAT_DIALOG_DATA);
    this.category = data.category;
    
    // Initialize form data
    this.categoryData = {
      name: this.category.name
    };
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSubmit() {
    // Reset messages
    this.error.set(null);
    this.success.set(null);

    // Validate form
    if (!this.categoryData.name?.trim()) {
      this.error.set('Le nom de la catégorie est requis');
      return;
    }

    // Check if anything changed
    if (this.categoryData.name.trim() === this.category.name) {
      this.error.set('Aucune modification détectée');
      return;
    }

    // Submit form
    this.isSubmitting.set(true);

    this.categoryService.updateCategory(this.category.id, this.categoryData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('✅ Category updated successfully:', response);
          this.isSubmitting.set(false);
          
          if (response.success) {
            this.success.set('Catégorie modifiée avec succès');
            // Close modal after short delay
            setTimeout(() => {
              this.dialogRef.close(response.category);
            }, 1000);
          } else {
            this.error.set('Erreur lors de la modification de la catégorie');
          }
        },
        error: (error) => {
          console.error('❌ Error updating category:', error);
          this.isSubmitting.set(false);
          
          // Handle specific error messages
          if (error.error?.message) {
            this.error.set(error.error.message);
          } else if (error.status === 409) {
            this.error.set('Une catégorie avec ce nom existe déjà');
          } else {
            this.error.set('Erreur lors de la modification de la catégorie');
          }
        }
      });
  }

  onCancel() {
    this.dialogRef.close();
  }

  // Form validation
  isFormValid(): boolean {
    return !!(this.categoryData.name?.trim() && 
             this.categoryData.name.trim() !== this.category.name);
  }
}

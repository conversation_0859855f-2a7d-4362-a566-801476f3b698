<div class="edit-category-modal">
  <!-- <PERSON><PERSON>er -->
  <div class="modal-header">
    <h2 class="modal-title">Modifier la catégorie</h2>
    <button class="close-button" (click)="onCancel()" type="button"></button>
  </div>

  <!-- Modal Content -->
  <div class="modal-content">
    <!-- Form -->
    <form class="category-form" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="categoryName">Nom de la catégorie</label>
        <input
          id="categoryName"
          type="text"
          [(ngModel)]="categoryData.name"
          name="categoryName"
          placeholder="Entrez le nom de la catégorie"
          class="form-input"
          [class.error]="error()"
          required
          maxlength="100"
        >
      </div>
    </form>

    <!-- Messages -->
    @if (error()) {
      <div class="message error-message">
        <svg cIcon name="cil-warning" class="message-icon"></svg>
        {{ error() }}
      </div>
    }

    @if (success()) {
      <div class="message success-message">
        <svg cIcon name="cil-check-circle" class="message-icon"></svg>
        {{ success() }}
      </div>
    }
  </div>

  <!-- Modal Footer -->
  <div class="button-row">
    <button class="cancel-button" (click)="onCancel()" [disabled]="isSubmitting()">
      Annuler
    </button>
    <button
      class="submit-button"
      (click)="onSubmit()"
      [disabled]="!isFormValid() || isSubmitting()"
      [class.loading]="isSubmitting()"
    >
      @if (isSubmitting()) {
        <span class="loading-spinner"></span>
        Modification...
      } @else {
        Valider
      }
    </button>
  </div>
</div>

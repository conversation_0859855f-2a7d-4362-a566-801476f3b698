// Add Category Modal styles
.add-category-modal {
  width: 500px;
  height: 300px;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border: 2px solid #C7DAEC;
  border-radius: 10px;
  opacity: 1;
  padding: 25px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10003;
  font-family: 'Nautica Rounded', sans-serif;

  // Modal header
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;

    .modal-title {
      font: normal normal 800 14px/17px 'Nautica Rounded', sans-serif;
      letter-spacing: 1.4px;
      color: #1061AC;
      text-align: left;
      margin: 0;
    }

    // Close Button
    .close-button {
      width: 24px;
      height: 24px;
      border: 2px solid #57B6B1;
      border-radius: 50%;
      opacity: 1;
      background: transparent;
      cursor: pointer;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background-color: #57B6B1;
      }

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 12px;
        height: 2px;
        background-color: #57B6B1;
        transition: background-color 0.2s ease;
      }

      &:hover::before,
      &:hover::after {
        background-color: white;
      }

      &::before {
        transform: rotate(45deg);
      }

      &::after {
        transform: rotate(-45deg);
      }
    }
  }

  // Modal content
  .modal-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .category-form {
      flex: 1;

      .form-group {
        margin-bottom: 20px;

        label {
          display: block;
          font: normal normal 800 12px/15px 'Nautica Rounded', sans-serif;
          letter-spacing: 1.2px;
          color: #163659;
          margin-bottom: 8px;
        }

        .form-input {
          width: 100%;
          height: 45px;
          padding: 12px 16px;
          border: 1px solid #8AA3D5;
          border-radius: 5px;
          font-size: 14px;
          color: #163659;
          background: #FFFFFF;
          box-sizing: border-box;
          transition: border-color 0.2s ease;

          &::placeholder {
            color: #728A9B;
          }

          &:focus {
            outline: none;
            border-color: #1061AC;
          }

          &.error {
            border-color: #F80D38;
          }
        }
      }
    }

    // Messages
    .message {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      border-radius: 5px;
      font-size: 14px;
      margin-top: 16px;

      .message-icon {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
      }

      &.error-message {
        background: #FFF5F5;
        color: #F80D38;
        border: 1px solid #F80D38;
      }

      &.success-message {
        background: #F0FFF4;
        color: #57B6B1;
        border: 1px solid #57B6B1;
      }
    }
  }

  // Modal footer
  .button-row {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
    flex-shrink: 0;

    .cancel-button {
      background: transparent;
      color: #163659;
      border: 1px solid #C7DAEC;
      border-radius: 5px;
      padding: 10px 20px;
      font: normal normal 800 12px/15px 'Nautica Rounded', sans-serif;
      letter-spacing: 1.2px;
      cursor: pointer;
      transition: all 0.2s ease;
      width: 120px;
      height: 36px;

      &:hover:not(:disabled) {
        background: #F5FFF9;
        border-color: #57B6B1;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .submit-button {
      background: #57B6B1;
      color: #FFFFFF;
      border: none;
      border-radius: 5px;
      padding: 10px 20px;
      font: normal normal 800 12px/15px 'Nautica Rounded', sans-serif;
      letter-spacing: 1.2px;
      cursor: pointer;
      transition: all 0.2s ease;
      width: 120px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;

      &:hover:not(:disabled) {
        background: #0d4f8c;
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      &.loading {
        pointer-events: none;
      }

      .loading-spinner {
        width: 14px;
        height: 14px;
        border: 2px solid transparent;
        border-top: 2px solid #FFFFFF;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    width: 95vw;
    height: auto;
    max-height: 85vh;
    padding: 15px;

    .modal-header {
      margin-bottom: 15px;

      .modal-title {
        font-size: 12px;
      }
    }

    .modal-content {
      .category-form {
        .form-group {
          margin-bottom: 15px;

          label {
            font-size: 11px;
          }

          .form-input {
            height: 40px;
            padding: 10px 12px;
            font-size: 13px;
          }
        }
      }

      .message {
        padding: 10px 12px;
        font-size: 13px;
        margin-top: 12px;
      }
    }

    .button-row {
      margin-top: 15px;
      gap: 10px;

      .cancel-button,
      .submit-button {
        width: 100px;
        height: 32px;
        padding: 8px 16px;
        font-size: 11px;
      }
    }
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

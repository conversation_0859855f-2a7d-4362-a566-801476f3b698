import { Component, OnDestroy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { MatDialogRef } from '@angular/material/dialog';
import { IconDirective } from '@coreui/icons-angular';
import { CategoryService } from '../../../../../services/category.service';
import { CreateCategoryRequest } from '../../../../../models/category.model';

@Component({
  selector: 'app-add-category',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconDirective
  ],
  templateUrl: './add-category.component.html',
  styleUrl: './add-category.component.scss'
})
export class AddCategoryComponent implements OnDestroy {
  private destroy$ = new Subject<void>();

  // Form data
  categoryData: CreateCategoryRequest = {
    name: ''
  };

  // UI state
  isSubmitting = signal(false);
  error = signal<string | null>(null);
  success = signal<string | null>(null);

  constructor(
    private dialogRef: MatDialogRef<AddCategoryComponent>,
    private categoryService: CategoryService
  ) {}

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSubmit() {
    // Reset messages
    this.error.set(null);
    this.success.set(null);

    // Validate form
    if (!this.categoryData.name.trim()) {
      this.error.set('Le nom de la catégorie est requis');
      return;
    }

    // Submit form
    this.isSubmitting.set(true);

    this.categoryService.createCategory(this.categoryData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('✅ Category created successfully:', response);
          this.isSubmitting.set(false);
          
          if (response.success) {
            this.success.set('Catégorie créée avec succès');
            // Close modal after short delay
            setTimeout(() => {
              this.dialogRef.close(response.category);
            }, 1000);
          } else {
            this.error.set('Erreur lors de la création de la catégorie');
          }
        },
        error: (error) => {
          console.error('❌ Error creating category:', error);
          this.isSubmitting.set(false);
          
          // Handle specific error messages
          if (error.error?.message) {
            this.error.set(error.error.message);
          } else if (error.status === 409) {
            this.error.set('Une catégorie avec ce nom existe déjà');
          } else {
            this.error.set('Erreur lors de la création de la catégorie');
          }
        }
      });
  }

  onCancel() {
    this.dialogRef.close();
  }

  // Form validation
  isFormValid(): boolean {
    return this.categoryData.name.trim().length > 0;
  }
}

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogRef } from '@angular/material/dialog';
import { of, throwError } from 'rxjs';
import { AddCategoryComponent } from './add-category.component';
import { CategoryService } from '../../../../../services/category.service';
import { Category } from '../../../../../models/category.model';

describe('AddCategoryComponent', () => {
  let component: AddCategoryComponent;
  let fixture: ComponentFixture<AddCategoryComponent>;
  let mockCategoryService: jasmine.SpyObj<CategoryService>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<AddCategoryComponent>>;

  const mockCategory: Category = {
    id: '1',
    name: 'Test Category',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  };

  beforeEach(async () => {
    const categoryServiceSpy = jasmine.createSpyObj('CategoryService', ['createCategory']);
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [AddCategoryComponent],
      providers: [
        { provide: CategoryService, useValue: categoryServiceSpy },
        { provide: MatDialogRef, useValue: dialogRefSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AddCategoryComponent);
    component = fixture.componentInstance;
    mockCategoryService = TestBed.inject(CategoryService) as jasmine.SpyObj<CategoryService>;
    mockDialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<AddCategoryComponent>>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty form data', () => {
    expect(component.categoryData.name).toBe('');
    expect(component.isSubmitting()).toBeFalse();
    expect(component.error()).toBeNull();
    expect(component.success()).toBeNull();
  });

  it('should validate form correctly', () => {
    expect(component.isFormValid()).toBeFalse();

    component.categoryData.name = 'Test Category';
    expect(component.isFormValid()).toBeTrue();

    component.categoryData.name = '   ';
    expect(component.isFormValid()).toBeFalse();
  });

  it('should show error when submitting empty form', () => {
    component.categoryData.name = '';

    component.onSubmit();

    expect(component.error()).toBe('Le nom de la catégorie est requis');
    expect(mockCategoryService.createCategory).not.toHaveBeenCalled();
  });

  it('should show error when submitting whitespace-only name', () => {
    component.categoryData.name = '   ';

    component.onSubmit();

    expect(component.error()).toBe('Le nom de la catégorie est requis');
    expect(mockCategoryService.createCategory).not.toHaveBeenCalled();
  });

  it('should create category successfully', (done) => {
    const mockResponse = {
      success: true,
      message: 'Category created',
      category: mockCategory
    };
    mockCategoryService.createCategory.and.returnValue(of(mockResponse));
    component.categoryData.name = 'Test Category';

    component.onSubmit();

    expect(component.isSubmitting()).toBeTrue();
    expect(mockCategoryService.createCategory).toHaveBeenCalledWith({
      name: 'Test Category'
    });

    setTimeout(() => {
      expect(component.isSubmitting()).toBeFalse();
      expect(component.success()).toBe('Catégorie créée avec succès');
      expect(component.error()).toBeNull();
      done();
    }, 0);
  });

  it('should close modal after successful creation', (done) => {
    const mockResponse = {
      success: true,
      message: 'Category created',
      category: mockCategory
    };
    mockCategoryService.createCategory.and.returnValue(of(mockResponse));
    component.categoryData.name = 'Test Category';

    component.onSubmit();

    setTimeout(() => {
      expect(mockDialogRef.close).toHaveBeenCalledWith(mockCategory);
      done();
    }, 1100); // Wait for the 1000ms delay + buffer
  });

  it('should handle creation error', () => {
    const errorResponse = {
      error: { message: 'Category already exists' },
      status: 409
    };
    mockCategoryService.createCategory.and.returnValue(throwError(() => errorResponse));
    component.categoryData.name = 'Test Category';

    component.onSubmit();

    expect(component.isSubmitting()).toBeFalse();
    expect(component.error()).toBe('Category already exists');
    expect(component.success()).toBeNull();
  });

  it('should handle 409 conflict error specifically', () => {
    const errorResponse = {
      status: 409
    };
    mockCategoryService.createCategory.and.returnValue(throwError(() => errorResponse));
    component.categoryData.name = 'Test Category';

    component.onSubmit();

    expect(component.error()).toBe('Une catégorie avec ce nom existe déjà');
  });

  it('should handle generic error', () => {
    const errorResponse = {
      status: 500
    };
    mockCategoryService.createCategory.and.returnValue(throwError(() => errorResponse));
    component.categoryData.name = 'Test Category';

    component.onSubmit();

    expect(component.error()).toBe('Erreur lors de la création de la catégorie');
  });

  it('should handle unsuccessful response', () => {
    const mockResponse = {
      success: false,
      message: 'Failed to create',
      category: mockCategory
    };
    mockCategoryService.createCategory.and.returnValue(of(mockResponse));
    component.categoryData.name = 'Test Category';

    component.onSubmit();

    expect(component.error()).toBe('Erreur lors de la création de la catégorie');
    expect(component.success()).toBeNull();
  });

  it('should close modal on cancel', () => {
    component.onCancel();

    expect(mockDialogRef.close).toHaveBeenCalledWith();
  });

  it('should reset error and success messages on submit', () => {
    component.error.set('Previous error');
    component.success.set('Previous success');
    component.categoryData.name = '';

    component.onSubmit();

    expect(component.error()).toBe('Le nom de la catégorie est requis');
    expect(component.success()).toBeNull();
  });
});

import { Component, OnInit, OnD<PERSON>roy, signal, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { IconDirective } from '@coreui/icons-angular';
import { CategoryService } from '../../../../services/category.service';
import { Category } from '../../../../models/category.model';
import { AddCategoryComponent } from './add-category/add-category.component';
import { EditCategoryComponent } from './edit-category/edit-category.component';

@Component({
  selector: 'app-category',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconDirective
  ],
  templateUrl: './category.component.html',
  styleUrl: './category.component.scss'
})
export class CategoryComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Data
  allCategories: Category[] = [];
  filteredCategories: Category[] = [];
  
  // UI State
  loading = false;
  error: string | null = null;
  searchTerm = '';
  
  // Pagination
  currentPage = 1;
  itemsPerPage = 3; // Initialize to 3 as per user preference
  totalCategories = 0;
  totalPages = 0;
  availablePageSizes = [3, 5, 7, 9];
  
  // Menu state
  showCategoryMenu = signal<string | null>(null);
  activeCardMenu: string | null = null;

  constructor(
    private categoryService: CategoryService,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.loadCategories();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Load categories from service
  loadCategories() {
    this.loading = true;
    this.error = null;

    this.categoryService.getAllCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('✅ Categories loaded:', response);
          if (response.success) {
            this.allCategories = response.categories || [];
            this.totalCategories = this.allCategories.length;
            this.applyFilters();
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('❌ Error loading categories:', error);
          this.error = 'Erreur lors du chargement des catégories';
          this.loading = false;
        }
      });
  }

  // Apply search filter
  applyFilters() {
    let filtered = [...this.allCategories];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase().trim();
      filtered = filtered.filter(category =>
        category.name.toLowerCase().includes(searchLower)
      );
    }

    this.filteredCategories = filtered;
    this.totalCategories = filtered.length;
    this.updatePagination();
  }

  // Update pagination
  updatePagination() {
    this.totalPages = Math.ceil(this.totalCategories / this.itemsPerPage);
    if (this.currentPage > this.totalPages && this.totalPages > 0) {
      this.currentPage = this.totalPages;
    }
  }

  // Get paginated categories
  getPaginatedCategories(): Category[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredCategories.slice(startIndex, endIndex);
  }

  // Pagination methods
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  // Additional pagination methods to match other components
  goToFirstPage() {
    if (this.currentPage !== 1) {
      this.currentPage = 1;
    }
  }

  goToPreviousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  goToNextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  goToLastPage() {
    if (this.currentPage !== this.totalPages && this.totalPages > 0) {
      this.currentPage = this.totalPages;
    }
  }

  getTotalCount(): number {
    return this.totalCategories;
  }

  getEndItemNumber(): number {
    return Math.min(this.currentPage * this.itemsPerPage, this.totalCategories);
  }

  onPageSizeChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.itemsPerPage = parseInt(target.value, 10);
    this.currentPage = 1; // Reset to first page
    this.applyFilters(); // Reapply filters with new page size
  }

  // Search methods
  onSearchChange() {
    this.currentPage = 1;
    this.applyFilters();
  }

  clearSearch() {
    this.searchTerm = '';
    this.onSearchChange();
  }



  // Menu methods
  toggleCategoryMenu(categoryId: string, event: Event) {
    event.preventDefault();
    event.stopPropagation();

    if (this.showCategoryMenu() === categoryId) {
      this.showCategoryMenu.set(null);
    } else {
      this.showCategoryMenu.set(categoryId);
    }
  }

  // Close dropdowns when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.menu-container')) {
      this.showCategoryMenu.set(null);
    }
  }

  // CRUD operations
  addCategory() {
    const dialogRef = this.dialog.open(AddCategoryComponent, {
      width: '500px',
      height: '300px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      panelClass: 'custom-modal',
      disableClose: false,
      position: {
        left: 'calc(256px + (100vw - 256px - 500px) / 2)'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Category added:', result);
        this.loadCategories(); // Refresh the list
      }
    });
  }

  editCategory(category: Category) {
    console.log('Modifier la catégorie:', category);
    this.showCategoryMenu.set(null); // Close menu

    const dialogRef = this.dialog.open(EditCategoryComponent, {
      width: '500px',
      height: '300px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      data: { category: category },
      disableClose: false,
      panelClass: 'custom-modal',
      position: {
        left: 'calc(256px + (100vw - 256px - 500px) / 2)'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Category updated:', result);
        this.loadCategories(); // Refresh the list
      }
    });
  }

  deleteCategory(category: Category) {
    console.log('Supprimer la catégorie:', category);
    this.showCategoryMenu.set(null); // Close menu

    // Show confirmation dialog
    if (confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${category.name}" ?`)) {
      this.categoryService.deleteCategory(category.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('✅ Catégorie supprimée:', response);
            this.loadCategories(); // Refresh the list
          },
          error: (error) => {
            console.error('❌ Erreur lors de la suppression de la catégorie:', error);
            this.error = 'Erreur lors de la suppression de la catégorie';
          }
        });
    }
  }

  // Card menu methods
  toggleCardMenu(categoryId: string, event: Event) {
    event.stopPropagation();
    this.activeCardMenu = this.activeCardMenu === categoryId ? null : categoryId;
  }

  closeCardMenu() {
    this.activeCardMenu = null;
  }

  // Helper methods
  formatDate(date: string | Date | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('fr-FR');
  }

  // Get pagination info
  getPaginationInfo(): string {
    const start = (this.currentPage - 1) * this.itemsPerPage + 1;
    const end = Math.min(this.currentPage * this.itemsPerPage, this.totalCategories);
    return `${start}-${end} sur ${this.totalCategories}`;
  }

  // Get page numbers for pagination
  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    if (this.totalPages <= maxVisiblePages) {
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      const half = Math.floor(maxVisiblePages / 2);
      let start = Math.max(1, this.currentPage - half);
      let end = Math.min(this.totalPages, start + maxVisiblePages - 1);
      
      if (end - start < maxVisiblePages - 1) {
        start = Math.max(1, end - maxVisiblePages + 1);
      }
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }
    
    return pages;
  }
}

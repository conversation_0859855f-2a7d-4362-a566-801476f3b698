// Category Management Styles
.category-container {
  padding: 24px;
  background: #FFFFFF;
  min-height: 100vh;
  font-family: 'Nautica Rounded', sans-serif;

  // Header
  .category-header {
    margin-bottom: 24px;

    .page-title {
      font: normal normal 800 24px/29px 'Nautica Rounded', sans-serif;
      letter-spacing: 2.4px;
      color: #163659;
      text-align: left;
      margin: 0;
    }
  }

  // Header Controls (Search + Add Button)
  .header-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 20px;

    .search-container {
      flex: 1;
      max-width: 400px;

      .search-field {
        position: relative;

        .search-input {
          width: 100%;
          height: 40px;
          padding: 10px 40px 10px 40px;
          border: 1px solid #8AA3D5;
          border-radius: 5px;
          font-size: 14px;
          color: #163659;
          background: #FFFFFF;

          &::placeholder {
            color: #728A9B;
          }

          &:focus {
            outline: none;
            border-color: #1061AC;
          }
        }

        .search-icon {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          width: 16px;
          height: 16px;
          color: #728A9B;
          pointer-events: none;
        }

        .clear-search {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          cursor: pointer;
          color: #728A9B;
          padding: 0;
          width: 16px;
          height: 16px;

          &:hover {
            color: #163659;
          }
        }
      }
    }

    // Add Button (now part of header-controls)
    .add-button-container {
      .add-button {
        background: #1061AC;
        color: #FFFFFF;
        border: none;
        border-radius: 5px;
        padding: 12px 20px;
        font: normal normal 800 14px/17px 'Nautica Rounded', sans-serif;
        letter-spacing: 1.4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;

        &:hover {
          background: #0d4f8c;
          transform: translateY(-1px);
        }

        .add-icon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }



  // Loading State
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #728A9B;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #C7DAEC;
      border-top: 3px solid #1061AC;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    span {
      font-size: 16px;
    }
  }

  // Error State
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #F80D38;

    .error-icon {
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }

    span {
      font-size: 16px;
      margin-bottom: 16px;
    }

    .retry-button {
      background: #1061AC;
      color: #FFFFFF;
      border: none;
      border-radius: 5px;
      padding: 10px 20px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background: #0d4f8c;
      }
    }
  }

  // Table View
  .table-container {
    background: #FFFFFF;
    border: 1px solid #C7DAEC;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;

    .categories-table {
      width: 100%;
      border-collapse: collapse;

      thead {
        background: #F8F9FA;

        th {
          padding: 16px;
          text-align: left;
          font: normal normal 800 14px/17px 'Nautica Rounded', sans-serif;
          letter-spacing: 1.4px;
          color: #163659;
          border-bottom: 1px solid #C7DAEC;
        }
      }

      tbody {
        tr {
          border-bottom: 1px solid #C7DAEC;

          &:hover {
            background: #F5FFF9;
          }

          td {
            padding: 16px;
            vertical-align: middle;

            .category-name {
              font-weight: 600;
              color: #163659;
            }

            .date-text {
              color: #728A9B;
              font-size: 14px;
            }

            &.actions-cell {
              display: flex;
              gap: 8px;

              button {
                background: none;
                border: none;
                cursor: pointer;
                padding: 8px;
                border-radius: 4px;
                color: #728A9B;
                transition: all 0.2s ease;

                &:hover {
                  background: #F5FFF9;
                  color: #1061AC;
                }

                &:last-child:hover {
                  color: #F80D38;
                }

                svg {
                  width: 16px;
                  height: 16px;
                }
              }
            }

            &.no-data {
              text-align: center;
              color: #728A9B;
              font-style: italic;
              padding: 40px;
            }
          }
        }
      }
    }
  }



  // Pagination
  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    gap: 20px;

    // Items per page selector
    .items-per-page {
      display: flex;
      align-items: center;
      gap: 8px;

      .pagination-label {
        color: #728A9B;
        font-size: 14px;
        white-space: nowrap;
      }

      .page-size-selector {
        padding: 6px 12px;
        border: 1px solid #C7DAEC;
        border-radius: 5px;
        background: #FFFFFF;
        color: #163659;
        font-size: 14px;
        cursor: pointer;

        &:focus {
          outline: none;
          border-color: #1061AC;
        }
      }
    }

    .pagination-info {
      color: #728A9B;
      font-size: 14px;

      .pagination-text {
        white-space: nowrap;
      }
    }

    .pagination-controls {
      display: flex;
      gap: 8px;

      .pagination-btn {
        background: #FFFFFF;
        border: 1px solid #C7DAEC;
        color: #728A9B;
        padding: 8px 12px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s ease;
        min-width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover:not(:disabled) {
          background: #F5FFF9;
          border-color: #57B6B1;
          color: #163659;
        }

        &.active {
          background: #1061AC;
          border-color: #1061AC;
          color: #FFFFFF;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        svg {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive Design
@media (max-width: 768px) {
  .category-container {
    padding: 16px;

    .header-controls {
      flex-direction: column;
      gap: 16px;

      .search-container .search-field {
        max-width: 100%;
      }
    }

    .pagination-container {
      flex-direction: column;
      gap: 16px;
      text-align: center;

      .items-per-page {
        justify-content: center;
      }

      .pagination-controls {
        justify-content: center;
      }
    }

    .table-container {
      overflow-x: auto;

      .categories-table {
        min-width: 600px;
      }
    }
  }
}

/* Custom Modal Styles for Category Modals */
::ng-deep .custom-modal {
  z-index: 10000 !important;

  .mat-mdc-dialog-container {
    padding: 0 !important;
    background: transparent !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    overflow: hidden !important;
    width: 500px !important;
    height: 300px !important;
    max-width: 500px !important;
    max-height: 300px !important;
    z-index: 10001 !important;
  }

  .mat-mdc-dialog-surface {
    padding: 0 !important;
    background: transparent !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    overflow: hidden !important;
    width: 500px !important;
    height: 300px !important;
    max-width: 500px !important;
    max-height: 300px !important;
    z-index: 10002 !important;
  }
}

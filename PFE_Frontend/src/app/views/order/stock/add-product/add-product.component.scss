// Add Product component styles
.add-product-container {
  padding: 20px; // Reduced padding
  background-color: #FFFFFF; // Pure white background
  min-height: 100vh;
  font-family: 'Nautica Rounded', sans-serif;

  // Main panel styles (White Card Container)
  .add-product-panel {
    background-color: #FFFFFF;
    border: none; // Remove border
    border-radius: 0; // Remove border radius
    box-shadow: none; // Remove shadow
    overflow: visible;
    max-width: 1200px;
    margin: 0 auto;

    // Panel header
    .panel-header {
      padding: 20px 0 15px 0; // Reduced padding
      border-bottom: none;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-content {
        display: flex;
  flex-direction: column;
  align-items: flex-start;
        .panel-title {
      font-size: 20px;             /* Smaller than before */
      font-weight: 600;            /* Less heavy than 800 */
      color: #163659;
      margin: 0 0 2px 0;
       font-style: normal;          /* Remove italic */
}

      .panel-subtitle {
      font-size: 14px;
      font-weight: 400;
      color: #163659;
      margin: 0;
}

      }

      // Action buttons
      .action-buttons {
        display: flex;
        gap: 12px;

        .action-btn {
          width: 120px; // Fixed width
          height: 36px; // Fixed height
          padding: 0; // Remove padding since we have fixed dimensions
          border: none;
          border-radius: 8px;
          font-weight: 800;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;

          &.cancel {
            background-color: #F80D38;
            color: white;

            &:hover:not(:disabled) {
              background-color: #e00b32;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(248, 13, 56, 0.3);
            }
          }

          &.save {
            background-color: #57B6B1;
            color: white;

            &:hover:not(:disabled) {
              background-color: #4a9a96;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(87, 182, 177, 0.3);
            }
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
          }

          .loading-spinner {
            width: 14px;
            height: 14px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
        }
      }
    }

    // Separator line
    .separator-line {
      height: 1px;
      background-color: #E6EDF5;
      margin-bottom: 15px; // Reduced margin
    }

    // Toggle section (positioned to max right)
    .toggle-section {
      padding: 0 0 15px 0; // Reduced padding
      margin-bottom: 15px; // Reduced margin
      display: flex;
      justify-content: flex-end; // Position to max right

      .status-toggle {
        display: flex;
        align-items: center;
        gap: 12px;

        .toggle-label {
          font-size: 15px;
          font-weight: 600;
          color: #163659;
        }

        .toggle-switch {
          width: 48px;
          height: 24px;
          background-color: #C7DAEC;
          border-radius: 12px;
          position: relative;
          cursor: pointer;
          transition: background-color 0.3s ease;

          .toggle-slider {
            width: 20px;
            height: 20px;
            background-color: #FFFFFF;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }

          &.active {
            background-color: #57B6B1;

            .toggle-slider {
              transform: translateX(24px);
            }
          }
        }
      }
    }



    // Panel content
    .panel-content {
      padding: 20px 0; // Reduced padding significantly
      display: flex;
      gap: 40px; // Reduced gap
      align-items: flex-start;

      // Left column (smaller - 20% width)
      .left-column {
        flex: 0 0 20%;

        .image-upload-section {
          display: flex;
          flex-direction: column;
          gap: 16px;

          .image-placeholder {
            width: 100%;
            aspect-ratio: 1;
            background-color: #E6EDF5; // New background color
            border: 2px dashed #6D9ECC; // New border color
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 12px;
            position: relative;
            transition: all 0.2s ease;

            &:hover {
              border-color: #57B6B1;
              background-color: #ECEFF9;
            }

            &.has-image {
              border-style: solid;
              border-color: #6D9ECC; // New border color
              padding: 0;
              overflow: hidden;

              .image-container {
                position: relative;
                width: 100%;
                height: 100%;

                .preview-image {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }

                // Navigation Arrows
                .nav-arrow {
                  position: absolute;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 32px;
                  height: 32px;
                  background-color: rgba(0, 0, 0, 0.6);
                  border: none;
                  border-radius: 50%;
                  color: white;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  transition: all 0.2s ease;
                  z-index: 2;

                  &:hover:not(:disabled) {
                    background-color: rgba(0, 0, 0, 0.8);
                    transform: translateY(-50%) scale(1.1);
                  }

                  &:disabled {
                    opacity: 0.4;
                    cursor: not-allowed;
                  }

                  .arrow-icon {
                    width: 16px;
                    height: 16px;
                  }

                  &.nav-arrow-left {
                    left: 8px;
                  }

                  &.nav-arrow-right {
                    right: 8px;
                  }
                }

                // Image Counter
                .image-counter {
                  position: absolute;
                  bottom: 8px;
                  left: 50%;
                  transform: translateX(-50%);
                  background-color: rgba(0, 0, 0, 0.7);
                  color: white;
                  padding: 4px 8px;
                  border-radius: 12px;
                  font-size: 12px;
                  font-weight: 500;
                  z-index: 2;
                }

                .remove-image-btn {
                  position: absolute;
                  top: 8px;
                  right: 8px;
                  width: 28px;
                  height: 28px;
                  background-color: rgba(220, 53, 69, 0.9);
                  border: none;
                  border-radius: 50%;
                  color: white;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  transition: background-color 0.2s ease;
                  z-index: 3;

                  .remove-icon {
                    width: 14px;
                    height: 14px;
                  }

                  &:hover {
                    background-color: #dc3545;
                  }
                }
              }
            }

            .upload-icon {
              width: 48px;
              height: 48px;
              color: #728A9B;
            }

            .upload-text {
              color: #728A9B;
              font-size: 16px;
              font-weight: 600;
            }
          }

          .upload-button {
            background-color: #1061AC; // New button color
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 800;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.2s ease;

            .button-icon {
              width: 16px;
              height: 16px;
            }

            &:hover {
              background-color: #0d4f8c; // Darker blue for hover
              transform: translateY(-1px);
            }
          }
        }
      }

      // Right column (≈70% width)
      .right-column {
        flex: 1;

        .product-form {
          .form-row {
            display: flex;
            gap: 20px; // Reduced gap
            margin-bottom: 18px; // Reduced margin

            &.full-width {
              .form-group {
                flex: 1;
              }
            }

            .form-group {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 6px; // Reduced gap

              label {
                font-size: 14px; // Slightly smaller
                font-weight: 600;
                color: #163659;
                margin-bottom: 3px; // Reduced margin
              }

              .form-input, .form-select, .form-textarea {
                padding: 10px 14px; // Reduced padding
                border: 1px solid #8AA3D5; // New border color
                border-radius: 8px; // Slightly smaller radius
                font-size: 14px;
                background-color: #FFFFFF;
                transition: border-color 0.2s ease;

                &::placeholder {
                  color: #728A9B;
                }

                &:focus {
                  outline: none;
                  border-color: #57B6B1;
                }

                &:invalid {
                  border-color: #dc3545;
                }
              }

              // Custom dropdown styles (exact prescription style)
              .custom-dropdown {
                position: relative;
                min-width: 150px;

                .dropdown-toggle {
                  width: 100%;
                  padding: 0.5rem 0.75rem;
                  border: 1px solid #8AA3D5; // New border color
                  border-radius: 6px;
                  background: white;
                  font-size: 0.875rem;
                  color: #163659;
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  text-align: left;

                  &:focus {
                    outline: none;
                    border-color: #1061AC;
                    box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
                  }

                  &:hover {
                    border-color: #57B6B1;
                    background-color: #F5FFF9;
                  }

                  .dropdown-icon {
                    width: 12px;
                    height: 12px;
                    transition: transform 0.2s ease;

                    &.rotated {
                      transform: rotate(180deg);
                    }
                  }
                }

                // ✅ White background dropdown menu (exact copy from prescriptions)
                .dropdown-menu {
                  position: absolute;
                  top: 100%;
                  left: 0;
                  right: 0;
                  background: #FFFFFF !important;
                  border: 2px solid #C7DAEC !important;
                  border-radius: 8px;
                  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25) !important;
                  z-index: 99999 !important;
                  margin-top: 4px;
                  min-width: 200px !important;
                  max-height: 350px !important;
                  overflow-y: auto;
                  display: block !important;
                  visibility: visible !important;

                  .dropdown-item {
                    display: flex !important;
                    align-items: center;
                    padding: 0.75rem 1rem !important; // Reduced padding
                    cursor: pointer;
                    transition: all 0.2s ease;
                    border-bottom: 1px solid #e0e0e0 !important;
                    min-height: 40px !important; // Reduced height
                    background-color: #FFFFFF !important;

                    &:hover {
                      background-color: #F0F8FF !important;
                    }

                    &.selected {
                      background-color: #E8F4FD !important;
                      border-left: 3px solid #1061AC !important;
                      padding-left: 0.75rem !important;

                      .option-text {
                        color: #1061AC !important;
                        font-weight: 600 !important;
                      }
                    }

                    .option-text {
                      font-size: 0.875rem !important; // Smaller font
                      color: #163659 !important;
                      font-weight: 500 !important;
                      flex: 1;
                      line-height: 1.4;
                    }

                    &:first-child {
                      border-radius: 8px 8px 0 0;
                    }

                    &:last-child {
                      border-radius: 0 0 8px 8px;
                      border-bottom: none !important;
                    }
                  }
                }
              }

              .form-textarea {
                resize: vertical;
                min-height: 100px;
                font-family: inherit;
              }

              .form-select {
                cursor: pointer;
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                background-position: right 12px center;
                background-repeat: no-repeat;
                background-size: 16px;
                padding-right: 40px;
                appearance: none;
              }
            }
          }
        }
      }
    }

    // Messages
    .message {
      margin: 20px 30px;
      padding: 12px 16px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 14px;
      font-weight: 600;

      .message-icon {
        width: 20px;
        height: 20px;
        flex-shrink: 0;
      }

      &.error-message {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;

        .message-icon {
          color: #721c24;
        }
      }

      &.success-message {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;

        .message-icon {
          color: #155724;
        }
      }
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 20px 10px;

    .add-product-panel {
      .panel-header {
        padding: 20px 25px;
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;

        .panel-title {
          font-size: 28px;
        }

        .header-actions {
          width: 100%;
          justify-content: space-between;
          gap: 15px;

          .action-buttons {
            gap: 10px;

            .action-btn {
              padding: 10px 18px;
              font-size: 13px;
            }
          }
        }
      }

      .panel-content {
        padding: 30px 25px;
        flex-direction: column;
        gap: 30px;

        .left-column {
          flex: none;
        }

        .right-column {
          .product-form {
            .form-row {
              flex-direction: column;
              gap: 20px;
            }
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    padding: 15px 5px;

    .add-product-panel {
      border-radius: 10px;

      .panel-header {
        padding: 15px 20px;

        .panel-title {
          font-size: 24px;
        }

        .header-actions {
          .action-buttons .action-btn {
            padding: 8px 15px;
            font-size: 12px;
          }
        }
      }

      .panel-content {
        padding: 25px 20px;
        gap: 25px;
      }
    }
  }
}
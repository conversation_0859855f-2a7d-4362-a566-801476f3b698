<div class="add-product-container">
  <!-- Main Panel (White Card Container) -->
  <div class="add-product-panel">
    <!-- Panel Header -->
    <div class="panel-header">
      <div class="header-content">
        <h1 class="panel-title">Ajouter un article</h1>
        <p class="panel-subtitle">Remplir les champs nécessaires</p>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button class="action-btn cancel" (click)="onCancel()" [disabled]="isSubmitting()">
          Abandonner
        </button>
        <button class="action-btn save" (click)="onSubmit()" [disabled]="isSubmitting()">
          @if (isSubmitting()) {
            <div class="loading-spinner"></div>
            Enregistrement...
          } @else {
            Enregistrer
          }
        </button>
      </div>
    </div>

    <!-- Separator Line -->
    <div class="separator-line"></div>

    <!-- Toggle Section -->
    <div class="toggle-section">
      <div class="status-toggle">
        <label class="toggle-label">État (Actif/Inactif)</label>
        <div class="toggle-switch" [class.active]="productData.actif" (click)="toggleActiveStatus()">
          <div class="toggle-slider"></div>
        </div>
      </div>
    </div>

    <!-- Panel Content -->
    <div class="panel-content">
      <!-- Left Column - Image Upload -->
      <div class="left-column">
        <div class="image-upload-section">
          <!-- Image Preview or Placeholder -->
          <div class="image-placeholder" [class.has-image]="hasImages()">
            @if (hasImages()) {
              <!-- Image Container with Navigation -->
              <div class="image-container">
                <img [src]="getCurrentImageUrl()" alt="Product preview" class="preview-image">

                <!-- Navigation Arrows -->
                @if (getTotalImages() > 1) {
                  <button
                    class="nav-arrow nav-arrow-left"
                    (click)="previousImage()"
                    [disabled]="!canNavigatePrevious()"
                    type="button">
                    <svg cIcon name="cil-chevron-left" class="arrow-icon"></svg>
                  </button>
                  <button
                    class="nav-arrow nav-arrow-right"
                    (click)="nextImage()"
                    [disabled]="!canNavigateNext()"
                    type="button">
                    <svg cIcon name="cil-chevron-right" class="arrow-icon"></svg>
                  </button>

                  <!-- Image Counter -->
                  <div class="image-counter">{{ getImageCounter() }}</div>
                }

                <!-- Remove Current Image Button -->
                <button class="remove-image-btn" (click)="removeCurrentImage()" type="button">
                  <svg cIcon name="cil-x" class="remove-icon"></svg>
                </button>
              </div>
            } @else {
              <svg cIcon name="cil-cloud-upload" class="upload-icon"></svg>
              <span class="upload-text">Ajouter des images</span>
            }
          </div>

          <!-- Upload Button -->
          <button class="upload-button" type="button" (click)="fileInput.click()">
            <svg cIcon name="cil-plus" class="button-icon"></svg>
            Ajouter des images
          </button>

          <!-- Hidden File Input (Multiple) -->
          <input
            #fileInput
            id="imageInput"
            type="file"
            accept="image/*"
            multiple
            (change)="onImageSelected($event)"
            style="display: none;"
          >
        </div>
      </div>

      <!-- Right Column - Form Fields -->
      <div class="right-column">
        <form class="product-form" (ngSubmit)="onSubmit()">
          <!-- First Row -->
          <div class="form-row">
            <div class="form-group">
              <label for="productName">Nom du produit</label>
              <input
                id="productName"
                type="text"
                [(ngModel)]="productData.name"
                name="productName"
                placeholder="Nom du produit"
                class="form-input"
                required
              >
            </div>

            <div class="form-group">
              <label for="category">Catégorie</label>
              <div class="custom-dropdown">
                <button type="button" class="dropdown-toggle" (click)="toggleCategoryDropdown($event)">
                  <span>{{ getSelectedCategoryText() }}</span>
                  <!-- <svg cIcon name="cil-chevron-bottom" class="dropdown-icon" [class.rotated]="showCategoryDropdown()"></svg> -->
                </button>
                @if (showCategoryDropdown()) {
                  <div class="dropdown-menu">
                    @for (category of categories; track category.id) {
                      <div class="dropdown-item" (click)="selectCategory(category.id)" [class.selected]="productData.categoryId === category.id">
                        <span class="option-text">{{ category.name }}</span>
                      </div>
                    }
                  </div>
                }
              </div>
            </div>
          </div>

          <!-- Second Row -->
          <div class="form-row">
            <div class="form-group">
              <label for="quantity">Quantité</label>
              <input
                id="quantity"
                type="number"
                [(ngModel)]="productData.quantity"
                name="quantity"
                placeholder="Quantité"
                class="form-input"
                min="0"
                required
              >
            </div>

            <div class="form-group">
              <label for="price">Prix</label>
              <input
                id="price"
                type="number"
                [(ngModel)]="productData.price"
                name="price"
                placeholder="Prix"
                class="form-input"
                min="0"
                step="0.01"
                required
              >
            </div>
          </div>

          <!-- Third Row - Full Width -->
          <div class="form-row full-width">
            <div class="form-group">
              <label for="description">Description</label>
              <textarea
                id="description"
                [(ngModel)]="productData.description"
                name="description"
                placeholder="Description"
                class="form-textarea"
                rows="4"
              ></textarea>
            </div>
          </div>

          <!-- Additional Fields Row -->
          <div class="form-row">
            <div class="form-group">
              <label for="discount">Remise (%)</label>
              <input
                id="discount"
                type="number"
                [(ngModel)]="productData.discount"
                name="discount"
                placeholder="0"
                class="form-input"
                min="0"
                max="100"
              >
            </div>

            <div class="form-group">
              <!-- Empty space to maintain layout -->
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Messages -->
    @if (error()) {
      <div class="message error-message">
        <svg cIcon name="cil-warning" class="message-icon"></svg>
        {{ error() }}
      </div>
    }

    @if (success()) {
      <div class="message success-message">
        <svg cIcon name="cil-check-circle" class="message-icon"></svg>
        {{ success() }}
      </div>
    }
  </div>
</div>

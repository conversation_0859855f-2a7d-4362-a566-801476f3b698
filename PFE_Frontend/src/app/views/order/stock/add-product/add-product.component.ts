import { Component, OnInit, OnD<PERSON>roy, signal, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { IconDirective } from '@coreui/icons-angular';
import { ProductService } from '../../../../services/product.service';
import { CategoryService } from '../../../../services/category.service';
import { CreateProductRequest, StockStatus } from '../../../../models/product.model';
import { Category } from '../../../../models/category.model';

@Component({
  selector: 'app-add-product',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconDirective
  ],
  templateUrl: './add-product.component.html',
  styleUrl: './add-product.component.scss'
})
export class AddProductComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Form data
  productData: CreateProductRequest = {
    name: '',
    description: '',
    quantity: 0,
    price: 0,
    discount: 0,
    categoryId: '',
    actif: true
    // stockStatus is handled automatically by backend based on quantity
  };

  // Categories
  categories: Category[] = [];

  // Multiple images upload
  selectedFiles: File[] = [];
  imagePreviews: string[] = [];
  currentImageIndex = signal(0);
  uploadProgress = signal(0);
  isUploading = signal(false);

  // Form state
  isSubmitting = signal(false);
  error = signal<string | null>(null);
  success = signal<string | null>(null);

  // Dropdown states
  showCategoryDropdown = signal(false);

  // Stock status options (not used in form since backend handles this automatically)
  stockStatusOptions = [
    { value: StockStatus.IN_STOCK, label: 'En stock' },
    { value: StockStatus.OUT_OF_STOCK, label: 'Rupture de stock' }
  ];

  constructor(
    private router: Router,
    private productService: ProductService,
    private categoryService: CategoryService
  ) {}

  ngOnInit() {
    this.loadCategories();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Load categories for dropdown
  loadCategories() {
    this.categoryService.getAllCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.categories = response.categories || [];
          }
        },
        error: (error) => {
          console.error('Error loading categories:', error);
          this.error.set('Erreur lors du chargement des catégories');
        }
      });
  }

  // Image upload methods
  onImageSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const files = Array.from(input.files);

      for (const file of files) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          this.error.set('Veuillez sélectionner uniquement des fichiers image valides');
          continue;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          this.error.set('La taille des fichiers ne doit pas dépasser 5MB chacun');
          continue;
        }

        this.selectedFiles.push(file);
        this.error.set(null);

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
          this.imagePreviews.push(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      }
    }
  }

  removeCurrentImage() {
    const currentIndex = this.currentImageIndex();

    if (currentIndex < this.imagePreviews.length) {
      this.imagePreviews.splice(currentIndex, 1);
      this.selectedFiles.splice(currentIndex, 1);
    }

    // Adjust current index if necessary
    if (currentIndex >= this.imagePreviews.length && currentIndex > 0) {
      this.currentImageIndex.set(currentIndex - 1);
    }

    // Reset file input if no more images
    if (this.imagePreviews.length === 0) {
      const fileInput = document.getElementById('imageInput') as HTMLInputElement;
      if (fileInput) {
        fileInput.value = '';
      }
    }
  }

  // Navigation methods
  previousImage() {
    const currentIndex = this.currentImageIndex();
    if (currentIndex > 0) {
      this.currentImageIndex.set(currentIndex - 1);
    }
  }

  nextImage() {
    const currentIndex = this.currentImageIndex();
    if (currentIndex < this.imagePreviews.length - 1) {
      this.currentImageIndex.set(currentIndex + 1);
    }
  }

  // Form submission
  onSubmit() {
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting.set(true);
    this.error.set(null);
    this.success.set(null);

    if (this.selectedFiles.length > 0) {
      // Upload images and create product
      this.uploadImagesAndCreateProduct();
    } else {
      // Create product without images
      this.productService.createProduct(this.productData)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('✅ Product created:', response);
            this.success.set('Produit créé avec succès!');
            this.isSubmitting.set(false);

            // Navigate back to stock list after 2 seconds
            setTimeout(() => {
              this.router.navigate(['/orders/stock']);
            }, 2000);
          },
          error: (error) => {
            console.error('❌ Error creating product:', error);
            this.error.set('Erreur lors de la création du produit');
            this.isSubmitting.set(false);
          }
        });
    }
  }

  private uploadImagesAndCreateProduct() {
    const uploadPromises = this.selectedFiles.map(file =>
      this.productService.uploadProductImage(file).toPromise()
    );

    Promise.all(uploadPromises)
      .then(responses => {
        const imagePaths = responses
          .filter(response => response?.filePath)
          .map(response => response!.filePath as string);

        const productDataWithImages = {
          ...this.productData,
          storagePath: imagePaths
        };

        return this.productService.createProduct(productDataWithImages).toPromise();
      })
      .then(response => {
        console.log('✅ Product created with images:', response);
        this.success.set('Produit créé avec succès!');
        this.isSubmitting.set(false);

        // Navigate back to stock list after 2 seconds
        setTimeout(() => {
          this.router.navigate(['/orders/stock']);
        }, 2000);
      })
      .catch(error => {
        console.error('❌ Error creating product with images:', error);
        this.error.set('Erreur lors de la création du produit avec images');
        this.isSubmitting.set(false);
      });
  }

  // Form validation
  validateForm(): boolean {
    if (!this.productData.name.trim()) {
      this.error.set('Le nom du produit est requis');
      return false;
    }

    if (!this.productData.categoryId) {
      this.error.set('La catégorie est requise');
      return false;
    }

    if (this.productData.quantity < 0) {
      this.error.set('La quantité ne peut pas être négative');
      return false;
    }

    if (this.productData.price <= 0) {
      this.error.set('Le prix doit être supérieur à 0');
      return false;
    }

    if (this.productData.discount && (this.productData.discount < 0 || this.productData.discount > 100)) {
      this.error.set('La remise doit être entre 0 et 100%');
      return false;
    }

    return true;
  }

  // Navigation methods
  goBack() {
    this.router.navigate(['/orders/stock']);
  }

  onCancel() {
    if (confirm('Êtes-vous sûr de vouloir abandonner? Toutes les modifications seront perdues.')) {
      this.goBack();
    }
  }

  // Toggle active status
  toggleActiveStatus() {
    this.productData.actif = !this.productData.actif;
  }

  // Dropdown methods (using prescription pattern)
  toggleCategoryDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showCategoryDropdown.set(!this.showCategoryDropdown());
  }

  // Close dropdowns when clicking outside (prescription pattern)
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.custom-dropdown')) {
      this.showCategoryDropdown.set(false);
    }
  }

  // Helper methods for multiple images
  getTotalImages(): number {
    return this.imagePreviews.length;
  }

  getCurrentImageUrl(): string | null {
    const currentIndex = this.currentImageIndex();
    return this.imagePreviews[currentIndex] || null;
  }

  hasImages(): boolean {
    return this.imagePreviews.length > 0;
  }

  canNavigatePrevious(): boolean {
    return this.currentImageIndex() > 0;
  }

  canNavigateNext(): boolean {
    return this.currentImageIndex() < this.imagePreviews.length - 1;
  }

  getImageCounter(): string {
    const current = this.currentImageIndex() + 1;
    const total = this.imagePreviews.length;
    return `${current}/${total}`;
  }

  selectCategory(categoryId: string) {
    this.productData.categoryId = categoryId;
    this.showCategoryDropdown.set(false);
  }

  getSelectedCategoryText(): string {
    if (!this.productData.categoryId) return 'Sélectionner une catégorie';
    const category = this.categories.find(c => c.id === this.productData.categoryId);
    return category ? category.name : 'Sélectionner une catégorie';
  }

  // Helper methods
  getStockStatusLabel(status: StockStatus): string {
    const option = this.stockStatusOptions.find(opt => opt.value === status);
    return option ? option.label : 'Inconnu';
  }
}

// Login page styles
.login-container {
  min-height: 100vh;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

.login-wrapper {
  width: 100%;
  max-width: 1200px;
  height: 100vh;
  display: flex;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  background: #FFFFFF;
}

// Left side - Image section
.login-image-section {
  flex: 1;
  background: linear-gradient(135deg, #B8E6FF 0%, #D4EDFF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;

  .image-container {
    max-width: 100%;
    text-align: center;
  }

  .login-image {
    max-width: 100%;
    height: auto;
    max-height: 80vh;
    object-fit: contain;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));
  }
}

// Right side - Form section
.login-form-section {
  flex: 1;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

// Logo styling
.logo-container {
  text-align: center;
  margin-bottom: 40px;

  .login-logo {
    max-width: 200px;
    height: auto;
  }
}

// Form styling
.login-form {
  width: 100%;

  .error-message {
    background: #FFF5F5;
    border: 1px solid #FEB2B2;
    color: #C53030;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    font-size: 14px;
    text-align: center;
  }

  .input-group {
    margin-bottom: 20px;

    .form-input {
      width: 100%;
      padding: 12px 24px;
      border: 1px solid #E2E8F0;
      border-radius: 12px;
      font-size: 16px;
      font-family: 'Nautica Rounded', sans-serif;
      color: #2D3748;
      background: #FFFFFF;
      transition: all 0.3s ease;

      &::placeholder {
        color: #A0AEC0;
        font-weight: normal;
      }

      &:focus {
        outline: none;
        border-color: #1061AC;
        box-shadow: 0 0 0 3px rgba(16, 97, 172, 0.1);
      }

      &.error {
        border-color: #E53E3E;
        box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
      }
    }

    .password-input-wrapper {
      position: relative;
      width: 100%;

      .password-input {
        width: 100%;
        padding-right: 56px;
      }

      .password-toggle {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        .eye-icon {
          width: 20px;
          height: 20px;
          opacity: 0.6;
          transition: opacity 0.2s ease;
        }

        &:hover .eye-icon {
          opacity: 1;
        }
      }
    }

    .field-error {
      color: #E53E3E;
      font-size: 12px;
      margin-top: 6px;
      margin-left: 4px;
    }
  }

  .login-button {
    width: 100%;
    padding: 14px 32px;
    background: linear-gradient(to right, #2d67ad, #4d95ae, #2d67ad);

    border: none;
    border-radius: 12px;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 800;
    font-family: 'Nautica Rounded', sans-serif;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-height: 52px;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(16, 97, 172, 0.3);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:disabled {
     // opacity: 0.7;
      cursor: not-allowed;
      transform: none;
    }

    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid #FFFFFF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  .forgot-password {
    text-align: left;
    margin-bottom: 24px;

    .forgot-link {
      color: #1061AC;
      text-decoration: none;
      font-size: 16px;
      font-family: 'Nautica Rounded', sans-serif;
      transition: color 0.2s ease;

      &:hover {
        color: #57B6B1;
        text-decoration: underline;
      }
    }
  }
}

// Loading spinner animation
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .login-wrapper {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .login-image-section {
    flex: none;
    height: 40vh;
    padding: 20px;

    .login-image {
      max-height: 30vh;
    }
  }

  .login-form-section {
    flex: none;
    padding: 20px;
  }

  .form-container {
    max-width: 100%;
  }

  .logo-container {
    margin-bottom: 30px;

    .login-logo {
      max-width: 150px;
    }
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 0;
  }

  .login-wrapper {
    box-shadow: none;
  }

  .login-image-section {
    height: 30vh;
    padding: 15px;

    .login-image {
      max-height: 25vh;
    }
  }

  .login-form-section {
    padding: 15px;
  }

  .login-form {
    .form-input {
      padding: 10px 20px;
      font-size: 15px;
    }

    .login-button {
      padding: 12px 28px;
      font-size: 15px;
      min-height: 48px;
    }
  }
}
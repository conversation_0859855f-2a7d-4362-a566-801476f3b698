<div class="login-container">
  <div class="login-wrapper">
    <!-- Left Side: Image -->
    <div class="login-image-section">
      <div class="image-container">
        <img src="assets/images/med4-screens.png" alt="Med4 Solutions" class="login-image">
      </div>
    </div>

    <!-- Right Side: Login Form -->
    <div class="login-form-section">
      <div class="form-container">
        <!-- Logo -->
        <div class="logo-container">
          <img src="assets/images/med4-logo-login.png" alt="Med4 Solutions Logo" class="login-logo">
        </div>

        <!-- Login Form -->
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
          <!-- Error Message -->
          @if (error) {
            <div class="error-message">
              {{ error }}
            </div>
          }

          <!-- Email Input -->
          <div class="input-group">
            <input
              type="email"
              formControlName="email"
              placeholder="Courriel"
              class="form-input"
              [class.error]="isFieldInvalid('email')"
              autoComplete="email"
            />
            @if (isFieldInvalid('email')) {
              <div class="field-error">
                {{ getFieldError('email') }}
              </div>
            }
          </div>

          <!-- Password Input -->
          <div class="input-group">
            <div class="password-input-wrapper">
              <input
                [type]="showPassword ? 'text' : 'password'"
                formControlName="password"
                placeholder="Mot de passe"
                class="form-input password-input"
                [class.error]="isFieldInvalid('password')"
                autoComplete="current-password"
              />
              <button
                type="button"
                class="password-toggle"
                (click)="togglePasswordVisibility()"
                tabindex="-1"
              >
                <img src="assets/images/EyeIcon.svg" alt="Toggle password visibility" class="eye-icon">
              </button>
            </div>
            @if (isFieldInvalid('password')) {
              <div class="field-error">
                {{ getFieldError('password') }}
              </div>
            }
          </div>

          <!-- Forgot Password Link -->
          <div class="forgot-password">
            <a href="#" class="forgot-link">Mot de passe oublié ?</a>
          </div>

          <!-- Login Button -->
          <button
            type="submit"
            class="login-button"
            [disabled]="loading || loginForm.invalid"
          >
            @if (loading) {
              <span class="loading-spinner"></span>
              Connexion...
            } @else {
              Connexion
            }
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

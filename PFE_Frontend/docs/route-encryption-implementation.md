# Route Parameter Encryption Implementation

## Overview

This document describes the implementation of route parameter encryption for securing sensitive IDs in URL routes within the Angular application. The encryption prevents users from easily guessing or manipulating IDs in the browser's address bar.

## Security Benefits

1. **ID Obfuscation**: Prevents users from seeing actual database IDs
2. **URL Manipulation Prevention**: Makes it difficult to guess other valid IDs
3. **Enhanced Privacy**: Sensitive information is not exposed in browser history or logs
4. **Professional Appearance**: URLs look more professional without exposing internal IDs

## Implementation Details

### RouteEncryptionService

The `RouteEncryptionService` provides encryption and decryption functionality:

- **Location**: `src/app/services/route-encryption.service.ts`
- **Encryption Method**: XOR cipher with base64 encoding
- **URL Safety**: Converts base64 to URL-safe format (replaces +, /, = characters)

#### Key Methods:

```typescript
encryptId(id: string): string
decryptId(encryptedId: string): string | null
isEncrypted(value: string): boolean
```

### Route Configuration Changes

#### Before:
```typescript
// Prescription routes
{ path: 'details/:id', ... }

// Order routes  
{ path: 'order-details/:id', ... }
```

#### After:
```typescript
// Prescription routes
{ path: 'details/:encryptedId', ... }

// Order routes
{ path: 'order-details/:encryptedId', ... }
```

### Component Updates

#### Navigation Components (Prescriptions & Orders)

**Changes Made:**
1. Import `RouteEncryptionService`
2. Inject service in constructor
3. Update `viewDetails()` method to encrypt IDs before navigation

**Example:**
```typescript
viewDetails(item: Prescription | Order) {
  const encryptedId = this.routeEncryptionService.encryptId(item.id);
  this.router.navigate(['/path/details', encryptedId]);
}
```

#### Detail Components (PrescriptionDetails & OrderDetails)

**Changes Made:**
1. Import `RouteEncryptionService`
2. Inject service in constructor/inject
3. Update route parameter extraction to decrypt IDs

**Example:**
```typescript
ngOnInit() {
  const encryptedId = this.route.snapshot.paramMap.get('encryptedId');
  if (encryptedId) {
    this.itemId = this.routeEncryptionService.decryptId(encryptedId);
    this.loadItemData();
  }
}
```

## Files Modified

### Services
- `src/app/services/route-encryption.service.ts` (NEW)
- `src/app/services/route-encryption.service.spec.ts` (NEW)

### Routes
- `src/app/views/prescriptions/routes.ts`
- `src/app/views/order/routes.ts`

### Components
- `src/app/views/prescriptions/prescriptions.component.ts`
- `src/app/views/prescriptions/details-prescription/details-prescription.component.ts`
- `src/app/views/order/order.component.ts`
- `src/app/views/order/order-details/order-details.component.ts`

## URL Examples

### Before Encryption:
```
/prescriptions/details/12345
/orders/order-details/67890
```

### After Encryption:
```
/prescriptions/details/QmxhY2tCb3g_
/orders/order-details/V2hpdGVCb3g-
```

## Error Handling

The service includes robust error handling:

1. **Encryption Failures**: Falls back to original ID
2. **Decryption Failures**: Returns the encrypted string as fallback
3. **Invalid Input**: Handles empty strings and malformed data gracefully

## Testing

Comprehensive test suite covers:
- Basic encryption/decryption functionality
- Different ID formats
- URL safety validation
- Error handling scenarios
- Edge cases

Run tests with:
```bash
ng test --include="**/route-encryption.service.spec.ts"
```

## Security Considerations

### Strengths:
- Prevents casual ID manipulation
- Obfuscates internal database structure
- URL-safe encoding
- Graceful fallback handling

### Limitations:
- Client-side encryption (not cryptographically secure)
- Determined attackers could reverse-engineer the algorithm
- Should be combined with proper server-side authorization

### Recommendations:
1. Always validate permissions on the server side
2. Implement proper authentication and authorization
3. Consider this as an additional security layer, not the primary one
4. Monitor for suspicious access patterns

## Future Enhancements

1. **Server-side Token Generation**: Generate encrypted tokens on the server
2. **Time-based Expiration**: Add timestamp-based token expiration
3. **User-specific Encryption**: Use user-specific keys for encryption
4. **Audit Logging**: Log access attempts with encrypted IDs

## Maintenance

- The encryption key is stored in the service (consider environment-based keys)
- Monitor performance impact of encryption/decryption operations
- Update tests when modifying the encryption algorithm
- Document any changes to the encryption method

{"cells": [{"cell_type": "code", "execution_count": 1, "id": "edcdf3e9-b449-42a2-af34-6dba5488a090", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: easyocr in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (1.7.1)\n", "Requirement already satisfied: opencv-python in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (4.11.0.86)\n", "Requirement already satisfied: matplotlib in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (3.8.4)\n", "Requirement already satisfied: numpy in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (1.26.4)\n", "Requirement already satisfied: torch in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (2.3.0)\n", "Requirement already satisfied: torchvision>=0.5 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (0.18.0)\n", "Requirement already satisfied: opencv-python-headless in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (********)\n", "Requirement already satisfied: scipy in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (1.13.0)\n", "Requirement already satisfied: Pillow in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (10.3.0)\n", "Requirement already satisfied: scikit-image in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (0.23.2)\n", "Requirement already satisfied: python-bidi in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (0.4.2)\n", "Requirement already satisfied: PyYAML in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (6.0.1)\n", "Requirement already satisfied: Shapely in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (2.0.4)\n", "Requirement already satisfied: pyclipper in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (1.3.0.post5)\n", "Requirement already satisfied: ninja in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from easyocr) (********)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from matplotlib) (1.2.1)\n", "Requirement already satisfied: cycler>=0.10 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from matplotlib) (4.51.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from matplotlib) (1.4.5)\n", "Requirement already satisfied: packaging>=20.0 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from matplotlib) (24.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from matplotlib) (3.1.2)\n", "Requirement already satisfied: python-dateutil>=2.7 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: six>=1.5 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from python-dateutil>=2.7->matplotlib) (1.16.0)\n", "Requirement already satisfied: filelock in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from torch->easyocr) (3.14.0)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from torch->easyocr) (4.11.0)\n", "Requirement already satisfied: sympy in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from torch->easyocr) (1.12)\n", "Requirement already satisfied: networkx in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from torch->easyocr) (3.3)\n", "Requirement already satisfied: jinja2 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from torch->easyocr) (3.1.4)\n", "Requirement already satisfied: fsspec in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from torch->easyocr) (2024.3.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from jinja2->torch->easyocr) (2.1.5)\n", "Requirement already satisfied: imageio>=2.33 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from scikit-image->easyocr) (2.34.1)\n", "Requirement already satisfied: tifffile>=2022.8.12 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from scikit-image->easyocr) (2024.5.3)\n", "Requirement already satisfied: lazy-loader>=0.4 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from scikit-image->easyocr) (0.4)\n", "Requirement already satisfied: mpmath>=0.19 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from sympy->torch->easyocr) (1.3.0)\n"]}], "source": ["!pip install easyocr opencv-python matplotlib numpy\n"]}, {"cell_type": "code", "execution_count": 2, "id": "52bce94d-6fcd-40e6-87b2-7ef83da23622", "metadata": {}, "outputs": [], "source": ["import easyocr\n", "import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n"]}, {"cell_type": "code", "execution_count": 42, "id": "fc927920-2e22-46e5-afc4-14256cfefd41", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgMAAAEXCAYAAAAwdEdHAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8fJSN1AAAACXBIWXMAAA9hAAAPYQGoP6dpAAA0qklEQVR4nO2de6ylVX33f/t27nPOmTNXGGBghBdDyh8isVbFSqsh6pRLK21anDBKvfRiNRESY4ugbVFspsEQSak1gqESA0VjQ6DxbcdSUtOkNlyCNXR04FVgbucyc86Zc9t7P+8fvJx31nf92OvZm30uw/P5/DXP3s9eaz1rrec5a57vd/1+pSzLMgMAAIDCUl7rBgAAAMDawmIAAACg4LAYAAAAKDgsBgAAAAoOiwEAAICCw2IAAACg4LAYAAAAKDgsBgAAAAoOiwEAAICCw2IAYBW49dZbrVQqdfTbe+65x0qlkj333HPdbdQpPPfcc1Yqleyee+5ZsToAYP3CYgCgBc8884x98IMftB07dlhvb6+deeaZdt1119kzzzyz1k1bE37wgx9YqVSyBx98cK2bAgBdhMUAwKvw0EMP2SWXXGL//M//bB/60IfsrrvushtuuMH2799vl1xyiX3nO9/JXdaf/dmf2dzcXEft2LNnj83NzdnOnTs7+j0AQIrqWjcAYD3y05/+1Pbs2WO7du2yxx57zLZs2bL83Sc/+Um77LLLbM+ePfbUU0/Zrl27XrWc2dlZGxwctGq1atVqZ7dbpVKxSqXS0W8BAPLAmwEAh7/6q7+ykydP2t/+7d8GCwEzs82bN9vdd99ts7Oz9uUvf3n581d8AT/+8Y/t937v92zjxo32jne8I/juVObm5uxP/uRPbPPmzbZhwwa78sor7YUXXrBSqWS33nrr8nmeZ+Dcc8+13bt32+OPP25vectbrK+vz3bt2mXf/OY3gzomJibsxhtvtIsvvtiGhoZseHjY3vve99qTTz7ZpZ76/9f27LPP2gc/+EEbGRmxLVu22M0332xZltnPf/5zu+qqq2x4eNi2b99u+/btC36/uLhon/vc5+zNb36zjYyM2ODgoF122WW2f//+qK7x8XHbs2ePDQ8P2+joqF1//fX25JNPun6Hn/zkJ/aBD3zAxsbGrK+vzy699FL73ve+17XrBng9wWIAwOEf//Ef7dxzz7XLLrvM/f6d73ynnXvuufbwww9H31177bV28uRJu+222+wjH/nIq9axd+9eu/POO+1973uf3X777dbf32/vf//7c7fxwIED9oEPfMDe85732L59+2zjxo22d+/ewM/ws5/9zL773e/a7t277a//+q/tpptusqefftp+9Vd/1V588cXcdeXhd37nd6zZbNqXvvQl++Vf/mX7i7/4C7vjjjvsPe95j+3YscNuv/12O//88+3GG2+0xx57bPl3J06csL/7u7+zd73rXXb77bfbrbfeakePHrUrrrjCnnjiieXzms2m/cZv/Ibdf//9dv3119tf/uVf2ksvvWTXX3991JZnnnnG3vrWt9p///d/22c+8xnbt2+fDQ4O2tVXX92WvANQGDIACJiamsrMLLvqqqtannfllVdmZpadOHEiy7Isu+WWWzIzy373d383OveV717hRz/6UWZm2ac+9angvL1792Zmlt1yyy3Ln33jG9/IzCw7ePDg8mc7d+7MzCx77LHHlj87cuRI1tvbm336059e/mx+fj5rNBpBHQcPHsx6e3uzL3zhC8FnZpZ94xvfaHnN+/fvz8wse+CBB6Jr++hHP7r8Wb1ez84666ysVCplX/rSl5Y/n5yczPr7+7Prr78+OHdhYSGoZ3JyMtu2bVv24Q9/ePmzf/iHf8jMLLvjjjuWP2s0Gtmv/dqvRW3/9V//9eziiy/O5ufnlz9rNpvZ2972tuyCCy5oeY0ARYQ3AwDC9PS0mZlt2LCh5XmvfH/ixIng849//OPJOh599FEzM/vDP/zD4PNPfOITudt50UUXBW8utmzZYhdeeKH97Gc/W/6st7fXyuWXb/NGo2Hj4+M2NDRkF154of3Xf/1X7rry8Pu///vL/65UKnbppZdalmV2ww03LH8+OjoatbFSqVhPT4+Zvfy//4mJCavX63bppZcGbXz00UetVqsFb1vK5bL90R/9UdCOiYkJ+5d/+Rf77d/+bZuenrZjx47ZsWPHbHx83K644gr7n//5H3vhhRe6eu0ApzsYCAGEV/7Iv7IoeDVebdFw3nnnJet4/vnnrVwuR+eef/75udt5zjnnRJ9t3LjRJicnl4+bzaZ95StfsbvuussOHjxojUZj+btNmzblrquT9oyMjFhfX59t3rw5+nx8fDz47N5777V9+/bZT37yE1taWlr+/NT+ef755+2MM86wgYGB4LfaZwcOHLAsy+zmm2+2m2++2W3rkSNHbMeOHfkvDuB1DosBAGFkZMTOOOMMe+qpp1qe99RTT9mOHTtseHg4+Ly/v38lm7fMq+0wyLJs+d+33Xab3XzzzfbhD3/Y/vzP/9zGxsasXC7bpz71KWs2myvenjxtvO+++2zv3r129dVX20033WRbt261SqViX/ziF+2nP/1p2+145bpuvPFGu+KKK9xz2ll0ARQBFgMADrt377avfe1r9vjjjy/vCDiVf/u3f7PnnnvOPvaxj3VU/s6dO63ZbNrBgwftggsuWP78wIEDHbfZ48EHH7TLL7/cvv71rwefT01NRf9jXysefPBB27Vrlz300EPBjotbbrklOG/nzp22f/9+O3nyZPB2QPvsla2etVrN3v3ud69gywFeP+AZAHC46aabrL+/3z72sY9Fr7QnJibs4x//uA0MDNhNN93UUfmv/I/1rrvuCj6/8847O2vwq1CpVIL/hZuZPfDAA+tKM3/l7cGp7fyP//gP++EPfxicd8UVV9jS0pJ97WtfW/6s2WzaV7/61eC8rVu32rve9S67++677aWXXorqO3r0aDebD/C6gDcDAA4XXHCB3XvvvXbdddfZxRdfbDfccIOdd9559txzz9nXv/51O3bsmN1///32hje8oaPy3/zmN9tv/dZv2R133GHj4+P21re+1f71X//Vnn32WTOzjvMYKLt377YvfOEL9qEPfcje9ra32dNPP21///d/3zJQ0mqze/due+ihh+yaa66x97///Xbw4EH7m7/5G7voootsZmZm+byrr77a3vKWt9inP/1pO3DggL3xjW+0733vezYxMWFmYZ999atftXe84x128cUX20c+8hHbtWuXHT582H74wx/aL37xi67GWQB4PcBiAOBVuPbaa+2Nb3yjffGLX1xeAGzatMkuv/xy++xnP2u/9Eu/9JrK/+Y3v2nbt2+3+++/377zne/Yu9/9bvv2t79tF154ofX19XXlGj772c/a7Oysfetb37Jvf/vbdskll9jDDz9sn/nMZ7pSfjfYu3evHTp0yO6++277p3/6J7vooovsvvvuswceeMB+8IMfLJ9XqVTs4Ycftk9+8pN27733WrlctmuuucZuueUWe/vb3x702UUXXWT/+Z//aZ///OftnnvusfHxcdu6dau96U1vss997nNrcJUA65tSpu8QAWDNeOKJJ+xNb3qT3XfffXbdddetdXNOC7773e/aNddcY48//ri9/e1vX+vmAJyW4BkAWCO8xEV33HGHlctle+c737kGLVr/aJ81Gg278847bXh42C655JI1ahXA6Q8yAcAa8eUvf9l+9KMf2eWXX27VatUeeeQRe+SRR+yjH/2onX322WvdvHXJJz7xCZubm7Nf+ZVfsYWFBXvooYfs3//93+22225btS2dAK9HkAkA1ojvf//79vnPf95+/OMf28zMjJ1zzjm2Z88e+9M//dOOMxy+3vnWt75l+/btswMHDtj8/Lydf/759gd/8Af2x3/8x2vdNIDTGhYDAAAABQfPAAAAQMFhMQAAAFBwWAwAAAAUnNwupUe/v38l25EbtTh0K1Jbu/Vm9trr7YZdo7EG/eG1O7PuJr1ZLVbCMtONFXae3lwNu0836sCW1JrV6p+1enYmydZJO9YJKzEfrr3qfclzeDMAAABQcFgMAAAAFBwWAwAAAAWHxQAAAEDBYTEAAABQcFgMAAAAFBwCoHdIajPMam2m0tWctuv03PD3+mc1xiVPHfxvAADMeBYAAAAUHhYDAAAABYfFAAAAQMFhMQAAAFBwMBACdBlMmwBwusGbAQAAgILDYgAAAKDgsBgAAAAoOLk9A+sl5XQUzGeV2qX1lhJRhfI0azUCE+VZ7aFxrzwr0cepOdaNse/GHF0njw4AaAFvBgAAAAoOiwEAAICCw2IAAACg4BBnAKDL4MEAgNMN3gwAAAAUHBYDAAAABYfFAAAAQMHBM7CGpPZfr0YcArP0ihAN/LXTlMEur9bgJkiNfZ5mMj8ATn94MwAAAFBwWAwAAAAUHBYDAAAABYfFAAAAQMHBQAjwGlEDnZoFAQDWO7wZAAAAKDgsBgAAAAoOiwEAAICCk9szkGVh+JFSaXWE0VS9zWY65Em5HK55KpVKyzrq9XpUhn7W19MXllFvhD+oxOusRiM8p1pt3f1zc3PRZ319Yb1aZlnHJcc4aR9qf2iZpXJ8bYvSjtcT2h/R9/qB0+d6TidBiLTXk0GrnHbXarXgWOdPNPedsY7ufZ2D8pum0w49R4+17d59rp9pu1brGfVa6UY7U3O0W/WsCKV1EoFrnVBK3tkrA28GAAAACg6LAQAAgILDYgAAAKDgnHZxBlT3SmmNHqlztEyzWN9XbbXUFG9DOa37pK5FvQ15ytBjvVLv2vPos+mGtP+T1yvu/GpTr/VW6aXU3JY5aM75TfW2qD9E5qDna1kSj0DqfsqjVWsZee5j7z5tVW+eMgGKCm8GAAAACg6LAQAAgILDYgAAAKDg5PYMqIq8WhJxSuWLNHH3pPBT3VudZ3+y6ve1nnC/dnMp9BB4uvvC/ELLc3p6etpuh15LSu/P4xnoSFvFM9AajZeh8r6e7hQRdbF4BHRl33DmgsaIyOQcnT8N/4ZqWW9F5q0XgaLdOefdCylPAB4BgPzwZgAAAKDgsBgAAAAoOCwGAAAACg6LAQAAgIJz2gUdUsNcJ3QSdEjNSlE71ihRSCdmyBSpYC5uOyKLaXHxkg5pYqIo4JSc7wUYUtOhjmweA2FVxnZ+cTE4PnHiRFimMxd6+/uD46TxtQPT6umadAjgdIU3AwAAAAWHxQAAAEDBYTEAAABQcE47z4AGRUkl+8lDnkQ9Ws/c3FxwXC2F9aqOamZWq4WBijQJjGrCXjtS2monlBNl5El2BK2JdfTW55edKE4lDfajuromy9LERWZWkXKXJBDW+****************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["image_path = \"images/9.jpg\"  # Change to your path if needed\n", "image = cv2.imread(image_path)\n", "\n", "plt.imshow(cv2.cvtColor(image, cv2.IMREAD_GRAYSCALE))\n", "plt.title(\"Original Image\")\n", "plt.axis('off')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 62, "id": "9d00a6f0-0fd1-4e84-8fb6-3ed07ac0ce77", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgMAAAEXCAYAAAAwdEdHAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8fJSN1AAAACXBIWXMAAA9hAAAPYQGoP6dpAACP40lEQVR4nO39eZSkd3kdjt/a96ru6n0WLYhFCBACZEyQkCAgCRAECA7GC0IycAhLDIfN4HzBCBwUbGMgYAg2WAYsTmzAYB8bs+MkziEOOMYmCGOMtc1M792191Zd7++P+d1P3/czb/X0jLp7pqeee06fnqW76q33/Sz3uc99nk8sCIIABoPBYDAYBhbxc30BBoPBYDAYzi2MDBgMBoPBMOAwMmAwGAwGw4DDyIDBYDAYDAMOIwMGg8FgMAw4jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwDjFtvvRWXXHLJjn72N3/zN/GQhzwEiUQCV1111Z5el8Fg2F8YGTDsOv7gD/4AsVjMfWWzWTz84Q/Ha1/7WszOzp7ryzOcBb761a/iLW95C6655hrceeedeM973rOn73frrbeGxpA/ns4H/K//9b/wghe8ABMTE8hkMrjkkkvwyle+Evfff3/f3/ne976HX/zFX8TRo0eRyWRQrVbxjGc8A3feeSc2Nzfdz/mfuVwu4/rrr8df/MVf7MdHMwwgkuf6AgwXLt71rnfh0ksvxerqKv76r/8aH/3oR/GlL30J/+///T/k8/lzfXmGM8A3v/lNxONxfOITn0A6nd6X98xkMvj4xz9+yr8nEol9ef/t8KEPfQive93r8JCHPAT/4T/8B0xNTeGHP/whPv7xj+OP/uiP8KUvfQlPfvKTQ7/z8Y9/HP/+3/97TExM4CUveQke9rCHodls4hvf+AZe9rKXYXp6Gr/6q7/qfv6GG27ALbfcgiAIcN999+GjH/0onvvc5+Iv//IvcdNNN+33RzZc6AgMhl3GnXfeGQAIvvOd74T+/Q1veEMAIPjMZz7T93dbrdZeX945ea/zFS996UuDiy+++LQ/d9tttwWFQmHX3rfX6wWdTmfb69rN99tN/PVf/3UQj8eDpzzlKUG73Q793z//8z8HExMTwdTUVLC0tOT+/dvf/naQSCSCa6+9Nmg0Gqe85ne+853gzjvvdH8HELzmNa8J/czdd98dAAie9axn7e4HMhiCILA0gWHf8K//9b8GANxzzz0ATkrBxWIRP/nJT/DsZz8bpVIJv/ALvwAA6PV6+MAHPoBHPepRyGazmJiYwCtf+UosLy+HXvOSSy7Bc57zHHz1q1/FVVddhWw2iyuuuAJ/8id/Evo5pi7++3//73j1q1+N8fFxHDlyxP3/Rz7yETzqUY9CJpPBoUOH8JrXvAa1Wu2Uz/A3f/M3ePazn43h4WEUCgVceeWV+OAHPxj6mX/8x3/Ez/zMz6BarSKbzeLqq6/Gn/3Zn4V+ZmNjA7fffjse9rCHIZvNYmRkBNdeey2+9rWvuZ+ZmZnBbbfdhiNHjiCTyWBqagrPe97zcO+994Ze6y//8i/xlKc8BYVCAaVSCTfffDN+8IMfnHLtX/ziF/HoRz8a2WwWj370o/GFL3wh6jGdglgshjvvvBPtdtvJ1n/wB38AAOh2u3j3u9+Nyy67zEnlv/qrv4q1tbXQa/A5feUrX8HVV1+NXC6Hj33sYzt6/+2wtLSEN73pTXjMYx6DYrGIcrmMZz3rWfj7v//7U372Qx/6EB71qEchn89jeHgYV199NT7zmc8AAL71rW8hFotF3pPPfOYziMVi+Pa3vw0AePe7341YLIZPfvKTpyhcl112GX7jN34D09PToc93++23IxaL4a677kKpVDrlPa6++mrceuut237WRz7ykRgdHcVPfvKT094Xg+FMYWTAsG/gIjYyMuL+rdvt4qabbsL4+Dh+67d+Cy984QsBAK985Svx5je/Gddccw0++MEP4rbbbsNdd92Fm266CRsbG6HX/fGPf4yf/dmfxbOe9SzccccdSCaT+Hf/7t+FNlbi1a9+Ne6++2684x3vwFvf+lYAwDvf+U685jWvwaFDh/C+970PL3zhC/Gxj30MN954Y+i9vva1r+G6667D3Xffjde97nV43/veh6c97Wn48z//c/czP/jBD/CkJz0JP/zhD/HWt74V73vf+1AoFPD85z8/tNG8853vxO23346nPe1p+PCHP4z/+B//Iy666CL83//7f93PvPCFL8QXvvAF3HbbbfjIRz6CX/7lX0az2QzlpD/96U/j5ptvRrFYxHvf+168/e1vx913341rr702RBq++tWv4oUvfCFisRjuuOMOPP/5z8dtt92G7373u6d9bp/+9KfxlKc8BZlMBp/+9Kfx6U9/Gtdddx0A4OUvfzne8Y534PGPfzze//734/rrr8cdd9yBF7/4xae8zo9+9CP83M/9HG644QZ88IMf3JEJcWFh4ZSvRqPh/v9f/uVf8MUvfhHPec5z8Nu//dt485vfjO9///u4/vrrceLECfdzv/d7v4df/uVfxhVXXIEPfOADuP3223HVVVfhb/7mbwAAT33qU3H06FHcddddp1zDXXfdhcsuuwz/6l/9K3Q6HXzjG9/AU57yFFx66aWR1/yzP/uzyGQyblzwd6677jpcdNFFp/3M/VCv17G8vIzh4eGzfg2DoS/OtTRhuPDANMHXv/71YH5+PnjggQeC//bf/lswMjIS5HK54NixY0EQnJSCAQRvfetbQ7//P//n/wwABHfddVfo37/85S+f8u8XX3xxACD4/Oc/7/6tXq8HU1NTweMe97hTrunaa68Nut2u+/e5ubkgnU4HN954Y7C5uen+/cMf/nAAIPj93//9IAiCoNvtBpdeemlw8cUXB8vLy6Hr6vV67s9Pf/rTg8c85jHB6upq6P+f/OQnBw972MPcvz32sY8Nbr755r73cHl5OQAQ/OZv/mbfn2k2m8HQ0FDwile8IvTvMzMzQaVSCf37VVddFUxNTQW1Ws3921e/+tUAwI7SBFGy/fe+970AQPDyl7889O9vetObAgDBN7/5TfdvfE5f/vKXT/tefD8AkV833XST+7nV1dXQcwuCILjnnnuCTCYTvOtd73L/9rznPS941KMete17vu1tbwsymUzoHs3NzQXJZDL4tV/7tdBnft3rXrfta1155ZVBtVoNgiAI/v7v/35Hv6MAELzsZS8L5ufng7m5ueC73/1u8MxnPvO0Y8JgOFuYMmDYMzzjGc/A2NgYjh49ihe/+MUoFov4whe+gMOHD4d+7lWvelXo75/97GdRqVRwww03hCLCJzzhCSgWi/jWt74V+vlDhw7hBS94gft7uVzGLbfcgr/7u7/DzMxM6Gdf8YpXhAxoX//617G+vo7Xv/71iMfjoZ8rl8vOvf13f/d3uOeee/D6178eQ0NDodeMxWIATkrW3/zmN/GiF70IzWbTXffi4iJuuukm/PjHP8bx48cBAENDQ/jBD36AH//4x5H3LpfLIZ1O46/+6q9OSY0QX/va11Cr1fBzP/dzofuUSCTw0z/90+4+TU9P43vf+x5e+tKXolKpuN+/4YYbcMUVV0S+9k7wpS99CQDwhje8IfTvb3zjGwHgFOf7pZdeekbGt2w2i6997WunfP3n//yf3c9kMhn33DY3N7G4uIhisYhHPOIRIZVlaGgIx44dw3e+852+73fLLbdgbW0Nn/vc59y//dEf/RG63S5+8Rd/EQDQbDYBIFLqV5RKJadg8PvpfsfHJz7xCYyNjWF8fBxXX301vvGNb+Atb3nLKffbYNgNWDWBYc/wO7/zO3j4wx+OZDKJiYkJPOIRjwhtuACQTCZDuXvgpOxfr9cxPj4e+bpzc3Ohvz/0oQ91GzLx8Ic/HABw7733YnJy0v27L+3ed999AIBHPOIRoX9Pp9N4yEMe4v6fKY5HP/rRfT/vP//zPyMIArz97W/H29/+9r7XfvjwYbzrXe/C8573PDz84Q/Hox/9aDzzmc/ES17yElx55ZUATm5y733ve/HGN74RExMTeNKTnoTnPOc5uOWWW9znIZGgF8NHuVwOfcaHPexhp/yMv2meCe677z7E43E89KEPDf375OQkhoaG3PsS/WT1fkgkEnjGM56x7c/0ej188IMfxEc+8hHcc889ofI8TUf9yq/8Cr7+9a/jiU98Ih760IfixhtvxM///M/jmmuucT9z+eWX46d+6qdw11134WUvexmAkymCJz3pSe4zckMnKeiHZrPpfpbP4XS/4+N5z3seXvva12J9fR3f+c538J73vAedTueUOWQw7AaMDBj2DE984hNx9dVXb/szGtkRvV4P4+PjkflbABgbGzvra8rlcmf9u6dDr9cDALzpTW/qGwFzU7nuuuvwk5/8BH/6p3+Kr371q/j4xz+O97///fiv//W/4uUvfzkA4PWvfz2e+9zn4otf/CK+8pWv4O1vfzvuuOMOfPOb38TjHvc4936f/vSnQ4SHSCb3Z3r7RKwf9uLev+c978Hb3/52/NIv/RLe/e53o1qtIh6P4/Wvf727P8BJ892PfvQj/Pmf/zm+/OUv4/Of/zw+8pGP4B3veAduv/1293O33HILXve61+HYsWNYW1vD//7f/xsf/vCH3f8/9KEPRTKZxD/8wz/0vaa1tTX86Ec/cmOfv/P973//jD7bkSNHHBl69rOfjdHRUbz2ta/F0572NPzbf/tvz+i1DIbT4lznKQwXHvqVFvroVz726le/OkgkEtuWnhEXX3xxcOjQoVDePgiC4Fd+5VcCAMH09PS21/SZz3wmABB86UtfCv372tpaUKlUghe+8IVBEJws/QIQvP/97+97LbOzswGA4G1ve9tpr9tHs9kMHve4xwWHDx/u+zP/9E//FOTz+eAXfuEXgiAIgj/+4z8OAARf+cpXtn3tEydORHozgiAIrrjiirP2DLznPe8JAAR333136N9nZmYCAMEb3/hG928XX3zxth6JnbxfFB772McGT3va007598OHDwfXX399399bW1sLbr755iCRSAQrKyvu3+fn54NUKhX8xm/8RnD77bcHqVQqmJ+fD/3uDTfcECQSieDee++NfO1PfepTAYDgjjvucP924403BslkMrj//vtP+5mCILq0cGNjI7jsssuCRz7ykaeMd4PhwcL0JsN5hxe96EXY3NzEu9/97lP+r9vtnlLyd+LEiZBTv9Fo4FOf+hSuuuqqyIhZ8YxnPAPpdBr/5b/8FwRB4P79E5/4BOr1Om6++WYAwOMf/3hceuml+MAHPnDK+/P3xsfH8dSnPhUf+9jHMD09fcp7zc/Puz8vLi6G/q9YLOKhD32oK8nrdDpYXV0N/cxll12GUqnkfuamm25CuVzGe97znlMqLPT9pqamcNVVV+GTn/wk6vW6+/+vfe1ruPvuu7e9P9vh2c9+NgDgAx/4QOjff/u3fxsA3L3bSyQSidBzA056TujNIPz7nU6nccUVVyAIgtC9Gx0dxbOe9Sz84R/+Ie666y4885nPxOjoaOh3/7//7/9DEAS49dZbsbKyEvq/e+65B295y1swNTWFV77yle7ff+3Xfg1BEOAlL3kJWq3WKZ/jb//2b/HJT35y28+aTCbxxje+ET/84Q/xp3/6p9v+rMFwprA0geG8w/XXX49XvvKVuOOOO/C9730PN954I1KpFH784x/js5/9LD74wQ/iZ37mZ9zPP/zhD8fLXvYyfOc738HExAR+//d/H7Ozs7jzzjtP+15jY2N429vehttvvx3PfOYz8W/+zb/Bj370I3zkIx/BT/3UTznjWDwedx3grrrqKtx2222YmprCP/7jP+IHP/gBvvKVrwA46ZO49tpr8ZjHPAaveMUr8JCHPASzs7P49re/jWPHjrn69yuuuAJPfepT8YQnPAHVahXf/e538bnPfQ6vfe1rAQD/9E//hKc//el40YtehCuuuALJZBJf+MIXMDs768r2yuUyPvrRj+IlL3kJHv/4x+PFL34xxsbGcP/99+Mv/uIvcM011ziJ+4477sDNN9+Ma6+9Fr/0S7+EpaUlV3cftTntBI997GPx0pe+FL/7u7+LWq2G66+/Hv/n//wffPKTn8Tzn/98PO1pTzur1yW63S7+8A//MPL/XvCCF6BQKOA5z3kO3vWud+G2227Dk5/8ZHz/+9/HXXfdhYc85CGhn7/xxhsxOTmJa665BhMTE/jhD3+ID3/4w7j55ptPMfbdcsstbnxFEdLrrrsOv/Vbv4U3vOENuPLKK3Hrrbe6sfB7v/d76PV6+NKXvhQqAXzyk5+M3/md38GrX/1qXH755aEOhH/1V3+FP/uzP8Ov//qvn/ae3HrrrXjHO96B9773vXj+859/2p83GHaMcylLGC5MPNg0AfG7v/u7wROe8IQgl8sFpVIpeMxjHhO85S1vCU6cOOF+hvLzV77yleDKK68MMplMcPnllwef/exnz+iaPvzhDweXX355kEqlgomJieBVr3rVKSWEQXCy+9wNN9wQlEqloFAoBFdeeWXwoQ99KPQzP/nJT4JbbrklmJycDFKpVHD48OHgOc95TvC5z33O/cyv//qvB0984hODoaGhIJfLBZdffnnwn/7TfwrW19eDIAiChYWF4DWveU1w+eWXB4VCIahUKsFP//RPB3/8x398yjV961vfCm666aagUqkE2Ww2uOyyy4Jbb701+O53vxv6uc9//vPBIx/5yCCTyQRXXHFF8Cd/8ic77kDY71ltbGwEt99+e3DppZcGqVQqOHr0aPC2t70tVFoZBGeXJkCf0kIAwT333BMEwcnSwje+8Y3B1NRUkMvlgmuuuSb49re/HVx//fWhNMHHPvax4LrrrgtGRkaCTCYTXHbZZcGb3/zmoF6vn/Lea2trwfDwcFCpVEIpBB//43/8j+B5z3teMDo6GqRSqeCiiy4KXvGKV/RNHwRBEPzt3/5t8PM///PBoUOHglQqFQwPDwdPf/rTg09+8pOhEklEpAmId77znQGA4Fvf+tb2N9FgOAPEgsDT2AyGA4RLLrkEj370o0ONfwyGB4Nut4tDhw7huc99Lj7xiU+c68sxGPYF5hkwGAwGwRe/+EXMz8/jlltuOdeXYjDsG8wzYDAYDDh57sQ//MM/4N3vfjce97jH4frrrz/Xl2Qw7BtMGTAYDAYAH/3oR/GqV70K4+Pj+NSnPnWuL8dg2FeYZ8BgMBgMhgGHKQMGg8FgMAw4jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwGg8Ew4DAyYDAYDAbDgMPIgMFgMBgMAw4jAwaDwWAwDDiMDBgMBoPBMOAwMmAwGAwGw4DDyIDBYDAYDAMOIwMGg8FgMAw4jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwGg8Ew4DAyYDAYDAbDgMPIgMFgMBgMAw4jAwaDwWAwDDiMDBgMBoPBMOAwMmAwGAwGw4DDyIDBYDAYDAMOIwMGg8FgMAw4jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwGg8Ew4DAyYDAYDAbDgMPIgMFgMBgMAw4jAwaDwWAwDDiMDBgMBoPBMOAwMmAwGAwGw4DDyIDBYDAYDAMOIwMGg8FgMAw4jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwGg8Ew4DAyYDAYDAbDgMPIgMFgMBgMA47kub4Ag8Fw7hEEQeR3RSwWi/xuMBgOPowMGAwDjiAI+n4R8fhJETEWi7kv/t1gMBx8GBkwGAYYvV4PQRBgc3MTQRBgY2MDANzfiVgshng8jlgshmQyiXg8jkQicQo5MBgMBxNGBgyGAUUQBOj1euj1elhfX0cQBFhbW8Pm5iY2NjZC6kA8HkcymUQymUQ6nUY8HkcqlXKkADCVwGA4yDAyYDAMIEgENjc3sbm5ifX1dWxubqLT6aDb7TpSQEKQSqUcEchms0ilUgAQUgdICgwGw8GDkQGDYcCgRGB9fR3dbhetVgvr6+uo1WpYXV1Fp9NxagEApNNppNNp5PN5lEolZLNZlEolpw7wy9QBg+FgwsiAwTCAICHodrvY2NjAysoK1tbW0Gg0sLq6imazifX1decpyGazyGQy6Ha7iMfj6PV6SKfTAOC+kzgYITAYDh6MDBgMAwYlAe12G2tra5ifn0en08Hx48fRarWwtLSElZUVRwby+Tzy+TyGh4cxPj6OcrmMeDyOXC7nUgiJRMLUAYPhgMLIgMEwQKAHgGmCbreL9fV1rKysoNPpoNlsotVqoV6vY2VlJVRlsLGxgWQyiXw+j2QyibW1NSQSCWxubiIej0f2JjAYDAcDRgbOI2y3mFq0ZXiw8L0CKysraDQaaLVamJ2dRaPRwL333otms4nZ2Vm0Wi33O6VSCcViEc1m0xkMC4UCNjc3USqVAACZTMaVIBoMhoMFIwPnGNrxbbuub/6fDYazQZQqsLa25pSBdruNVquFVquFdrvtqg1YMZDP57GysoKVlRWsr69jY2MDm5ubLp3A97CxajAcLBgZ2GP43dyi/qxfvV7P/S7LttSpbTlZw9mCJICGwXa7jaWlJTQaDczMzKBer7vvCwsLaLfboT4Ea2trSCaTKJVKSKfTaLfbSKVSLn3Qj9AaDIbzH0YG9ghRGz6jp6jNX/9fX0PrtzUva4TAcCaI8gpsbGxgdXU1pAp0Oh0X+a+urjrPQCaTwerqKlZXV50i0O120e123WsaGTAYDi6MDOwyuLlzcaTMygVYF05+Z/S1ubkJYIsEpFIpJBIJZDKZUNMXbQVrMJwOWka4vr7uSgfr9TqWlpZQq9WwuLiIWq2GWq3mTISrq6vuNdbW1pDJZLC2toa1tbXI9IDBYDi4MDKwi/Aj/l6v5xbNjY0N18Sl2+2GvnNR1X7wsVgM6XQayeTWI4rFYo4w6MExBsN20DFJRWBtbc01F+p0Omi1Wk4V0OifXgESWp+8mhpgMFwYMDLwIOErAdzw2dltdXXVRVOrq6suOuPCTMKg5IDegHw+j3Q6jXK5jHw+j0KhgEKhgFQq5X7GWsAaTgdu4PQKdDodpwIsLi5ieXnZKQLtdtuN016vF/KqJBIJJBIJ11OABxYZITUYDj6MDDwIqBJAMtDtdrG5uYmVlRXX1EXzsszT0pQVRQpoGGTbVy7mPByGKQQAthgbtoWOT6aqqAi0Wi2XEqBCwH4CVKs4vtTIqoZWO7HQYLgwYGTgLKBSKTd/HuzCBbXZbLr2rlqyxfpupg58ZQCA6+Q2NDSEXC7nnNxBECCRSCAIAneCnJ0YZ+gHJQLsDUASUK/X3RcVgU6n4/wAVLx4WmE6nUYmk0E2m0Uul3PtidPptFMJjBgYDAcXRgbOEOrIZnTf7XYdCWg0GlhbW0OtVnNyLKOvRqOB9fV1V7/tKwNcgCm/jo6OIp/Puw5wNBOyDSwApxDYImyIgm8epEJVr9fRaDTQaDRQr9fRarVCvQPoA+BGn0qlkMlkHCEgGSAptYOKDIaDDSMDZwA1TvHI11arhW63i3q9jvX1dSwvL2N1dRVLS0totVqo1WqugQvVAioDJANqzALgqggAoFAouPavxWIR5XIZ6XQa3W4XsVjMGrwY+oIbOlME9Azwi2rA6uqqSw9odYCqAtlsNuRbyefzjhSQEFjawGA4uDAysENohKXGwFqthvX1dczPz2N1ddUd+LKwsODIAGXYZrPpPANcnP0SQwAu4lpbW0OxWEQqlUIQBMjlciiXy0ilUlhfX3dkwNzchn7QRkP0CrCskMoAVQHfOBiPxx0RKBQKKJVKKJfLqFQqKJVKKBQKyGazruzViIDBcHBhZOA08Muy1tfXndRPMrC2tobFxUWsrKw4MrC4uIh2u41Go4F2u+2iMeZuSSp8MsANPplMYmVlBfF43DV7WVtbc2kJrfE2MmCIgt9oiGoUSwv556ieAaweUL9APp9HLpdzngH+u51WaDAcfBgZ2AZcHP3T3VZXV7G4uIhOp4Pp6Wm0220cP34c7XYbs7OzzivAum2asijDavMhNRCyhwDTBIlEAuvr6xgaGkI2m3Wu70KhECIEtggboqAGQu0t0G63naGVRJXEgGM+kUgglUo5VWBoaAjDw8OoVquoVqsYGhpCoVBwRxinUimnJhgMhoMHIwMR8CMqtl6lG3tlZcVJ/9z0l5eX3b+zTIu5WFYJaPTEXD99CPzymxapT4GbvykChp3AbxBEUqtj2h97AEJ9BdLpNFKplEsX0ETIf2dFi6UIDIaDDSMDAjVcsVJgc3Mz1CdgaWkJ7XYb09PTaLVauO+++9zfV1ZWXNqAv8sFMplMuiNeSQJoIGT6YGVlxS3KfloiSs41MmDoh6jTCTmO9ewBji+OK+AkGWB6IJ/Po1QqoVKpYHh4GMPDwxgaGkK5XEYul3P+FksTGAwHG0YGBCqr+r3c/eNdabzylQD6AYCtRVXLsyildrtdpNNpJ80mEgkXtekmb7Kr4WwR5RnQA4b0oCGfXHKcqm+AaoAqAtp8yGAwHFwYGcDWosmFkTl+5labzSZqtRoajQZmZ2fRarWcR2BmZgarq6uo1+tuMwfgFk+arliaxXItph1oPlxfX8fS0pLL7WpjIS6+SiZsATb0g45njjM9mZA+AVYP+B0H2elSKwlYTVAsFl1ZIc/OMFXAYDj4MDKA6INctEELqwKazWboi4oA1QCmBeLxuFssWZPNL0ZaTAukUimsrq66/CywlabgQkszYVQrWIPBh0b5mirg2OaXmlAV2nqYhFRJqaoCNhYNhgsDA00GfEWA5wnU63VXHri0tITl5WXMz887ZWBlZQULCwtYW1tDq9Vyjv5UKoVisYhMJoNqtYpCoYDh4WF30BDJQDwex/r6uutMmEwmHakgMdjc3HR13IzMWNbFPK2VdBmioAcTkdT6ChcVApaqasdBVhJkMhk35khqc7mcSxdY10GD4cKBkQHvnAH1CNAbUK/XXfOger3uyrMYbQFwERMl1HK5jGKxGCIDxWIRwMl87NraGoCTUVgul0Ov10Mmk3GLOP0GzNEy7WBRmeF08L0CTBWoEZUkQNtg64FEqgZo22FfpbKxZzBcGBhIMuCf5EY3P8sDZ2dnUavVMDs7i/n5eSwvL2NhYcH1D9BjiLPZLBKJBMrlMjKZDCYmJlAoFDA5OYlSqYShoSFUKhXX050LdKfTcWbBXC6HbreLTCYTkm0rlQry+byr8SbBYHRmXd8MPpQE+ObXZrPp0l30DNAvwHHEssFisejaX5fLZddxkH0FzCtgMFxYGEgyACDUTMiXU1UNWF5edqe7cWHlYsvoPZlMuoVyaGgIpVIJ1WoV5XLZ/Z2RPYlEEAQu6tcz4tl6OAgCJ9OqCTGTybjfscXYEAX/yGKeeqlf2mfAP4tAzyPQQ4lUmbLeAgbDhYWBIgO+IsDIqFarYXV1FbOzs2g2mzhx4gSWlpawsLDgOg2urq46KV8lfHoExsfHkcvlMDU1hWKxiLGxMRSLRZf312ZDfs03F2hdjOPxOKrVqiMW1WoVlUrllOjMFmQDwY3db5KlBlh2HeTY146DiUTCpbmGhoYwNDSEarWKkZERDA0NuUoCU6UMhgsPA0UGgLAioOcMtNttLC8vo9FoYGlpyX3V63WXZwXC3oB0Oo3h4WFks1l33PDY2BgKhQKq1ar7mXQ67d6Ti7VGa1yUY7GYUwd4SmGpVAqVdEUpA7YgG4BTvQIcY0o8aVTV9tjA1lHFTGdpOaGmCNS8auPOYLhwcMGTAS6QUYoApf/5+Xl3rkCj0cDi4qL7P9b78/RALog8SrharSKTyWB0dBTZbBYjIyPOgU3Jnwvy6uoqWq0WlpeX3XctTWTaoFgsIp1OY3R01EVn7AXPkkVLExgUUV4B9QnU6/VQOay2yQbgUgO5XA7FYhFDQ0MYGRlxXQdLpZIjo5YmMBguPAwMGdBaa3YKZAnh3NwcWq0W5ubmUK/Xsbi4iFar5XKqXCRzuRwqlQpyuRxGRkZcCWE6ncbQ0JAjCSrhq/rAroULCwtOiSBJCIIA6XQa8XjcvQfJwPDwMCqVCorFojsy1g6FMRD+GOeYUjLANAFTBGxBHARBqMkQ1ahKpYJKpeIMsNpsiMqUkQGD4cLBBUsG/B7/Wl7VaDTcyYPtdhuLi4toNptYWlpCq9VyndmSyWToLHfK/7lczikClUrFRfOMrthauNfrOed2o9FArVZDu93G0tKSUws2NzddsyHmYkdGRlwVAQ2ImiIwJ7dBwfJASv8+EWCTLJ8IMDXldxzM5/OhMd+v46CNP4PhwsEFRwa085p/2A8XQ5oC2VL42LFjaLVamJ+fd9UCQRC43gDDw8MYHx9HqVTC+Pg4stmsUwTy+byT9/UUQpZusRpheXkZi4uLWFlZQb1ed33iY7GYW2R5VPHk5CTy+TwOHTrkDISqClhUZiA0PUCyy82fY25xcdGNQ5ph6V+hT4VeAVUGeCBRqVRy6TEdfwaD4cLBBUkGVA1g/nR1dRWNRgMrKytYWloKnUDIRZK5exqk8vk8yuWyO7GNuVQ2FUomk8jlck6y137wzM2yRLFer6PRaDjzIKMxvk8mk3FmxKGhIUdECoWCmQYNkeh3MiEP1KIyoBUEbDyk5YQ0rfrlhFQDtATWxp/BcGHigiEDvkmQTn1K9J1Ox0X+09PT6HQ6eOCBB7CysoL5+XkXLW1ubrrNuFqtYmJiAmNjYy5KHx0ddblVGqkAuOYtTDPUajXUajXMzMxgYWHBybVcwLPZLEqlErLZLMbGxpDNZjExMRH6O4kHmwypcctgoArFDZ4GQbbRnpmZwfT0NObm5rCwsIBWq4VOp+PGuZ5KyPQAVQEqAtqC2KoIDIYLFxccGeBC5zuq2+02arWa6yLIdsPaiQ2Ai5LY7IfROUurKNXT7BeLxUJExO/61m630el0XPtXnmGgJxpShiU5YI6WjV6s/bDBh99Gm14BNs7iUds6/vR0QgAuRaBHbLMUVo8stsoVg+HCx4EnA/5hQ5RC6aCu1WqYm5tDo9HAiRMn0G63ceLECZfP55HBAFAqlZDJZFxZ1eTkJKampjAyMoKJiQlXTaAnDOpizAqFZrPporG5uTnUajXnEaA5q1wu49ChQygUCjh06FCoQqFUKoV6wlspl0Hhp8LUC9NsNjE9PY2FhQXMzMxgZmbG9ctgFQ1TBPSrUKVi6+FKpeJ6W6hXwCpYDIYLFxcMGfA3ZT2pjXXW9Aa0223n5metNXP4mUwmdOSwyqT+QUF+DwO+NyM0zdHqe1D6JzFg/wD/ffyTCY0IGIBTvQI67uhV6XQ6oQ6XUaqAHolNXwDVKPMJGAyDhQNLBqKOH15fX3enCy4sLGB+fh4LCws4fvw4Go0GpqennSLAhTQej7smPyMjIy5iHxsbw+TkJMbHx13ERBmVC6N2E9R8ba1Wc62Mm80mVldXnfxfqVQwNTWFoaEhHD16FPl8HhMTE8hkMqeUJ9rJcAYfSgJojF1eXnZNs5aXl50ywEO2WN6qXgGSTb+UkMoASwp9r4CNRYPhwsSBJwOqCPCwIfoBWGKlfdkplbLGGoBrw0olgNG6X1/NDVqvISo643toRMbzDLSBEb/4HmoSNCJg8OG3G9YzCOiPUUWA45AkgKoAsKUMkBSoIqBeAVMGDIbBwIEjA1GKwMbGhisXZI50ZmYGJ06cwPLyMk6cOIFOpxNSBBKJhPMI8Njho0ePuohdD2ihmU/TA75pixHYwsIClpeX3TkHXIDZoGh4eBijo6PuzzQpUqI1EmDoBxIBEk9WB8zPz6PRaLixPzs7i6WlJdfkigZZegW0lJDth3nCJrsN6oFYPgk2GAwXHg4sGfAVAeZLtb6aXgEumisrK+j1eqGSKrr62WylXC6HThtklKQbtDq52fXNPxCGDWD4OzQd+rXcfH12djMiYIiC+lO0yRD9MfTCqDLgVxAwumcVAcd/Op12ykAmkznFuGpj0mC48HGgyIAuhIx22N9/enoa9Xodx44dw/z8PGZnZzEzM4N6vY6lpSUnp+pZ7aOjoygWizhy5AjK5TKOHDmCoaEh5xMgIeBGDcBJrpRgG42Ga2lcq9WwtLTkVIGVlZVQCoCkQ8kGX98WXcPp4JevkvjOz8+jVqthfn4+NP60pFW7DbJ0tlAouC6DPJCI/QV8NczGpcFwYeNAkgFVBRgVUQWo1WpYXl52Xf+oCvD3mCelR0AlUhoFo8xT+v7M13JR5jXwu54Kx4NgNArz3dq24Bp2CvWpaAUBe1qoV4BjUL0CviqmlS0sI1RlwMalwTAYODBkQI9n7Xa7zqW/sLCAZrOJEydOYGlpCcePH8f8/LzrwkZDHw18PFSoVCo5T8D4+LjrLcDDWbggAnClgUoC2LCInd147DEbHLFk0W9gxEWXigBPN7QF17AdtIqA6QESAG07zHFJ4yDP2QAQIgEkvDyDwD+dUAnBhTY+eT/874R+3gvpcxt2FzsZR/x+EMbRgSADUfX8jIjYP4AHsvCLJYbcvEkE4vG4i4bYdpXHtbLRCrsMAlunH/rHH1Nx4CLM7zwIhtGYH4GpT0AX2oMwWAznBr5fgOcQqDJAn4D6VVQVUK8AzapMFegX0wN6DPeFNDb1XvKL/+4v3kYIDP2gB+Lx7/zqRwLO93F0YMiA1laz01+73cbCwoLLl1INUBc1zyqg5MkUgTb9YQkhIyEuuPxdnnq4trbmDjSq1WruXINOp+MUAU1FxGKxU3q9qzFRlQHDYGG7qML/M8mols4yNaDmWCoGNAxqakyJgPpXmBrzOw6qofWgQ4kU1xLem83NzdAizpJLEnXOTyuvPDfwo21g/zbVqDnqE3P94r/540i/n8/B34EhA9yg9fChZrPpjmdV8xSbrKyuroZeQ8mA9hVgvT+VAzYTYoSlVQLNZhNra2vOuLiwsOD+fWVlxfUUYHQVRQb8MwfO18Fh2BtERad+lKo/x7HPlJeSAb+vABUsbnJEFBlgoyF+URXzUwQHGVEkgPeIX/wZAM7Do2uBknvzUOwP+knvir18Dv3mp08AGDByHG1ubgKAI9M8w0b9Yefr+DnvyUDUYSwsIdQ2w3ooi9/shzdf26/qJqz127FYzC0ebFu8srLicrGNRsMpE0wXrK+vuwiMFQJUAKrVKqrVqsvHsuWwX65ouPDhb0yM+PXv/RYfNcsyBUZCTOMgNzf1CQDRpkGSYDXLanrgoI7NqCiO92R9fd2VZDJ9yLMa2JmRGz+Vwlwu58qC+X9G4PcWHL/+l0bVGn3vJpQYKnlUMqkEQBt/8XeoKPF4eo4jpqq1qdz5hPOaDET1/adhihUDTA3w71wY+ZC4EBJ6Ups2EWK5IOVYmhTZZrher2NlZQX1et21H+52u65SgYtJuVxGPB5HtVpFPp/H+Pg4RkZGMDY2huHhYUcSfM+A4cKGVqJwc2cKiguLLzf6HS5Jdufm5tBqtZx5ll88C0NVAb+ngB5VTLVKUwRMXx20Da8fCQiCwN0TVvkoyWcqcWNjw6XtaDKmspdKpVAoFEJR3UElS+czfALMccy5oMqMNsLarXGq+w3VYZ2j/Dt7y5BMMkglcYjH405dK5fLSKfT2NzcRDab3dXr3W2c92SAiyFvPBdE/4hWLoQqkfaTmKLUhng87rwF7GHARZZpAKoSHAhBELhBmUqlEIvFkM/nkUqlXHdBdWn7rY3P10Fh2F34JbGbm5supdTpdNz4ZsShhECP5GZ6YHFxEe12O1Q6yzEbRQQ0PUAjq7bCZhSsJxMelLHpqyi+msIupd1u16l4jUbDtW/mc9jY2Ajdp7W1NWQyGQRBEJJ6GTzs1Fy5ncwNnP+msv2EjnklzOp9YXDHZ7FbKpaOH+1u2+12XXDJJl76nf/HhnYkA1zrgyAIVaZpaljJzPmA85YM+FERCUCtVkOj0XCNVvySPr+kyp+M+rorKytIJpOo1Wqu/DAWi7nXIdngCYj60KkEUFYtFAqOBPDQo0Kh4Bq5sJyRcqx1Gxwc+GNuY2MDjUbDHazFjZ5jPSofSfMqyUCn08H09DQ6nU6ohJakAoAjnNpTY3h4GENDQ6hWqxgeHnZVNL5idb5JmFHwc7hsucxNhFF/s9nE+vo6lpeXsbKygqWlJacwtlotd99VPalWq8hms+7o8iAIkM1m3bHPKlNHzeHtvCBRVQuDvg6oDE8TLAMvNYGTCOTzeTe+NYWwG+9PhZikkYFgu912HjKaddnYq91uh66TpvTJyUkUCgUAQKFQCBHt8410n5dkwI+k+h3RSn+AfxhLPxKgUi0X2FQq5TZ6gq1c+fr+AqtRAqMqykGVSgWZTMYtsOzxznysKQKDBV/21HQXq1LYt4Lyo5ra6APgeFxZWXGVLHrwFsdoVIMhElC/goYL1kHsKaBpFK36oSeA6svGxoZL7dVqNXdGiR5kpmkCkgEA7v5sbGygUCig1+shm81u6x2IUiq4dvC7Roa+EnMQ7v1uI2ptpmKrplg9aZOkgCXgPtF6MO/N6jFWpNXrdWfY5TVx3mnqiapCIpFw189x0+l0kEgk3J6TTCad2nG+4LwkA9pgiFE5W6zOzc05ZYAdB7drtKKTk5OeD2ZxcdHlDVlSyMhCc7jAlvmQG7pWC6TTaQwPDyOTybiDjdjSlbIs5Ud1Jm8XUfDPPgZ50Tho0IWGiwPbBE9PT6PdbrtDtGq12in9AVQd0Iik0Wi4TY7yN8ktDUx0MqfTaZRKJQwNDWF0dBRTU1MYGRnB1NRU6LAsbT98Po8tzglfNSFZ4sLMNN/a2ppTTubm5pyyQlWg1WqFjjPPZDLIZrMYGRlBPp9Hp9NBsVgEAJRKJSQSCXS73b71474vhMZFJQX+WqLdHulvOp+fwW5D58jm5qYjuVRwmAJLpVLOnL25uelI7Nl0y/RJQL8W9zwanIZdJQR+moCvmUgkUC6Xkc1msb6+jlKphGQyidXVVZcu4BzVVvfn+pmfd2TAf0h+219t/csHornSfl6Bfq8JwLk/+Rr8rrWi2qcgkUg4SZVkgJ6AYrHoojAuLDs9iIjEo5+6EeWmPdcDyLA9/AVHvS/tdtstMPV6/ZQjh4GtTY+KgkqT/FnNrQJb48Q3DRYKBXcuhrYfjipzPV/Hla4PqhrqfSFZYiqG95aNwfzqIz3JlFEoTV/NZhMA0G63EY/Hsbq6ilQq5ZQEBhGE/6ypVPB6gS11sdfruc2BON/v/15CAzbtqcF5wWcSj8extrbm7uHpfBn+e0RV6mg/Gb4vySIDTva2UTLAObi6uupel+s8iXo8Hken00Emk8Hq6ipyuZx7T3pQzofnfV6RAWWIZPr0CMzMzKBWq+H48eNOGWBpFWV9yqtRr6sLKiXZWCzmurEpI2fpHzd4uq955CtbDCeTSffv6jrW0iQlElGsP8r4pJGhLjRaBTHIkcRBgW5a3KyWl5dRr9dx4sQJNBoN/Mu//ItrZ61VMEpuNV2gOVXdZHScMFrioVhjY2OYmJjAxMQELr74YgwNDeHQoUMoFAoureV3xTzfoIoA87q8p5RxFxcXsbq6isXFxVD5L5UB3mMeIsZFnesD51Umk0G73UY+n0ev13Nzu1KpIJ1OY319va+8y7WLFUdcb0hcgiBw97lYLIYCCfUTqa/gQoduyrxfPIBreXnZ+baY5hoaGkIymUS320WhUHAmPQB9n4u/xpIAqrLEsvGZmRm0223ce++9aDabOH78uFOhNQhlSoHXTiSTSXQ6HbcHNBoN5PN5rK6uOlMhzb2+MfJcPu/zjgxoHtA/nli/KOmcziugr6vpBwBu8aWTWGs/OSFJFtivvVgsIplMuvpR/Tt/lotxlHwVlV/0XayUF/st8voZBzmSON/hRx/ayEoPF9IoVce0/xo6L3R8kDhoLlrbDlNapTpAH4vfCfN8kSv7ISq3qwc1MTAgCVheXg6ZNPn/NIKxLMw3IgJwnqBOp4N4PI52u41kMomVlRVkMhm34VP1IzSYYZCiaRxWJ3Dh39zcDKmGfK3zJVrcL/jPVg/iYqQOwKkCVIS1HLff60a9vpYIqkeMp4FyPPHLr1xjZK9rNgD3TFOplPMKcPxks9mQqsA2+b1e77x41ucVGVAZnxLqwsIClpaWcOLECSwvL2NmZgbNZtOxfW7oSgiiQC9AEARoNptIJBKO4TOXT4cq/14oFJzremxsDLlczuV/aGLhdzJ6LiJ+Hax+xn4ylV9upjlgAK5zItMQvA7fh3C2A6vfvTsfBur5DD89pcYxLjYcz1QG9NjhVquF5eXlkGdASaI/XvxeBOqm5pjMZrPu8K3JyUkcOXIE4+PjOHz4MEqlEkZHRx3JVTJwOkOT72nZzuMS5ZrXv+/03gJwiy4X0kajgUajgVqt5nxE9F/Mz8+7RX1jY8N95wFiarZMJBIuz8tr07KyVCqFTqeDfD6PVquFfD7vJGuVhAmWIbfbbczMzKDT6WB2dtZtNpubm05drFarKBaLGB0ddT0fqBwoqbuQ55+/WdNjoc3euCFznUylUlhZWUE6nXbGQkbYnAuqJKkPjASSz4MkcXV11VWbTE9Po9Vq4dixY2g2m5iZmXEpAxoateTRV3CZvkin065dfbFYxOrqKrLZbEiFAk4ST38MngucN2QgKheoXgHtL0AmF1WbrdDFx998ubAwt8MonrXF3NhpAGQ0RTLAzd+vQd6uxbC/sGukx81fFy0OPL6Wysda76zvycXtTAbUdobFqPtpOAnd/Pk9StrXBiWMdvwvjmfdqHzlSN+H76+LnxICHbtMF6hfgKmBMzFf6fjtdz0Kvp6vtvn/v5P77DvNuSZoJ1Iu5qoEsDGYNhPj/fSJjxIbknCWHLdaLSQSiVDuV/1KhLZMZzRJcyjLz5SA9Xo9Rw4ymQw2Njbc3D6TXPhBhr/+RClhGsn7adR+4y9qLirRYJTPEkKWmDPtREVAjwXXPSdqbvJ9qRRwj+l0Okin06f43aLK4AeaDKhXgBO92WyiVqthYWEBCwsLjv3T6UlW5zdaAbYiD83FKINXKZYeAUbZvV4PmUwGhUIB5XIZw8PDqFarGBkZcVUC2vSCzN03X/kLZ5TcS/MJm6FwEePiQdmYr00DWLFYdLneoaEh52FQcnK6Glb/uhjJ8v/0s6j8POgpiaj7xgmth1upWZC9ANTrwm6ZHIu6ibMBlpoCo8YTwWfDUiuOk2q1itHRUYyNjWF8fByjo6OuMya9LdoWe7vqFn/scmPmuPEXRGCLBFB10O86J7cbo1p7zgqKdruN+fl5dzjZsWPHnDLACE/zufzS9J1eB99fzZg0F1OtY8phaGgI3W4X5XLZVQixvA2AU4CWlpYwPT2NZrOJ++67z206vV7PmYpbrRbK5bIjEFQqer2eSz+cbbQYpdhsFyzx+37PbX+d0XFM8yvXyah22f2CLo5JXWPZM4CmXXoAqDIvLS1hbW0NCwsLWF1ddQoT+1Rwv9DnomRD11Cq0EwRLS4uYmNjA+VyGbFYzBl42ZMAANLp9L7eex/nDRnwVQGyN+ZV+cVIymeIyqp0YPlfQDiS08WI/6612dqxjfIry4FO5+qPknf9nBhLHVkyxkGpTS64UfDn6XxmeQ3zjow09D6c7n7rxKHbmfeS94uy5SCZmrYD759KkBq5+DltRrCadyRR4OKhsrC+hy7gUf/G3+UY8fsK0CNAVUB7Xpyut4Af+ejY1QZJflqD0Dy4bnK85qjv/e611p7zftLUxdQLSXSr1Qp5KpSwU/1jpYVeDwMRPkedE51OB7HYyYZkVAY0uiO0Pp4bDzeder2Ozc1NRwbojmcZWqFQcNUKUevaThFFHKPGjSo2fI8zVRV3Exq8cY0lMVAz9unGrCrBup9QWWY1CZt30XjKVB1JAYM0fcb+eu/PEd1b1J/Gcnb6W7QUXpXAgTcQ6o2jvMZJvrCw4E4m5DkBuilutwBxALFjGCV0X+Lhw2W1ALu18UvrsH1FIOqz6MDgQqmmFcpNrVYrxERnZ2exsrLi/k4ywAnAPvKVSgXNZjOUh9rY2HDOZKYxok7JUgKg0hnvv95PTZ3oIRtnWtN7IUDHCzd8TmatNSYB8BtlLS0tuXw25UlGO8zz+xENCYOemcG5QvibHBtescvgyMgIqtUqhoaGUC6XXYrgdIqAqh5aduXXVmtUqwod55s25tJyRkZFTLfp+SH6/pznXEB5Fsn09DSmp6exuLiI2dlZJ+tyvsXjceRyOReF0QPE62APhnQ67eYqN3AtN9T8MgC0Wi3nFo8iA2pqVDLAyijmkhn1bm5uOpWP1R3MhZ/p5uwTfG4yajTlswHgAho/xbnfc5tjmObofD6PIAhQrVad2lkoFBzJrVQqjtiSKPiKrPYs4Bpbr9ed0kw/GucmfSgca3z2/RQlDSy5BjAlpfecpJJGwlarhVwud0pvHK0eOlc452TA3zh99q+Ozn4dBwlVBbRDFaVzRicqHyrIRqkA8EtLr/QoU/9zaMStUqrmOrmIUjbkAtfpdJzhiPIUZWRO0nK57NQRmpwoM3Gi0Ni03XWqUZP3mwNaF3SmTwCEHK+DqhJotEHDZ7fbdTJivw5ljAo7nY5TfxhVq2TO9+DiwXwjxyk3HX/M63jn8cT+YUQ8iGgnnTB1HCvx4efiAuu36PZLe/k+PABJyyNJ0vlZ/A3IVyO4waoaoIeUaZkgQRLL0sBSqeQ2XW7GjM65kPN+sr8Ar49mY9/nwc9EROWltWpEyUCz2XR+BBJ7PV9FVaKdjE1VrDTXzvWOz4fPnSQAQGg87HfuWpWtIAhcupaGShICTYNpkywltXoPVBGo1+tYWlrCwsICZmZmXAO7lZUVLC8vO6+W3jeCmz8VHe4D+j7xeDyUttYgEIBrbMf1gMTaN78PLBnwHxw3wGaz6Y4mZp8BlhT6pguCg0FbVnLxY2TAXCyZHwcPHy4XCP6uqgH9jIH6GdScyGibkY3KVcxfkQQcP348RAYoW3GT5rVTNeHvF4tFdLtdFItFxGKxUIUBN3Hf7cyBqqe2sTlLs9l0C3AsFnODv1wuu7JKNZ7pa/eTfaPyeVF/Phtj2X5AN0Y+U26ClKWXlpZC9etqOOICT8mR7W85norFIuLxuFvYSFK1Qx4A1xCHCz3BSIW+EZIAHpDFswcYlW9HaPXz8r1IvvV6/BM8GQ0xCgK2+nWwnp79/svlsuvxwfvg58aVCFAhYUCwtLTk/AL0YXCe8HX4OdkifGhoyCkmXA94bUy/0SioqTlgiwyoksa5rkayqDGrc0DVD84dX2V7MONelRS/tFENlHo9nN8+OVKn+37MRSUDAJz/iV0H8/m8O0cmkUigUCi4Dq9ci/R50WPCaL/ZbGJxcRFzc3OYnZ3F9PQ06vW660ehLalJVLkfUFFSEkJCoNVCrHDQPhO+Sqx+G58AnEsSQJxzMuArAprXqdfr7mEqo1KvAOFHSMzBZbNZd6wwFyDmB/kw+OC1dE+JgE8GeO3AqXIq88CM/tQBSwmRn2dubs6VIPmtaTWnzE2XpU2UmMrlMoIgcIse88I0KZEc6TX7jFkb3tRqNbfoxWIx1xSFeU5gq+mLqi2+d2InPgq9h3x+Ud/PNaKUFJ4roNI/W2bz2Sj7V8mRLmMubPrsGD0DcHnwZDLpfmdlZQVA+N7QcKWdBsvlMiqVivve7yCi7dIDWu7KjZ8twXlq4uLioiM6vD9KBkhwhoaGkM/n3f0gAQdOLvwAQiY8XRdIqmgoZnS3tLQUaldLUsSSymw2i9HRUddaOJPJOFJLssD3oqTf6/VQq9VOOazJV/r8aM73Hil8NU3TmDsx+u4EUdEwAwc+Oyp/HDMkabwPGnTwOvdTGdB7wmAmCALkcjlHSHl92uRN013Almyvay2N6LOzs5iZmXHGTpr6eF/03iipHB4eRjabRaVScSpXOp0OBXfcR9rtNgCcYjD0VeIoQnCucc7IgEbT6rpmaoAkgKYrbTLRzwzjL4zcHCuViptwZIzxeNzlOqkmaFpBZamoxTMqgqLUSFOgEgE+fCU8/Hx0SDPiVyJBed5nvgBcznljYwOlUgndbhfVahXAVh91LlSaS2O0QLI1Pz/vvApcUGKxmOuuyHKoXq/nStO0WY0uHiQHfpWF3jPfbAOEj/b0Cca5gF6j5q1Z7bGysuJIwOzsbKhtKdWAKJcxFxpGYzwdj+50jfxjsZhzQjPypWzKaFTHPI2Cmh5QEuATQ38OqcTM+UY1gJsweyOQDHDeqjRKcFFfX18PnTLH7p4kOipN63Xw3tNITHVAe8RzE+Pnz+VyGBsbQz6fx/j4eIgMMMLjpkOlB9iqJNBmRPp5dGz6G7kalFVhZNtZblyx2MkeBtxQhoeHnQ+I6o2vRHIcbDdO9bmRCGjFCjc93aiSyaRTTJLJpBuPqgrst6FNxzM9H1yXtTMjgz3N46uiRGWAKRqWnlJlZsqOaVEGXFzTSJJYQcY5WqlU3DjmWQM8oCibzbo5yjHpm7n5TH1vxrlc5xTnhAzoAKZkTXZO9s98IJ2f2qo1KkXAm8yJWC6XMTQ0hFKp5DZImoTIkhl5c/OnIsAvzQ+pJK6DjpEfG5uw+5kep6x5eE2FLC0tubabLHGhvKeskVC/AV9nc3PTRV5ra2uoVqvo9XqoVCpOWubmwevlYkFj5rFjx9BqtTA3N+cWRHoUMpkM1tbWUCgUsL6+HjJVammaDnSqM1HKhBqcdLJohYa+Jv9/PxGVr6bkyJKjdruNBx54AM1m00UaJHUcp0CYpHJRy2QyqFaryOVymJqaQj6fdyYyjieOx16v58iANrrhd5pFWfZaqVRCRxQPDQ05mZ7PBNg6QY+fl98Z9TIlR4Vienoas7OzWFxcdAe4cLyqaSqKDFSrVdc2lpUvxWIRiUTCSfIkrUA4umP1BZWJxcVFd2x5q9VyY4ybfqFQwMUXX4x8Po+pqSnkcjl3rDgrgTh3GWgAYeMf1waqGHyOfmdRlfn5Xfs7lMtlAEC1WsXa2poziTJ9OT4+7hpAkRiQEPhlj9uNVT/VSmI6OzuLer2OmZkZp64AW51MJycnHbFnYME5fS6IAD0DHPd8Vj5BVkKmYxrYInX0a5AELCwshNJLVA34miTUnJt6/PDY2JhLTdCUCsAFcrFYzFWKRDXwUgXVHze7oQztFvadDPQzDGoTEXbw8pszRDkuVX7TFAElU35xwgRBcEqekhu+1g1vlx7gtXOjIBngkbTcqP3DKxKJhFts/dyRboxq7NFomp+bkRtJAaVcSlf6HlGeBi3bJAGjzE0ywKiUHoGVlRWnpvD3/QoD3n91ifuTlVIyr0+9HmpYVHXAd5rvJTS/x3uppZ80HHEzVEWAqgBfQxUn5jdJsIaHh91GxY0jlUo5VYikgffTV2B4j/xSQh3zejyxbrQcV/pnVY5IBrhgqmmvVqs50kP3NcmAX03AxTGTybh8vLZzpfKlah8jPJXl/SYx2g5WPULsDUK3uZ4gqqZJKoJa+kmVzm91zjHopxHVUOyPf03XxGIxRwZyuRyCIHBR+MjICIrFogta/EqPnW4UUcoAmzExuNLNj9dOtU+7+W0nXfeTsndzI1Mvi3YW1DVAyRfvjx9gauULCZ6SPK5DXF94P3K5nAuuRkdHkc/nHZklceK9JnnR4EHvk+5NXN9UdY5q+HUuScG+kgGV1jkIeaAISz3m5uZc+QcjZ79bU5RXQKVSRkgjIyMolUoYHh52GyjNWsx7d7tdt1CzJltLoHQRAbbyh5QSebgFnak8JlXLAvmeuVwuVFXgKx0cXNwQ+Z78GQ5idWVTOq7Vam7BTaVSp6Qn9J6TAMzNzWF+fh7Hjx93B3JwIqlnoN1uI5fLodFouAWXZzFQWuR9YjRYKBQcKfDJgBorORnV8EmZTuXXvZ4kSrQ4VriwMi1Ab8f999+PVqvlvrNckGOCE50bIReYXC7nmv4wKpuYmHASNnOO3KB4OItGihpxcIFhKezw8DCGh4ddOWG5XHZpAo4pzVtqCRTvAdWs5eVld9Ty0tISHnjgAUxPT2N5edmVwJIMsOe+qg3cQDk+qS4xp9/pdJyMzp9R5YjjUE2LJCM0FFMtoWlyfHwcQ0NDOHLkCAqFAiYnJ93GTPMwny1VBxoSeRDawsKC20C4WSSTSdezgSkY/9RHPm9eO4kzSZ6SbKYreEwy1ylWfeyk9JPQVBbz10zlHD9+HEtLS7j33nudUTgWi7nrpru9Wq0ikUhgdfXkiXp+0HU6k9tuSd1Kcrnh93tf/z15zRw36j1bXl4OEVmOnc3NzRB5Izk7evQoCoUCLrroIte8i6Ww8XjckQumCDim1GBKkMgrWWc6i+miKFJwLrDvZEDlV0aZWnpDdk6ioBFE1MBQVUCNgFoWyKiK+Un+HrD1sNQvEKUK6ITgtXPQcaLR/MfKB9b+MzJjhynNL/I9mUvkQqKskiTAz4upQqED0Wf3frSrbZ6jDn9aW1tz94jyJj+LNkryyQAjtFQq5YgQjYyEX2XB+6DlnpQpeb+Uae8lfMWK0QTlZEbElKhVweImSo8H3f0kmDQfUQmoVCoummW0yQV5u7I/QiVHrVv3y2E59vzc8urqasj0p+Oj2+268aw5em38xbmrxELnpyo6US787ZzU/hjXcauv5Uv4fvTuH8/sl0mqIsBnqX4PACEPEhdwXch9ZYwLf7fbdYSY1003P/0SVAO4OfgnR+5kvPsRsaYSOa/593a7HYqwtbQt6pkoOdPvOgZ1ndrN/Le+hu8p0TVc/y9KdeZ65vu3fM8Nq0z4PMrlsvPfUGXTa1BVm6+vCjavjyRRU9G+sqQE/1xi38iAvxDRiMWDPJaXl3H8+HGXm+QBLpqDjfIKcBJyEaTsVq1WXe6nWCy6BYDXwgicOTxKtb7xikTCX5RYXjU7O+uixWaz6f7OSUYTI3NhJAV8v2Qy6ZznhULBeRg0IuYixYieg1Bzqyp/qYKi7nCaiFhzS2Vgbm7OVRXw8wFwJ7Wtrp48enNpaamvp4J/pmLAz8YyId53XRi73a77PXo7SqUSAITKhvaDNauZVfOuzWYTJ06cQLPZxAMPPOAOMOG45aLLz5JMJp0HoFqtYmJiAqVSCRMTE056pCmJaQOOsSAI3OIC4JRnyO8c92qUZevskZER5xXgGKaZibIpyasv8WuEQ2WA0eXMzAzm5+fRbDZda172F9CN2ZdIuRmSPERt6Dqn/YBBiSvvtaYJ+B5UV7iQ00zJCJtzgJ3mlpaWcPz4cZw4cQILCws4fvy4M9txXpHcMmosl8uYmJhwfgxVXlS1UYVsY2MDQ0NDIY8MjXFMC3D9UXK9E0LAe6XVQSRvNHxSbaVnIB4/Wc7Kck2WVkbVu+u445rCyhY+Yz93v5tz9UyIhRJHBmskQBrw+IokS05LpRLGxsZQqVRw9OhRlEolpxBwrdMKIa7FPGaZlUX0tlHpJTFndQ/b21cqFee9UtJ+wSsDfoTKjYmTW49x1UiLUeTpvAKqDJB1aW6GEysq/84BrcpAlEtVFydtJqPHXaqywQGhXc6ArVIwlutpDpE5Mi4IOjC4qPK1lBUrW49i9rrYax0sNx5tgMEv3huWlzGK12enCgqvn8SB+Vx6DQiSGD5b3nMAjiytra0hkUg4l28/eXK3oPcqSrHyXex6eAk/j0rKUY1/yuWyO+iKZJCRghql/OtSMkDoRsv30yoYfqkjXQ1mHLesFuDnVmWAZNf376jTXudlVHS/0+emfpx+z8b3OOif/dfyNxElxH4zIG0RTXVHo0auJ5R4VRXwySpTMZzH3PT9Z8hnzo1Aqz3OxCegG7bObf9ALP5bt9sNGShP99q8Xi2npEoLwL0W1Qbeh/1S8qLgK0r65RNq9ZmpmsxnrQobPyPvgd5f3mOuaUGwdZqlX/YbpdzttHJkr7FvZEBJAM8Vn5ubQ71ex/333+8OHJmbm0OtVnMNXShF+gulGs/YEIKqwMjIiGu6ouYTbamqdbUqDZGxqftaXeX+8aR04t97772utSU7A+pgCILA5aeoPLACoFgsuqgM2KpB58Cid0JTHRsbG6Hyvah7riSGiyANYcyhaQ6Wmz6vY2NjwxGARCKBZrPp7rfKW+qy5gJItzg3eF6Tr1xwclSrVayurjqzTrfbdR3I6DvYCyktSjnhfeGx2ffeey+azSbuv/9+V1LI+8kIMpFIuFzy5OQkxsbGMDExgSNHjqBSqWBiYsLly6mgcCECEFIDdMFR8uYbYHnv6JFhJYFGHHx+3OAXFxfdsayrq6uutwTz/lz06/W6a4zF44L5b0wx+IYphS8h85rVcHc28qi/SSnJVSmYX/y5brfrPvuJEycwNzeHY8eO4cSJE85kx3vOjTqXy7kKhUOHDqFSqWByctIpjyR4quRpVRPPDSmXy6HNl2oFNxmdSzutoFHyqu55mgb5xSCFP6fwy9x0nnKOcmxQ7aSXAoC7T1qOR8WVz3i75xdF5Pj9bDZFVTI4Dvz0kgZlJNJMB9BzMzo66uYU+6tQ1aTRenZ21vUt4AF6DKgAOLJIhY5+lvHxcYyNjbl5qo3ABkIZAE5t3qJOT23ZqT2/VbriayhUGdD8uzqx+XPAVskSB42WwmlEpSVz/D1u1swja68AlaIoJTFi1rwmF3/tmqW15RxI/D8udoxaOGD8Cezn1zTS8r0FXPBUDYjyGxBUIdTcSHKg18KFnoYwRiH9DITcSDhRs9msM5VFtenk59ntyaIRlprL/OoWPc5UVSteE8eOKgJcZEhUKQlTwernIvbVnX6KmFYr+GqYjl/dLDl2Scjr9bqbj1RsgiBAs9l0m0BUVU+/OanXqF+aV+9XVhX1bKNeS39OfQhKAvh5lehp1ZLOWX42fZ5aBUJiwO9MYamvyCc2vhlOo2WSeH8+n8kmqGTfV/y086VWbBC67umXqo0kBBpIcDPkmsBr1oqRfvPVz+tHjR3/85/JXI8iGDp/VB2Iug+qsGn6U4MyXR/8tuNcr7iv6PjxjYM6fs4HEkDsORnwF1pG13Rhs1e01oBqlBqVIgBOrSKgo5rREQ0gjHQ1X61OfHaz8pt/ULqmTE7WTSZ47NgxtNtt10p4bm7OTURK/VHGFKoXSjSYeyUZ4OdlrwJ+0QzJQarpDV924utws+c9137urOLod4IW7zknP93QHLz6XWUxkisuFHo9nJT8/HxuvV7PETh6FUg6fJl8t8Ylny+fMSsH6KN44IEHXB8GHpvLDZXjJ5FIuAhiYmICQ0NDOHz4MKampjA6OopDhw6hWCxiZGTEKQKMCnXh1Ry4jnF/M+W/a5MhqgLsNsholZ+P+eLFxUXMzMxgeXkZ99xzDzqdDhYWFty85AYQBMEpJbNncqiKzs2oBVGd+FGekH6GYK3w4UZP3wM9HrFYzLXz5uuQvLENLX1Jc3Nzbr3hz3Mu0Ts0MTGBSqWCqakpV6HE/+N9VoWO36ng9Fu79OfPhgTwOZCoUe1jjxaqAlxHVZkkSWUeWw/94XzTYI2VXiyrVcJEX1av10M+nw99DqZNeN2+gZSkIEpB0nt5ptiOTCspUzLDjVnTxPx53yegfXCWl5fdMwDgxjyNwlNTUyiXyzh8+LDzEFG5pnn4fKgkAM6BMsCBpmYmsnXNYUc5lBVRA0g3YD4Un5H6blhG61pJoDkczSPr2QDMH2vpIxcoYKsGXJkm34OmOk48XicnrUp0KrtpVOF/dj/q4mfWCJz3VvO/UT0cfBLgM/moSI1kgBs40xm6IPA1uZhohKt5NyUl20WgDxZRapXvY9Gjh7lB8p5qjbt/MBCVAW5+NJRRUlaCxPu83b3VhVEXMN+h7BNCkkw1RqqiRWWA0r/OUW42qiD5ylEUosanH3FFlUr6RMj3A+nvqdrnR2v0qfDYWBoOfSVPK5Y02tVnSlOiRnX+EdC+IkCSx+/b3aOz2QB03Krapz1HdC7pmkQfhO9q971VakxkepFKEu8Vxx8JZzKZdOSa5mxCVUfeb3Xdk4gBW0cqk0yd7SYZRQZ8+CSU16P3mePe92JEdYrVvYjVRGzUpkT4fCopJPbVQKjlGP6BRL4MG9VTgIiSSjlRdeJyoPIadCHTScHolOYgTgpuEuzWt7i4iOPHj6Ner+PYsWPOmcwJF49vHZPKxhV0H4+MjLjIrVwuu+hZr4uDigZESnLcyLkgA3CkhwNNo5VMJgNgqznR5uamY7Fzc3OYm5sLdXHja+tm7z8/vp4+A/2zrxL4KoLP1DXNwNJC3zR6uk3nbKD55l6v50iItmaemZlx+UBGXFwIeO+TySRGRkaQy+UwMTGBYrEYYv/j4+OoVCqurznHFX0oupn5Blitaeez5bjgPWHjIlZhMAfJ3C3TTIxoFhcXnbOc1QFUtOiHUfKtYzKqZLUfVMVgRE8vztDQEIaHh50vJypfysXffw3eEz1Mh1EbcJKYUeXg/CcZ4OZ4/PhxNBoNzM7OuiNt2UqZz5VRM/O6k5OTqFQqGBsbC/UYOJ0DXElBv/t0NmNXK7I2NjZcuSsP4uGR72yEpaSc6xxz4vyMbHrE+8p1Q9e9Bx54wKljm5ubbixvbGy4TW5zczNUj6/XzICKypqfFtW1kz4EYKvH/5neL12LolIPutFrkJROp91ZA9xfeMgY78XS0pK7v6we4M/SrzM5OekqEiqVCg4dOuT8bP4YOltSuNvYVwOhRmFao0mWpa7VnSw6PiHwcz7+oNR8lbI4/V2VY9U4yEiRmwZb01Iy1oiCphT90nwj5VFu2lplofl0Hbi6IAOn1lb7EQuvn1GdH+36JVp6v/uRgSi5Tf9OFSEej4cqHvT++8qJehbUGR31nruF0ykCejYGo0iV8ZVEsocAy4ZY1sc0ldaPb7c46QYY9VxpFuT41bp3pqF82dqP7nz/Az8vf8Z36/uS7k7ImT8v1anN6+Uc0egoShnRnK5fxqrzms+v3W4jHo+7Uzh5v5XwMfBQrwDfl9GpnmviKzz9XODb3Y/dgj4XrWyisqreK1/Fisfjoc6sXJP4mShXc9xwk9Q28SQdfD2mWJlW4jkpXKeofGqKmL4DRtoAQioLxwVTLWeqDERtrEoMou6lGjE5F/j5WBGlZx2oqqS9RXj9NPWSBHN90J4FZ1o9sh/YF8+Ays6+2cd3AO9k4dGFQqVSLpxRLVj5nlzcdPFViQyAIyr84olXjKyYJlBmS2bNQ1JGR0dRKBScIsANQiUibtp+KQwHpJa1sdUta2RzuZzrjsWadjLOTCbjNlWyV147fRpsqUt5mOmIqOfmpxC2y4FqxK/RkZpw9Pd1ovpO892eJBrxciKzkoIRFU81YwdM9uinD4SKQDabxeHDh1EqlXDkyBGUy2VMTU25SIsdBzUl1G+h4hgCTm7ydKCrUZXPlPeOHgWOAf/ZU1nimRd87jyPgm2z9XhbfX3/ee+EnPM5kvxwA6UioERJ+3io61wViH69RVRV0bViaWnJydXZbBb1et2RgW63i7m5OdcngV4ZGl1Jzkne9ZqVEPjnBuzXIs77wHWSc5fnt1DR4vNlNAvAbVBUAKhajY+Pu7JXjca58bH6hH39W60WFhYWQq/JRkaacmSqENiq4GJKSk9RXFtbc2sZVVztlrkTskUomfTNtVEpKd5Ldm2Mx+PuFE6OW3423suZmRm3dur4YYDADqPsR8EqInpNOIZIhqOCg3OJffUMaESmhEDzMjuNQHgT1c3uu0D9RUMdpX6uWx+KVg4wmmYrS8pyLLHhe1Oa5EOnrEj5Nqr3OCdMPB4PmcdUkWAOkFInI29GMHpSHUsiffMj+8izPEwjI9/nAISrL3wi148I+GSA8pf/7H34v6fPYi8micqD+oxZkqWtS0nAKLWSdHJSU/Kj3Do6OuoWV7Zy9jtZRn1+IFxtwoWFKYKhoSHEYjFX/gfALe6MXrlRMe9KKVlPbeMY0D4JJEVadqX3Sr+fDr4ioHl3jk9GpFHnJkSRjyjfkKadOFdisVjoQKd0+uR5GrFYzBFeHtVNAsgoVsmo3ns+Zyo82mjrXCziGtREnUHAMmE+XxIdplvUcOqrV6xuIZH0xw6VgUajAQBOpWJfEVUEeJ2xWMzNMa6hbAHN9S0ej4cqbXq9nuvQuLm56fxUO7nXmvPv5zfRdJSus8lkEo1GA5ubm1heXnYqRiKRcEZukgBNZ/d6vRCZ5FhnOowl7trITnuAnC9EADhH7Yj1y88Nn27h0YfsS5BasuHLXfzSDlQsAeT/scUua8cpFTOipgOfvQ/i8bh73+HhYRSLRXcSGUmBTjbNc8ZiMbcRcxIxCuWk4QTXyU25j13uRkdHXRSjRjUOdF0sdDAzWgqCwN0vrXcGwmciULFQwsYNWyccu7GxPpfPlAsyX6ufJO43zdmtnBo3FT5/TvClpSU0Gg3npZifn3e19YyaNR+YzWYxOjqKYrHouguqP4C+DZYQ7kQK5OIVBIHzkfBIaqpEbEzF+ZFOpzE+Pu4OxGHfgiAIXE6W45WRnbrMlQj4RO9MUjO+IpBIJNzirqeG0tegCoZuqmpcpKrByiNt6OU3PiKp43jk3EqlUqHeHKyo0bI730Pkpyb8BVsJim/63EvoezKtxXSAT2A5bqn0JBIJ57cgsVTfBklrIpFwAZlPjvm6JJAkF8BWO3ffdE2VgM9veXkZMzMzzr/C55ZIJELHePMZcqyfCRHlGha1N/gHf2n6gmcMLC4uotPphAyk8Xjcbfz0mbBzrvpVmK6jKqhkgGsCyaSvUpwv2PcOhPpn/+87efBRPgHtHKWmDCUD6lbnINSmFCQDlLzi8XhoMeVZ7nTTMgJhdM6NgIoADyHRfBwXfA4i362qJx/6ZIBGJ05Eqg4sU6FRjWcB8HPS59AvcuAE4oDm3wGEIicSF83jKRnQEk8qJap00B8AwEUs3ED4xcnrez52gwzovaaHgmdJUEbXcjNuRgDc5+PiydSMyq0s1Yo6k34n18+FgSWHxWIxNE/a7bZLAQAn86xMV/AkRI4vbpi1Wi0k80YdCX4mapwPf/MkEad/gflSbcHKFIGSAT4freOOIgNMe6hTnmlGdbFzfPH1SXj4mbXToPpk/MocVax0HEWly/aLEPD6NZevqR+SAa6nGjxoCkvbrnOs6nkrVAT0dRk0JZPJEImKkuC5XrRaLSwvL7s0Blu2k8gx8i8UCm4jVqK2U0LgpxtJBvxzarT/C+8n11auQSTlSgY2NjZcapXKqiqq3Ad8oyxP0FSvwJmkP/YT++YZ8M0augjt9MHrA/fbPKoZRqVSbbKj/dH5kAG4CIJH+DLFoGSAeThuEEC4IkE3MpXIOeC4EfpSmkbcdP+SoWp0CmzVsDKn6XsRGJX7i4a2eiYpYtTgmx65iHJT4XvzdXUh1GfByUbvRKlUCslxwNYhRXw9JQOq7vQ7l+BsF1xNT+lmQ+mS9dl8xmzyxOvktVH9YXqAz8DPBfo55Z1ctxKrIAhc9zrOFb42P0sqlXIn4jGy4zOnS55EgJ+N4ymqp8RO4Jv89BmSAKbTaVdjrffJl6T9NJJ20mNnxJWVFafSqIObJ4JqsyyN2tUMyKCA76GbGD+3SseMWLVtM3Dy7Hol4/qcz9bxvlPo+NWGbWoc5BeVFQ2a1DTomyHVbKxlhEwp+WMG2JoTagrlPVHfFZVJBiPspLq8vOyeQyaTwdramvMMnC0pVWWA84WVDvzMJBxskMTPo2kmBorsdxKPx50SxfJKrSDgGkplIOoen49lhFHYF2VANz+V9rgA+Ey73+Kk7J05Jr+2m+oAN2AyXf9LpXAlASwrAeDIAEvyOFkovXGiqaObxEBfn5NYFxr+mZNQ0wQ8f5wKAQceN85qtYrx8XFUq1WMjY25PKBGQiQCZPjqd/DlLfoZqtWqU1vI6kkcyJ6VEGhEyM9PyY95bj5zAE6+BsI1zzqRuKn6xOrBTCBVBWgKZbTD1ADbizK3zkUplUq5fPfo6KhrQEOTEIkZo+F+VQOng25g8XjcKSwkWnyevP+xWMwdQsTNiM92YWEBjUYDx48fDykeXNxJiM6EDHA8q1dHU0NsvUpjI8urqJxQwdI56svKLIOkoZOtg9nsSzcUzhWuJ8DWok6yv51pUwmmKolcM2gqq9Vq6Ha7rjMmVTKWhmmp6F4t9JrfpsrJ9B83WqY0uVFxHLE5VaFQcKqAtlxPp9NuDJAgLy8vuzI6NctxzlP9YX5cS5pJZrlW8ljoubk5TE9Pu2ZzANzYHhoaQjqdPm1vme2gz7LX6zlPQ7lcxvr6OiqVilPEOp0OADgPjva1SSQSzstAcqMNnrT0mc/fbwCmPgGui+erT0Cxp2TANwRpv2hl9aoSbEcElIn57V99ZYBGQW4C/hew5eAmAUin026ABEHgNgYuPrxef/LrQsKBRMc6X0vbhfK1/Dw6SQJzm+w7wE2bX1quQgZK+Yn3k0qIf5iGSlskNXq8LjcgbjiUajkZ+B58Dpqb4wJBySwej7uJxppkPxfrN6RRg9ZuuG3VaKYlTiSFLLfTVtJ8xuowZ1qGBkE90z6qdPBsrtk3YqZSKTcv6D3wo19gi3BSuWI6y08N+W1Tga0Iebt5p4qcPwdZF85FnY7qQqHgnNVaPaD1+Xw+vpJF5zmjSC31ZJQaleLQCFFTWdzsSQ78MaVeGZL0drvt1MG1tTVHyDhn9D2UkO3U7LZT+MqqpjX9lrj0TvAaSNZUKtdnoKnUbrcbmg9sqsb7zXQhn7uuuToHgC1yp/1k/JJmlcr1mfjf+f87gT4LHiOtnS9LpZL7jL4XCoBLHXEN5z7BcaaN5frdY1VJolpWn8/YMzKgg9iv56bU4vd17scK/QVSHd1aAsS8fTabDUXkfucobtJ0qyaTSVdTnslkQnlaTgjKhcDWRsovjUZoLuEA0FI2vj/TAdpUSNMGSmR6vV7oWGD6EViyMjY25rpdkZFzMdOmTtrimRE584iTk5MoFouYmppymzOlNm6asVgsdDxnLLZVb6+GycnJSWceA+AWE0bbqVTKSXE0ekXVoWvZ2YOZSEoIScQY/VDxUXMdqwf0ECBWCxw9ehTlctm1GK5Wq86c5dcNny34+9ozguWG2rnNN9utra1hbm4O7XbbtVE+ceKE+1y1Ws3NP82T66as94yfgc9JHdnaSTOdTrs01eTkJPL5PI4cOYJCoeBKL+mpYCUBiZMSAUrePEyIDYKOHTvmGgWpiZCf3fc7+ERACQzVPFUldJ0ieWVeu9lsIggC5PN5dLtdFAoFV3q2ubnpfB18z72I+vzxSyKrMr62FOfGxg3RPzlTzWx8fc5RNtRheS19JrzX2hCLOXF+J9lj4MMU3OLiolOm5ufn3frHscQ1geuAbqBnQqr5PElIWNI7PDzsUgBqIqYiwrVXidbq6mpobvD3OO80oNF+I3qf9bAwNcqer6oAsE/KgJ8i4E3nd90ATxehaCTJwambCP9P2aVeB7BVJsf/Y/c1LgZ8+H75nb6m5gq52QNbzYB4vSQlzPNxMnPQcUBGgZ+FkZdGpzT/+Izcz3kyiiDh0mtUeZ6LNTdgGiRZWeEbdICtWmNNk2j0QVkxaiOPijb3orzQVwa0sUjUYSP6nPlZKLOSgPqVK7t5vfx93jPea/WgEJxX2tKbhjumhEgUVNUCwmWkmp5TosD3Z/pCe3owOlSPANMlWvKoPgHfVKmeIapm2lvBbwWtVQS+f0WvmX/2jY0kbbwW9TAxCuR9JWliP5F8Pu9y0EEQuE6P9HZQHvev48HAV1bVMMl5zXVU/RDqF+Az8yNVvf+cAxwvatbUcUPp3K/g0hJRrUDiuNTWvYy+OQb9kvAH4xNSLwufr65P2gSOVROqXnJ95H3k5+431lQl89WAqEqi85kIAPtABlQVUAmQMiAHi8qXPiHQgc2NnxLk2NiYa/c7PDzsJjo3XZ+t0yTEBYiSfiKRQKPRQDKZdNfAycff0+oFTUfQaaqmJLJIfi6dvMyd04yokXYikUCpVHKLF0vZ9IhNRuKMyvl+jGp4zKbKxFovTwc6PQdHjhxxUT3TAFQXAET2vueCyOuiujAxMRGS6ZifU+VDJWrNPft9Ih5smkCJAAkAZVCaBrViQyOCbDbrmjlNTk5ieHgYU1NTzjvACMln/rsBLmh6f/T+AVuGLxoFm80mHnjgAdRqNdx///3u8zHlpGRVCStJAe+XysyxWMw9b5WZmW+mCse2zOPj4ygUCpicnHQGQtbsc76oyYxRmubAWb7Fo8zZ7EZrv6PKIf17x2tOpVLOx0Ilh+SE95NEiuODc5UeIjbX4dHaPBpd02VqWNxNaApFx69/RLF2U+Smrb0qNFrl/GIgRuMsD4ybmZlxzn8qopzTzImPjY25+aHNmEja2LCIJlZeK8exphb1LA+/GudMNlFNBVEFGh4eRjqddmXkfFaFQgHASZ+Npq4ZtOrY2o4gk2hoHw3OD67n52v1gI89JwN+FYGWAemN3y5FwO+8sf3KRpSV+VK+SvoE35OyNYBQhMBr9Bmg1qzyIXMi+iQgSg3xFQEu0GwWxM/oNxXyJwwXVr23fqMiLiK+KsDIzm9nSzVDo3r9YqQKwG2I6hWgeVIjWVWG+qk/mgrYCxat16HRFf/M/+dn1v4H/caZH8Fsd81nm0v25Up+57hVkq1prX5HLXNuUCrnZ/Bfm5+H41yP8CUJ4HeWVNIzwMVQ1TqNlPSe+GuD5sN5/f0qB3Qc6TOIKnXk9XKs0r3OckS+Fk2aWlbLlCH/n5tLFDE5U+PbdtDXU2VLx62WVvoehn4km8+Z91SPZdc2zfx8/Wr3fXWM16nPUb94jZzrvCaSRF6vqoZnOmeifDdcS7V998rKiqs4INnms1YVYLv9SMcaP4dfmno+GwZ97KlnwM/FaDmM3x+cE92XYlT20XahbGIyOjrq/lypVNxD4KaoGx29BJz8NNRRDtLI0F84uaBQfeB7UY7yzX/Mr/uLGCehewD//wiG+adMJuMiGLa5ZT070wNMj2jOlbIwGf2JEyccO2eNME2S+Xwew8PDGBsbw/j4OCYmJlxHvSAInHymk5WLKgC3QQwPDzuH/cTEhHs+rIxg7lDNQ5R6SSq48SrZ2C3zIBDu2qZkSaVQ7e6ovSMYBfmNnbiZaD93X33yFxOfDPjmqO0+p/4fFQGqP/qsjx07hnq97vLdnF/8fVW1yuVyaOypGsaFMRaLuVQAySjvC42n9Iew7wGrCjjOtPSRz1RzsH4JLCNemnf5nPwUQdQ90jIvpi/y+TwOHz6MYrHovB4kMdxIqUDQV8Hx0ev1XH671+u5jWR9fd19RhL0M22SsxP494hlf1r6RwKoRl0SWT3ETA2cDESoKMzNzaFer+PEiROuJ8Xy8rIjksyLl8tljI6OunnPMaFjSLsNshqBniVtFa1pT67dfsnj2aYKNGhhrwC2PtYAj4ZorTLQeRpFOIFwQEUFJursmd3wEO0n9txAqJ4BXZQ1ao7KyRBRbF+jNZWwNX/rG578nBQ3ZE44/lklWrJYKgJasaCnG2qOkQuMHm+p8i4/ow5ydWerSSfqoBTKX1o5oJucRodad6yRob6Pz/Jp6GHjFv3i/zPaU1mMJ45xEvpReBQp4vPdbZ+AjkF/PGokp5UsOt7856FKkG8U7EcEoiKMKALQjxRE3QP9DFomyWfN564GO0rZqj5RnmUqShc/qje8NpIBlkqxfCqXy7lmR+Vy2ZVXqadgu/pqPhs/b++vDzuJuv1o0He866ZIuTybzYby7bxvfLZ+WosOcz9Pv5NKqLOBprj0/kT5BfT9db30K3U0eudn0gO6/OOP6atiukgbvOmciJrzfA0dh4RfEebn2s/WM6DQdUUVZf9rO9IR9Tx1zquy5n/pzxkZCMIuWFUGdNDppIoiArzhHIh0bFar1dDhJ4xaePMLhYJzWg8PD6Pb7WJoaAjx+EnHfyy21eudC6AOfnWKMopWt3y5XAYA9/lisZhz+fI1feOWT1K4qbI+m4ccaSTDdq7Mu2q1w/r6uusod/z4cTSbTdx3330uWmTDD5bycAOvVqsYHR3FxMQERkdHMTo66qIcXViopDBHymiKpjD+LmvtdZNV1742L2G0ofciyjy02wZCP12lY0+bqWiutd+JY+pI99NOuolwYVT1iePaL3PTfKQuZIRGiFTWGHUxqtV201oKy+vVTpmTk5OuHFDle78xD/PBmhMlCaAhj0SQRJWKia/y8HnwXmlwoM9CN1Z/wVXjL+8l7xfVQ64F7ANxySWXoFwuO6WNn4mbIedUEJxsSavGMUaUdN93Oh1XZugrmrsFJQJqPtYzRrQklqokn5umGbXpDdelRqOBbrfrqk+OHTuGWq2G2dlZN4bop6LCQF/W+Pg4hoaGnFeA84HKjZ76SbWS10ffAbt1sveEtqnWtNKZ5tmj5roS/u2CTsMekQE/OvJVAb/HwHYPyGdg6mb1GaoaeLTiQJUESlVa+qfXqwuNTirKZJRGi8UiADhjCk/i0g1CP5efW+Kmw9eiqY9nDqhPgJOEk1qjqKjjabVuXptp6OfxKwC4aHDz4L1bW1tDJpNx5sNut+uuU+vt8/m8M2Px57h5+Y5nzW2qErMX6oA/LnVRUIVCx5pf6qQLKsfOdqkBP5LT1JBu+hzXei847nQc6rXrPGJuXR3bUXK6ElHOA5aYsbcE35PXC4TJAMcj1SpNA3BDoF+EUWiUIqD3ip9HF2//WWwXaSkhUEWQ810rZfz+EKpatNvtUK28eou4JuiaFVUKvVd+Af+ZR1URqLrl59t1jPmRux5pTgKpawbHu1YN+R0HVUU9nSpAZZLrXz+V4Wzmv5JMf89Rcu6PN95jfY0zeT7+7/X780HArpMBfxCrhK0dAPv1GFCoCcQ302kzE90sOZByuZwzx4yMjKDX66FarSIej2NhYQGJRMK1I1YZHwiTAJ5TT0f5RRdd5JQB5sjo1Gb/AEZI7NrHvBs3GKYamG+j23l8fBy5XM51cKN3gIsX7y/zfY1GA9PT06jX67j//vtdfTbzyf6Rx4VCAaOjoxgfH8fY2BjGxsYwPDzsZN5cLhcyVbH3fRAELtLs9Xqh3PHw8LCb3HymvV4vdOKjtkHmc9VnqgbEKGl5N4hBlHTvP3OqIerC5mdV06a/yal8zyoRHfNcIJUEccPk5sPv6ronueVmpPOJvem1oYtW5ij50PvNPO34+LjrLcH/Y05V5WY+E82J8lAk5oo5Nnm9SpaU1PjKjLYHZzTO9FW5XEY8Hnf96pmyU2mcv8P3VKKjXhvthEjPDecnv2/XQOpcgOPVr8Tyz2rwD5wi+NyBLVWJB/JwzWIPhxMnToTKUUkESKZYdUQlkGoZ7yOAkK9BiQWrsUiw+RyGh4fd8+HJrn6Z4unuvT+XmaalMtntdl3vEKpnc3NzmJ2ddS26eWgX18p+FW3+c/HToDqWT9c353zFnisDPqultKa5rigioJIqGf92VQT+BqLOV+2Wtbq66uRvrTDQgedHUpwUVAaYQw2Cra6F9XrdpSco4Ssb57WyNIsmRJZmsYRQO7lpy9BkMhliur4iwNIdbTDEaJ6LLO+FHiWrjm8qAzRD5fN5xGInmw3RLxAEgVMreD+4GDOKAhCSf9U3oW59jRL83OZeLsT9vClaMaJjzXdjb+cT8A1fLFWjp4Rji2kGSuq6sHLjU9UEODW/rhFYVMrNT0tolKddFbnZq7mPv8uxwflGI2lUlU4/HwU/U5Q/QOu7AYSixm636xou0ceiaqI/Z/X59avEoa+BnptutxtZhaTjz/88Zypfnyn88cRUgU+g6ND31dUofwbXjCAInD+A5d3azIkkknPUL53zibEqKH6ljvpPNDXqe420VLufmrTdPdKxxTmghy5FKadR/prTeT/4WaKej1Z0HNR0xJ4pA8r+yWpVklLPgM+glO1zceBm7k9sbWiiEhM3NkawGxsbGBkZccpAMnmy62A8HnebpuYplYCo6Yr5cnbYKxQKjpmTVTebTVQqFVdjrxEnIxc1XrEWlgszCYf2ugfglI5ms4mlpSXXLWx5edk5yOkCZqRApzMrLiYmJjA5Oen6M3Bj50bBCZFOpzE2NuZasTKyDYLATWBKhirL+SYlfmn0lkwmQ328mTPk8yT52Q/zjUrzAEKkUzcJSqFcAHQz4j3jwsK8Lqs52NJZN1qqR7z3lN5pzvM3c5UkfYnST3/oPVepWAky5X76VXhIlX9f/IoS/tlPcejv+M9MF2pVNlZWVlwPDG5CLG8dHR11nf/y+bzb5Ph6/O6nCLQMUnvnc5zT+KuGMi2d4xcJMdUHzn9tqsXNa6cb2JlA003aIIvKqq6fKn/r72kPB01X8RCo2dlZ1/dFD0SjEjk6OuoURCqjPI2V45dqkl8+zfGpxlJ2jJ2cnHRVOlRxfFWm39z3xzuNjmztzsqKWq3muiq2Wi3Mzc25Mxfm5+fRbDaxsLDgmi1p0HI6QuCrA1xrD7o/YU+VAV9CUePg6U5OUyeobsh+GYfP6nVxVyl6dXXVnRFfLBbR6/Vc1MHSIc2VcnGJ6mtAyZ05VubWqRLw2vSEQ+2SSImNGwBPn9NmQ2pS081Wm+c0Go1QcyGyXe1oSFKhZ5nrQRrMCTOqCoLAkRBO8FQq5eQ+Loy8t+l0OrRg6SSOuqfql9AUgeYh+0XgewVGxOrr0O9cnPxoRFUQpm/0eFmSAR6VrNI2SQBVoo2NDSe787kx8tJNWhG1yEQpHjqXGDlzPPLes9RQo16+t/91Jt4OvWcMELgAazdO3sNMJgMAjhCzzLXVarnf53zV99c5S58H1wkt+eI9VU+D/8xJKkiKNV0S1c9kt8dpv8hTlQH+XZUB/3e1pJsbFhuk8RAhPc2UhIsbN8mib9TWfiJUdfRaVV2gKZevpy2MWU6o6uTpiFWU6swgaWNjw5kfedgbN34eTb68vIyFhQV0Oh3U63W3nvp70U42cN3sH6wH4XzAnigDvslJlQEOPO06qGRAFyCt4SS71wN6ojrA8YsbGxc+Ot6DIHBVBTxml45hNU9x8dINmFJdKrV1kA8lR0r+QRC4jZevy8WEk0iZskaH2jtd5VdOenWS69G7rMnWCIv3hZv++Pi4Y/p08VLl0Pfjc1D3MVUWwo+KeH0aWWlaQscENz86kqkMaM/0fuVoe40ouV97lbdaLaRSKee0JrgIUnbkc1lcXHSO7aWlJfdzVAb4fOhm17y1KmYa4ataFZXGUKmb95A/r7/jl03qffclcd+I1i8V0O9+6oKtJ2nqgszNinOK7X7b7barBWcJIBdv/zp80hN1X9TT4AccGjxw0yNBY8tl3RQpl++VvyUqJ3260kbOM01TLS8vO1VuY2PDbZTMl6tfg/1DqCJOTU05rwDLMvlzvprKNZtrHAOjjY0N15SKlVlUBNTv5afE/HGk94X3gGSSSgC9UvPz81hZWcH8/Lw784KpVPoIuF765e3bIYpcq2Lmp57PlefkbLCrZMDPC2ruVGvftdFQP+OLRvZqClJTV7+e58DWZkYJdHNz00n7NBLSpMRTC/0BqNKmn5dlJMmFplwuOx8CNxCtLtBFmJu+lqnx8+qiy+vggqpRJ9UAlgLpSYdcUOk/4IFG/KIJiCkEKhCciFQj6API5XKhe8OFVZ9Zt9sNRVW6yShjLpfLzjSpKgXTPkwj7KcywPsMnNqpkM8SONm6lJ9Pr4sLMvOuSgbYTnd+ft69Zjwed2RgdXXVjYeNjQ13f6JK1rSixq+U0UYnfiSvNd1+hYTWj0eRAd1o9e87vae6FnBzqtfrLjqr1WruM6opkWOBh36xIRDvsfph/Ov0o3y9L5S2+bP+WkPjcblcdnM9Ho+jWq26MmOO134VCA8GnO9+BZbfzU/LGnUjUzLAA8Y4vznGuHFyzdAUDecmD0ObnJx0pcj0YJBcAnA+IPUCkQxwnaTpmMSf5cgkwv7Yi0qJqb+M6Q8egLa6uuok/7m5OaysrGBubg6dTgfz8/NunDEY1bNh/BTLdtANPsr7pGNN19ODQgh2XRlQ+UbTA1HHbWpJDLAVhaiUWSgUQmVBen72dgtYlGmqVCo5UhCLxZxC0Gg0AGz19+ek8ut70+k0arWa8yIwutcNnEyXGyjJDSeQ1mFTauPg4ULF+8DNwK/KUP8F7y0jdxIK9Tao1Ed2v12OjmRAXdx8TnpvOXm0HFHd+EEQYHh4OHQ/uNmNjIw4ZYDkjtGZRgh7AY20CV1I9WS4WCyGhYUF55vgxqHgIsWolWceRB0Du7GxdYwzgMhN379O3m+6vP3SrFwuh7W1NZdjJ+HgwkQVjWkZ5oR9422UcbPfn08H3k/mdbmRk8gyfbK8vByaq/yMBOfWdukJ/3lq9NYvYtegg/eS/qJEIuFahnOzY8XPyMhIKCBhMLKbykAUIfXJQBQR8NMDbK2sm9Pm5qZTWbhmZLNZJBIJtzZQCdDurnrglKqIHJe6xvLZc54EQeA2/6gjrXltup4wANJxpMZZVeHofyAJUGWA/gjtd8BxpSSOa7VK/Lo36TjzjapUQP2Sy3Ohbj4Y7BoZ8HODfgmUfwKZ37BDJ6cSAW5kPIiIpjf/iEg/ktRNmBvR0NAQEokE6vU6MpkMFhcXkU6n0Wg0EI/HQyoFS8TYWpMegXK57Ex1nETqiGfOU+HnopVV6uYfNWg4ubUdaaPRCB1UQvNPEARuQ+UEnpiYcCyfqgCrFXj9/r1TZSBKOlPVQg2HXFA3NzdRrVadh0JLDplOOXToEKrVKsbHx90zJSHYC1XAv8/+FxdfJQJLS0su1UPpWjdygveI41orO9hsiQsz1SPeYzUu8TqjrlVTBDo/mHqh0Y7ziYsVVStWfuiBNczTagXHbixefpRKEttoNFzq5MSJE2i326jVaojHt1pxc84ydaJkQDc1X4bV9UNTG1GkQBd1LfddX193zcXoGQBOzl+S2snJSRQKBdcIjArPbhheo9JUWqZK4u9XZOnGxfQJzcuqopDU03RHcB1lqd/hw4cxPj7uWpWz8RnnOO8znzGwZfykGZrVUrzvrD5i23OqB6q0KZlhMKZOfZJKrsudTscpACdOnECn08HMzIwjCWqe9Ek3xwkVXqZKNE2sa5+vWOt6p6e+qpdNSyQPAiHY9TSByltaAuMfOqKqgEr7UeV8upAxuokiAf2iBVUH1EBYqVQAwP293W6f4g6lvNlsNpFKpbC0tORegzlDjfCjFiyVI2OxmBv8UQsU7yO/R5mIfMcupWMAbiBycDKK8a/3dLX8O40Io9I6lFmTyaRbvFjWxFyspgf8nOFeTB7dAPwvve+qDFCa5v2lmc1XorhoMPqnR4YEiL9PDwtlcJpFtzNR6uasGxjv9dramuu2yRMsY7FYqFxL+79rO96olMxuSd0ayWmqULvTkXSRDOg1M4JTJVFlZ/+Zag5X0wL9iIO/NpBIdTodZDKZULQYj8cxNDTkKoA0CjyTcrid3jtdR/2SQu3W6G9YvrGOKiewFZD46T5WsvgqIhW7fukQflaSAvqoqJTymfK+aKm0bpY697gnMIKnKVDLZxlcMAhiumlxcdEpAVShdK+JxWKhsa7eBP4clRNei68O+CRSU3UkQGosPUiqALAHygCjHV0AOPnJ5rT3taoCvKFsdMHjY6vVKg4dOoRKpeJKjtTIEpWv08lOUM5eWVlBLpdDo9FAoVBwKQAaxbSnf7vddqx1ZWXFGQQpfdH4FY/HQ4uZlqRFbfy8xu3up19BQAOWml84yIvFovNBZLNZTE1NoVQqYWpqyh0swla0O41mTrc56ELDzY4ucEbZKl0HQeAiUqoB2sCEi8duTSKfaOmGofnydDodKlFSdSudTmNpaSmU1tH0gk8KgC1DIaMmLrgkgolEwknMY2Nj7khu+mJ0bGtZI1+LVTDlchmxWAwjIyNuXNNsyxr9TCbjFLXR0VFMTU25kk7d0HZzM+Nn5ybOUksejzs9Pe0iuXq97u4tiSRJUiwWc3ltLadTMqAmLp173Gz6qQmcN9zAgiBwGx6NacCWr4BqAcuB9ajd3UoTKBHw57xfH69lhbpp6d/p4+G5CiQEWt7LZmflchkXXXQRqtUqpqamnMmYigB7S1D655rINCJlf3qmSIz9tV37VLBSg9fL9AcPVms0GiFFRBUSkgF6BGZnZ7G6uurIAOcxnx8JCMkIn7+ms+v1uiNQVFr5XDgWVE0iwdF5y+qV3VKL9hN7YiBUdUAbiyijVcMGH1g/+UUj234RTRT8TUBfv9vtOkmbMitlV14jF2EtnWu1Wm6RCoLALRrMJ/tRi28kOZvB4Uv1vFda3sfBx5pdv3uesvHdjMCjNllu+pxs/OK/U3Lv1yNiLyaPL7X7Jjo1RaoBi4uDLl6+MUijJP+5M8oE4O4PF2LtK6AGyn5eDn8s0x3ORleUZUnG9PQ+9Yv43RR3UxHQ1JEaMP2KIu1GSpl2c3PTXRc3EtaNU+7WAELHHH/P7wPQL0rzSYH2FWAqh880Ho+78aypFT/Ftpv30E8X6Drqr6FRhjv9fY5XXis/Lyu1WHGkBzlRqfJ9PP6Y52tz/tIDoNI7SbBP0Pgavj+CAY+eykhCwO8kRjx7hUoco3xeI9+PayA9M7wmKr9UXegXAeACmKiqHn1tTQH7qYGBJAP98l3+OdlaUgjAPRQukMViEWNjYzh06BDGxsZwySWXYGhoCIcOHXIHrWhJ1U6iW05sLRPKZDJotVooFApoNpsuZ0hTotY/b2xsONMNXeMAQv0BSCy4aABb0pxGkmdzT3lf+VnI1Pm6HKgsC8rlcu6wo8nJSZdm4Qa0W5GgLgjqsNc+BcPDw6EomRuQpiy0z8FuTxzdPPl8Njc3UalUnJmU8qSWcnGR4KRXj4dP8vj/2qGPUjIjSvbxLxaLbqxraSENavRUaMmnjm+m0QA46ZpzLJ1OO2mVn5XSNmVfdrmsVCpugd4tSVNzvmzCRUf38vKyUwRmZ2ddJNdoNNzzSSaTrmKDJIrRIhd9Tb1w7GjbXG1kpSlF/9npc+XYU98FST2fMe+Tbw7bTSUrynelh7z5KZOounhVCTRy17WDAQOPdL744otRqVRw5MgRDA0NOZMko1w+H3+d9eeBmo5JVvyAzE+z6bxrNpsuym82m5iZmQkdeERVgH1WtK8Aj5xm0MFnri3fGSCxa2oqlQopEFTXmCokqfB9AyTcWp6rHTp3O3W0X9h1ZcCPDviAKF/pFxCO2Mi8mUNVB7Q2plB2ydfoB2WynMTpdNpN/F6v50oPeWof89vxeNyd+EeQofu5LP8z7gaiIkJl9bFYLCTFcXOhqzeq/HK3zXm+DE8ixA0eQCiK4XVslxvfbfRThxgZ6Ul0wFYTIaZhuPnwXmveUaMufk4+C25UbDBFMkAZnESSz1MjWj+60MhEx3Gv1wuZWzm2WRLK9+Z40EUrKop5sPADAkrGXMj1XBI9VIkpPI4HejU4t/z2tnwGfgMlXS9U/fA3bX/c6tjl9QNbFU7cDJVAKLHYLfjrh7/RE/0iT/9zqfmSmxfHvRrfoloN72RucmxSRSUZYBUGoWuPkgcgWhmgikQTLnsiMFXE79orgNdDosh5RqWUKUkqaEyhcDzl83kAcIGDqhu6X/n3+qAqAT52VRngdyUBnOxRxhfeSC5QrG+dnJzEkSNHMDY25iJcHrNbKBROcRafDqoOxGIx52JdX193zTFYZliv11EqlUIeBw42LhbMh6ZSKccoebqf3zfhwUA3efZLYEdARt5ra2uOqTKnmUqlQucckGD5DtfdgvoiNN2jTF1TQirxRi3UuwWdrJT4eX8mJydD7W7J7llOSGWAkS6vWVUQNYKS4FB2pQmL9dl0WZdKJSSTWw2mGHlpwyndFKNkWeZt6Q8BTkqabLvN+cV0mnZ549inSrabeU1ND7COnW1feUDM3NycawBDksDPFY+fPJhIN1sts1Oixc9DRaBUKuHQoUMol8s4fPgwSqUShoeHQ8TAJ8I6bmmEi3KR6wa22+RpJ9C5pSZJVjxwI9Tr8wMHekdyuRyOHDmCQqGASy+9FKVSCUePHkWpVHKVFFoyuVNfEf+f9/F0P6uGQW7q3PTZYv2+++5zHVbpHdEKg6jPTmWWz54NjvRQNioDDPbYq2Vzc9OlH9jPAkAoMPCJ2naf8aCRgl1XBvTPUTksf2MAtvrBM2LTg4W0LlprXM9m89DotdfrufIlGohYI6uHygBwREbNSmcr/Z/N9fIzkxTQZdzr9ZwZhukNRpqc0GoQ2y2fQL9rBcKLZRQp8onDTgndg7kuLo4sgWQ+nVUhNIoyUuUG5Ztc/YiLGzY3cD/yYic7PfaXaQKSAO054ROk7aI+/ixwsjSM+U1+V0Oc5tFV5t5tRYBQR7u2xFWZW/PgAE4ZnyTvUSYuphH8CDfqlMnTdQjUcatz3o8E92uR19f354lfLcH0mz9GfG8Mnz+VEyoBesaLf2DQmQRbfN+oP/f7GQUJH8eF9qZhqqndbjuFSEkAnxnnDZ85jdJajabeCA20NK3W7XaRyWRc2+vdVlLPZ+zJ2QSngw5usrlisYiRkRGMjY25+lY2puH/c8E9m0mp7J4mFz0UZmVlBZlMBs1mE9Vq1bHUZrMZ6kLHiJCDjf21/drSBwNdoACESnSY82NrZUp0lN/9A3C4GCoh2CtwEec1AQhVcyg0xbPXi6tG0zwkiuWPiUTCRRGVSsU1GGKZEhcePne9x9zQKflXKhXk8/lQQ6XR0VFXtqVOaj4PzaP6G2LUfeHCx/tHeZ2NsJRsq6mJ76fvu9v3XvPdWlJI0yAjLqYK+HMkjvrdvw+cW9zotV/+5OSkq5wpFAqYnJx0rYOjuitGEQKfBETd972Gkk2N6tkMCTjZPZVBDMt2mdIAwl0q+dk5LsfHx5HP53H06FEUCgWnEPDf/SqWsxkfZ/Lzvj9CxwsrUBipr6yshHrS+MY9kj/uF+wHwT4J9EGQDBE8kTWfz6PdbiOfz2N5eRmpVMp5wzRldCFjV8nAdpPMn+Aa8fqdnNSApZusn+M8m+vT96bMywWa/gDmtvnFQcpIl7koPW/AZ9O7IXv7EiYdzz7h0EnB8p4oErBfhhaVAjVvGPVz+3UtXEBICui5YG2+Rh4rKyuIx+MuYtAWz9yQSAa4wZOksbmKdstUeT6Xy4XkeT/3fLqxrURRzalsnqJqjBINP9e9FyTMVwK1xFLThFEVRfoa/Dx6f7hWqKdIW9zyzzRtqiq2nTLg41xHgLpGkbxpTwk2UOOaReMjwXWAv6PNfmiqZABDk6A2oNoPDw+hKWX1mtBD4v+Z90c9HlS/1JBL8s3zaLRSgv4Svd80DVJhy+VyThmIUur8QOZCUQ52jQz4m75GJOq4VImWOU3tWc0DdUZGRtyg7VdqdbbXyYfLBQuAO4BobW0NlUrFlRHyeEumDrjo0mTDnDw7lHFSPdjIi/eRJECJEycJf04/EzeZKGKy3wP2fJkgunHy+QTByVMGSQjot6CEyrIl3utYbCsVw6oMPWiKC1I2mw21XGXfBb+2Omrz3+n94s/RrMVx4m+u/sZyrhcuXcR9d78/fjl/OPZ5z3nsLTvkDQ8PuyiQviJfhdkLn8xeQEkr/SeUtCcnJ11fk06ng0qlEuqvT/DeqrFyaGjI5c/z+TwmJiaQzWYxOjrq+rpoCulc3Ss/xUESHY9vlejq51NCzqqciYkJ5HI55wmqVquOOGobbhJWkql4PO76dfD70tKSSynrek7zsQaD2pTpXK23Dxa7rgxERf6a5+KD1khLc39a46ru6t2SNXWTJeujEgBsueDZcrhQKIQ66GlHOc3RsxRsN/NMGgXqph/VhU2NQ2cSaQ4K9JmTFCSTSdfHn8c+MzqhwkLzEBekZDLpyvKYHiAZoFTJWn5+kRD7hr0HS2qpvmynwuz3GOj3Pn5kpcqWpi981772ZaDkXS6XndmYHfO4cfoNcvZbFXuw8JVLbTCVSCTQbDZDZZdaVw/ArZVaLUMyoKZibZykfVvUtLpf8H1E6pHixu0bJ31VjmW2TMsxXcDPTvVDjZckBOw7Q88AO3ry79r3AkDoOlRNOchEANgDMqADmYORHftY388cJ1nV2NiY65DGCa5MbreZPV+Hi732QadioccWM6cFbMmXKsurdOxf724s+ABCi77vdga28lkqYen3QYc+81gs5kgfyVw8HneRVKlUcrlLqkckA/wd9WVwDHBsq2FPjYG6Ce72WN5JamGvoYSV94PkiCkTdvQEtvq/857Rj6HPh/eUlRNjY2OuEylP32Sve7bA9onXQSICQNgkSa8U1clkMum6OjKtpRJ6VOqVqSpGz7xPfO1zVRevY4X7xebmJoaHh911sOx7c3Mz1DGUc88v0aWngt/Vz6Xl1ZqayGZPnqzKs2tGR0eRSqXcGTYA3AmQsVjMeVW0xbc2adotwr/f2PU0QZSTlQyrVCq56IWGp1Rqq3e6uoHPpNPg2V4vyQA32Xg87hzn2k+A0aJfCaHGPS5AezGxGNUSfp7VNv6dwc/xcSzS5EimTzcxDxfiGPE9AyQB6mnh2Nfc7V4Z9vRznWv4qiDnAgkBjXA8zdLPc/OesnSY5ID3mP0zlADwsCC2s/Yb5BwUEqBQqZy9IhKJhNv46WVhy2muT4QqKkrGWMnCaFpJ07lKDfjjhdfMdAgrvrjukuAwRacKgZp46d+hYuv7uVRV4/zVqjKWHtK0Sf8AoR6Efr0ZDhoRAPZAGeBk5EPd3Nx0uSl2IGQ3Pz5APXeAxg8+9L2W+DRtAISPUSYbVVneb5bEB68Grb24Xn29gzbIzjfoIsRJzgWSiySVIS5EsVgsJMFq/l9bsGru+1x7NvYb/Iz0ZOTzeacI8qwKAM4czLmkpbCUw33VhaZANYTRLMaf3Y8S2v0AxybvIyNZbYRTLpddxKxkwPdrqYJFYhHVRfFc3Cv/c5II0ihZKBTcmAHggktu8r53gJU9+pn9z8r31fvFtAGbDg0PDyOVSqHRaLg0H8uNAThFYGxszHnbqCr6RveDhF1VBvThMt8eBAFGRkZcXmZtbQ3tdtvd/FQqFSr/UDKgUfZeQq+dk8+X46Nq5v1I8yBKQ4MKPmuVDlUVYPMcIFzLrPnt01UEDNp4UIJFMkBZNRaLOe8FyQBBEsDGOIz0lAxwrWBJL2vjuUFwA7wQ7rc/jhhtqqRNRcA3jap3SEmBKibnQ/pE11wSFCoY7ApbLpddJQ9JNytESAJ8RZabv87rqM+p6qCSkXh8q119p9NxJFXJAFUBHqbHfg1avXIQx+GeeAb4cDjh6bRnYwme/seJPjIy4kpd1OhxLm6qSu7a8CTqGvRnTao/ePAXJP17r9dzplI+f1WBAJyyqEaNg0EaD7rAUtYFTi6eAJxZU8kAf07JAHPCXPQ1XaDdIjWNeNDVgCj4GzZNbBybzHn7v6PrsCpWSlr5s+cSSnaArX4q9DjQPKk/pw2+fGLum9Z3Mh78NAWbz1EN4H6kCgWN7poqYJrgIJlVfey6Z4BMC4C7qcDJvAu9AzwJjiyOD5+mQb+n+LnIZel3w4ULKgSaQ/RVISBM+PqZNQcdqgzEYjEXuesJisViEa1WK2TOZDmmkgE15HJxZ+S3n36McwWfXHKscfP305b6e/7vn68qlY4Xfj6u/UyL0LxLksmfjSLkwJnPTb4u35PpYZ5CSyWbKiGAUGtvGluZIjiXpZkPFntSWqh5K9bLksmyAQkl2ng8vu+tcw0GhY4xXxGK+rnzcWE9n6CLPIDQ+QnAVvtxLvj0F/E70wRcpNWLo5vBIKwTHI8krf4m168/Pr9H/fl8g44XJTv9Tj6MSnWcrUrrp2OALYVCz2hQwysJi98U7yATAQCIBf36b54l9BwC7T6m/fTpzNd8kea3LlS2bzAMArgGsNMgz3zQc+lZqUFTHM3EPEiKhrB+qRgjZBcOfF+WfwSzbu5Rm/9uvD8DVTYj42F0eigSoZVDJKvs/XAu+jTsFnb9bAKVtfQhk9Fq1z8+WD/vc6GzfYPhQocu3CzhZJkY5zXJAN3gfpmmHxQclEjXcGbw9wzdK/yf2QsSqO/PboMAXIqL5k1CPRi71VPmfMCuKwMKv1e5Ol+5KOjXhXJTDQbD1mKuR9XqoTT8PxrBWD2gxrCDLLsaDhY0eFVzZlSbb1+puhD8Q3t6aqGfi9V8rP9zJvsZDBcWfHlXG7cAWwfVMF+rde8WGBj2G9yfdC+KqiTrZ9I86NhTZcBgMBg04uJXVLMcNYZRqr0QFlnDwUY/I/GFhj1VBgwGg8FXCAgt6+znCzAYzjUGZRyaMmAwGPYF6hfqt+xcCLlXg+EgwpQBg8GwL/A9RDv5OYPBsD8wMmAwGPYVttkbDOcf9vYEIIPBYDAYDOc9jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwGg8Ew4DAyYDAYDAbDgMPIgMFgMBgMAw4jAwaDwWAwDDiMDBgMBoPBMOAwMmAwGAwGw4DDyIDBYDAYDAMOIwMGg8FgMAw4jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwGg8Ew4DAyYDAYDAbDgMPIgMFgMBgMAw4jAwaDwWAwDDiMDBgMBoPBMOAwMmAwGAwGw4DDyIDBYDAYDAMOIwMGg8FgMAw4jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwGg8Ew4DAyYDAYDAbDgMPIgMFgMBgMAw4jAwaDwWAwDDiMDBgMBoPBMOAwMmAwGAwGw4DDyIDBYDAYDAMOIwMGg8FgMAw4jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwGg8Ew4DAyYDAYDAbDgMPIgMFgMBgMAw4jAwaDwWAwDDiMDBgMBoPBMOAwMmAwGAwGw4DDyIDBYDAYDAMOIwMGg8FgMAw4jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwGg8Ew4DAyYDAYDAbDgMPIgMFgMBgMAw4jAwaDwWAwDDiMDBgMBoPBMOAwMmAwGAwGw4DDyIDBYDAYDAMOIwMGg8FgMAw4jAwYDAaDwTDgMDJgMBgMBsOAw8iAwWAwGAwDDiMDBoPBYDAMOIwMGAwGg8Ew4DAyYDAYDAbDgMPIgMFgMBgMAw4jAwaDwWAwDDiMDBgMBoPBMOAwMmAwGAwGw4DDyIDBYDAYDAMOIwMGg8FgMAw4/n9cx9kYn3uN2gAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Resize image (larger image improves OCR for handwriting)\n", "resized = cv2.resize(image, None, fx=2, fy=2, interpolation=cv2.INTER_CUBIC)\n", "\n", "# Convert to grayscale for denoising\n", "gray = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)\n", "(thresh, im_bw) = cv2.threshold(gray, 128, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)\n", "kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])\n", "blurred = cv2.filter2D(im_bw, -1, kernel)\n", "\n", "# Apply median blur (smoother handwriting strokes)\n", "blurred = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(blurred, (7, 7), 0)\n", "# Display the result\n", "plt.imshow(blurred, cmap='gray')\n", "plt.title(\"Preprocessed for EasyOCR\")\n", "plt.axis('off')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 63, "id": "64ae7bed-542e-41c6-b7d5-9b1d6782df1d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📝 Detected Text:\n", "- <PERSON><PERSON> (Confidence: 0.17)\n"]}], "source": ["reader = easyocr.Reader(['en'])  # Use 'en' or ['en', 'fr'] if mixed language\n", "results = reader.readtext(blurred)\n", "\n", "print(\"📝 Detected Text:\")\n", "for bbox, text, confidence in results:\n", "    print(f\"- {text} (Confidence: {confidence:.2f})\")\n"]}, {"cell_type": "code", "execution_count": 64, "id": "51fe071a-cd30-4c6f-8534-52474e86af44", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgMAAAEXCAYAAAAwdEdHAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8fJSN1AAAACXBIWXMAAA9hAAAPYQGoP6dpAADkYklEQVR4nOz9edhtV1UnjP7mXGvv9yQnHYSE0ISYhEbA0CuQhNBoQSGFIqU0WlKW30Xro275eJ+rVVpevFBi1VWvZdmUij6lfB98fl9dy+LR0sISBRvAhl4hJEQgNIH0QLpz3r33mvP+Mefo5pxr7/025yQnWSPZZ79777Xmms2YY/zGmGOO6WKMERNNNNFEE0000QOW/L1dgYkmmmiiiSaa6N6lCQxMNNFEE0000QOcJjAw0UQTTTTRRA9wmsDARBNNNNFEEz3AaQIDE0000UQTTfQApwkMTDTRRBNNNNEDnCYwMNFEE0000UQPcJrAwEQTTTTRRBM9wGkCAxNNNNFEE030AKcJDEw00URr6XnPex6e97zn3dvV2Ded6vWfaKKTQRMYmOgBT295y1vgnOPXkSNH8PCHPxwvetGL8Au/8Au4884791321VdfjTe84Q24/vrrD6/CDXrf+96HN7zhDfjKV75yQp8zRk94whPw5Cc/ufr+7W9/O5xzeO5zn1v99hu/8RtwzuGP/uiPTkYVJ5poojU0gYGJJsr0b//tv8Vb3/pW/Mqv/Ar+5b/8lwCAH/zBH8Rll12Gv/3bv91XmVdffTXe+MY3nhQw8MY3vvFeAwNXXnklPvaxj+GrX/2q+f69730v+r7H+9//fiyXy+q3ruvw7Gc/+2RWdaKJJmrQBAYmmijTi1/8YvyTf/JP8M/+2T/Dj/7oj+J//s//iT/+4z/GzTffjG/5lm/BsWPH7u0q3mfpyiuvRAgB73vf+8z3733ve/GKV7wCx44dwwc/+EHz23ve8x486UlPwplnnnmgZ999990Hun+iiSaawMBEE62lF7zgBXj961+Pz372s3jb295mfrvmmmvw7d/+7Xjwgx+MI0eO4BnPeAZ+7/d+j39/y1vegu/4ju8AADz/+c/nZYg//dM/5Wve8Y534DnPeQ6OHj2KM888Ey95yUvw8Y9/vKrHNddcg1e84hU477zzcNppp+Fxj3scfuzHfgwA8IY3vAE//MM/DAC4+OKL+TnaG/G2t70NT3/603HaaafhwQ9+MF71qlfh85//fPWcX/u1X8Oll16K0047Dd/wDd+Av/iLv9iqn6688koASfkTHT9+HB/60Ifw8pe/HJdccon57ZZbbsEnP/lJvg8APvzhD+PFL34xzjrrLJxxxhn4xm/8RvzVX/2VeQ4t6fzZn/0ZXve61+H888/HIx/5yD3X/xd/8RfxxCc+Eaeffjoe9KAH4RnPeAZ+67d+a6u2TjTR/ZEmMDDRRBvou7/7uwHArG1//OMfx7Oe9Sx84hOfwI/8yI/gZ3/2Z3H06FG87GUvw9vf/nYAwFVXXYUf+IEfAAD8m3/zb/DWt74Vb33rW/H4xz8eAPDWt74VL3nJS3DGGWfgp37qp/D6178eV199Na688kqjyP/2b/8Wz3zmM/Gud70Lr33ta/HzP//zeNnLXob//t//OwDg5S9/OV796lcDAH7u536On3PeeecBAH7yJ38Sr3nNa/CYxzwG/+E//Af84A/+IP7kT/4EV111lVlW+M//+T/j+7//+3HBBRfgp3/6p3HFFVfgW77lW5qgoaRLLrkED3/4w/Ge97yHv3v/+9+PxWKByy+/HJdffrkBA+RBIDDw8Y9/HM95znPw0Y9+FP/qX/0rvP71r8dnPvMZPO95z8Nf//VfV8973eteh6uvvho//uM/jh/5kR/ZU/1//dd/HT/wAz+AJzzhCfiP//E/4o1vfCOe8pSnNJ8z0UQPGIoTTfQAp9/8zd+MAOL73//+0WvOPvvs+NSnPpU/f+M3fmO87LLL4vHjx/m7EEK8/PLL42Me8xj+7rd/+7cjgPjud7/blHfnnXfGc845J772ta813994443x7LPPNt9fddVV8cwzz4yf/exnzbUhBP77Z37mZyKA+JnPfMZcc/3118eu6+JP/uRPmu//7u/+LvZ9z98vFot4/vnnx6c85Slxd3eXr/u1X/u1CCA+97nPHe0bou/4ju+Ip512WlwsFjHGGP/9v//38eKLL44xxvjLv/zL8fzzz+drf+iHfigCiDfccEOMMcaXvexlcT6fx0996lN8zRe/+MV45plnxquuuoq/o7G68sor42q14u/3Uv9v/dZvjU984hM3tmeiiR5INHkGJppoCzrjjDN4V8Htt9+Od73rXXjFK16BO++8E7feeituvfVW3HbbbXjRi16E6667DjfccMPa8t75znfiK1/5Cl796lfz/bfeeiu6rsMzn/lMvPvd7waQ3Ol//ud/ju/93u/Fox71KFOGc25jvf/bf/tvCCHgFa94hXnOBRdcgMc85jH8nA984AO4+eab8c//+T/HfD7n+7/ne74HZ5999lZ9dOWVV5rYgPe+9724/PLLAQBXXHEFbr75Zlx33XX828UXX4yHP/zhGIYBf/RHf4SXvexluOSSS7i8hz3sYfjO7/xOvOc978Edd9xhnvXa174WXdfx573U/5xzzsEXvvAFvP/979+qXRNN9ECg/t6uwEQTnQp011134fzzzwcA/P3f/z1ijHj961+P17/+9c3rb775ZjziEY8YLY+U4gte8ILm72eddRYA4NOf/jQA4Ou+7uv2Ve/rrrsOMUY85jGPaf4+m80AAJ/97GcBoLpuNpsZBb2OdNzAM5/5TLzvfe/Dm970Jq7/WWedhfe+97248MIL8cEPfhCvfOUrASTAc8899+Bxj3tcVebjH/94hBDw+c9/Hk984hP5+4svvthct5f6/+t//a/xx3/8x/iGb/gGPPrRj8YLX/hCfOd3fieuuOKKrdo50UT3R5rAwEQTbaAvfOEL+OpXv4pHP/rRAIAQAgDgh37oh/CiF72oeQ9dO0ZUxlvf+lZccMEF1e99fzhTM4QA5xze8Y53GEua6IwzzjiU5wDAk5/8ZJx55pl4z3veg2/+5m/G7bffzp4B7z2e+cxn4j3veQ8uvfRSLBYLEzy4VzrttNP2fe/jH/94XHvttfj93/99/OEf/iF+53d+B7/8y7+MH//xH8cb3/jGfZc70USnMk1gYKKJNtBb3/pWAGDFT5bmbDbDN33TN629d8yVf+mllwIAzj///LVl0LM+9rGP7fs5MUZcfPHFeOxjHzt6/0UXXQQgeRK0t2K5XOIzn/lMM6FQSV3X4VnPehbe+9734j3veQ/OOussXHbZZfz75Zdfjv/yX/4LAyUCA+eddx5OP/10XHvttVWZ11xzDbz3uPDCC9c+e6/1P3r0KF75ylfila98JRaLBV7+8pfjJ3/yJ/GjP/qjOHLkyMa2TjTR/Y2mmIGJJlpD73rXu/ATP/ETuPjii/Fd3/VdAJICf97znoc3v/nN+NKXvlTdc8stt/DfR48eBYAqGdCLXvQinHXWWfh3/+7fVcl4dBnnnXcerrrqKvzGb/wGPve5z5lrYowbn/Pyl78cXdfhjW98o7me7r/tttsAAM94xjNw3nnn4Vd/9VexWCz4mre85S17SmR05ZVX4pZbbsFv/uZv4pnPfCa8FxFz+eWX49prr8Xv/u7v4txzz+VdFV3X4YUvfCF+93d/1+yiuOmmm/Bbv/VbuPLKK3nZZIz2Un9qM9F8PscTnvAExBibYzHRRA8EmjwDE02U6R3veAeuueYarFYr3HTTTXjXu96Fd77znbjooovwe7/3e8Zi/E//6T/hyiuvxGWXXYbXvva1uOSSS3DTTTfhL//yL/GFL3wBH/3oRwEAT3nKU9B1HX7qp34KX/3qV7Gzs4MXvOAFOP/88/Erv/Ir+O7v/m487WlPw6te9Sqcd955+NznPoc/+IM/wBVXXIFf+qVfAgD8wi/8Aq688ko87WlPw/d93/fh4osvxvXXX48/+IM/wEc+8hEAwNOf/nQAwI/92I/hVa96FWazGV760pfi0ksvxZve9Cb86I/+KK6//nq87GUvw5lnnonPfOYzePvb347v+77vww/90A9hNpvhTW96E77/+78fL3jBC/DKV74Sn/nMZ/Cbv/mbW8cMAGLt/+Vf/iXe8IY3mN+e9axnwTmHv/qrv8JLX/pS481405vehHe+85248sor8brXvQ593+PNb34zdnd38dM//dMbn7uX+r/whS/EBRdcgCuuuAIPfehD8YlPfAK/9Eu/hJe85CUHToA00USnLN2LOxkmmug+QbRdjV7z+TxecMEF8R/8g38Qf/7nfz7ecccdzfs+9alPxde85jXxggsuiLPZLD7iEY+I/+gf/aP4X//rfzXX/fqv/3q85JJLYtd11TbDd7/73fFFL3pRPPvss+ORI0fipZdeGr/ne74nfuADHzBlfOxjH4vf9m3fFs8555x45MiR+LjHPS6+/vWvN9f8xE/8RHzEIx4RvffVNsPf+Z3fiVdeeWU8evRoPHr0aPzar/3a+C/+xb+I1157rSnjl3/5l+PFF18cd3Z24jOe8Yz453/+5/G5z33uVlsLY4zx7rvvjn3fRwDxj/7oj6rfn/SkJ0UA8ad+6qeq3z70oQ/FF73oRfGMM86Ip59+enz+858f3/e+95lrNm0D3ab+b37zm+NVV10Vzz333LizsxMvvfTS+MM//MPxq1/96lZtnGii+yO5GAvf4UQTTTTRRBNN9ICiKWZgookmmmiiiR7gNIGBiSaaaKKJJnqA0wQGJppoookmmugBThMYmGiiiSaaaKIHOE1gYKKJJppoooke4DSBgYkmmmiiiSZ6gNMEBiaaaKKJJproAU5bZyB89zv/ELfcuoN/+6an4ONXP6j4NeJ7v+c6fNerP40yRfrffexB+I+/+AR8+fY57rhzhhAs/pjPB/w/f/Bj+AffVKd1/cP/+Qj83C88AculPWDlEY+4G294/YdxycXpSNkyL3uMwFv/j0vxlv/90QDsb0+67Ha8/sc+gnMfvMC2VKdiSJ+p5JtvPoKf/fnL8KlPn4k775xV9QUiXv2KT+N/+WfXgrKzjqV3CAG448457rxzhl958xPwgQ8+BN/zTz+JF37jF3Da6SucftqA3/v9i/CffvUJGAbblxd/zR34f/+/PoALH3n3SDuAe+7pcc+xHn/wjkfhbb/1WMRo++exj/kKfvzHPoCHXXCM63PnnTPcdfcMv/rrT8D7/vJho/30tKfejNf987/DWWcvcOYZC5Tn4txzT4+f+dmn4r3ve3h177f8o0/jf/3nH0PKy1PTzTefhp/4ya/HtZ+0vOdcxP/yvVfjO/7x3zPvvfOPH4mf/8WnYLGwFXjYw+7Gj//Y3+Dcc4/j3/1/noGPfPS8sofw6ld+Ev/0NZ+o+PjaT56DX/jFp+DW247gzjvnVd93XcD//XUfxTe/+LMQzth8xHCbKP/RmitOQnqQw3jGlMZkPZ2s/hl7zjbHYJ9QytW6l2txn6ETxQ/f/K3fsfGaE56O+OKvuRM/9P/4GK7/7Bn4zbc8Brfedv87BOSccxZ47fdeg1tvPYL/7W2PwbWfPGffZd111wz/+9seg09edzZuuOEohuDwP/7wQvz135yPf/jCz+MfvvALB6rrn7z7Efjjdz0St912BNvw3T339Hjb//lYXHPtObjhhqNrr73uunPw//25p+LSS76Kf/qaa3DO2dsDrvs6PfKRd+EH/uVHcMMNZ+At/9vjcdPN6/tiookmmuhUohMOBs44Y4XHf+1X0fcB83moL4jA7qLD3Xf3mM8HzGanniUxnwc8+tI7cd5DjuOMo6vmNculxz339JjN2v0QArC72+GOO2f4+0+dhU9cIxbwl750FF/60ul42lNvXVuPEByOHetxzz0ddnYGtsxjBBYLj+XS4/NfOCN7diwW9z5gZ2fAkSMrOKfrM8enPnUWrv7Egzf2w513zXHNNQ9GjA533DHHznzAzs4Af4DFKKrHPcd6hHDv2Q9HT1/hcY/9Ck47ssLOztC8ZpH5eDaPmPWx8i5MNNFEE91X6V4/qGi18vj9P7gQf/P+h+AfvvAGXP7sWzbfdArSX/3N+fjSjafhaU+9DS99yecqd/jtt+/g//rtS/GFG47i81/Y3xnzt9x6BL/+G4/HBQ+9B6/49k/xcsEwOPyPP3wUPvyRh+D6z7YPYrn4a+7Et7/80zj/vGM45+wFvvLVHfyX374Un/v8Gbj+c3s7vOWLXzyKX33z1+HhD78br/j2v8f55x/bV3sA4OZbTsNv//Zj8MUvHcWXbjx93+WcaArB4Y/e+Sh89G8fguc/7wY858p62Wtv5LBpqWCiiSaa6LDopIEBB6DrA7ouYBgcyDIN0eGT152N6/7+LDzxCV/BcnkrvI8HsibvTeq61MYQnFqPd/jCDUfxhRuO4owzVlguPWIEohL2d901W6usNXkf0XcRMYZsLafn3HPPDB/68Hl4yEOO4YXf9AVccME9AJJX4trrzsF716z3P+icXTzzG27C2WelI1xv//IOPvLRh+DvP3X26D3ORXQ+IkSYetx55xwf+OBD8cgb78KLX/RZPPjBxxn8dF1s9E/ig9XKIRT674475vjgh8/DF7+4P4B02OQc0PWx4uMYHT716XPwqU+fjUsuuQPPXt3IfLy9h+BEKf+xcifXxUQTTZTopIGB8847jn/6T/4eX7rxdPz3P7gQN998mvk9RuAv3vNQ3HDDUXz9M27FlVfcdLKqdmh02mkDXvYtn8Uzv+EWvPNPHoFrrj2nuubqT5yD//Srj89gx4KB227b2eo5l33d7fgX/+vH8cnrzsb/fOcjq4DFu+6a4bd/5xI86EFpzT4EmGWHw6GIZzztZlz+7Bvx8asfjHf96SMqN/6Xv7KD//O/PBYPfeg9+OYXfxYPPf8evPhFn8WTnnQr/vTPHomPfexcvvbv/u5c/PKvXlYpzjvvnOMrX9muX04GPehBu3j1K6/FTTcdxf/4w4vwpS/VIOVv/uahuP32I3jyk27FVc/54j6XCw4LGEzehYkmmmgznTQwcPbZS7zg+TfixhtPw1+856EVGAAcrv7Eg3D1J87BGWcsccXlpx4YmM8DnvXMW7C763H1NefgmmvJqhZt8PkvnLHHZQC7cwEAvuaiO/E1F92JM8+8AH/y7kdguSSBn646frxf6wVolU8P4KDCUR0iPzz60V/FS775s/BdxJ/++SMQilCIu++e4c//4hF48IOP41nPvBGPePjdePrTb8GTV7fi058+24CBz37uLHz2c2dtWed7j844Y4nnXvVF3HbbEfzVX1/QAAMOn7zuQfjkdQ9C34e8XNDejTJOh6HAW2VYPqk/TzTRRA9UutdjBu6P1HURV115Ix758Lvx/g+eh6s/sX+r3Dngmd9wMx732K/gyZfdbn676FF34TtfdR2+8IUz8Gd/8TAcP7734bzk4jtw+bNuwoUX3oUjOwNuu30Hf/pnD8eXbjyK226vLXLngGc8/WY84Wu/jCc+8fZGiRPtnU6G9R6LvycAMNFEEwlNYOAEUNdFXHn5Tbj8WTfhrrtnBwID3kc88+tvxktf8jkAdv35URfehQsfeRf+9u8ejL95//n7BAN34tWvug47eYfDbbcdwdt/72LcOBKs5xzw9U+/Gd/2rZ/JX+z5kQ8gopiCMWV/slz4Y16CafAmmmiiRHvSHqcdGfCcK27CRY+6Cx/68Lm46ebT8OTLbsdFF92Fxz7mjq3KOP30FZ571Y245JI78cEPnYtbbqHlgognPuEruPTSO/GEx38FzgEXXngXXvqSz+OGL56OD334ITjj6BLPePqteNjD7sFZZ67fw/64x34V3/rSz+Mz15+Bv/vYg3DBQ4/hqU+9DRc9KlnAB6P1QtS57Np3wNc98ctYLj2u+/uz8Ilrztl4787OgGc87Race+5xAID3aVnArjuLm9c54Nxzd/HCf/B53HTT6Xj/B8/D3XfPRkqPeMLXfhmPfrSM1dc+7svoO9kGd845C3zTC27Al248HR/4wHn46h07uR4BT3nybbjwkXfhkovvMM246FF34iXffD2++MWj+PBHzsNqlaI/jx5d4OufcTMe+tB7cO6Dj5v+edJlKVD0k588B5+8brxfzjpzgac//WaccTQFNi6WHh/+yHm49dbT8KTLbsWFF96JR1/6VXPPhY+8C9/84uvxpS8dxYc+fB5OP32FZzz9Zjzsgrtx1lkLzOcDnv2sG/Gwh92Nj/7tQ/ClLx3FE55wGy7+mjvwuMd9eWxoDO0cWeHyy7+ICy+8Ex/5yHm48SbKOxDxuMd+BY95zFfwdU+8jXmBfmv/fZg0tiRRfk8gZQIEE0000R7BwNGjK/zjl38Wd93V46d/9jLcetsRPP/5X8KLX3QDvN9uX/WZZy7xim+/Hl+9Y4ZbbnkSgwHngCuvuAn/+Ns+C++T4Hr8134Vj3vsHfib9z8EH//4g/DQhx7DP33NdTj/vON8zRh9/TNuxdOfdht+/39ciI9ffQ4uueROfP//7VqcfvrqpO1UcA54zhU34opn34T/67cvwTXXnLNRBZx++grf9q3X47KvExf8prY+4uF345+95lp85vozcd2nzhoFA84Bz37WTXjFt39KfWd3bpx/3jG85rs+iS9+6XRcf/2ZDAZmfcQLv+nzeP5zv1goOODxX/tlPPaxX8Zf/81D8fGrH8xg4EHn7OKVr7gOF110JzrVBu+B5z73Blz1nC/ibf/H4zIYaNO55x7Dd776Wjzi4Wmb5F13zfAzP/s0fPnLO3jB87+Ab/rGz1e899jHfgWPfvRX8cEPnY+PX/1gnH/+Pfju77oGD33oMe7Lb3npZ3D8eIef+/mn4MYbT8dzrvgiXvqPPrOxr4mOnr7Ct33rp3H33T3+w398mgIDwDd8/U141Ss/uWYnwcn2CEyegYkmmmg97QkMOJdc4Ds7A77uiV/Gzs6Ahz/8HvT99sKNyjiyM+BJl30Zp5+ekvR4BzzqwrtNWc4lRfiQh+zi8mffjHPPPY7TT1uNpqzV99G9j3j43XjOFTfhsY+9A/N5qFLknkiiejgXcdGj7sJzrrzRiuVGM844Y4kHP2h3z33a9xFnnbXE1z/9FlzyNXcWV4in4lGPugtdNw7caHyOHl3i6U+9BQ9/WFLCs1nABQ89hq4LVdWdj+g9cN5DjuPyZ92I47upkx9y7jGceeYCfTFezgGdA6KL+JqvuQNXXjG+J/+hD70HZxxdcn8k3rsNR48u8bCH3d3sJ+/T2J977jE8+1k34kEP2sXpRy3fdF3EbBbw+K+9HTGmFNf74eP5TsATHn8b9wsAXHTRHei6cC+net0YCTrRRBNNxOTilsmQ3/3OP+S/KaPdMDjM52FPQrRVBtFsFpoZCIfBYbHwcC5iPh8qRbZO6K5W6d6ui5jPw762eY3m9R6/o/pmuXRYLq1LolWuc+m8hm1AS3l/CMBi0Rdphm2e+7E+LimV1ckOAQfMZ6EJBihfAo0T5Q/wPo3XmCcmxpR0armU/follWXshfeEb9CsB5W1WuEQ+FgKn80C+p54rdWuE5nzvwQB657l1Gsvz9ieprMJ1tN0NkGux71bi/sMnXJnEzgH7Ow0UgufoDK6LuK004Z9dVTfR/T9QWMEDk6zWcRsJvU4EYPuPXI6Yb11bH/PSWXV/bau2jRO25JzBE62r9d++GacEtCYz7d//nh9xuq0TfDgXkThtuOpYwJa9+hnlksG22yF3FTnCQRMNNGpRKdonr+JtqcJc9/7FBuvk0FjpyeeGG/FRBNNdOrSHjwD9xVhMRYVfbKfS88+FWjKc3/foDEeOsyxaSUUGgsWLL/b5BHQ5Uz8NNFE9yc6tfIMuNjAAichKrr13FOOJmvwvkn7HYO98PxeXfotgLLfgMRTBTBPNNEDm7YHA/eFOb2tgXOinru1HFxXoZOpgDfVY/IYnFg6kXkFtOXfom15cGzb4dhve6VN9ZxooonuCzTFDNxrdF8RjveVetyf6VQAXGUsw6lQ54kmmuiw6NRaJri36VCWS+8ra673lXrc32ibSPx7m8qxn0DARBM90GnyDEw00QmjSbFONNFEpwZNnoGJJjohtM7avq8tzWzaVTDRRBPd32nyDEw00UQF3dfAykQTTXSiaQID9yrdV4TufaUe9xfaJlr/vmJ9r9uNMJ6qeKKJJrp/0bRMMFGmyVV8eHSqbdnclIOibM+p1r6JJppoE02egYlGaLIKH9g0lsJYv0/8MdFE9xfa2jPwu4/8/RNZj60pHZQjiUxOxqFb9pn5uQculEo8oIVVnhx0qB2y5pCdeH/ZkHaiar4pcc/Bx+nkHgjYOlhpuwpMBxduphPfR/fNdOr3fg3ua1TL1m3pojsfiSfe9nj4fdr4W4OBX3rsm/f1gIkmmmiiiSaa6MTSiz77jXj87Y+DjycYDAz+3j8GeKKJJppoookmqulLR2/E+x72Nzjv2EPwmK9cij52e7p/ihmYaKKJJppoolOc/vYhH8fPP/VX8d8veQcW3WLP90+7CSaaaKKJJproFKfBDxj8gN1ugf1EHUyegYkmmmiiiSZ6gNO+PAMuOpy3+xCcvjrtsOtT0Vgi14Odqu5GCpNSTfnFDgZAB+1vV6P1sbz1Hv/YqBPg6P921exle6axssZrue7GBzIdjDtPRC2EXV1xga3rNjVvZxlYl3J5cyT7WA+MPmerTRoPpFj1af7dL2jbYXTA7TtfwV3zu8Yv2CPtCwwcGY7gVdd/O556+5P3c/ueaCswkIVaJfycVbfOkSK37zFrXtpCGGMqK8YIxPw5RtBWRuccvPOAc7yNIwSbVc7RNuyYaxZr4ewcKXc7cCEERMT0HuQu7z2c98i3SR11h6SG01vRH7bPbB+q/qMyC8Djyu9iBP8Xg7n1/kNxizZRf6X3xFbrTi909d9xm8lr79umr2OUaeCcg/eSJyCxexq3wLxO00nxsxMOcMwEkbGEcE7UnKLq0EardZmN+heMGnO9mtt9q9QIDyAwMAHy+w9tMYzBBfz/HvN2/Okj31P9lnTL3h+7LzDgo8N5xx+Ci+551H5u35pqIbL/65xz5jVWjih++znEwErbOwfvu1RWBgMxWKXhPMCZBKJ9X1efGBMIAIBhGPhvIIEB7726h+pXt1W/y/e2reXfm/qxJbQJCEQFDPj6+4FlNt4nY8o+KsdTCapKIiBAf2/qL6XIq2pZYMu1UmDAewfnPI+jKFb9EoXLJSswQe/lHCmv1W0Z60PN++vmpP67fF6rzHWf74+0rZyc6BSgTSIj0+AGnLE8uvaavfL+AzyAsO3i18Iu/Rr53bFFJdZgjLVw1kJ49OkNMFB6KzbXv667ft+WtnneNm16YFK078mJtOV923ZomQFwi9JHAIMsdRHfOgAEOrWLKVp+UstP5RzRz9wr7z0QFPZEE93X6ZQEA6XCHBNM6o7R76yVpMtqr4ymB0uR2l3JdSChOXJ7S/a1QYBaaxilujAR4GtuG6FNVleMkS1+7QmYbJOCYu35H3OEW1Da5lVR2iOlRfu9Xj6KWMcLbUAZVV0YCFjXkn28mT/bAeFtqeTJzfN9ookm2iudgmDAWtGbAcGYRVUL3hoIkDD04vKlN3an5rXyQGXQNa4p18Xlap9L72WbvHfqWdvQ4XkEdD30ZxudEYvPD0TaBNi2oXEgYN+3LKrxHkPS0tEpQEdLAo2lJgfhSfqOlxaaD0zllctJ5dzcr/eqrN/mZa3J4zDRRNvSAcGAdikeJo1N8rYVXCqtjaVHcn+OB4c5p1yeNT5ISwaxLENbRTGHU1nFOdZXrbpTHej3sXiHTc1u70zYDghsus6U6XC/iBPYjtpLTE3eHZsmG/HDOnBgy6iGWIFWzUfgpS49vnKPUyCWgmWdkx3Izm0TUNmo9Rom3YuVv+18HwsSnmiiidp0H/QMrFc8m5Tm+nIdWy4CCNQVhUTlnx2Sz1fpdlEFSf2Nr89S2RKcRfX13i4vtHY7lPEErSCu/dC6AMJtAwptEydhuz9a57HaS0wBDAAo38vgUVnqsdfWjy2fL5Mgb0DYGJtwEN5o8eA2wYYTTTTR3uiQwMCJmoRluTaoia9i62f92rpWxOuUqYAFeiZEBhbGfq7RuMgmDyx7Y2NV323dnZu2RvID2QWsv2sDpn0DASkWtKVuovU0qjS32la4oawRIFBfrz0DbUBQ7QioMEk5d8Y9GGVMjq2T/W6vVny5C6cFEiaAMNFE29EBwEDWbkYQHHTibSqr4a+vFkhb18vvpdt088qCcvZnwebIHa6XB4IVPE7JUeTtUJSLIPLvIVv7QIwO3udtiltsnap2PBSxE+Nej3Xf7QEITHQ41Ozq/XkELI3FMbjm7DHFxRr76us3s0cZE9BA0Qek1hbCiW8nmmj/dAiegcOcgKXQaHsGasEyBgT0u7Yi0tpn+ltbMOpJxdKD43+Tq94XAYJRlVULJVkiMEF40cF5SWQUwrh11Np+2BKABAQ0INgOCOiW7JH2qLsmyrS2q1uqeK9lFJcqALzpNgMI2Js29jz9g/Cz/n2/sQbNp40g+AkQTDTR/umUixkQl2Np8a8DBGU0c+lCHxO2rfgEBwe9lk/LCFIPq4QJCBAYkCUIDuRmq348gRA/u3CNlm59DQQ2BQHKd+PLCFvRBAT2RuJmwubOK35fi4HXu8nLKH8uQvFgSiAl4JgDV9WttNSVri+f7fjveguw5bFyh8EYv67bfdDaZlzeP9FEE22m+yAYWE96J4D+rnHlyN9j7vHSQ1BbZbLdioSOEk5OyrFCTd61d4CWD1ygXQLgF7VpW4FWCtFtLKQaCEzC8+TTOiCwASRsBQTGrrHLceT6T0tZNvkQA19t7Y9WzdXzAjUIaNHEfxNNdO/SKQcGgHHBsV0WvfoasaZJaCUgIKCjXPvEyGcuUf3eJr3ssA21Ureu8wzsnyiGYcsyJq9AQXrtqHTz7OV+1/huL/fU3h7tvufsg4p/yhfNAQEEOf+/8hpo8q5V58NX8lP+gIkmOnw6JcFAiw5jm11yjwoQsF6ILDxdTG7VHPxH99GrJLPeikYYlcNeMMFo/TUoOIykLm1y6t/0h0SlT5Zd3Q/b9ss6xb8fILD+ep2hMsbEoCEHwA7DwB4C4iUCAym+JR925JAP68qlUpkQL9dEE0106tApAQZOpAuxtW5Zr7sTQABAiYZQrn+W7nr5zan/UrqCqNZi5T9NfD2s5bZubbVd9+1AgSxPSF+MXMn/8lkNbgIEQi3PUdtD0FgBT3dVWw1dA0G2PjsId+a/GIgmxNkKPk28GxBCwDAExEjvAga8d3CRTj1Mh2XFLgFjn5MSVVsS+e+SL3TMwXqeGZ+T8vtEE010cDolwEBJ4/kBDiYYWkKn8gyQeqajhVmo1dYdZy/MXzlPCYq8sbTSSXI209s2ilV7A0rPwNi+63GqYyRaV9jPTgDBAxoIbGp7y2NQ/j1m4W/zubiX+FTFq7QAAf0WIzAMAcMwYLFYZlCwyvED6fqu8/Deoes6dF0H7z165R2wSwltQBSrem3H4+05OdFEEx0mnVJgoLWNDjgx1kHtGRBrOF2Qn2+iqaUepWXuYjuboPYQ0Ho9YpQ8NGyqH3oTVb1zg+j5WwAC6ocHrj9gL+7/uGH89gMAxsogxesyjxIvihtfX5eql3JgUC6MBAYGAwbStT4vE3jm/RIEcukWlzDJFG57t6pWbQAA2wfKSnkTTTRRTacEGBjbT39YZY1RKYRoS1WMETG7UUVXJ2We3KhJYFIUNpC9AkFdk60q58DJhsgzEF0Y0TWbheb+6SBC8oEJB04OjS8JjH+nAYECk9ECVkSHGCJCjhUYhgHL5TK/LxDCwPzf9z26rkPfB/7Mywj08uVyQX5M1abt+eUgnoBph8JEE21P92kwMLZneC+TvM7Et7f19AoQZL+qibqOovRLF2grUxpQWmj6XZVBf5O3II6Xu01b1lG93luv8+q60XeTuN2Wtj/CyVV/ucr+5vGKln84X6b6PsYiJiUW/pzMV8krkDwD5B3QMQYpMZY3y1JV3Zm/LXdIgO3+kw9tEzMwAYCJJtof3afBwImkvXsItDBTL04x7AFEIPjm1rxyC6D+PsZ8RHKW0SEGRESEMCCGwNu5kuDTJ8jVSYj0byeaYvH+wKX9LJbsHRrUn8cApY7/KGMVCGADIUQMQ8QwBKyWA1arFRa7C6yGFRaL3QwGkEFACiwEkOMHvPGK6WDYqi+aywR7oylmYKKJTizdp8HAQZYCNv3WtPgPUheJ0mpeP25NiQ1F5/3QtbSOSyKcPAMEAnQbWkJy7wGErc+lR8B+jhoSPKBkdOlB2QAItLHObqQtFH0sv6fytCdg5Jr8fSsREFnqic9gPAJhCBxQSAmxus4zIDC8HMvnUF3q39sA5WB0kJwjE000kdB9GgwQnYyJvd5TkIVYlGv5UCFXKksoOWij/PWrtOrN02LyNgwh7fn2nYN3jo885jiEwoV7sqgJBCbCnhTd6GXbjOMmAAC26C1Pi1dAAGdACCleYLWi1wrDasAwhLSl0BNY0HzO7qoiSEAp/Wo+0RkFhwsIJppoooPTKQEGSjoMxbcpliB9Z64ACVOHtC0wxsjvdAktGvAtsEAAAEIIa9tAYCDEZIk51yH4DC5gD0w+0a7TUp6XMRHGI6CN5AcMrfEQ0LJSccnG7mkeaSyK3JSy4fhj8grYBFkCUHWcgIADtbsgAj6WOQmaT4IGKbXnQK5zOS9FGf+yl2W7djvHP0800UTr6ZQCA60JfthegzEPQVoFyIDA5f3VbGElBR+RYwiiKG4NBEjgji0ZsMBlZZLWaEMf0/5uHxB9/rWrAcGJpjruQQn8SfY2aEwhZnL8z4GKHyuc4lz0tCHeo90Dq9Uqv7JHIC8R0JKBdz5b8gIqaKeM5MawSwQUW1CyicCk9NeUQGiiie47dEqAgXXu9IMChHZUvrV+RAnK8ay0bdBlq4nPd0HaqkX3kVAdAwNj7yw2XfrHOYcwhJT1zclvdXKkljW/uT8276ooy2oEiU2yHAD1VStqLlvAY56U1jDF8o/CQxDLQuhva3XLfdHwpd45oEEAb5tVddKxB3ppqnwGPQcoQK+DCjA8XI/WtruDJppoojadEmBgjE6WRTz2bKCOjwoheQfIK0DJW8ZAAYAaHDgxGlOktoeDQ+h666Y96U1fp7UmAjYBsWiU4UgJ+X38d1lzHwPJcn/pFQCglL+8hEejUqyA88kT4H3KOkg7Ccg7oL0ErXrY7iiDCKfYgYkmuq/QKQ0GgBMBCLYoq4yZyi5UAgCs9GOowAAJ4jKgsGyD9kBQ+lday/V+u2pOdG+QmNPNpaAi5mM7l4pcI16HNiCwjyzLFf4UIEDLAkPaypp3CwA5UNaJ8tcvDQRsPVWMjGLSWu1PgGCiie5LdMqBgXWBf7XwLT/XQUbrgES5XMClhogAsrKSJ2BYrRBCxGqVcgOELFRryysJ4nRv4Pey/gkMeAYDznn0/WDduByTUCueTZ91H2xDNj88l6ou2KqY+zFZpVYvq+SrmOfy9c7B6c8m0t6Bdb9ZdlCu9uoeqksdmJf4pFwaIP5MQJOK8S4922dPQNd5PpeAwKnOPmgpA6IcO0M/J29XqmuKZdjkJTlYTNCUkGiiibanUwoM0J7n2hpqWGDlgmf6FlpQbhu8ZEBBTPbOEAYgAsMqKeblMh3sssqggBQ2HfiyzjOgj43V9UkC1zMYSAFeA7x3CKHjFu0XCOyVBBBoV3ScgEBBAgRav9nDryTCTyt149svPkNdM3JP/iws7Qy/EZ8mILDKr0GOLQYAn7wCpPz7vs8pifsKEAB6jihvVzn/VH0cJdVoMo+Al/16/sYydE6AYKKJ2nQKgAEr6FoBUfWWQLKyx6xhK6jH0xNnxUeO3agEXvYIrFZD9giIZ0Bb+mW8wDYvW59oArt0WWBxW3tFDkOA6gAvW16OVC/dvBMoUKT5z/Zd5O5KQMB4Cqr9+VbZ106Z1r592b5XnihZ8prxSuV4lVStfG6Gc+jMEgGdsqljBbaIJWHQoEFA7c2Q9gBwcW2R448qAxsbALnx5BNB632TE90/6dQc9VMADAC15WPBQQsISDSzuos0uhKw6/fpO/k3K7+Y3alDBgHLfOTrapWs+9VqZVz+ImzbOwr0d7R8QET5CIZhhdXKsxXnO4chDCirXAOCojV75ElZEhgBVYWimWgbKtSQ9gwwENg+psDGELTvEbVrea4CqshV8ekwrbQ8IEsDXd/Bdx0HExIgsPUZ8Qo0atOOezicGIK1eTwOXPpEE93/6JDBQB1MVNL+vXTba7KxZ5DcZfnbvKawwrQFFnRSFknZmnK8D4Wyl/K05afXWWlZIAUHhlE3pgUPAi5afTIGCFrekPUklpx21zonCisFw4H/fSBTtSBF418E0ekxixUAGPMM2CewJyDKNfY5Mh7JgyMVMgGuTa9VLkPlEWgHEBIQkOUIw3trTW+x/M2BXRE5xiDzGreL2iPLB1VRG+TD3pYIDsLLrvq0bkHkQHQYU24dZrtP0LpGHl4lx2Oq9vOs9qivY7/7wo7YQwQDZWsOS0FoN6QzirwUYOm7betZWjT6bxGcvCyADACW5BFIsQCL3SUvEyQBK65+ALwrAAC8T+v8XWctNA0MtLC2wVkJAIRIkd/peudJxYy7RKl95RryttkLKyCQg8u0XTd5CFAxX90jtatfKztjy3NZ2nJ2jUL1PKN3SgRE6Dd9q4MGl8slVqsVlsulimPROwmS0u97iReY9TPM+hn6vufthvq5tNJQsV8+1bNagmsIWAIADuXySdFE/XmvHq9GMSeS7gNyfqKTTqfeqB8SGGgpWLEe+aP6YxuQboXHOmVHEfXaSmmVZ8utk6VEI8zorHcGA0MwnoEydatdokjl6md6b6280huQToITICLWmWqpfgZLtTHx1vIS2HSwFmzo/rH3iie7AQSUFbceFOx/gpzQqZUboy3qEtcX7Av5WPaxWn/X5YOOIXZ8Xbo/Kf5GjxceA/PQfIkzpUrNlZfA2e+sh0kvXYUMMIkJQEjWeLPoVW4rLGNK9PKSDvpt7SBQHKi+qdsu+Mi4uNqmd77dFaW3FibsTY2/SxHWpPwkR6DOqYcUT4syUntDJSMXb1OGK3qzfHjpbTFlbph9rr6qqtIaD0Tt+2rLeL7FlXdYGZnuX/fQNpXPtF6Bcb2l50G9bFaW3Vq61nFE9x4dAAyUilRb8PSZXNktpbReYdfPsVZHzSwyzcka15HvutyUQtXUyJQfQpAlgZjPCKClgdWQtxKGJiAIwQpisrAkWUsCBdoz0HUdYkzxBzEGPqDIZWHcz3p0fQdHhyMpjwJraU9ib9MEiBk0WWFrPS4j41FMekfXVkFwZc/ujZTuaFX/UIknr574RjmL1b5WwLn1Akw3KqLmY3oWKXcGAlHzJvF3PiyY+HgdAta1ifpQolXeUbA0219Lxe29h8/bCtNuAtlJ4BU/AjHzVESa9y0ynNP4NXsFoupxkaG2jfqzE28CYuRTvp1RyDXEaLGYhYL1L82fGHDRHCQZJH/bqqtxbRR3Imhtz69h2221U3nZWmBQXSf/to2JVgW1Am6U6zZOh0aZY4M7Dh/1ddqTK4aVVv56+djW9b5AB/IMiCVO3F5e4YCG0tHvYzQWEawt5zV3Iwknu6xQegaknMyGWeBGPqSlSCdceQa0N0AUanqv11zTFkGHrhMwQKAlhADfecQgyYtoCSBFcAvI0fVmpU6egq3IikS7g4H6jvqnjXz5WkYEuvTiMwnFpgnXrl35vPLWpiDfcvaX6F1bj/RNRGZd7qNCwEVdoYIvYy3SnFM2fFMREFAYW/22WjFSLZ329JTX2vq24k6Ev/USgVIeI56B0mtnvVdKEtN4USrvKPe02ugi5U6om86bCyrdYCaeld2ltQdnfk7fFX2aP9XYboS/MiBuKQ1rhMg0rWZCo+xxHd2uR7NvS9ZGAQg2TRnVpPFZ68zvlYUN1GNaemUb9bRlRdWXZV8L36Z7Wo1o13n89/VUGphl2Y6Ng026z3FdXEOWnkzaNxgwwHztlS08vqnBqYMs0koKS6+tj613p6+8+pu+t9fSYKVgvKzsY8hKXyUMIqE55EDB7BmIERgGmxvAOVL4yXqazfpsVXUZDIA9A3wYjF+knQQRCDEAQ6pbAgFQFpl4FvTywcGQpQUEmnHLvl9fhn6vf9kOXY/Vbr9374PGm1JdM6YrWkAgWfNAdA4uOgTYw3wcsts9ZnDCgiOjzKIXiI+ofPou6o7Klrp4BFZYrZZYrRZYLhdVzEAqQ7IL9nonwZpkQxoMN50jY4PY6OOYdUYsx0Fhi02ifYwkHmGTBGr9ukFmkYeCvZF1TA5tmazathfaSlmojh6ZONtI4UOlTQCu+Cm9FwCYbylydWz14BZjtpmRqiPLus7wtq6fwaBKZtJ9DWlgnq3nkaOHxzHP2omlg8UMsPCT2c52DVmDpHALibBOt9SWB5TlKkCA/q6vywPqtNVnB6qF2sgrYGMCtFeAAEPEEAJiEEFIkdUkLElwdl2fg7A69gx4n+4bhgFp66BnT4ALKdlLdEEpfWf+NgzU8KDQXu4xuVErdgEEZX+XgEBfQx4MKqFC9WsTy2xJseCcEYUrl4+LOOvVUJ+b2n2knNL6Kp+nZQ+b2ILaXHZpw1kPALn+LceWBYtwao17foSqXpT/Inm7KIulpB8OyiugQXgZH9Cal1K3WHxeQ3Gsz2EsSAZKxaWliB3ri3Ug1qiBkSFc902LSgBQ8qL+WCs8/YOSU63aNHhc+qQwsfMPFVc5AUctsnJ8AzU9XXU9S/ligECrG8wPGhDUxou9Z6wehmuqh4pxZXWGVvZtYILimjbVAdti9HKNyoJ4Epx4U2j/YCAWXRnVViAFflS3pn9dPQiaxpTcOsYqr2sBiVQDEf6OPAJRLQswABiKdwEIlABotVxlAJGEeJcjrGezObquw3w+Q993mM0k+rrr6FCBaDwKmiGY4aKtv93iRRYa3Sv9a1Fn3V/rLXx7vY67kLqpgkukDBFeVshsg+APTpuWCdZ6OLaT92svbbuxXVL8njxVLnkHaMsmCwTyDDjS41RpUeScoTK1gQCi9G0wgi/9HRDDgBBypsHVCqshvYZhwBCCKpfKTDyWjs32Jj6A+0DVpwyg3dR5Gggwv4wqzoZlxUF66TNbVNiev08U2QDg/F3+jRVsLGKWuDtaSsuWO/rcxl9mh5H5Xv1rhIS54EC0SV435UtVkfbnGnSNP3+836KRm1rfaE+bfWYJ6OwznAOcC8WcbOspuoYMPVUoYhjS/SfZQ3BAz4AgQt4KREg0uzw5mZjTnTPObWMov/14UXBrQUDB8CUiFmEbjeKXtMKw1lXIyjwitYXPdPfofIe+S9uw+lkCA7PZDDpWgIToMHg4F5kxar4trTSb+U0vFeh7Wv001jeb+5SGuQ3iWpOtsjZI2O3B2GgUeihUejs2PqMUYupv0yPjcpyFsnNOeQQExFEwIAsIOCBEthQ4pqRQyNZVX9Yy1Y48XikYVi150fkZofQKlLkEfDWPasuXYKDaTTKKuWohvVFw05HdZeplfhvnqLEym8rKldfk8jcwbNsLINuSqZYc5IiWRTzO4NvGwlT3ITb7JlXLyoQy2HKbSbqNwm/clMtve1DsXxFj/dJahimfu9k4ACSQGkauqqtGny+/a0nQFCJcPlEJCvj7XN69sVX7wFsLxYBRE9bJYNW6f/0SAdBy0ySTS1sf4p63At4oShSDq5Q6KfQYo8keSOunZpcAaGKL27TreyAC3qVzA2bzOTrfYWdnB13XYTaf5f3Z2ooXwUynxelz5KXNCSTQUgPt85b88CmyO5XXRo8tl/9eqEbdWoAp61MJ9yYIOCRDTU+5E2L7bQME1nThen8Xq3zoMEpAhIFnoJABQXEyJSWmonqky8cAHZC8BIlfh7DCKqxyjMAixw3o8wjAFkrXp1iB2axPPDyT8wgs+Ij8rBR8WHeQuJoFsGyyriwwKXrXeMBQ/F72wXb8Lgo7snyoqbYUqyvGAKZ5RhLy/jCWzw6RtHw8LNrsIYoGEJifTsbzmdYBSSmLQDW9t5S/Bn+bdFxrmUP9OvL9iaX9BxDGEtRG82eN2iw62sby1+59WkvUQIDqIe4ecb2wtWUKzPek1QG1HbA8yU3FCgTJ9BchijoZaQ6dy4GC/cwc6NL3sxwn4Kv8AuKFCCi3JIqQd+h8Z7Z10ctu6xLm1Pyv+3ede3zdpIm2QCPFCzU5WkbuNHNZLHhhWzosQLBWUJQ/VdcWHhbN98VtY3V0yN2p+VUBAbmqHbOh3ZtjjSDBpPl7CHRK4cocoAUA3vfJe+VT2uFOxbl0XdrW2gIC9B7VB+N+NTW1PDoOBOy1ZXuJbVr8s5V1an43n+jJtnxmuM2goHxmy2Mj/Ks5eROcPDFk+vCwgPu2ingNIGhXajtFe1Aqx68EAtYroN/bbS/nzTrgaHjgJGPF/XsGTEVj9bcV3HaL3/YkZbHlSaCpEBQc7MTu9Gxt5YsYRAQCAQIAVqu0tYoOGZLAQZh6e+fh+tSW2SzHCnQ9vPPo+xlb8d777OINGIaIEAgApDiE5XKJYQhYLheQkw7JXSvBgl0GE2mpoQYDGkTo/AYnhJTcakMC+dtVn04R2ggEWhdlashyngMGOeuiM387ILjAngN9kRZAMYSimHGAp6vOga+FJ4qTDLnkifCdeAa0F4p3Efiifk3XqABfq8PbvLKdS7cFBMr21/e3liLqa8r7o27CGm/EejA7HkgoPSgxTPJbqx3rlcc43dvxE9vTOiBQ9F3hVdlPG1vu+triFxJgvW4MLOy1948DVLt0mJ6jJ+/JHMHDyUBYjxkIXydhuPdB0y4UAgJ6iUA/jP7zej2dBsfJVRrd0TKBXhYgt31paYsb1cO7Dt7JmmrfUzwAfd+x1Z4UdBK49IyU7GXJiV/oO92BqQzHllnf9xyIyGDDOVAWuZNGEbzuP3qBoVNFGDVoj4LX3ivvEXnvfLRCzLjVqF8dXa+rkXlWHSakH6QVhZ4b2nqn5S69NCWHEyWlTVtW+7wNtlyW6rxn5VV6naRBCv6z0o4buWBbJdcK7FK/YhuQsf5Z0bylIh0/0AKRcZlWAgHjVMsDbXY5mZqPewj2Ago2ydv7MlAY64HaQ7QZjLW+H7t3nbLfFqzq67fxxkZlbRq/4L0wPPsGA+zqLGRcSdTWMuBjvWtIUJpxzTTGowz60LECqaD0Jko/GiWs30lI0nMJtXkVVNV3s+wJyG7Vbpa9EBTcRUmDUuNp+xblLEggQOeDF7cqBQT2HS0P9Oi6tAuhDOaivq3BUbuPpE9R/b0nYt9t80kwLgSnvz84ueL90Kjsi710jQKb+rsYc/CWUzyq8b9xtec54ZwIQgVEY3ZRGUCxphPKuJqUX2BlMg3S77Qs1eXlKL1EQGC0PKFwvaUjnWCDs1KjmgIa411eCu86EFa32dZnO69Dqybadnf8V3peNM9dTy2BZUuvx1E/v1HiBrlZPU5ZvlQfc/86Q3eftHWMkmmHdsdoY9B6fzYBgpae0de2+CjJ+vHt6msawO9SLxuA2Jozul6RKqBLzXrV0YeTRAdeJjCAoElRDUwLNbdRlXkPsv3PukCl88lyr6JByRMwyDIAJVpZLBbms1asGmTQdr6+7zGfpUDB+XwO5xz6rgdASSnE2xAiBSUueY1WAwL7LOSyUpBWP5uh63wO3kqgoJULft8K/YSQnrwnrl4nfGocoOpGCKjPVTxArK0QnhOmHm5EQK23tnWcQOJBSjZkAweJvHfigZqn3S/pfY7ZTJaljBDbCEBT/d2GDl3HNevji6ySkLZLH2xTz3a5FnYKIMif3F7KLAQ9l+Ea3wN0qJMpoVIkxT1rn0j3rbvj8GkjICjdLO2LYHwmlWeovrelX0o9U+oI0WFigJbVLJ/R/lyDgdY9pl8Kb19EPmbsXnANHPIRxuM0NkB6oMYQfXt5QLwTQCF4nRUKEhwoudntrgERnGVZOpWwPt/ddz4vS6RtheSu58NfcjZDAQChDTgA+E4SFTlPCYscW2S6D7UQj/kMhVisM0nv5Otai9l7Iq3kWtteSpGex2CvjznBtFExaCGwz2cQ73jDjyQgMp9vCDgq61K74kno2OdaK9CCU83rVrlIxkE6P6PzdPZAa6vVmKItFTdpLP25+E0qAUSbGbAFBNYZDrrcMSCwNwHrDKCzD0Vuinhq2rJLySwCd2zdjljo5isrj7TxoC8ue9WZT6o9uuxIT4itS7ecvC1rnsqPxed8nWXaljo3ZdaAoGzLiJLVJTbnYtmv+vttyrHPtl6CcRCr9Z7mFVeUou4Y/WU97V2CnTQwAMgpfUCN4IBx5V+6PQEBAqVHwDJ8ZEG4yse2Jo9AWrMnz4DECYCVMuBy4pXeRPPP5jN0PuUPcMjrqDEr5ggGHGnpYcAyewYoeFAzbFp+oIRFLscDyHsCCdJ3GggQtSc+fTciyPZErek6BgjKPw8PDpx8nLw3KpVWpO/Iu1QpatqtogVd5sFma0tAAIz3SgqQTUtSA5bLFb+0Z6r0enWUH6Pv0We+b3rbADMXU9236qYs2ulftRdDAQKsEaa2P0plQ983gIAqr63guSbS/+u6OT9eK33t7anAQC4o8UJrDE8sdB7rylqBukpn7+95dltyGl7F2Xsq33pj1nkG1tWn/Fx/l/bzem/HrbVUZcGAfd8GwBodp1LsH46g27x9v0WHAgZGXSANWremUyp//i42ys2WVik4CeWRBSTbqcRC15+1kuXBhljlOuOfXjt11OEZWMcIhKiTF2UrLJ98WApPiUNwYpF1+pkCbsYtnVRbqLeitxt9syURArdfiLWiHcDsBbDW36G6usasoZO8VLKpTQIEwECAhyla3gZGhHHDcb6pK7VwkWyZg1miooyapaAjHtenEdL3ZZs1KE9zWOrc1rEbzgJwmctiRGwJUYdqjqvS878RwplcQO0ti2u0nLN8BfNJcTcZ0i6VxxZ2VECvKqO2nKtqrAPQ+Rl11esnrSmlXSz/LTyXmzZeV35a21MhTgDtOVnX5tazSpC0xc0Vp21zD41LPUb6Pu3Nsd6JcaS4fpmrfH6b2PjdR26KvcrfQ/cMbFwrgngIaleSBgM1uhaGou2DYO6ha8jS0so/rZkOOH58FyGIR2C1Wpm6pqBAD+/7lEmwn3H+ADlwKAUOOt4xAAU6Aq/JrlYrASJh4FF1zvMSAC05zOYzeJd2DzjneNkAkPZXLl6X+rpTAntcaFaicgvazEhrhfz2vsZDo21470Q/n9/duCWaeJtyTATm65i1DAGI9Wvl6d4kaC2PrFZLLJcr7O7uYrlc4vjx49jdPY7lcpl3rggIYI9XzpSpd66MxalYsI4NQlFDxGi+LzrPlrClHIvqGfxOnVkAAlZ3cf9AledR5c1Uz2cqwY1dLtKltu6ufz8IWU8KyZb0l32+8Y5sXV5BxfSvsNkB7AQ5LXcv91gwUvMAnRmTPzmJAxMgIEHi21PbW6CBe/opmuWC9M0hOQq2pAOBgZa7prb47WRJ9607zIMi8c3t+qmNv8klFSvBOAS7napcuzcAgzwCnrYROrbUrbK1SxucwjjaV4xKBSvFkJ6Rzol3yhIr17SkTwrvQJ5ozqWJXO9X5jvXfNpA9JiCG1kVieGm7df9POlA1FqnPExAkFhjC2C0xTXJg0QAL3+nxpbLyUxpBdaYmLAegTJOgDwCehcBPaf1KgEAP7EBBNr9INdT/SyPqIndkB/8qWHm7kVVMh84mRsEBEBdXD103YNj/X1DthWT0wClGghIOVLGyJzdB08bZeLs9/J3S9BuAvJqnusHcNro8plazraK2hRmquqrMF4zJqHxma4lrzFQygmZY+sBQ12X4htpltFx0vZ6acIG19N8GX+WGIOHTQcAAwUGdvJOjW8scac7m4zdaF1xmZ5M7Bng8lJyHzqbfblM1jnFCugYAUo/LCgwKX6JD5jl41tn6HMkv3c9DxpixCq5BLDKRxoveLvgKh3+Ys6HzwDDuZTYRaUZptTGSRCXIECQe1SfS8jYCEAe68I90clGppvoXqnLfizILHRE8eigocDv2jOQnoV0yid7hdYJIrlXdg3IbpnkGVhgsVjwOwFkrfzJO9XKbglApUBu7eZJVdWvopapDA0MRsBl/WE7qm0FJ/KSgUAECKAjAiGDaF4acLWwVuVUTyrrqVCzUf7lEdNggaWUcOCiR+eqVmxj16y5NYGgRr1HLa5NJnyrsDF+3WvZ21PD+WNAtn23R0sTILDGVzFevN1QGWIYMzbsNeTW957mvgXZNv5AgZZ7waNKdMBlAmp4+lR65yrXUHl3C3nFujPsftx60gHirtcnD5axAjr9r7WQlDfA0y6BTtbttXsvyrSMMeYjj/XhL7UFlb3GKYObOX2wcTSsg5n2FDNRdIjUR/dfYZjXgrI1COt+tIjUyMfWM/Y4t/fkqj0EQDz6vMqSqT9urGvpBVIeHLlExrTyENSPNwKjeBRXXIMBvWvG8r7dSTDmDdB8qC2nyiuQ21bOQVt3VB4tEsZgVqf4imIAjHkGdRS2CM36gbnEEijnG6KjJF1aaKt7tfW4UXCZh5pKOFV96UsPfYdE8G+3PXh/MKC8P7WJDSiqjyvAXfWH6lDT3PImYfa6puX3uUyncnEU3uVGjdTvVPe9AgEZ49p7Pb70IF6GdYq65qt63m65PKXA376MkX3SAT0D+oAg5Hfl3syDEGq5uJYoWp0tcQggoK18XItsHaW1+iBR/Nk6GoYBS0oqtBrAsixbRc75vJe6w858zlYSRVjrjIIAxSQIEFitVgjZQ2C2cOWthWI1qZwFnQUE+iCjcsrkxidh5T187mwSxE0v8gkCl6Wrb8yuWPftqUIHmYIxBOjEwVrJtpaoYlRbET1FnMu2UrZw2Sq3SwPL5Yp3yYQw4Nix41guFzh27BgWiwWWyyUWi4VR9OQJmM/nJk6AvFbSFvWsRrZL58AeLV1PbUG1wDF5P5zTufobHV8Cqk2dry1zlyRJCClKPObO1kDMuJobj987aYuPxjT3p0Z+6uNmOoy51GqdjMcG/buP8mPxa9kGAgR6DLRcr8tY+/QGALDv9ntKiJVks/ZYiUUly2qtZQ7RexpUkH4ogTW99NIBgWUN5h0Af3Am3BcdyDOQOqg+JlMa3TSQ1lKJ3EoXm3SqCEdZtxeLaLUaGCAMeVkghKh4VlvpciCL5GLv+HfdXm2dVPEB9Fl5COoGWuZIbWr3bUIAYKuF2m/fbdmgKo7Py7W0aT24/Dma57o9CZK1VpGyHvUjtiGrlLa5Qb3F8qctn6wUTdzS4tPCOL0rvmhY6eTe1IKPhIj1BKwy/w8myRCBUf0snUNj/TZCq8i4dworWNdX6mgFpmJqALLFcAvnSy6hUZeSX7SC4S0AGLH6I0DW+yFbYqYvN3kcThQ1rHztmk6fi1tG0VlN2lNlvx//XK+d05xtKdmSav7U9SjrM74sXZozVlNpvq+9FbEaTvt33XbbZutt42cpD8bJpv2DAdXxrXpzt65BBCLoHaCFfgMpkiXsfWcqQEqehB5FTCfPwBJhSOcCsG51jpOqzGdz+M5jPttB13nMs2eg8x279Emw2T3+ztSNJF6krYQ5biGdQZ+b75MLMwbHiYKiT22QveYatQpZN671IpC11tot0M4HsH9aO6fuJTR76HQIXpUY8rjEvGFLKWFALGdAWF1v57OeAcdTyMYcgJe+iPcXiwWGYeAYgXRc8crkFaBnVGcP0OmEBRhYJ5BpqYp4sgRhAl7yXM3eROpmQgDiScT2fLQORxaA2cNlr0Dy10TvQBHp0oZSG5q3PVrxEjQWozaWbCEnBRsYfF5aDu3toIcxmdcBAbG0XcZhMga6T9ijs49O2tS3shxEihpwzsbHtLbhUh237SN9f+kpS+eFDLzDJ1I8j0/tPtmAYP9HGJu/FcOXDVBAoOlmQT0Zx7pACypreZReAbGMYj6gRU96bRF1+WAW7ztOtEK/mzaqgUxl+bUDLe4lmyugvkaYkXuT+0oEFi8xOM8dFBHgglr7IpSfL6BxOUxAUJEy9A5DkI/IzerWbR6158mkCt/vNCzX1qP6fswaor/tGr43510IUBT+10LLxgnY9NctgVYuU7U8A1qJR2GuXI6855m1sV+QwQDXhuWCM3wUN6X7LZ0M+rsRTwVU/wsQqIGDIVV2y1KtLd0GEHCuuK8EBDK2+jKtzPbq6Wq1pbb0N03YbTwC23sCys/ORWVJ2i2X0nfWSrbepVYdy/4hwDMS9Be55035LTAw5pFtedFaVPKCBfaRP3vWK3vfPnlQOlDMgBZwJWvRtBdbQJO2/AtUToKBS5HrNJE7Pu0YSOuly+WS91Yn7wAhLmTlT4cL5V0Ds1nKujabw+ddBL7T1ph1/RPwSNUkqz4ULxt1LUrdghBjBXrN7NY0HfO8VP14wk2Mk0QFI+0Ha9xbJMqzJitMHLynmZGtV+UZMAcDRfA1Mf+t3aHlaYTlgUSlQCtPwdSnYbYyDjaBZJ6OzpNGbYxPw7tXd5gt03gIxvq2AL5jjGHBF8299L320thGlZWq67ZO2Kf3JBv4ssLIIfAE9XM0OxHK30rAsXe6N1zOm4CBJh6lbHXv9zj2ck0+fae9AKrc4hFRMZIsP4v8LcG8xLC5puhtLZ3rd83PfDheiAg+AQJ3L0i8gy8TwAICO0vrb0uPgDEx9Lt6TnlvBHVixCoLweVyhcViyaCAAgedEqZdl5/sKE4gC0EShH0OnoqZObT1FSMHU+lqahBQAgGutbL6rPXXyitgPQjCbLESfGKRJR+AlFAITC3oNs6zNUJxA0VTxz0CFI37dN+hDQjWlVw+Vvp3/4BpW6vMwF9H0eUWCDgHJH0kyqoGA4lPqbhQgEICpgQINCAtTyfU/UBgVC8T6C2FFT82LGJpC7W5mLZi0rV5IBaMDJEjsfFbabkxGyvGsH3TiDlyAKWbbVZHWlJ8rsvbxgVekQPzgnd6iWIc7Le8SYdHJ07RbDNXjJFDmHIdbmyUKd/J/LY8VN7T+o5vBc9enlsa4OlS7HZE+a0GDHo+tbxnxlOAmPRVtPL8ZNH+lwkcqSCnVJEGBVCegVLS24AhjjsrBAyXwzM/CcUYEwgIakmAdg8sV+k1DAOG1cAWls8cl5L9dHBdl959eoEOHCLlGsn7EFPiIooCz6iV0mTQscgUJ0D1JI8AH3SkghT7vjOeCvIMyJ7uQTEJZZlDVgoBJXiorKXRQRv7ocV465hx/0p1IxW6oAQC6/HymAA4cWSWf3jsZdqTMKDzLmjsyhgU5wvAyDBPNWNEyIpiIbBhX1SPMkjWBswKGKgfMPLZyd/GsuKeiAwMtRLVdc1XcYzfxtEq8ewIg9QWtaz31vPHCn8BAXK/uK2tohlbKtTvlGfB+3yaSU+8ALTAha339kD01Kc2B9ixSUTy1VINrliRR8ujTdLzFg5wSnO52qiDo1lKZZNFr8pzOgaI7qN6RTXnPJBlvZ5XY5SWIddfs1faFxjQE10AgRXFJRCQf4sBo0HVQIALckqwZKag9L/ZNUoR04vlCoscNEi7CFIEdZqEAUjmGGX/8z4DAc8vyo0e87M4q2BQSwUMBtKFchyxOuMg/+tc2jJG5w+UwrcM2CLBkNxkdj0p9UuEi3LtJsuipnXKsm2NbSznROjaQwMEJ8/6sZ8bsR486aW/tXJsgYH0Gxkt40BA5tQ4ENBeAZ1oSPPi2G4C0COo4wthpY2Cev7LvU2rWhdNgGBs2HQOEmuMjzKItq7lJFDHF4pL2Mb66AfUTg65R65pR4fLfAa6ziO4nHhMbZFeBwjod3rG/ZnI6Gv1YetaVyaVYFLWuZNy15KZc7K1XX5OCp3OlJF5wtI+G2reGgVOgoE9LasB+TAkCQpHDBk8nFjjZR0dytkE5BlISjR/F+UF9R0gA0SCzqoosYKTez7dKPfGDAbyiWyrFVbkEcgns61yXoEQI7xHUvTWb2/BR14GQIxwUQJH0rkCksCIPQORDLW8g0AxrEvoAxkA5iWIlGVQ1mZ7I5ioP8QdVVooEp9h1nOjLCNYYaXL0DQGOe3kMePQuooVEEG8qP4aeTTQdJOZ2modrocLbXk/TmPP2XxnVa+G5Tf6mZ6irOFScFgLmcY9pkR0LmZhMoBcjGnpCUinENJywMDZC/VSQMmH+vk2YHZ8F0HVc/RFpPppS0gaHYV5pT56VjNjee6fsuPsUkE1Erns8quoLC3J+kjyI/VhKOaMV/UUhS0KPrafx22hdz9S15Hr899qtjGIWqcExj0HUk51T8HnrvirGqNyrpsi96igtplmXGSt7MdAla1bkcLXAG3wd4otc9mtuqRxoFgSsxwUpX+IP0KI8C5j00JGD8OginVw8IAPqi5iKJjYIO8Qg6vnoVaaJ5j2DwYKC0FjNAIFui81c1RAwNXsFkFLAvkVZIKmlKtJ6UvgYNpOtVgszQFEXZfAQBfpaa4CAyFG+DyQDkhLAiEIqFCBWDEEAwZQMarLmQuT0EuCt0M/6+VvTj+sMpO5aPpLWyw60ZFYOXa/+DZGw3bzuwUMiis0YEB2bWFkrXRjRTJVQt5+rADBvufG3gEBMA4EKkFN1jhgXIPGinCuOd5pTgxyHQgM2JMwKZ22PmeDgpAMrHayRDEWK1AtE1D7YBVKzGa79gIQQEjzIWlkvoMRvemcjeO2tfXbuKwEAtKvMkdCcABCBmItpVMXbj0s9Xf0HF4WCGkLY8gpy7NfEuLxycBwu5aemlQC+sJY0T9H5huKgUn9puVevjJfDzWfSNFa3hozkmjcdU0IeJfbf2leyRwlto4IBAQc1HU1GEigJc2tdIRzkmIC1vPONdL5ZYedRE/BoZ5ayK7BLDRkQvIFAFrto7V68ESmSWldbemlDxyi5QKT/U9ZS4z2qCyIAudX9iJQFYZ83gCVqQc7N86An9Qm65alVMZsfXmPrpmC2Aoi8j5sI5y432M0QrxFDs5s2ZI/XXlhbbmVZZWoguu5xorRzx+rZKk7qmcpQLCVznBGGOxlXrU8AtspqhIWu+K3VJ9g+Fp+S290j4MVNCJw6HuKVRlzqWpea6XArj0ChMwbg0Ffa2AQC08BYP5yUSxiLn+s57ZGk+pBmhkKBVSP22AuKvssKj6m+pL8EPCtQYHMd12Gz57IkL8PVAboXm+hQLT912xq0Rbh63Fwy+5qvsJeq5WsgLiybMjdmyYQASV9ihn3NfVZXdeoZCoAJf/q9NFUBdKTEs8RC5AA813UDSx40M6FEjxQGut0bwi8mMD91gIDes7FGNF1mX9UXI4GBFxu4Z2jqpwMSLAvMGBxbUNa0wtiIKwryZh8EIspBCAGiZbWaC0FDsoreQoG/gyAXYQ6rSTy/T7IGj+7hXJFTUphei41ppA7egDT+pCAAOcdZjMJGuz7nlMRy8SiA2zACYt0W3XnkYuxYowoIKzq3lwvsug2UTWqSgCU3yVgRRHaYVSo0bRZ+/xRRVDfU0/n9cUeBFzvDQjwXQDbACUQhVHsYbBbVkUwisXJ1r8BA2CvQfocKiDcAgJjeQV0nELVFPVObTCfua/sdwQCvCPrS6uXaO7ZU9eaz7HJDLo/taCW7b/BXKep1W98dHn2+OkdHzbOwhowJdgAUpiZrn4sOznVomiL9bwZ5ca6VwOdVpfpXAuNZyqZLYr7oGrI5eeW4IVcOEjKluQgwEuvWj5zaaxIYfhcntHyELSeL7/LMhotE6S2h+CMEcq2oDHckiFJAH0YBi6Pnp08b6QTLKgqQYNTc+cwen8vtD/PQNZIsn6oGFcxbYvfRltX3FO64PWajE2/ulJ/y2TXCJLKr62AaJiO6k/nCtDJg+vkFYGB9ObSscROnT+g0xt7FX1KM07Vo5RL4ikiZs/3OzbfrTDTfL5GQDqAgyVb7dCKrLhCoQXHh8KsU5QNKLEv2t/EEMEo1kHNlKXFY7tRn5OxXnNR8SxIM8CDcwg+C7YgrtAY5dRL8VJpPnDZgxDUbzqXuSg7CyBLIdfePWDiTyCjrjAv90G6pk5iZT0odFW+T51ZQN4H3+jHugxL68FcnkMsdxiRcE1kecUu++lnO/aIwAhzukbnBCGlYD18DT5i70FxpoOWcfyxJSzVLetYrwHMRi+N7bErBYcBG+xhW1++K2WEea4eR3sBtV/LY+O5bYIBvcavZWUsnqVqV8jHdnsyfFf6ogTZZgv5BjCgAYFzDjF4zjyr2yLe3Xop6mTRoS4TAKgYvcmfWZnV7m5kAWpd5TKRI+cQSIexLPP7CovdBf82hCFHfTopiwOESmBBqC+yAGm5WwERGKIPRcg5LzEC3nvM8nvfd+j6LjXZi+3FIITBSBZoyEI0Amm7ybjSalSQvRSNIbGfoz73IY9Jc5JK0dFcO1aFDYgE7UlI7r3xGti67JW0JbSuG/XYagOGftzsIUhKWp6VeSTKTpEY5XChxLP5/Ay1hZVUM2IpmMUyQUzLDavlCiHKklapzCi50Hw+x3w+50RDaWutB7IwCkU/6Y42vFeAwNrKpnmSPGW0PEYeghijAO0YWBhuQ+WxwCKUgSHIzh4wAIxYDfkgp91Fzj+yZHcuK/VcplPLe/qYcQBZoANpDZjSome+dbJ0QG2i9LZpgaAYu2ZfR/W9nUMCLDQpGFH1H3liyhml5ZwtR55TPIXr3FLK3j5Hs4lqJwFk7caPlB6aPFyFB6wEufRMArgCfL0xAMULwD2RxVvOIRPpPBk6uI7kvQQQkvduGNJR4DpvR2v5WC9RA2DQPQwDe5VijLSD3cR80bvWDycXBiTaNxigoItEtVXbvqnVRGXCk+CL6TNbGowQc2xASDsJ6GhiPVCk1E1vxvaEkfLzmpB+pqquU8INxfcu+z5JGNCg8nYtOgWOmlkg4FSPcpJ5IKfr1NnMyu7TXguDJgkQFKCmfA4jaDWLZM5nARVVLgm6Jw9b5ElXjqt21bUEGKHvOktc2T4jE0lolyWOMF8bdFheFTeis+Nd1TqygtHll58Z0BqvRITPkedayGlrdbVaIQwh714pGZhAm7L9FLAtj84moE1109sIefdAkVNAFDs9UtpLnildp3XWPQHmNBfUVl4a4xDUXNJAQJBXyfPlOiv9nea98FPadZHmFZ0EWSdkChYMQM3daIGUnqPpWTKO1P/SBmd+o3gQDZaEp5WCdbrjYfiHu7Q4wZLGI0YaKiUnAOWOVgYIIhBt+nL9HDP9oua2NnCQea5kjq2emiPEP9ob5bgvdP9oeZ9kvvV4AaJM6zoVcg6U08aBk045Bxco8NneR2MpB9/JFnaKzyEeIo+dBgfkGSB+JCDAwEYZuY7kbUYvxjiranfi6WC7CfZ0vTAO/demaJgQkIm4Wg1YLHaxWg3Y3U0egd3dXRU7YPOwE/OxgI7IWwOdAhwZ1alJyts9HO0RBbv4Wfnn8jUadXDockKhfjZD11PwoMuWUGJsep4sTyRG6DJoYPdX3k7BSj+/J4GihIhzAFk0vhOBEdXadAx2GhMQoLFQiJ2vDOriPK2SFSW5Jfh3pShbrlMtJFvgkawx5oIYMQyEDKEUE9iS0wJClFkNALSlVQqV8m/whORBzvK6/QwreNJTjBsayEBAKe4QsFok6/T4seP5cKFdTpQVsqCqEpZ4Erp5nDIPk0VF7945QC0HkFdgNpthZ2cHvdpOSPUfiBcJSxM/MdD1ybBSSlBHTqe+gMwV5zi5Vt/16BQYCEMAVB/RdmE5Sa8F4uwaPn0nZy9EDApYATKPkmcgpmRkeSvyMKy4HQTmvXMmpocEdgiBLdHEp3kLaAS6ToJAE87RyzgDSClUHaUt98j6II9rPs7WKYODjrb2zvBimtcxHYAW89q4AzzEe0FtiUV9OHiVWF3JAOYFfgWWPQ4ptXOyW1J9vKPtqXZeiNEj8iCd+xKBkABHIEHDAYPSf2kLeWAlS3JCLxNQZQ2eykrXd13O9dIZb8JqNSQPBOyuBW3l0061Y8eOsY7RS9QEGmLUnoJUz/l8xvWbzWboZz3gkLzEXozLkjf0CGhcdTLogMsEI9WNxXsta83n1B9pmxopOvMO1enZM8AxAgMF+sm19NByTS89T7qa0HwZ6OO9rM8bBaSVnPYSaEsoM6nTCU6cg3ZP0TqwCEPqLgqyy2t0WQlrIFB2MQicuLq90bmsMWAtAhqTWI9HehTdE+3F1bssbUixI0q2QVoxUx+Xv+kaaCDQstCtldZ8YnWPoZH6lmXrZ2iwk/+AFrgxAtFbb5B5FWcKrPLyQdqP70ySEzk3w0nZsNYrYkx5LiDuVN5KmHe1sLeqqn+2aHgUsxWlgbwS9mIB2e4reZHBBJy9ENpyI3Yrx07mawkyaWpRX4j1b9foQ7HsqAGNAXbqHlGYAd77wkOgxzPCezXW0Z5PTxZuq0XeeeWBVOOgWMngVChwADU9TZ21q1lkQzZBq/Yl5Z4rEZWsNOOhvBhskLDQAtkSVs5qUGdlQulhLRrBclx7CChXBIOJqvG6zno+lvWT9XmRMXpMBdDp+DTyRLfBQGRPQeIXYBjS/Kp2uRXxEE2w6KyprP8el20HpxMWM9CGM6TotNXoWLVQhrEIvfWK3DQ5r8DuAsvlCsePLzjPgAYGySoRBFoGUOkoakK8q9UyC5BUr9lslpFvujdZ6w0BB6jMccjWUFpj7Pq0RJCBP4bs2QjDgNWwKsBATkns+lRO7geJfK7dZBrNkzvWO4eup7TKgIsRA0nZER7KpfIlVojlR/Fkou0x9GwpxQKCcSBQCmQqX/MFtTOqe7iyCvjQb+PKWoMX22K0q3cg0gJWKxz9O1n+5MlaLBIf7x7f5aO3KY02ICdVuhyNjAwQzAAUfd1nnp/P5+j7HvP5PHkEcuxA4s9e6qrqHrLQH5/GrT61QMBn5U8Z28j61f1gXrA8rkGHz8fb0vyTMgJCAKciJ1mgPQMxJmuM1qAleyh5w+RZ5HmhfmePSRb6q9XKbDNsGhhRzfOQ6qWtRmJ42XHUcxR7K/ujI++Qd8wDqQ4OYIBBgEzNESDLUttWDROoD9O7HUMZWw1ywH87+slcb/mhsid0q7K7nvMvIPNEqLfJagCtgYDID61TnGZEJQN0m1KdhpxmPgUA5syBmfeXyxUfBU5n3iyXS5bH9G4Bi9Q1gbgUj9b3S8QYsVws4JCW7CKAWRa4Ibf7vkCHk4FQo8QtnRoCBPQ5BRExuqJzhRnkdLbxvAJUmFbaY4ewlM+wCklZq04QuS4TAG9H8b54lo4T0MAm0Hpl8hDQ00IAPJ9DQILIbm0kJesUw8uhJwJMWEBoS64aM1S/1Yg8TTiKoCYLKGXLqxFrLUzapMsnUMak2mbqpkpuAY11QMA5uds+q66XWK/2+3VtqdvGn6r2Cr9pvhZP15C3x5ZggLceOYdIwWsEqosEVCUQLhMMkcI2lrA0QH1DYyAWp1YMYs3bDjPeNC2E8xoEK34FlgwQMMUJT5deGK0otCwgkE0KpAQabC1nHqH6mvzxylIfK5O8BroDrQJLdVoul8riRVoy0VYqAx+VhEzLGzjVl1q5ibKmZ1fzydA2CLgA5AbM7q3Esl72PmfKJx9jOVf2QvoRkYw1y0wNxU1H3CcFDkDNR81X4gVYUwNudwlk6pfdNZFrh9IroOu9V9rrPQc+tbAmbTm0L5LJTa7DmJWToNx0HgApz6ACBpN1LQCgdLHIMygamF6z2cxsq2ohTwpGIks4TVB5hiQT8vm9M58ZdAA5cRFZgoQuBwYDZDA5OLgILJcicGKMjEapuzsvOeT7vk8WOscKFLnvk7mn+jx/LHVdhCSRyf1O+9jFcpCJK+AI8o4MPZyMod6umcqwE3ArRs2ah916SOmpXXVYU5u0e1srZuqAdZOOPTH6t/zeCvwsn1taV3R9eo0nCaJllxQRT1H+2UU6ZEXP6YPpEK42EKiyDebEVxTQB8gqkouRx5AUFAH0/L/0TYjKtVr3N/EB1T39H5gnq2js1DkCbgrgTeVynxEACHRa6QLDsMJqteRkYeVSjp6rzjnELjITM3Bx5KHLIxGhFEaax2TFdmqpJT1Dv8c8f8nCPM5LQkBE51M68vl8SJZiSPFFBNh0XXU6aWPINJQnfe9gAZCr6uj4cwlcW5Seb4EfGx4NgEbver5zv6t65hUIBtDldNL8nJxijmWyzZGRz5lR4NNl3aABpDUoV8btDziuKy3ZpfEjS1/yEJSih7w/JOcFYMqz2bu9XNq5xT0qshOutbV7LxSrvtyG9n9qIfL8BTLz6V9q1Go7UNzs9LlkRnLrhezGMYgq/wZmIBHwXKKaQLJMUJ/MpsGA995MGFFuXGv1mySqKAVNWbZBmRngZJ0qQg5ICdIATqdMWyW5vL5HjClCNU0gW5+WgmyjeVdcoyfwuKKSG6ji1OcR9l+xuKTc8TLb9Y4kK7hU0hd6yNvkDL9pQMATrvE8vmpN2VyfyqJt1EILP9PH9X1KrqoHkRs9NdrFtBwUIQI6ubdrt7X2DHhvs1965hWXcqyTdRrlfum3uq4xiiIC9xqqv0ngB3Jp6zlN6+qFFmCXdgEEuEzlCVgNqwwCRLBHXZ4CGLpfknclWtCRtywmL1hE5CC2dC8BjBAjXAFoub9U6/Xcp50itHzRd9EEQg66Tr0z9bRKT+TdujkVc9+L4lUjU8kJO4Zixds5vI40n7Re2oNCvFPWl/9eAwgEAOvYGcd/GzCQ57ywgrbSBzbSTLBvrm+dhr7kR+nDNCYUVzJw7+uGyLOFJ7quMwaT535WRteBaO9o4ADLBE69J1dsOZD6M3VopEh+QCRgTAqE5vEwRFaefBDRUia9nBQojEzrUJTrmdyjem/1bDY3A1lGh6YqiXDoco4AehadQJiSCYmwdT5F8UtbI4Z8mMxquUxCi6JRY2JG52g9VCYIZTwkj0AJBtjKm82wEwZ0vkOMtA868mSRMsWyMe4+sh5AFihypkcRtPp6EUydUqxZSCsXg8V7pYIQwRmjBYwtMuIq5rKzIkVom/XivbDfSXXomQ0LhZ9lHlz5uErLR/etcf8RaFUKQ7uzka1w2noExLwneZbiCnJbdRZMCkAaQkpkxIGuZh+8gF/jFej7HC1vI7Gz/QTHCobaKEItNtorVn3MiZXAAHcYlOCGXSoQSzDavs/3kqve53B1x303ICIlaRpWKyxXSzmfZLnM/aKUc5pguZ9SQKWz5i0keyAxCI1tSHE9DaVPeeRnSlEY7wKt6UOCxxaLJUKWZYgRQx8yOEvXEVAjbiuzRGqibdAWQ5U8LgrIweU8+jDfa6BD3ztnlVCpkPT8Mn0H4hnrbW0BApErisdU+TomgPqw71OQNwPYzMNwans0aBw8G1gaNJE3lngmxecs1DZTxwGdetsgAN7lpGPOxEtA83qFEIZc/1ymUuohJJ5aDT2cT7tgzNK1mpMlrTM4DpP2BwZ4DLU2KJFr47Zi4JjkRpB1ykpapRimIMFYBNWlIsTNp4MGaXmg72foup4HR+8RLQNUABjLSlxdFqkSKND36bVFsl6WyyQMyDOQkpJIH4SY2kTghLaZVWBgNkM/DJjn+1OwYlIKsnOhQKTZGmtZDyAXcAS7MUswwIzq9DhbZDyu09lOWvO7QRRWxyuJRyznItRpYUrRsIVbCzCpqwICdL3eT90wUdhrw1/L97XAU0AgUEIV2dKp76G6iUcpjUHX9XmHQcodsFqtMKhIZkAvU8BEulOZxiPQCJylMU3K22WHs90jz+/UB1r5F21H3t7G/Z3nksuJd52SEbofaaicy54PuJzAXytWMCChJSw6oXR3ueStgjSmAORY8sKq9KoWAvrt4TRAAvIue+8iUpAX1cOHFNcTFF84QkHOG8u79A6slktWmsF7zIaexy9SZ6sx1GCgHhv5zHUw/Ut8UqNmC2Ktd0PzkZbXAgQcf6ZncswYyp0WEojsPUCJgca8YuUzKFjSuRShT6PHYM/IDQGOVCb1lc4ZIEHnK078tVolGetdUvomZwfEGKITZ7suJZNLv7lsTPoq8ZBuZ4gBLrjskfCFN7oNwFoggL87qOOgQQdIR5wHgwQKM2VkJmoCmghmnghHuXUy84pQ5WQP+cwBLRBF+JGFKu59UeI9+j6BgBQr0DPCI0RfBnXo7VYaDBD6cw5prcwLKqYmhhz8R8saBAQSiFnxGieBHQIDEbIeuVwsMYSA3cUibzOzYCBFKicEGmNE3/WIIZo90DqYDBChLkNHojmayRJjTo0bY95zLt4DFum83kx9LpObBItYSOpJMS1pkIBQdrYIZSVEaWhJSfAEIAGcBap1HxA/1AARxK7a9cn6f3vU3fIEaKHHAZ8ZWMWoXeSiWGkc4ID5fK4bkKwS4v8QsFwssLu7wGq5xHFoTwE4CpmArG5zva46Ij2c8Eqai/mQnQDQfu8INVeiejceusjXsurPnoEUBCnnugt+c6beLFd8fqmeSVtyA1ZDwGoYsMy7CAJ5SgC16yKfI+BTTnjf2SOaBRApHmMrMsBF2lEgYDAqRRZ57oDrTHPPZUDAqeYUiI6mRcUgwHE5rPBAytYq2RIElAqEDLL61D+6RuQf9z1EDhuQxvWz9ba/1b9TWfrlnL2Gbic+SJ4SC36ciwhBL0PnfuIe1XNSBxGnqwkIUPzGYrHMMSYDVqsldPB41+m2iIzzWeb1sz7lzOg79F3PIKbrPJZLMGCgNPnUDuqQUn5QP9ZeoM0yqTRSDoMOtJuAJlf6m5S/40ljKcJICjj+CorBtZIWRL0y6ziEogUJikARa6jPgKDPXgFJOiFC3HoGmAG9ta6cndMGFRMFZVWwS2i1yuuaAwvQmBsZlQCiti2y8ifPwGq1MpalV2tNadtKjwjZrkITil20BeJ0NEjOMTOFqNayAoEBQspejW8Sdj4fukFgiOxKahfxgnfePJsEkwiEbNlvECpa4RP7MBtF+7vcUFoe5UV7n0YxlsK4jhROnqsUNBp19LGZC1mY5e1iKQlJsjRIiMUI9ort7u6i644LT2CFxVLmTWsZqHQztwEB9T8YV0Xn4EyGPSeegCg8EsLAuT7K/rB8oGJ2fH51kgiHlgJo1BgIKMtP8+kQAlYhA4FhhRXlGSGvXuZ7XgrhJZGOBXoNEGV80xxFDj6TXtIKOMTIwZaQmksfe9pJpK1XC2x0/zNgUNdq2UIytTSYND+V7K09CNoTRb/paxK/6GdJm+ry7YNa3ggGUAoEyG9SLz3vkx7JY0dneKhdZc6pBElFPQgw6N6VNibgQwHo5BUgzwDFZiWRmICIc7J11+dxoeRZlMa77yjQE7luYoAlj0G0Hh/q2wYgaHuB7JidLDpQOmL+S3QMAD3YrQEk1BZ5HWgYImidhbwAq6WAgBT5qT0CyJanh2Ml4xiZlcsDyZ3ecZ0kzeVgBqwcmK6jZQJnmqGFq1EQAFvuycVPdR9qC5SsjIZSkUngGGWRUNKZuGKMDA50nQUUyERny4vwdIw59a2k2lxmdyt5IzrfmchzWhIBvHLbZaETgxJ7TjiErQwl/NSkDUGCtsROH5kIDB6zKHVJYbF1oQV0BRJUIQ0id7m50gjOOpmMjjmhfjN72rPgI8tTrEfHmdFm8zm6rsveKw8KFqTlgTQ/krdhNweOeu/ZTV16sUycQLE8IH0odfLRU2c2+0y3fTVkIJA9XSlfhvaMSBa2GGX9v+9n6LIwpfr7DGApox4pUANaHHLshYoCXw1YZTmxXK5YwHb5rIWu69DPZtwvBEac9+gKMJD6QScCU3ORvyMGsV4F8QDoZRd5leOh5eGs79F5myK6vfU519L0rbbmnZnfziFvk7PbLZMb3HoIpYxEFHdECtz7sh6lZT86wUBzXHsn9T3ULsFCSZZ7n+dzfgYBgBBIfgiokLqluRZ9LJ5F8tIuDyyXCywWC4Sc0t5lIeWdh8s7Fii3jEMCBbQTZ9bP2CuQQK3efVN7dUROy3iR3mx5nyluJA6pa0I5YCeYDhRAaN18MhDUARwwktGtZQa6Xg4MIvS2XKZ19uUyoTj63iIqn9fKSXHSdqoZ+r7DfL6TX3MOHEzrOzrC31o3FAPQd11+9ej7BAZWK1JTen2e3DXShsUiW3DLhSq/yGHAoERvdxkq74dL2jwJvAIoaKWdhA21v4NzHrMZKXFn6gsALgIDCXheQwtpksSIkANqKIGScw59jHwiYyrEc0pjjuHII+zpoCVjsmgQADOWWuEYlyVE9GiKUXkI8h/RqQBG7ua2R4D4bhsPga5jqfR0QCuDApXYhh7BAsqBc8aTEjhy5Aj6vseRI0fygVZpXi1z0Kz3HQvF47u7aV51CyB7jGjcCVCUL33QjgEnMWaPbKwElR4T4rsU/ETtXeXzQdIaOJ3yqc9ZiDEyGJjP5ikWIkTEefJkzTJvsxcrLyVoF35E5GUJ2uq1yLIhCXZKO+zQAaxcZ/O5KUuErlW2vORI8yko0JEBgeESqiunh/YFjxdCPnvu5jvzHDCWrp1lcERJoTRoI0bR6+sacBIv2ZS8+gQ/ug5YrQIHypXWaCpDH8scs9I1rVk7N+r5ZX/TirlcyiAjkj0CiIDXXg9qP4tAkDdX3jNgUPdpg5HmapJxabltd3cXu7u7Rt50voPrUv28o0OqHG+fpEPnCLjTrrT0jICui1ithC+0gSyeFQ0a5VoCxrSUFmMw3uCTSYeUjjh/Ugwg3gKnOkUDAmkuBQbxKww8KZMA1m4Tsni9UR56/z8NWPOo1oYlDmRPg1eegU4yfpFgs5ajKHS2CkOyXHS6ZAEaOlpUu9HsJGVLRgmz0hUNcp+q+1MfCTArBSGDAbIMUS7H5DVYBgNggCTRtD2AnL89qtPKKCiSxzgwyvVQoEm9KL20BZOSWKcyYwxl4JHd2Ol6UNK1SoRZl1ttIdFNqS21b6IEBBoUkPKjwEFtJfDkJ6BUWAOtF62Gep+Di1Raa3J1+6xII8aCZXsGAdorQMqVGxiB5BrNufaL4NNIVrPxiKi4CPU9xcnQHAgxyM4A1wHRIfQqNifzI7etsoozj8ZQzFe7JYwyFJbWuHMCbUQGePZClWNq5M8wcPtAiiv3u/Qp7ZRAc7WK502MmM3m6LoBnU/R6vOceZDynnSd5BfQfa8VXOnuZ7ZV7UogguaU8CqBM5J1whN19L6862eAv7MGX737oPxc1rWofeo9R1w/Bi6SzKfUzybuyMhQPRrW00MGl945ZueqDbqluQbnOGhQj32SweuWm23fcVI6lsvWC819F8f6gMZAy6dxGanHalva/zLBSJ21WyQd4enyCZ4EBOze6yRUV5CkDLI8kDwCpSufov2RFWV2E3bJIiaBmF69soxEacq+5AEhDjm9rkPfdcmF19v0xTFGBJe36ZACy2iPs4zltMjkGSBXqijmvJaZWs6CjUgzZp9TxVLdCVQsFumY5nR9DWxEsQooovUumvw6DbJ4BhZ8HLQGSLPZiu8dhmQ2DHn7jBbihGzouSEKqkb2EkSOTRBXMtU5gYKO197Eo0TlZt6CFbqREYDaJ+9QXKUnhbU8Rhk78mY7freCRTLL0a6PpDhSEX3Xw3U02ZNCCASsyMqYkRWblPdsPsugK/VXCBFhiOg85Qkodgdkt4jPFuZsNsORI0c49fB8PkdPiXEKIEzeLOp7OAfnbSIno3zjgDCsEILMG0r+Ra7YEAKfq5DcsLSjwAEhAQENegDYcxd09j8FWkl403Kh7CxKy3HOiYVMQGhnPmfZkPiJLGGflYo9bY68kBrYkQIlbqKkXsat79vGDc2P+XyOIaeQJgAFADNKHNb1SiFo614HDObtxjlHAc85+rtQKGlsxCpOXpSFMiTEqzDPXhoCaCUgqGGOBYs0X4z/j04GRMwWfeJ94S0nelwZieTlojkfIxk1AG0xcU6yBWahkmRAkKUE+k0bacOw4n6g1NUauHgFpimNt+5TkqGkY6gvYgTHIyyXspxNh8NJF6kYNGWo6lNtnUu7XQKDgk2KPI6KsE1AYYwOFkBYViJqb4Cc7JUuzhNHZDwoqpUT8jB6sx4B6xWQiUDvpEjLbYU226DdB05/I9IEG9+GRRYv18NYuPbgJDnQQp8tj6xYwBaobUdhwWeiPPIMSHS9Ie4wMybKKtXCwgIxZ8rjQ3LUMgWV5X2X1xwdulWX6xrhPXkCZOCTMFJn13uHmD0INEG0S50sOw0CSutoPcOlf4TFxkCAte7VzcRVsCUR0LCWqYkNKLxLIcixvEm5igcgxhR0pq0OCqbrvOTvd9rjUywJ6fH13rPw9F19PDF7GYh3VB+IxU+ZLXOwFOwuFFaYaveAnY/rrBJtXUqMTstVXfKr9mJYT1KRbyHaZDYtxUh1LA0XM3eVtVi640uPnS6fPTQQIK/vo+sBqCRhSQb0OV6k8x3PE+u9pH7LVqeuS8m5jtzO5RKInd/UNvGkao9Ay2LX/aXro+SOc/D5ft3H0vdk7+ddRrpM/ZRRfC4udtIdKY22AhbtEiveEY/WyLiqvDGl7ug6zzthaJxlx5v2KGlvdpojJe/o5F/ae1VSlIZwX59oOlA64swjIyRR+MQ/Md9j3awRi90lH9IiL1qfJISVyvOc3IdcYiGDgIS45vM5jhw5wtYRDapW1mTRkWVO7rr5TrKqdNri/KjMZzELaSDG5Aqlso4fl6NoExBIbRTGoslMExigtbqu84jqxDBC7js7O+i6jiczMRFZRzrIB7DMLYGUszzxaQzEK0CHcBw/vstHdWovw2o1GI9LjBF9PxSMLGvL3nvM+g6+8whhh6PkAXvgixZQYtVJcJwWnrptUMCsyYsQoZza2rBktChdM79isMBlCIOxHpMXaGHaQtY7rTv2XUoQ5fLzqW3ee8x2cuBgP4P3XYpgjiLEaa2XvVjZI+WyBQzn4LN7+ciRI3w8sY4XSNZr3tOcT/LjNpFXapX5Rp2tUY7XECQQVp/uRsLXKnPx2BG8GkKAI0+c8vIRKCKFpscnZg99yEGDvLOoeJG1TvOGPIPOSXpZvcwo1mIaP7IWaSytF1KWaKg/66A/63HRQJp2+/SzWWpv7qPOZbCs6lSDH7FsYwwcTOo6veThkTwj3vBy4tkVB8sdP75rwMBslgKqZ/2cdwzRUoNY42ouqPbprc7OOUSOgbFJiJxLAXYxIgd5p7FlL0AEp7XO0E/JKKtUqL0UlRQjeNnWghm7/CpgL2C1HPLR1YkH9ZLAfEYHeqXDvHZ2jpjxFYMyCZpVWHHiq8Q/Cxw/fhxyOJXILvLO6YRx2mMteS5UTyij82TSwWIGonnLRAIi/R0je175Jhqwcq1FJpQWOHXZ/MmJxa4nSInu+MmR3G7E3KlIr+5rWehS72gmrrEsglgXGhk2LdyiTRqtk3DT7QCScNb1M+tMaFta5TUkwNn6JMujZeU6Z95p3c25QSa9svxCCFmhBXShw6rvBQA5US7l2qzPyip5COqkOqZtEEnFlphmQstoPOb6Pd0rniZ6a3l9Squ0XhccGV8ag6zsqIKlZ8B7j5SYx6XAQ4+UnKphsbKSAThwTccf6LiClrVNQjhCCXeIw86r/d0tRdryCGieS4DcIe00SYlo1hk1hXNZfZJdHbpvy/4u+7609ur5qz0C7W2hleWryrNegToOqXwG3wukNU3kpQYAHQWaZUEYKAuompf0Tp7UXHjdkcjeApaz1L52O7VhoBop40AMAZEZ3NeNE1R1xlPdX2ypx8b4R8fnoQAw4036AaAAQuV1ILmj6jtGWp8asCV+RPDJmr79EoDjqKKpHOrXIWAogogtmGzxT+EF0/wbpR9PNhAADng2gX6vyal/87VZlqSEJQq1rWTHAG3NI0Agg+EM86XxoQxbsj5LSYZo/ZCI0LI8I4DWhPtZsiYodbEWrmmEcsRxLiNEsRp3F7s5OcxSudrrABWtZGlbCgX96HpqK4TqohmNFChdWy5tlJYL7bHWCky7uCguQ+IzkgJI5XeIEckqgcfKr/LkSgE8UbnQwzDAZ2uo73sMGX33M0ralCLtlyvdT7Ilkvqsy2usLg+yA9iNroEAM5dmQG0lKAVvlXVtdWiiI0VTtPzKeAS0Z0BbS+lxRTBf12PW95jNUjCbz+BAjriecbtiRMqih4jFbvIGHNs9jt3dZHEcP36cPVAxRvTkrckeLOJ1HXgKgPNCJAULYEiZ84aY1qD1sgh5mWTtOc0P2jkwZPAQ87gkt6psE9Yggrx21D/pYB7ZieLYfewYDGnrjg5o4iXFwHqzCdDWAjPoZGYSe7NarXJ0+QKrVdqdoF27GlxZT1u9HTA9Q7LOMaDI497lgMae14ZJ0eaGDVBBbXVbEaOaD7QsIEGMMTEuAqKSo0O2hgcMK0kWpecDAVYtJ3R+EqpHCJHLGIaV4fkYZX567+D4QM1sTbsIF1CMDYGdJEcoR0vkTJaR+dc5ZDmdp7sBuBQroEGMYAXdl8TqDh7e5fk5m2M2n2GWd3X0/dx4Uzvm8XQz5WFZDZSvYJfn5nE6jCqnmiePqs9xW6Rj7FbTVK009jHrmMw/Mdby7QTTgTwDAlh1jV31r746DUxkJrPr+KVHgCx/QVnm+Sbyso3gZXKWWbxI4cHcx9tKMhCorES11UlcyFYo6T4RoCcCk1xb2i2b6uKangG6t4pl2OIlfWXrQR4Sa/nZeup7yTIILsgkVt6QIZ/3TpbnfD5P5VCg5xrPAE08cmPGEPMRpNascLDWaIwS4CdttJOoBAItK1V+LbwBRbxAqXz0uMFBdqOoNVxWKiVvqm1IUP3OfbOy/aQFOJC306m1znUWsZ6Gun0aDKQ5Jn3LXhpWSFKYNvwcewAj8zIF6pHhowMFGQgo4V1Xlp7noKOrLU+OewpKQNDy8ujYJJvDpPYwtKzGlmfAPF8DWjX+zjnJGK5TYUPmGVdD9Tl7T4xVLC73wHO6fJXjp9tm18LtEpyWf2r+k6ICEOHgQ0Rwut+lnGT5yjAzcNTjApHFBAp4DGK0XaBhpNMe4RoElJTkkvWeEvih+UgxZ/L7yBirA4fKOUp9IPLdGU9gyTugNmbUYvUf2lPkBNG+zyYo0SsJBULWxBZ8A7unY87SRi6WYA6PoDgBQCxfM0lQTLqMlPV6IUVXS2pI8UBQNGmIISUhgcc837OzM8+RpMlyD8Wgr1YrtWtggSEMWGRrkZSE9ANyG6TTtKJNjNJzO+m9BAMaLEiZteVSMpqgelnro76mPdpyRGfMyoC2KNFyhV7XSlYlhoAhrEDJZzj4cLlEihmQ9bAUqTyHy+7vGGM6HCQMHBNC1wEpHiRGoO+D2VIH50yQFTsynTN8wYJmBE2TNdEi7eFIia9WnLa09AhU49jJfvq+y4djzeZsDWjFQnu70xKBCBo6c4PXyPOrXGMHSMB0vO+5TDJUg+a8hz6EnLBnJWubrPxkvmm+5XV+fV1GARqga88Avad4n8jCVnswhF9pvdvKCgJ5JaiSthEY9Vgul5jNZjmOxnMsgQZYEtsxYHdX4gRojNMSWAr29Z48brTrQ44/J6+d3mmk+UeDEQLHY5S8UGQUiQVrjSHa+gnDPy6DyGEYEACsBjnXZPf4Lha7C55jVF7nu2ypZgs1r133HfGP2j+v+FLPgbSbiTwoacmBvAoA4IMHOhpPhQhzqWRQxZjjVmJMx1srUEA6hHhdefYTZ+QlqRjlACMt90rS35FfxruUEbPvKKNgr7wjBAQ6eN/YfbIS3tnd3WXdleZHujfNyQ47OymPyM6RHSMb6ZRM8kTGJKzVHOAmnzQ6mGegUMoMBJSVkSgzBDO7XbvjSM/8HQuEEmCoiVYOuhYaWtjoPaWlaxGcpU+iPGmdiFFscR8xBCVg0VuQdF1sveq+S5Nbouj1e+nu1X3danNpFWoBXQora+GS5QCItWb70gjhSEMpSw3peNakQNMOg0Sp39PygItOYgZ458WKAxTpem3JuGjbCVZAWYVGcZ0yTzQYo+JDGGdDgfjVGBd9Va4H6rIJqJhYlYbFrqO+XTnNo51PPHYNi44Amu/EM9DyGOWGGas4Bj3vYg5MRMU/cnstXbV3gj5rgERjn842sCcRahAgoq4coww81O/shlb1FA9Xzdu0NAaUwlxbdOQVEGUlyWDaHoEadFtvID3H/Bath6v2JLRkacFj2tuk1+kzi4Qh72oqznDh8XMOOo8KpYe2nior+4QnW3MgZtkZwWdYsDeurcS4rTlwkN8ZlGrDEvmz5QwYXi1yehhAWT7ccSHJwFDyE0qucJ9r/cO1YSBT5rugJFEC2NPSiywb2uVcbgwt32T546SaPG4ni/YFBhJqpO1CwpQZukn9CQPE5FKKoMNb1PLAoINdwDeKgPJmonC+/kDbncSLIJO1U5OHhAENoET6Q+VO73sdcCiuIEqmMuQMbMvlEsu83hhCTsmKnBPAJTSZJlRCu1qA2HpKBC4dh0ksYNoaI3Z3d80+2ZarmsdGIdiU4jmVSV4AKoNO7KI69j0Q48zUkdbOZrM5OrIYNNCLkZVLyprl03j6mCdHYItL+jKoTG9ybLQWMmIdClUKVFmIDqWHwJJVdNpYEaHMII+OzM7WM1lDZGECoty0B8d5h53ZjuS56HteK6d6Jh5M1gkwGLesBrIhxpS6N/9HPMjtMEBNlBNvKVTKkjxwKb2xziI4GBdtBSJc3f+AuGhpfuq5SjyZPGuBwS4pnflMPHYyJ9rCzrnkGaD52PUdIqK1XiPtO5c0swB4nzgRKUfJCyGxSaLUZE7SurF9781c1uPWAgT682pYwQWX8ww4gGIYBrVVMntf2MlcAXyxOrWCIBmxWOzmOIhd9nzQnGIA0CeltLNzJK+Ztzw1FK+hPaPWOyqej5C9eQKAJHNirmYui6xfWlcnnhYezPNenSya6qMXUjJvECc6QHtNNNkFh3UU2QtCwKRsC+mnYQhyoqgGR6CkQhRb0mePQIedHZIJc5PRleIjQj46Hhl0uSgepfaBxieO9h9AyKiRkC4h+hEkwyPD6EAmDu/9jIicREasII24ERlDKuWhJ49EbWqFEWNQR1PavbKy1usyI4s7ywTv5Re7HGNyvxKqJIGcBlvaQUJP6kgTu2MBpIEAKR3jiVBRq6XAaXsRAuhMhZjdiRIoKCcf6r4Wdz0FTeVAtY6CEEVR05DK88A5wmtLR9ae7b71WnBW3o+CjTbh5Jblrj+LrivqWXh+gooJqcBXLkMLWRa2OsBMWY+5dgyS0sl4tm5UJnsYgk3SY6w8vdbrCjd6ful5Q+2JGbjJ3nXb32Tdg3/JcwKx6E8CArpPtWdBtrslBZ68Je0dOw2LzmngIbsVdJvT+AUMOQCvXL+l8m2cAI2reAS0otXWXFnXsdiMShERzysPhXMOA1mng/XUWJnkqGuzN0SMCDGQLNhL8kgFDq5W3AfEU8xXZlmA2igyiuQ4gWU5x8V6lBJJ26q5S2LeeIK1N5LKjslIZG5LN4uHIn+mfqEucvqI5S2sZwVY5XKtv2DmQxPgkfLXS0KKByz/9NnA7BkgaI/vEJTei/XzSp46CG1b1gF2E2hhmr5hQOCyIoQcAxrII8CIM1uVMYJxoRlclcENshVQzl1HviZ1cMdrYGmtTyJRdeKYhEgDMQRNOhagqW16jVEHCqZdD0vZ8RCFgWli0ZqioF2t/L2ZmAkpChgghtB7vAntc6xD0AmB1P7VIv1sCDqqO/L99K4FpuQQ6E09aX2y85R5K3kbeJNangmUsEnqUyfuCDkDpf4PIPvfVf2jha648CKJBe4DwAKAkqpyHEBK2QrTleS6yGPcSsKUeC0JUDpciKL657M5Zv2MDzKhKGvafZLqmpdSIgDqW1J6+bjd3gFD6NlSJDCX+Cq3y8vOhDLSnRQIAdfFcsk8tKQzM1Z0eJZYxqXM0KBgTJwQDxkQ5pPvgzwHfT9DT/2jPAMaSOjy6N2UmaPADRAIAUNMCa26bsk558kzUC5BCRiwAZlyumJ714Dm4zHB2vpePDPJqxNcSCm08/HW6TArUih0MFkGAbxcIRkWyeLW/FvKiN3d3XzWiGT47PMhTjvzHbZW6Yj31Da7LBmj3nEkQCPFs2S55gg01K+Qjn6ED7VBQxlgYwwY4pCFPoEWykQo3hDqD+KCZt+bvyLzlYPkdvBOtr7K8pF4BNPzk8Hj/ZL/ds56lpYrkcOUuCuBgMQvKddHn3N/kGegS7EnKoZE+91Yn5UTbZ+AwOl/91DGgTwDIaMa1gsK4GdW4Wst41i3jJ34MvBk5SeBk6y5ksRFqq2OznRCjNFYplEpMnokAQetkClYRA5rsdZHujfdzGtDKv0xdwY9xtl1T9q6QkJLztYWD4FWUiZFqrNb2cbWMuVEQlFwGlQA8nwqlxCsBCvJoRzUbGNVOrE+fBYu5J0hC1G8JXrqumzlaiuztHRFKIxRCQp0H+lrWoqHBbYCf+0IYai62aUlHVw2I7dy1+d54IBhwEDPC6yCc986tbMALLDokBsCz+l7WUOlcdLgkhWgsojLJD3aOmZrzKW1X42pWFgZYWyBVwsIcL97fWqhHPplgUB7UG15pefP8dekUCMCzxHnHJY5mFVb6DzOPKZJqWl+M2u8Kgi2rG/L41R+X1p5pg+HyKdalmV4xad6HmjvUCp7MDJClhElOJQG0PvOLAuksxK6yoCQuut01DqOZhC5kRGtWP9yL4C88wgMKirvTAwIUZZpnNNB5+LhIoCkWEE9H3AqzT31WYyRATbLFT5nRh+GFpXMzaeDRsmJQg+SUzpluzHxlMvGawpil1T4c0ozPrOeQtJFdizLxh2cqE+pT7ahAx5UJE0Qaydb48hHErBnQCl+/k/udc7lGA8ZyDSIkq8aThKnUCMljWTPgVta8DPgoGUF7clSICUJ3QFD8PmEtoHfIzPxKqNB2UZFyq+0KCwYEEFbBkFZLSdM0pqMLbdf6Rmg+ANygba2G1lAUSvirqO0wzrivXb7URldl0796rqOT2Ij64wyECY3cw4YjBHBB0QPSctbWmHeNU+FK9Gzg96eZa3JerVR+phclqXyly1nto1wYGs/WVo+n2FRpwImN6x2ARKR4HEI4iFxyEdyi2LqvEfM45vG0qkxlX7vGrxH7ksCA8liXPDuD2q7sF/NA/QKsHvTuR0Ua6CSwrDrlDLLlR4xDVq9q4SU5i1R4MqTSEqHZAp5d6IcU+u9x2q1BGUq1cBYt0G8LNHMJ4m7kCBHem6yFGVNWVe/7Dfqs6TkSENSOwO3I3W/M2W0gHHKuAceNwE32nqXs1xocElJle5q3UY7tBLPNQziHTD978Titm2uQTmfAkm7dBhkBwQkL5nvXPYOtDyCFkhxP9LcAiUntoJBA3ftcUz86fK4Rp4jKcW6DRIVkCAejdSGnDMGGfCycdCz/JvNlIeQwJYr5KcC9oAHnBz2tpX63p/jYJQOlo5YaWYaCppggGTPJ0XMZ4Szg1KEBpCt8wg2BS0a99BJhmgCiXtaWR3e8XOpNjGq56lGMFjJ1qELgwECsn4sTBwR+Vx6CrLj41Oz61h6RXtDAkxEOQsN8LsWXNqKkwmuFEFnFYF29cqBQBI4SfWvrS3rnhdLIdcvyJjKEk0aJ+qHLitJSqEsW7AInCRG7/yAmI+e7ro6pwJFyFeWoGaygpyw4VZEliH1DYMAXkuWQ2WI9FIIHWQ1diYA/a6VFikl2kvtBrVH2gM+5XVlb0mXt1mGLiLlpFJKLY8dHXRT1kUDP53mW1IKl7siqBdrAe/hs+KXXiZwnYZEdn6USlcDej6QhV4jnoEaDBRb9gworU/e9D4Bnq4ji9NXdZP2ATFKKvPWsgD1C3VX8tKA2yd9WIMB3abcWelv5RWQZS95luy0UAoxK4hIfKSC2lZ5G6qk2802trOyggCB9QgI4GHDyCyhFYAMxAaljLb9JOMoMRoJqKRximQyOmSDT+prgECLRwqva2vuU1kM9LK3Mua8CDSWzoXMN7GSgbKcYL2rWhYnkOvTNmJKeNenv7te85LtF54jpo3Zi1rwVou0fD4sOoBngJQyWJtFV1ePvQJGKSqUzm4WWi8CMwIyU/Cljr6z2/C6XpAvLxEUnW+VMgEPUroiUJxzJnJWMiIO5pCLtBOg7UakiZgqQUg+MPNRHwD2QBONVEuhzW7EwiMgE73OHNb2MND2TZp4Kjag60x9qBND3gWihSjdTxONwNh8Li5zKSuymzMtIaSDjmKM+RQv1Y6cp1+PNY9jHjOv+5f4p1z6AarPut+1gEpr6CtOLRoGu6YsAZX5XIG8BiuxEYWrNaNPEqwiIaUuYgGayuW2ILfdm2ezxZWbrk+9E/4RC2xFS0PLFVarJVtoxBfMs1HPOxFQ4nKlJomSkAY5Bt+6vwCosxr0eNIz6oHSIMDmBtBuZhvMxu8c8e6TsqG+dMG2RVnbEckgJS9US5Fz8N9Ah/z4DCBEGbYsUk0GNMAhejvWpYGgwUGM4F1xxoyJ0cxvccXLslK59NHyeqQ20g4AtWxmAmjFM6PHmMCEDmYVVhbZqmUpxV6lcmI6zKwnxa+3oNYBmzx/uXzyvti+ljGJWVZGttaRPWyUOyVGOUSNl5cAkySKjCgC1NQOeSYFZyq53NudPSRzKy8tdIwDjfy4abMJJKgrt7xO6IDLBGYUzC9a8LLrMMpaXSEZs1UEkGtRT7T0JlMknePewbl0CAgdATubJTTmaH0VmsGjqlO2KmJ2MQZZd2MEOKSDKAYGAypjm3Ocl0An67ADRZZH8mho5UwMPAw2vwByfcu1axJidN1s1qvXjK0aIj4kRk3scu+xAAHxKvA58MWkBoCYBYxG7+nEPcfusVn2DNBnsb5E+RAK77pULiX9oMQfBAwUJ7FDILBiEsvHMv34BJBxD7mPrZBaKmFVe2HSGM8pUHBOWy5necIrpUc11BYWpZ1Fef77aHVZIXe+Q+xFKaUeyQK5s56cxKfAYrHkoLLF7i4Wi13m7STERPl2zvPcI4uEwTiUPyCDFU6Qwp6FmBY8lBBOYQsp0LLzCixtFFA2oJPmYunp0K5rBnV5LJ1zWCyWeblE4jtM3+ZENi53Jisdr0/3JL6xR4PLGHrI4WnyewnsZTx1Z8q6NiB7050CTTTQDOzJSxfreb1aWU+mANnkmRPDwSbXoWFN/V6Wq3dSSeyMBlMSU6FjtXz22CRZr+cYJXharZaAo+RODn0kOegMuCaDS8vHVFtr2FE7UteRkZiOhOj7BABWc/GYSF4JZMCyQgg5YZb3WGXgR0shJDNXKwnMjCo/RzIQUrzQnFMbz9B1AvapPzS4FSBQLg2IAbsnl6fitq0xg6L9gwGtNNBCLKJ0y/W6aBooCka7gZvlFb+xZUrCRq3Hp+eGCk2b8iJS3vMQOPeBcy4FiORYAeOilKYzKqYYAI3SgcgCRrukS5c/zXtGhjmnQqmQJCCRJmLtytTeDqjlDz1pbPuVK1itq9HzSEhwf1M9veNgHLJ+OxIKjWDGREEAnwITZlnCe7MOmZ6ezaJYLu5Y3uHOtD9W2ID6giamQejFWEs9JXeFDlCVY01tfUMMaetYEEBQeni05S0WFbsIsvDX/JYDT9GxV8AhKy+y6BARlon3KPf+aqmFl3ZPUl1QUeaeJKiMjRJtGWV7IDEmAhY7/o7d4gGInrhZTXgzRnmJLNhtctrS1J4uMja0O9s5mmeWp+CQgs5IMTswUNPtE/DlQIHL4p0TvtOBis7l+AAaH7qeytF82fQSKT6IQHRFnwPNzxrc0f2lxV56LORaKcvGaYiXwcrrcv7KVu4SNFnvxWBeyD0SfMd9Uc4J8WBAyVL9aukSArIpBi2BQjnRMoQgcTWFjAQC54BogwHK2CrBp74j3tZzWokjBluSXI/5GXppAhxgyN64fQEBO057oX2DgZY7rEmkcNkSB3lRdWlUaNL5ptxsUUEsFwCswCSATicNyrMtP6ieMPJ3ihIN8KsV0lHCsTiJSkeOCrNKWtnkodCu02RNUrpSYSQSbMvlwlgOpHhSsJ30K/0uk43iFGRbkIAfmbSiyO3anwYEGtUSsicLSru2eEJDUgSn6ynbYHbB5XGYcQxDx/UKQU90B7KmYlTP9p4Pv9GeCTOOpCK5jMLSrrS/+lMpE61U9EtiBZKgJKBJEcEUByHxEIUnJQQMiFhFqrP0NykJXopxHStQmgJGyJMCz/eltUkCVDRf0jGuvGNksUAI6Tht8nakjHQrSJKwoDrGadNTXtx98psRwsS3hXu8U3yUPtOBLDmddYhwednMeZeDjFVegyieukXeArm7uwtKTgMgbe3KCcCqLHBDQPB6q9gqbWl1yvqHY2XNFqaeJ04AkFbO3tMhYx4pf4cECWslyPzggM7RWKee8pRGpuH3j7DzU9zhKVGVBA7SOArIjBos5/7WS0xksSO3LcSYg7sJBFgwYJPqqPXt7N6wngExxnwRFKo9DSm4ccnyLwEnj95FRMwzT3g2KDTYqFG9AHvdbDKoePkC6ZwT79JhTYi0LGC3FCblDN4p5VbisUkGlgUD9DzfJQM0Mg8XxkyUe2V7oiwxEABI7x50KBLvODooGtgjHWiZgCyHUndzE9RBHGJNRHONDGJW4GRBqgK1IBJETjmg9dqtHDIUC4YRBaknQVJwlONcmKOOvLdgwCo2Kov7hVFhNMygkwdJjIJuP9QkSMxFyoIYnQBIbX3LM6t1Ke6/YPqhdPmV7rgWgNIgIss8Xh/jAMBGvTSY884hOJ1IpnjR00jIqUkxBj+FYwqzCzBjaPtHR0tnSzLzjPUKZKDSdbw8VMeJ0FbbzHPR8jn3QcE3ulG15VMcpkXKiQJk87CkU0BF2DLwLM42oLJEwdVjkAoGewak/xz3M/MqrFep4yRapATJO6bK1QKcyoqkDMnCD/yuPWnSaMu7qS2tdXewccGeCyeeGSpblJziOwJm3HTi97TenPLiO6QtmeIJRMyevAhQNj1dH8unqYKRv09/lV5U52rPQB0LFJj/jKdGBwfn/0q+rMo0u2oEEJCHygT8jsyFso4p6ZsAGT2Ray9ZyyDUAbSlknQJYDnLm6nvPdKypA6ilBwM2psbBgGGiaeI/2XLId0nZyZIvcc8tXVfqPGl+ifhANdo3d6pNPy28xDs3zNAnQ0osQFo/wYbdoVVIRYKuLJ8Brojpk83U3KfEFeICEjpJx1ba0eO7GA+n+HIkR21hksdEgBJacQdxQoSiUF3d3fFvZ9Pzwu0/UULIgAWcYvisXtTKeuadTHpnAEJDKTDLUgYz+cpYxltyyPm6thilj3Hae1PMocR8KHjn+VZ5JFYcRAUvch1Rn1JRynr9UPNzNTWtJbXcx/MZjPszOfoVeBg6d2gydJ5D3QdC8Bqa6GXLUCBGKFhAuhnkAdCmF4L1jzqBUKnbULLHFxHIM1lpQDVzzPeLrmDzqc0zc45OWyE3KvszgZioFVeDXT0UpaKE4EsK9AxqRR0y0rPpV0M1AdJOKV5tFqmZYHjx+7BcrXCIh+eIkZ/qot3Do7jDFK5fQaWfUdLNVQpC5x95v2Ydz7oMkgpzOZzjiFxHCPg8vG3kRWRQ9qlQIqZ2x8yaFbHmVOyG7K2U11SveezGW9Z5u1cfW+WJ7jvunS9V8s7dP6E9nYg12U1DKb9NG49g915Xpf2IAztXV62QTR9aEQxgQsGGY77PEaZx+mwtvRK8zrVb7VKLndJIpbn+HKF1XLIng+Xk1LNchrcOSgLJHs7uIriLVtxuubsfcnjINOO4mckt4ZO8KSVqw0aXOYXJesZMg917G3kua+WBpJH0S5jBMrRYOCVlzZFB3AcTDKcAI/ZLKo5mHYKkAc49euA5SLpgcVSjrNmnohSD9mm6fiMh+SptfEYZGykWAGb0ZTkuZCWXdsp7/W0d0hxsABCRmHgd0FEdaVayK7yDNCgOrGyGE0WnoG0NY1QqldKUz/PVNUAE3GPDQjBp1O0nF07o/ZIC0kBRdNWXa5MYjs5bBCf9jqksqMZP7umbmMU2tsTqc7a8h1DpKkNYl1pK0sHeWrvhvYokEVJk7i1q4LGzFqm+Zlw+WxysKDXVmeLLOrPfxVAgD6XE6ocI31Ilhk7hyRQlBLXFpaNOnfQgkKekZ+pa+GcPo2Xf6XFjzI9M1ko0i5uMjeI4x4yeNXbYGm9XPO/cw6RAGWOoO9Mhjvq3wjy6un6O2SwHlW/eLU9NMd9SFleLPOCtxMPiDUtxoIAIwIOeszpOGROMpaBk02vK/PEZzBAgIB/dw5On5uR+TUavs8gwQEuJs9AIDkSAqKnHCji9alJzHDxNpAJBeZ78kwQsJeihIss77aDgymI1TX411jvLKSlbJZVIWUaDLSfXrVLymx5Bgg8xZxUyM4vaU/mR2/lD7207iA+0IBZeDrPQTU5yLNTeeWcg3MdfBdS7MAgh6qlNgj4DcNQpOumv1Mb6Vq5V/UDlGeAx0zkj6qR7g4ei4N7BWradrlhn2CgRi+MSNkzIMKsdJVwRHJuuncOzidkDND2CyAiMWbMbtAkrCO8c5jNOvSzGY4cmWM+38HOTkp1SsFh4hmQ9ZzcNfkI3hTo5b3HYrHIZTv0XZmYJ7eu2D5DCUgGzshHGcFs27Qy1ssDelIYZeM6UGpLifKXWAgS6jKB7EFO9IzFwmYupChWEogAASoqn74nJUN1H9irQJnDnHfonE14NJ/P4F3aYaCt5CFnTXQxrVN23sskQd4C5YDOeXRZaHuXj0tOTMAThoVHJ3n/NcijcU48F/KQ0yS3WehMJPYqHZs9DLQlK7GLrGHSdqGeJ7/LdUzVjJz6N61RFt4ANU9KMJw+I3kEgoCCrCVkDuX/oJQBpZ3d3d1NhxBli45iXcBKwbNXoMvrnBTT4X2nFFlWDABSoA/y/IThf+9FIaTxEGXr1fcUtJ13a+d6yA4WUlqDG/j45pCVEALgYlLk4G1mQOxj+i5GNaPTQWFHjhxB16UUsDRPvU9HS3ddh9OOnJb4NSux1WrFuxBWy2UKWMzWa7JiEx8z7wFAnKHzHqHr0bkA9AKQO3ar5FgZiJKVOSEgWTx/yboEkhz0IGexWPA0EWg+Umrp3d1FPk43HT7W550vyYuR9rvPZ3MeJ5JlJHxcVrJBHSVPqY0JcPiOkoc5ngvz+Zx3KkiAc5pntFU39e8Sq2Gpsg3aHCkUhS/bwr0JGNUGepTpDIoJkFgI7Yl0IMczKeJ0iJB4sshISUcypx04x44dR4hQ6ZwlVoXkNMUK0THFs9mcX33X53p5XvcnfST6hLZNikERAxBcOspPgwcYDrekl55KitzuvZ1xcDDPgDZ1suWQTQH6yqASgxA1WnHmLd3OkbT2NDuH5AVKh57Y3QTee0QMku2o8EDYp2SAoqPKhyEFEEVtlVkrUV6qGxT4KZFwa+1e14MUhqB3mwVLZ22z95SWb5kspI4b0KTbYplT95EdN5kYguiTm8yuGxrw1/AO0BO08nZ5jIQH6ong1LUoxkCPc4xtK81Y3kpI2xfXrhpzz25MpyyrQl5D23LOfM5cAkSa/ODlgLIudEN0Yq0CsgQhBytZb4Auh56ZltbEatduXU9gJfdXDLZN1N/mGGI6bZNy2pNSY8CaX8o6M9aq+o8eZtquPQLOQ+cKoFPyKNNjGq6IvpNjYjsvY8R8Sgqok63A3KsKvIrjJQJqLvts7MQQENXYIwIuSoCzZkrJfWG9DSEI0KL6iTfA0f8aMir+1aC2Mc+99LjxCPikpEoNEoFqTlTBg6zIbHyRPh025nZKWaXXTZ7JY+ms3NDqTcsz6YhS/ul3uo/6LvJnkgcxSuZYktnDMOR2UB4JmL4Qo4Ker9BJ0ZaWXKY6aJlCZennOTiuky5bS5O9kfDMtnSgswnaZOwgrlCL2aoyjC6KiFB7XPP2NHIPzmbJGp3P05o3HVAUkQ4v0Ydf9H2H1Yq2lgxsGa+GFZCtaT0pqSI6O95sJla6y/UoFYlz9lyBUuEAYGSqlQwns5nN+b3rOvT5cAvZJQEun8ojgUBxCJRcxqbOTG0idKuROb1oTzIJJWFEBcSyYug1qp+lQ2hm/YwZwzkna/MmT3rNnHm3dramPSvcGEO2kADZIEoCgyaR7Y/KFZnviKj5r7mTIML20WyW8ljwUa99tdZHAiYipmNqoxZCgeuRBx+BgHIQXmGPDi+HSUAcCRJuYyDrcOBYgbR/e4XlasHeDnYZc7862RnhvY3tyMotAulYXZq3kLSzeimg69Jab+c7VrgJzCVlwyA6l5m8QTkATYl8MybKLU1A3HuPHkDUYDOXJ1I+1VSs1U5yXOTlADosJh2UROu6JMRTPUPebsYqKdB204HHgBRKBdyE5Yz0I38OGRyc0z4rOR89R5EnIEy8n+ecAqfDQOmlh8rjx1veKEMgyoRDtKsjVzVv6Rpi8siksobsXViy3KAZ5H3HFvws9yHJKpKlMdL++expGVKsAB0ZDxezZyLxFI1TP+v5oC/HPD+kg+04uM+ZOWGXPrrc5zoGR4FaNR81CZ4TudN3M8QeHDs15LGPgWSgzupaGhAonkPmQAIK0QfE6BU40ffEJOOC5h5dzvYK/SB04LMJhOqGtKxXeh8bJLGfCJEKWnIOOeAk7e/UgXwyoaQ2JOeM9Wui16VeEllMSwKCQiWCVllU2SWot4no8sqX7hPtQtZuJw0+rGeAwIB4G9pI3mYis89NvaL3H9vJ5UybR0e4sA5svgMz2Gbvfom6efBlkHj8NrF/pHvlUTwpNyHh1tiUnpOkSJwoFLOmqc5LYDdE7jMH005T31xRtlj5q2zpqWDVGGM+gKVR5wawSWdqlPEoyYIVT0vOqqbGz1gyWdHB5504eRx0PInmT3a3QvOLsnZ0/0CWt9gCjPVY6EEnawteABfVAwDPDZIPhifVtS4D+DqeIP2egrm8sGFuN1WnlFMt/tpkfFHbeNzyEkw5nhIs15KPdsw13waV3pi9L8Wrqm+MiEPkcx1aO6hoHJKs0HlExncziVtceNGUBRjZwd4lDVaAgicSD4llr7x1BDyVgVDKj7ExiyW/KY9H2jrtlDFR3qvd+bWc1+WSIZXurT2a7BkwEyD9I7k4TjwgOEQwsJ07oxTc5DbVHUkpXPWAcTSv2cbmACfo26mtjNql2fceQ0hZCiMi5jvz5A1YJNihmZEmJeDyYTTepNmdzyXNLlt0WYjTdhzrLbBpUI1nIDPfLGf+Ew9Bz6jeKQBDQpX6iiJfh2HIe8xzjIWKm6BT8Kh9yYtiT9NiSw6a5bJrMYprjajvOvRFABFZNCGWgoo8ExbIVB4TUiTUT7Tsk0EF8ViK58pR0zxJLUoX/ZaVd4wZAAkYLetBpAWVPhK4BXoSOM2pgkOa0JQWV5dPdZE6OTk/Ix/qE4bAh/8gphTQ4qlSijOopa04pFfh7TB9qzWcmp7awtJUCne9rk2xGsK/1rVryqHHuRSDoU+sDDm6OtUxsFckRFkO6HyH6DIoimDeoDTWgrxJeci8IK9FP7PzieaO3l8ufabAVi7DBb21V1ukrmgp8VSWHQ5N3pJ+yQoiN4CMkKTsyPsTWQ+mItSyEFnbuUReIo3Cv47rRf0i9ZQ97xRzsuB+MMArGwyz+YyP5k07NvTSJZUpdUup22knV+Yj55N3oPBMdr2dVyIP8ph75B4rPAKe4oska6S0V/q7lEPac0ZtXi5XCEPk/CHJA7NC13cYVjkT7SDph2mnxGKx4KytOztLzGaRM8IywPQdYvRwlOHQSSI6IQMDRufUiaYDgYESQWoLaM1dBgho1Kyv0R4B6h6L3jSaJDTq5D7yIkSnTjYUJgSAQU30uj3ZmsqAgpYI0rap7Pqh5wx63a9G41qQmPXa/K6PS6Xvk1VDa7raapf6hmjzFyRlXHsitBXY2h/cAgLU6y2FIQDAsRu5HGPqTzkiNPJndoOqOhL/a8u59FSk/kWObZNnlGMXSXlwobZdcp2+VxA7gRvZh9/wfIB4PmY3YAZNEeLyD7IERdcb6zh3lVb0JZWgki1JDYBgvUNBgSdyo495e3T/Utv194ZvtXVOlr74OFBN4ar8NCYxaiHdyoUgPOVpnbvghdSsaK1Ifb/askYZI6k/6d1awrVlp+tRWduo217LMPEKmHIh7eGrFcBPt2jZmOSaXcuX+UD1Y76lerM3w9aHyinTntszS2Tbb89nh/TGM+CclG08bVq2w85j8Xh6k5PEyEvV//TNmMdD5nnN362xllwvKjVwNuA6nxKBzefztMU8pPTWBNK156613ChLxyKnzbIP1ynymNK1uq8izGb9k0YHOLUwGmdAcnXwj2bCRyWkIqPbYNZIqTDnnJmgJMi6nPQlWeiyp1grCtoSku7JaUC9B/oOIfTY2Ulriactj2BYrVLUerAHmdBntuggcQe0l5nASIxJEYS8A0G7HVOmMr0VUragpLgDib5OUeoShd3pCaJAgHQ9MfUSi93jWA0DFrsLVho8Hk5cubwXnM80n7F3wGsLL6ao1tR1yasQ+w4h+FwHJ1kGu7T+TPvTQ0a8Ua2nicCVPfipPVbRWFe84z4LMe1EoDZJ4iTDjOZTKleUQYwRwYW8LQ5GAZfbR0XxdbxfXfrdWunyQAmw4/5nK2TFddLtJqtaKwF9nRYO2kVO/Glar/paR2KXVm1Lmem+LvuQvi/HSZdbgwD1Odo2kwLT1loZW8PP7byME2SAXNokUYEiWiLzXvpRTvS040jPtGmN7Wme7JXKjTEeEjZGUMi8KE5dBXwZ0AGA83DeKl09niUg0YmsbLwVzYMMzjrA+Zgs7bxjBJD+pufpfl+qvAK6/dRG3ik0m+HIaUfQ9Z2Rf6n8IHEsymsh/Et5VChuRZ+0mq1oV4Nt7z17zUjhi5yw3g8Nxmg+E2giha8zAKYsgAGr5Yo908459LOeeS+ElBAuHYftTLI44SUa22CWRLUuS66N5DHynnSig96aTv1E7WnAmuqbE0H7BAMZkauOTF9r68wKzhhUhylAUHkXShStLHR96AYxlAjfkFOJysQjZQYH9H3aX4oYsTOfY+W9STqi681CKX9N7jKaILTuSg6tEEJW8MBqJdv2SoHedXarIKdv7VVkdyEgjAOJLSFKwCETerFYAIAAiUK50paqvu9VQJwsE7DbnfodGUyxABQG5wndWjdUVpYo78DLJwQoBAzUuxn43fu0j7yw3rSlQ7XVVSBUXll2xp3Y9lDQOOjlD1HQGpCosXXWwwQWTMG4F0sLSVPJK7o+5b3mWZA6lWu+LUu3RS2lpL9rATbb95GGncdHgz446vfMvyEai0rXV/NHmsdFFtPs6bBt18BTfqPcIzohDl1rc35Q+XVf6RgF3Re0fEf1oTHQ5RgrmRUEjOerDQj06BDgQQEExOhxLnk/XZTgV+c8e000gNR8qT0DdI2Wc2w0zOd8CFx5mBkUINNnvOg+1OVaMNAXy6BZjbp6aTW9NBCQ3hE+kBMIia/oBE8OuuWEVunIZ6qfloXJkAsYQkpRX85h4oXE52pZWOVBYCUPx160GMs5xn/JdwYQnBwQQHQgzwAp3RgjH5oCkGvMKgMWVjEwggyDBgORFVj+mK1Uij71HLWuc+CTIGaXWrHE4FyKVu98XpsHOGvWaljBe5dPJlR1ZssjcBvI40qTmJYJurxPu+/S+3zWY/COET0cudzoMB+f9qPmdU1HkxfIzF51s7FQECNiGBCGnFVwRaeVZcZGEgS9CpLyzuWzAxIap7Mc+hzB65Wyg7PK1PuUQxyR1t0pbkOWa4RnlVBkjVC+SMhqwdNVCgeoFV9Uz9HE+gZtjwNZydqtKsyqPylrmZB6hFrXHxBBVrzApi6f0hfJfVu4TVtKuWXhAzBBeWVbuq4rPFC1VTn2vErxOBrrGly07h0nEYAUGMcK2SXr3ntqc8y/Wxc19aOMoTw7ElMyEECep4GXYijWgFy11shoW9/2tWaZQoMBUlxagXGxkY2j5ngI62eACQM0pa2p/RHI53ulDyHKUls0ywS2fR2736U/rbFjAxC1JU9J2yQzKWUw7NmKpxwGToEy287xftQ7mHgLqLHw+UbO5qjLKQ9eYtkYUo4Q8QQQ2JPzBMj4pLwiIX+X5lfeHcPHwDuE6DFbpfgw2p3ivc9bEQUwtZKt0TgJX8B4S21THfenc9QTJxcEEO1/a6F6Z0CgGDoxRgD0Pv5IwSW0N7oI3gLyMaJCJOi7vGY56/u0HaWno26zTUuzh+rG6EzWvwgM7Mxn6LzDajXDyqW9w4h0TDE43wp5M8qMaLx+BUeOB96+E2Ngr0CEnFgmWRIpMFAUH0XUlj3MQjbQhCbrWPaXEyAY8vGtKed/hHMdvAOnT53NBAgIOreJlJIwUyDKp/zrnbJUJfaicO9FqZuuK7SgUNYRIfA6i5lGzun45/SdniClwJG/U/l2C5IWmqwFY1FkuS6pHcBs7aTvVhl48fN5eUt7LmqlU1LLKhyNz1Du+XpZRfVM8azWM1r1GKvXpvtJ/tu28q9KoIP/DlHWWtMyil4KcXI9/RGj8GbRp7ILw6Hr9NHGETpyW1vGseBJY5C4OqMlAFYU2lXttMIlj2TxnJLPyIqnzIhlvzMgABA8UlC0mmK10tV95gQISF7pnEXRAV54lDwM2utm53dnvAMkLyj2guUT6v6EAt0lGNDJ1LqEorVhzAYgVP9rMEDynEiWA5LyXyyWrPRtUK31CA3ZMCXQQfKO4mF8jLz9Vi/16Xlo4ie8bYcBA8XcsLJAt8/2A91/suiAngEdEZnPFsiauDwilpOjZAFAa3zWWsslOVpjAmIsMlZlxuyzy13W1CjKOD1fapWI1v2dA0JInoHlapXKGAYMaFgMVHcluNIakoryhpoEaK+9OqdP5RO3pcvurto6I0BFXR1Mfw7DyqxhxYpxXDGx7fKK3pFBFjEJ21QfcmqQy1rappU417naxpgtp5i3f/GYJJDinONdDRSLMRakB1Uf2zc2KC7VUy89jHsZSgHj0F6HD0Mo7rEuSLIMYy9KxK7r1mCgrPeY8qG+1wKoBAOtF8VIJEuvM9+3YjPKuuyFWCFTYJU6thlwvDaeCqehS7JguVoi5CCu9Nw+KzU6uVOPtzpjYkQ4pp0iWrnTriAFLOoW5Ouk/RpAUhuJ59N7Z5a1rHcA5r7WuDtI0K1YuPYaYfVo+jgMNjeGLHXQriFqU5IZQxjgBmBFwCU6Xt+XQ7BkWcrKWbscS3EIBMytYhP5x/0Hu9SllwfKPCt51IwMic4uETnkrKIgjwAMCFjlOAANBtJclZw2ejyS8qe5kedWCUyYt0SWah5Juyt6ll9a3siY5uDDIqU7qFWOlrPF29KwG04KHcAzIOiUVZFiDn12cwirrMCWfAiJVmRaKFmUlBVplwJX5vN5TiDSc4rN5XKR1uV08Ea2POGcuPW9hz72lwDJcrlkq3oYBhOsFoYBYbXCquuwXCzgACwWtNVMH54iirTcglbueS4tYGprrYxyL0dx58lWGEk4Ilsb2UdjGJ2U7Xw+yweXiGtLL7GEIBnrUr2ESame+mAak3jHy+P1ZAGAvu8QgggRQtKpPj2/E7ig+lAdiKwQssCjVHBjSw5NIFAoRRIyYUiCxgfqG5nkq2Fl+DbM7DZAHcRH94wBghIIWOuzBgQAWOHTfugULR8MEKDr9O6RMS9MCwSs8waUbdIKK70n3hVPnwXOOnhttVypcfIM2GnwyWel+SIqxZOnuamTtvZF0dfXaMHsffLAaU+SlCHbKbteAXpf95Ee4/I78iCVmfc0oLKFyc4LSjOdDtUa0vHUZhupBtOStpiUtijpCDowqGwbbRtMywMzDjIWL6L1lslBcHKWCRkCfHyzFxBVggHiZ/KgRO4rGWdHS3WsJCUwc8gplEMIWOwuMxhYcN4Ekmup/ZbPU52cmiPlEq0de+KJEAKDB+qjZgwbzwvJI0J/6+cIqOSaJRBYc8MJpwPHDBA5V4KBAtUWh+Zo60mEYGcmIAECfRhRJczUJKctIOxhpIqBJgOS27vrEKMIVbsWq8BAjBxkmMCMw3KZ1/mDOsoS0m7jaqYXoU5YYVFbheKC00xPXhQKgKG/bd+1lQe5NGl7lU19rA72MIo39V45eSgiuPytFHbO0Y4IAkeO+YOAhJz81XF9gLqP2CLUnlFVJ6nbdm5t4YVSySqXL/d52iLoI51Dn4RSchtSDEX6SXuE+BwNVbeStJWv31ttGGtnGUtAW2a1Z6DcSrrOM9BUYAWPtgAOv9TioYyb+jZqGTDwu3Ok4O0AE8/U46f7IuWPkD5xfE1NFHBHzxLwTIYHZRGV/k6/6URFpPDIpayVemqnavsWZp6+r+x3LUMHdU5ICNa7SmSDVh06tXPB5wx3ZKWWvJA8hj10PJCWsVAK2cp5sKvdeQcPzwDE8Gpj1wDLH/XO7YkKEBAvRNquHDn2JL0vMfBnOSMmFaXnkl1e411dCpiIF9bm7eB+VMCa0mCzHFEyJNVZL+volzZ4U/sct932Rx7KE06HkHRIGFkzsFb+WpHpI3ypk6kjE3KSaHya3Amt9tmynbHbSmpgM7iRsNACmpVUVkbee6xWlP43pdIlRqIBIWZLTJ7SU1KwEk0kiq7lI2H73iD/0v1dWolUt/RO11oLkzwA6XASSUWq0ygTU2kFQHuDBZFTSt2OJwNizLnW9U4OsuJtlLxuk6YsUgUEAHCznoPkCMWTgnJOPAOErGUy6j5qgwA9xiUY2gYI2AlNOyWs4F0NK2BplTl5YMwyARz6fpl5OFsGxdgmkBnUOLuqL+065Lj1XrbZ5M0YBuMdmM/n0MtDLUCgy9bUAq36t3FQYJUhjEywe70p5iVdG5CPalFKuFUnsfjlBE/ZbWSUGIq6pL/U2JDcyoCCJHIGJ5rHeLy6AnQX6/5SV6jn2LGjxpEiFI+K9CvJQ4mtoCOLF/no7UXeK09KL81raqtstfTohlWuq/WISfS8bCWklNU9b1FM4Ilc9Lp87WUhINCBALKN1WHeU/3Fy0sNPtK/c58GeS4leFssVuwRCCFguVgxWEh9ng2NKG0VT5nsvNK8TPqJlhzSfE/tT23peMslpVROia2SwekcBdPaHCsCBEh+Jb0CYYkkS13ulZPsHjggGNATzK591Z4B2RbT8gyUyjG51SUK30aylpJCnqVlcYmyRfCm+2vPQHK30hoTlRFiWjIYHLBaSaATWfK01smBg4VVuM6qouukHG1dtYRujVJjTMdq6oldbtmrX6r3GhaMHhNB0eLuN1Y8ORYIhHW0t5YsERpjgJYJJIGJWObc3+ydqYW4NSKkHq3+HuvrlmVtj5QWz4C2+oSfh6xEUnnDIF4MDkgydQIAC6y0q7QECaWyLttRtoX4mIAp/V4f62tBqq5PyaMt0h4CudZa9MQHZv6ECEBvZ6NTNAfuYw0cxaKD6vM64l9c+mVQJQlXAXnyWe7XskcDXm2NesUrcDY/vlh0tTwq+6XqSwAuktfEjq3uB9CSaxzzrNr1cG2MUUA18SkYBBDwTIqSMjX22cgSQKU9LHpcRBZRO8t5pfnF8Lkuj/kIvIQUEZtjHmNkMBBiPsdiCCl4mj1N7aRrzieZ1Hnani7LZkACMTGCl5p14CF5haUtsuwFKrshd/TYi6wCkCMhWJ4WvGMXCPTfJ941cAAwUCJtGjRS+OTSksM0VjmtI7m6NPOLUEqCss/JLfrec7wAIVexTMWlk55LQjrB+xDzRA1W6PV9l+/bQdd1WC4X8N4rwaTX4SNCWGG5DBgGny0ZAQPz+Sx7F+YZsPTcDq1AhakFBA2DnazSF1B9CQWiJKkHTbD5fG7KoO+tN6DMy65GsRAiIhh1so82sxMQIDSQBEsGC32XPDYqqCwBMlmf1EqqtjYkV4PdoaDrDtOWikMLoUJto/6ha4ahM3utxfIYirI0cNKCbkhtV2vzEnBGgLH2SpAiJzIWp/K+UBt0m+h6iqehMdYCtDP16Zrlt0Cgft9EcrsEvzmocYkRq1U6Jvb48V0sFgscO3YPjh+/RzwFYZYTvkSE0GdeF3BC46EBW+rXdI/0nfA58TDJhRC84QHqA1oe1H0NgKPxvRfPQwtUU9upL2qApNaIKVUxAhwkxsQGnAKAKB3J07HiI5bpNQwBYcggNNePeDgFu7rcbvFqeO/RuyRHU6a9jg94kpggMibIyq8t3HREdkREQERkeSiBjOt4KqbjrUkmwnobaHluGGT7uVbSUXmdV8scQEj6JDsTyMAgDwcFnZM3kuYE1LNTrpbAXpjd3ePsgU1gmzwoHsAM3he5LHg1WABM2g0lfUEHsvFOFJb1AlxBL20bngQ61LMJNJqn9/olgRQtK0Qmajof3CgxxZy6fLYAoCNQnWI2HTFMA2bX1QlkdJ0OeApVW8R1lVyLIRBDpclXt0tbr7VCSffWfajvl7oLYCoBVGldag/AJsHesvDs82pvDAEBrZj4PjjA5zbmrH9JOJHQ0hHwqY4ty7fBHoCZaCiur9PujlnT2iugy5PnRvN3jPaZui4p02RA8D5Zew2QS8/WIKAEA62+1nVvAQKgXmIQMFV7A1rek7K/xuqyno8IQCIxJMs06Q9rGKx4PpFSplcCgQQGyPK1CYJIRtDcos/aO6DbRDsLdFtq4JvnKs8vvVzZju2wc1YJdJR8oiuULcBKTjauVaSvY6VRX7Xhs7LgsxJLHoGOXdyj8x3lHI2q+M2yhnqY+6eoYWQvCO3iqj0hIQRZLgi1ASEeHgVslGe5DKglHtRttsHvOilTqqcYn5Eb5tzYqMHUi/O6KK8L8QK/H4pDwBXvm2nfYMBasPK9tcbs4Rr2zHVxxZOCpmNW+562wnWYz3p0ObeACLqAECBlZwYCwwCBVDFGBAQgevjoMkruAQf0vYdzKR4BiJjvzFJswOBVvSXBTIgBcSDrMWRvwgoxdmxFhjDAeyBGcn2Tkg3mnYBR7gGuKwEV6hfqaxLqdISyCCu795YEnE2GAa6HALH0XRqLoaiPBU21YMiZ3nIZxkVIR4rSoUDUd0P2DGT4LMhchFsIpCCoH0RRRzVJtHLUk5jaS32grQ3iO+pPnUkt9dWK9yanF7L1A/Ns6RtRRDGmVlJ9PF8s67P0ksOoZgYUlEKNqLXjplSKfd8jxrQvegxgjy0P1IoL3Jd0XQkgatBlLT/qG9reRTsHjh27B8eOHcexY8dw/Pgx8+z5fIEYQ56TAshpych6d8QTYOvojYUbQ0R0lMvf9hmyNZu2Jjv4zOsxShAwXCNwcFTZRfMqAaM1luhYXt33bROQ+1R6WbVZz1GtyEsgD15y9Z1HP0seQ9nRM8+5RGJTd2ivgGRZDDz0Do49KSUg0G1OAEbmYaB1+OwBpHTyQz6GnfhGB0oOw6CApjVGnHPwfTpfgHK58HHwneRXkfiZBAZWqwEOcqonyf7lcpHjtGi7Yq9AvM87pZLXqQXidF9oPuJzbVBklVQxb1xGkyu2pb3dfSDPgAhe+gyMI9O20KH7zITz4qaitR4a7JjRaJn3u/VcGh62UPJ/Yq2qAcrPCV15LCt4Eqw1jKr21hbk2HWtzy7LCqNoVVAlnUTYFu6l10C3wyrV2hoXRUb3c33SBWYSQgEYMgq1cKDxgs85uSkIh6A0W0PWe0SCueyT8vuyDRoItNpn+9Omqu06rYDEM9ACAqIYlElgJkLtUtaWemm1b2qL4ZQCNGyy9FsKvQRNrfvL+8buKY9l5bKUJTUMg9pWvCqWYEIxTqK49e4ePc91OzQgLpW24dWibeRJLPuKy3d1H5j2VZ68tgxSXSFXqfnTmvuav1rj4J1D5Fws2gtkZYL2lnjvOYW4TfhF2w7FGLDyIBbvynDJQtblimsjUU1wQB3MU5ZNe5MFLGQPgQpA154B6sSojvnWc8w5l1O8iyego6ypnZ17bFCorJIiL3UdBrMUY70DyvCJVpkLUFPjZ7K2irERY+Quky48GByQcdhMB/AMUD1VQAX0JFUTt/hd3HtiLVFE5nw+w858hp2dFCOwk0+QojXeEFKUJh82Yo7zVPUqFGC6N29jIsWQt8HMZj1cPkSiW3WGAdJzhry+H1UbZK8p7XgoBTu9rxO0VGdiGvksfSl7YK3FK5YltRUsZEsBlgQsgSitfPR6pfVUlPURBRnUtRExejZdqP/ZUoAwPlld0OMDO/Gpbd5LEGIaM3EdlwpS90cJbFoH4ZRrhTGmIFLa7eIcWSaJj6k8FjS5z2U7ZORxongESuyUjqem8fCVZ0ALbi3stPdD84L+jb6n9mj+o3t1RHYJGkrlrp/TUkBj92gybcIKEWlnxmKxwO5iF7u7x7FYJIuLyqV4AB20SdaoPmuA2iiHUHnTfupjXi5B5LXnEjSXf8MhHZdMz3E2YLC+fwwItIG/sRtjUOhaOIjW27VC7/vUD13XI0ZwjJKDHHqTrpN18NQfVJbwHMURHTlyBF3X8bHsfZ/SuocMzKXPS4AuXgJHxopHAgGFB4Wbmu8PcIgDlSl771P7qezs5Q2yfVI8AxosJghnl4WSRynNKzvPbMxUkT7YefSICAMQZjM4l3KjCFgNDF5TlsHkwdRn6yRDM6SkTpkvKAxF5o2keiYPt+gvkYFwKTdNBIUc75f2DiL27xlgAdF2jwgRIKDJWltWjNTyuo7vLKLTKXPLtSKMWH300JgZvGVxCVLz8JHiBVKSHDqlz7mUmwCgLVBl1L5G5K5oO/ge6ZvawjAyqQAC9LcOtKM66DVv3UZ9n66D9aDUfRIrVFt/jub+PL5m33aDXOPvDG5Q1aHlQXKgveRlu/S1Zftb5dL9sgaslxOg1rDrTIF27b0MZKUkQLUHQIOB1ho+la+t77IPSgVce01K8Gd5bR0obf3WtoTbVAIDfibVnQK+hva+bWmPHn+7nCUWsrV49RxogZdcAyTPnhfrKxc72rZKTq3rhxYA2OY3AQIlH1DchD6cx6xzc4Kb9ErGiGTzpEbqHC2UcCxdUwRbZoxOMsDOGy076HMto+RV9AB3QVS9IS43cZK0snaWz7a6ROaS3SZZBs/aZdPSc+r4bIIQbCyTtMHKKhlb3SLxIFtvmdZ7GUCF/L0qPyImp/XIXN2eXPG+HR0wZmDc4hUmTms3zhMiqgdmNhevwGxeZr6iKOiUVzuoAygIkdnnkkAFgCjH1kKsR3onoq2BabJ4SAR8BzrkYhh66Amr0bb+vsr3H2slVgYj1X1n+5VAAD1TC1IqQyv0llKkrUbElNoNK9YoGNWmMgqwoid1lC8E2VJe8XwOPQSMkbLVzO+8N9t5SivWWmVJIWiLuVSM7XbbvcqlpSeNol0BJIyHCnwRL5bpR4lfyFKlg5w6r8FarZw1Hxoro2iHJq28y7aW1OofXc7Yfeuo7OtWnxsBGfPW3PyS56ulQNWXBMDVE0GWKNWbhDwptb6v+1gASczHYAPel2mnW8aM7KIphWnqd9sPNYmnaJRy+QxsqBbMe7J8RZfT4WrDMHBkPK25A0DfaQtYUjoTQODdA3kdXedokODnvDc+ivVeKmZWyLl7auVv+1PfF/k9sNcGABBSn9HBU5GfIUtA3ss8ZWDtvNIPNDdnuf96aA8BKXiqW2Ug+HSEsXMOO0d24JzDfGeOiLRTAYjoejFW6fyH5BnRwCUwUEr9n54ZY44TKPW0ARzq8xrb6kTR/sEAaNK2LQqZkMR0IQsAOUFOR3xKLoE6axpPCmKYGBFjCjZJVShdmrJ0wUfYG8YOGZ7BWACSjIcUjoP3gdEitU+73ogZS+8EYK25Evmvs9Ra36Wv/dpnlSBHU/o95SMQgd5SFON1SxdSnRSiV/eHGODgjBJOFndWqtIoOFKASrHzz2jxkRYSDiGo+jc8I2NK0Jabfuv7NPtKV30JvMaD/2wEc3K9pr/X6dtNyrVFmgd0/bYBBK1rW16GVhljn+t6kGwQC8reo9a+G94RVaoqg6w4feKlyIx19RcUOi5d0/LVuAQmIKCbYYFPhZirJ+hnOefsXIDlMfk7shVPBoEFchkM9LN8JLpsmaMyyCNASwxpydUqRW5LVBZwYZWLkm60SIG4Fr9Ya5re2SWQ7bV8XTYghEeQDRPNN2pXDgRQ9hwrQMao3WGijZj8yOwVIW9M6uMQUkbP1WpVeAzaeUC4j0w7N52p0fiOuuUkAwHgwDEDJQrOaCloF57PyD1iPu8xDCmrFVlS3nkc2dlBn3MJzOczyYCVJ3viT2tBhpDQJSX+sR6JpDgIoVF9abBCiHB0uiBEuaT8AzoTXmeeyUjeJy+C9gDQGlMp0HXEu2agTcJb6qz73FXv2noFkPtLchgUpUNnEpPvovlMgKBWJKXChkzi/F25xq37gcpOcjmNHU1+SWBCa3nWUrL9ExGCQ8QAFM3kmsQG+lflWSUEEGhNn+moUmtx6kxt1jNA1gBd5zkdsyxD2L4rPRbrPANUT+0VsuNSt7X13SZ+o2etA4NlHdvPFR4T8JzyIUjWTHrJuSM6m2IS3NYL5pyDzgVPx+rSPWWbUlbD/J1WZtGBl6lGSMCMBEnbZq4HbI2e4z7hggz/jRsCJENTHeZ5Db3PxaT51OWD23zXpR09uSjJNeIx35mniHnfIQIYVhIHYJR+Ob7KpQ9d18qLA8U7MgZSbrBKsdClBCi6ziEdaKb5XXkEaJnOJSvdNUACvxPQAnk6RaaFIv8MgfudnTmcA0477Qi3Zbns2RN15MgO5jv6nBdbrvwNkAFH8QA+iEGXXgDKPrkXgABwoN0EVjHRVy6qzGDZleI7jy7qU5/S5OQDH9TSgAABCRBCdkvTmgztRQWQjhB2InSkPgq3KsRqlZz+3SodEkaAHJNpwcDMtJ0GvLVFLHlBbGpkem9ZbvJ8VN+xsoS1cml/NiVrqQWLIFW957pW9LUyqMFCrjtgDnZqtYO+s0GeLieCUvUwFkPp6SFlSCeWpUW3GB2CV88GeR8sELF9aAEBUdfB8E75+xgYACTiXSxXL1u1IAJR12cMDKyrt35vUYvv1nkE9HX6902eAV12GwwAQGS5pq02Sv1dzqWUzjUpLilf+ojmtjl9c9ah85JQqawXnWha1R1R2B324Bh9lfTHPoFAaSs1nmGuLT4IEKYlKCDGWd750vG1Di7nCcgKUoFYSoSWklORV8EBEQhOAoeFH7VHAOZvp2SEBgItQCAmbgvcgo2wUv4IXzhVfq3sXXEQEl8HHVDoxONjAErk8UzjKwnCnINsNZ/PM2hYqd0XcpATAYHyfJpkrKRHsqc05gDDaBOIaR4wOOBU8gy0EAy5upwDnM95A3JghkOHYUjrXL5LQj0pfVfFCOgB5oldoFcCCcQ89FzAKtJ6oot1TMqe669GgrwLADGnzVymXZMiTK0ApqAlIr2LIv1O9QEsmhyrO7Iyp7Y6yG6OMQuvLqNscy3oaOLEZj/WbliqW6nMau8C1ZtQM5VHzXHkXdKWBxlyJISiy4ehRKStSFDlWIBWj6kdAxG8ZMl36DoJ0NRlJaUjcSIS+6E9AyoaPLc51Y/yULQ8NhYM6D7WPFd6W+hzq8wxBd4CBNt4DMrftvMMZDe1yvZIoGC5FGFOnoH0WwILErcS82e9U6NHp46+7rzdUUN54ZFPT7T9M9YuZbmb/lonS9b0V55C2ZQxj4kuANEwdn58yoeSjJ18be6Dkg+YP/IkoeDVZDE35oCrgWJQ3lazO6gxb8fJyjJteDWBQJ6HHmm3RuR6OR5zR2iB7lABu957IHsFYOZ7tG+RtjIjXVcBXA0ENLChpb20rEJyMC0X0Imrc/Zikwfbli8GLDFPDBHBBQwFL1qd1lSrJ40OJQOh6QYHPjvbe4dILlMAfT68hpQweQDk0Bp7xCVAg6rW+tVzrAVplZRVXm1kX1vFgFYYpXWs1/IozWyaTCI0hPk1IKA1dGvp6jq0BbL0rm4DCalUl+q2qg8EOJTBPuv6pwQTJbNL+WRppfYLaJN3uVOjaOfkOeDxy31EqT2LiYqYFEPIW3nSklTOAtiYRtraLz0Nuk1kTRDGaylnSThCS0S5/cr9KFaNaKfkrbDKjfoAje9Lj5NWBHT9GADYJLy15b8OFIzVpwUEWuXo5/SzntOvksu7V1su+77Hzs4cFPgFAKsVnV+fSIMB733eN97xvnk99C4IT22n1Eo5YNtrx3k9KODflW5SpeVy6MuCZyMAl5S0C/miaMGfxFvRxFf80TgVkLEvPYAMkAC1K0vAQFSGRd1nYw3X/ZVLKXgjSqNBMV2k/AM/k7YTQxmWXoGBbCgyMGYBBFlGtnEH6a3UD/pQNquMAbChl3gyzWfankpLxFpfld4JO4/SM0IMQADCEMQMceB+ZxGterSGp9uMxf7pQGcTlJMi4TQ6OMTlALw0cd3g0McEBKizZrOOLYPydDVZHtAnINpJkf4ukLKyEHQdbV2j+d1a5taCpefVigTmeu4D11Kujd5rCKlxhbC3smqLmCwIaUv53NZ3Jek+TtfXdaCJZQGBKaUqy4AL1ce6XbWQU+MRS6Fnn5du9WasRaG6aryI/0QRaGUssQXywBElQcKQBCPafKH7vgQC9jlU7DhzrVtG0L+PeQla95d8UYKYVt1BAD069LAZDaUfBWSR14WuS9aZlKczv+m94ptMqZZFqMF/rupoey1vrBfP9fXjQLtVVgLV4lWhk0RLMFC3ryGbFBghQ2oYBgYfAtxD9gQAyGoZkeIrKO4qyY9IcqRRc1GwJFPH5Br/lXgkSgcZYBnTbiNaAhHPQKfamp/pYva+KRQ26gFyuY6uGpcSYOv4lWEIHJRIHmzKJUIGQOpTOWeF2+v4EYjKk2lAk6nftnS4gODQzibQbiuXM2PprVjBB0bq2iJIW14o+cXMxhVESZyik8bQ81puMyGrWLWyEvdfKBhiXPGWgGDMqiufKeUpKxjbWiy2PqVgKS1ELeD1NhoA7Dp0SihrF+wmcMLleMeVojWwdPoaZQ6je0vLWkCKnIhm3fZpx4ljmLyxj+RWuMYl5JKk8rQAkJTHdT6BFrjU9SwnoeW9WnGGNUCgXUYbCGwH1up50FLwJQ/rZ64DFNymkfTJVGeX3bsA4HPaabLsV6uV8XJQ4CABg1QH4Wc9z+XYWXJ/JyVql4fqNrb6bbyZpWVX/lYCWv0cvVyp5wP/DFnDbj2ZwKPL1dC7M1wOrhNAar02uYQYs6ylz4FP1tRgUKzoNH8FwNvEV+n5Pht6yiOh+kNknT2KmcrkPnWAy+e6AOkwM6/6R98nwJDAgOSckXaQrNFAoO5Vo5EVeKc6k64hIiOVYi2oPsSLOkBeE8fLKUDknCxn6NgM+Zz7ZwOQF9qAgPdJBwIDTYsAwrgykR0AzzsDrEVAW4T0cbZSPnkEdJR6CQjqSVkrkVLQbXKXjnkC0gVI+QuASii26jfWb2MCvayrqTOA2GD6MYXJ7UgfKjCT7lUNgx1Xce8pIJbLIiWcutzx8lBLAQkQkPU/yuDnXNqr771XuF76tDyDoN1QuY8VAwtl9SFP/Nrb0FpqsoBA36/7SVsjzqVzGEgYWOs5cnml4h5XrBZEpv6gNth6aovbWkmNUnP7121HHb+3/X0JmGXMIlL+/4CQBSN5dHjcYttTUZYP1bSImOICMiAgtDwG1MvxinJLo43j80nXtQZQsVmuAfUo7ymfDWrgSB/ovtVKzsoWvYOlrK/cgaygHWRJltqhK9AILAVF/RCPlu1QIED3AfGnk76g6yMBi8iXqP60O6FoHpceqrrT5DbHCMAZ/avBgBieZLh0BgwIIChkUm5Pkokiy7i5TkAuAYDSG7I9IDh82jcYWDdZiCyyEwFJnTqbtVNGEiOn5EKSTlbKaqUH3Ww56d/S5LB74bWgp7L14AM5TSndo908OVmGtmL21n+y1iyBab66h57T2guv663rr8vSYEDuDXnPfluwiNRO71yuujzAwaXQ/hxJawWRWHb21D5B6Wk7pKPn0pgrIWbqroSVgKNWPcHPKIUHfZf6R0/ytreJ7tdCQ/cXgQEHNelj5LPoAQuE9VitE2o6kQkJ6xYAFa9ae6nFClIB1+t4aC9kBWVpwWW3b5CxpFgT+j2EoNzYZX10hyAfPlY+X+/gaMU1lOANaFlY68B6GwCYWsBxvgKruHQfoQCe5lnRyiFpX2qjlTG6v+iER0kmVvIJUCY9054M4vHS65MCqPW8Zm7ittbAuOgWUwfpg2IOQJ8KSMVQu3RdrTdTvBGx+TwOONTzWungGNMpiVGNBdXNuXaGS40r0iPFYyPjp8ybqP8uz+PIZWwFCKJ6HR4d0jIBoWEZ6LJDE/+lFJue92C3BS8Ju1opWRpH1usFW/u+9c9iK1VvbI8CCDSSX2fFrgMprTpqiyMv2ikwQ2t7Uv98t5FxFhzQd+k6UhKyc6JtKTH6H3FpOefYW1K2v/XZtg3MPwbQIU2OmCvVVFwxWzUVIChqr5SOfR//vRTWbUtEd1apxCMxc9X2sl+stdkGp1TP2uKy4K9cIqrbUs7RNq+uAwQtS7MEIgwY85Dw797xyW0BNg+HFqx1+QL8Sisf2bKVy9d5Wrajkh+3u4fqIvUikKjHxKExfwrep39a48CKCMIrQm1FYXifilSHtpEc0GNL/NieVxoItICNvLdhk/xL5rPjvsrtcslbgKpMWeKtFKouFyXwMI8zfRf5ocV9xmuojAzdKMW36V4rR43MHgH+ju5fCzY17R2wr6NDixkoSTovZdECJJKcIrHLLIMiGOVEqFgMjl63IbKW3vrJuxeLR4SuA3ncXHYv1VZpbvOa52iGkJvUvV6EZbMMxchRz8MGChcGlvKbpK6h3XB0pKhck5S/PDLCNYSNg+QCAOqEMXr8xhWQnve5d4MkjzH3jQw117OyhmkCEo9JISFsPieA7tXnGJSVIB4QAdoGpy2AZPlDhF8JVOhvGVsBATI3LCDX4Ef38V4VXiqf+tUX3xe533kygPFjiiXyiLMu96e1dIn3EZF2i/iagcmLUHquZPygrqXf220Z+17z7l4BgSopl+GZF0UujlP5NOuNKOey7oMWQJUyhNdzHWgTBs//FPTdBvOt52rAFuXrPZB+fqlAnYspnhIBlLWiBLANvG10ZIsnbAWikqFWltMuFc87hzSSaBTVkGtsRBrPhX6VhagKjFKJRA6HDpSB0FJtxeh9+ho90oRoddw6d2l5bYWmRwBBG1lb4VILYxT9LczP7eXvTcn5vgaSZi0e5bLGc7geFaJWDGSYuHiGqTGVKZONlEPZElOMmpmqp/LEs4qmno2qDPV3GyCV7auFgmnJmrHVuoGWbHS9y+cUNameUQZnWu/AWDk11e2B7b+mm3U9sF2npGiM9Wf73ppjsbhPL89UT6+eUY9t5s9GPUn50/M1gGDhDSRHmBLmelmB2iFl1jzO9xXtKutSkla+pXeiRfZ6UzqEp60A5zKFRduM0niWvFteqT1GZV3k2QzWVH/r38bAgAUFGhDYZ0gFdHu0TFT30imqBZiJergLo7D07LVI1ynxgOqvutjcJyRzU/8YUEv1V80wMUotkb95SOlpzCPOfHvyaF9ggHioDQjGlIJ2RacbdfASpSktLSM56MFOAr3tiJ5t3/WzW/Wxn60AyUyclbpl/tYzy+9IkdfMp4GA4Z2sVLMaqoQt3RqhBYECLaYweVqI5HZ20qRGnSLVyzn4siuLiqQDRTLTak2jukNSdDpTREvRSjFtQVYKn5roGVbpl7xYKgVZ25UyyOp0zjXquF4AOV2US9OaBEt0ZV1Cs66bPFy2X+TkRTtHKDU2QHOyLFcfTpVrULWtBPHQLlJ1j7qDGgPAcZZQAp4hZh7zHn0LKKqyY+anEqiO9Q8J+/Rezm+pn27vtp6BsWvKerUFv1vzqU0MxBnIaMA7FlcCaNBbKvTSwwrAJCcCdCS8gDVbr3a5pfyUltY8bOdQ/l0fmWw8Yfy1LgGlx0+e1yJKMEfP1XVXuikvXdXyMa/vqzqzus79ERFTEjSW7QJ4KllNtXWSU8F4fkCAQIPHep5ZkFL3xF484EQHXCaI8q8WbE2FbAdQI29K2Uifpew6aleAwJgiLp/bphLJW8VAf2sOL9ey1k1s1S/Vg1nvW/St71uDJGUSkbCS+kjb7NPHyiuhiu3rZqH8fSxnrBE2tlVjgGCsRi0ro1ZAzvAECXqOHsjVX69UWyPYDt4q610qUSk3W1Gm0kmA6LFrPWPcs1XWXX+xvm9L5VG/t9rhjJCyHoJyjrQpAQAWp+k5UY2vFn5OBeaWTYuqPFX31riKQtIgtBT+dd3bin2zMK3vM7/mt/X9xO3jf1Kj2wp23dzRv5VjNQ5wWop9HbWA+ZhHVfCvU0q+xfvCJ5UM5D5p30Plu7X93JarGjhR25xLijh5oeolOgOq2OjL7///9s4m15abiOP2DQKJgJQQUAYIsQCQEItBYicMWBQDFpIZSGwAISYMECQvvJhBt+2yXeWvtts+t/+/p/vOOd3ustvtj3K57NY6UWBDa03aViSKQFLXpfsv06oQDPQZMMn/B5r9SkMY+3pd2rCdGlZoLfCZm1YKzpzZSNCJ2ZqY6ZkncMR2jqjswJTPwiaJhqsQSacdNj7R4gL/1cSjShWs8w/a82C47IlfaMTFnyJVeOE5CcGlOOKRutTh1z6FUNWju1R6ma3z0VxDLs1TctMBkmUmlBmb3LnykeaBH82fqy2Mbd79aC4eXR5e3r4htOXUmo7tC8moUlCLPF98xQ+gn9q661RxrZIOzFJ6fnbZrj9WEW9UtsI84pSvtumUI1x4/fFJdz9s78RKiqkPo1w5jNE6WnFltPp4Wtjsts2HfuBv4E2/eZ+WUza3Aozvr6L4nQ3x7PyyloE5DFtNwH8nv8VnRbQoZbdtVe4aTQp03oTKjwZKpI1bnF4qa9xDkZNogg/3XSdfq2WJfZoTRiq1O6fZLDBS+pKDWug4eFN+j1mL4rXyKB1s3Q87CT9C8N9lReA4Jqe3tWHqg1MEQssF738Th4llcpah2oZeKVo+TkWAWAjivLGmztBCdo7KaBIqs4lLIrUe0edbYzUcQbJqQLiZsIbocDwS0aMQxWWSszjQ50/LQRxdMGBgyobL8UzT6cpjNFhJZE4Zj9GyrQKrQuzLEaxeiZQB4/y2lLs2fTayc3v2OWrxxzSmrSZIaBlc3XPvYBD846LztzUd5B6k5lnNKAY1nX3am40cjaaNdnhOuqZ0PpaZmIAJNIxSZLRKLAK5dMTxsmGSjohx9FX+eXnLQ8nKtIb7a8KVGI3yDyBU9FOu3BUxzXOihigEoXXOHXUDgtQXw65YcTurkvNp+Yt3nA2RpunceeWnEleV2zHKQN6aJFwTNqzJvOgpK/QKtyd5TXQ+88023jklpz3Vxq/J/5zMWG6NaC7vTXQmHn3ailhjuRFUCzJ65zEVBUEoa8TqokyYPl85WxvVxmFtkJ7KUXimo5YczWplSudoGjkrQ/JJFSjNpzl3D6eQIJykBIXw1p9daUle6CsjyHMdEx82X5+8EpCzwnLTL0FcFTflfMXOztC1Ua4ZJFqe2CRy2kOcXqX4F03J9+rqD/m0984rAryCSuXl8KvH1mgD/cqAUxjH17KSz80Uy9FLsUcO7DPwCvOCjjKCjr6G6ILQJMoF8UONpPK7yr1/Z9QLNwWRKgI2sBIVgpx8Sk4R8NYBaeT6Th9ChraRZp0Zm5OZmXRRys/fud/h89c+HPucclMG11qhUPH32x0nU2xOEVAkncqF9f41crpiJ84Vfis5rlkGAs09Gh0WlMNktBo3vmTrSdqS3mN0Ntl2I135MAP+Dn1HRxawVKejnGule4uMuZVSz9BOpK8Qcpgo3mSEJ6WPePb6g1VpzIWhW6EGMl0LkCofOl+Mhs5N99Aafy58URFgwrc0iJI3fBpOSmuho6nien33a9JtPvFR1Kku5Xw4osqNVtME1FuUShbatLWmry0/luXRa5nWnXauYsPPWwLi80XfKhN+D61rsUy6jNM3A9LUKGc9OO4/l8/zezrK9WkCkoHhsYPirYj3m56QdMc7m1TaFx0dz/3pCObXTPRbUTWhjVQmjTOtDLkUjqZ6hCN0/PXNOF8gtfQjGAnspelLlDph6u1Pv1+RycE1jj3o6OHwYkaUyZHPmJE1vPhwArMqb39U1dlbysOrCtt1vFJw/MV+Bi5Vmiqf9s/XGx8uWkJI60okV3e33knqmq94KwcRIMqSjo/3yKoK582umvzdyTEaDH+vSYeO0uH/jZNJ7+2Ou8zLbzKA6PRQPVmzUCFi+23tqL8Eb2bXrCIQKwTpctD+e+VWQLSl2X1ToSKgFV87W8oxJ2Pkc6XD94FSg3yKR7XcKJcmYJ5CW18nWG37BrwC4K0BUn55Uh8kvm5xv33dWtWTeC5YBqxm02YqzoqLrSvRXGxxymEyoUmLpkcHx+enw+a5Dn771LSnJJUZEhr03CLQJFRrrPI1/PGc4xOdYqIifMpJeuWHyR+P49NMwEBGW6c2EnGKJ5gflebd05ENd32J2LKUKxolZ8KcInIcSyOQnNv8lrStHVPcQHXaBE30JdlJlUi15bdhKqUcMV/gqYk8vUaQGDkpmuSezryShtWKbrdeqnCKe6idlOSk6Y2tA94SIMQQnYjrG7dix6dsjXWg3zKgVMNQrVZe9Ak2xW+RcZ1FD5sbMI6QuZiWjoP7s+doGHqshZ7saJ1q8KOqGXAFZIaFYAdC60H74xaemzucy0sVnZP+RpCX48tfqKAenyr4bIo1Y00Ll8K+mGXgf28f1V8++6v6qD8qOyIa0qRrZsCl4gN3dB4ljdh+W90gyFvnXpXbEoYfVZRJPW9L+Tkyvwvzlq23o1WoHC/0FHbNWXd6fEXU+lT8Qo+0JC5llKKOVrFhujrm4BFr8RiT1CJjHknvnXni9FalqxSoyq+jNgoTPNicBdRuHEWOhHYT0jaEm0/F32aT0/x5K4l3ivVhqC1Tnz24tt+DYzqqMumdsopvxlJE+U5/p/7+w39Uha2lSxn45u0b9adf/ll9Yj4Zmpi9KBXT1YqABWYUUEGjLnBLPCPjfQIF/fW2uGbFuROVA1x58DqfD2/fDpXX5zOglfrwyYehCQEAAADAGq75DAAAAADg5bnv3QQAAAAAmMKPPnyqfvLN5+pn//2p0qZ9nA9lAAAAAHhxfvvP36jf/+136sfffqp+8PH7zddXKwNffP1FRag7nNna14gCcAuznec2Ldrp+gELswA1XqIfyIivzMAF2DR/XpvO/RQuxbOWaympW3Q9Y8+cL//zpfrFv3+uvmf6xvjVV/3xqz9UhJr8QHXFdgzsm/nGs89LJmYtL2xIQbyRSjPcErKRcMvBWjefIRKkpaXBUr6MAK4cF9cYp+7N4otkSHgTp4tJS9KAVSVDlu9fEnWsD9dv586Yxxos5d4Cl7mPeDljuk47TWjpFc2SrHGccsX+02VYv+wBSxurMSa9lSDv0u95HU1Ku/FlagPi4lNeOu2XRtfuy0HbzLgl0sV+jj/3+defXVrhV60M/Ppfv8qc9euL70DO6DMNk5d57KIIzEhHj8zryoBScxWCWAHgx6Ft9x4rAoLonISGbX2516vmwtV2jL3pSOS7DxIf2czo7e0t7CuNKaYx3ZilXDik9HEbKnWTzdJC2zSkznYUttYYqraFrt0fJJAcfLryctMgTsJkHk+aF5Id61R+Nf30bVqoAFtJoSxt/9yOhFLejm8osZoAPIhZWqIhf88g6VTPVkzbDVeUVm/Rhizu2kjGaNw4M2jEqZIyJdqb2aGsjZgX61Eo7iVVRHX01yPTS9oFOBCCy6Q7Ce7OlQl6zlxLT1tT8Nq84F6Zao/nrpF+xyNsKju4zqjg1rXQ6vW8rTAXPrGcjOwsE1GvUs77SV92NEwykamFz3W0tGXpOzRKssP6ZN93sEtpGqgMrH+QAMynYu5Qq9MOuI9CwJ0rXcvJKsqmzcDpIzD7xUwJrKmXNNZLH80rtpMzM2s/hcBPWbbet7cUxO8x8Eq0P84p6zT+uxlsGeAeZOnGJjz4GpEj8nt1mbXMSMcVH6dS5l6ZN82KLsntibfymvMtbMEbExujG+EDQkfu9HcubI8sSck4AgZCzkOaHa3zjWEpnbHLFTkjvCc+DtuV1UN8Y4606AEy1jdAVxrRXS0EvG9JtryLMlQg4/AjCOuTk7uB9WnCNEGPNrW6UL8qepBD0kiZuxi9lHpyuZo9Gude0ZoGIuenaazxfXIdCpmXlqwCL1lU7ryRu+t13C9I8c+439SPQZoyC8s9tQzwn8rY6/z90f8PKWva0E0cCEfd/E4d0Q3MmJu+JHOX/O9z5rvbmv0q1CgW7jXIys0OLCwNtDH3lgnRCf8lFQGJGbm+6klqRZ+hHGZW3MKZzJJXeixduaLPRoauOHhTpNYsUwSUmuZASDOLDxEOJEaahu6YluDMOiuQzaUrZGrmGyfbdKW3NsOvPZi4vNYZSdKRhDUJpsfHcPc8fHndtJ8MDQzAtqoIS6l6oM8kzeNz1MZaBsj35fU3l5+lxJVGySOtrVzZviKHSxdnAeDC5aaLRj/Q0ITETZnlp+JSRYAWVWM4GWutAxMsA5zZjvvjrsHQDLwavCLwVHKjnDlTBfnUPPdZjLjvndrlO9MgmZBaqcm/fdqNQZYBWTMqX7NcRQfDqH3mlpZnP0P7vxpvqdyvq9xVmw3ZsCMjFh4xv/a/IKphk6FgOiBOx2OamJE33a4I5LzitY6tNL3WDy7MmAdsdxqMl+VyTq78hlk+PeFqm9PKwCY3lv/upgkAeHViM/P6ytoNb1dnwtmPgnd/jSxG8LKdO1fpkeAl4RQBzoHw+LTbwftCFi8bDBSCjRmuDNS2D31zs+A90DaXHvvaFqWTUUgkqSgkHllpZkQTfyfBNyDuyJMOmDRs9ExLLvsVdlTJOEblxqQeIYZLB8MIP4ikbFkFZ/F2t6+M306XHq3Jz5IvwL7PxO5LUbIMkCPKKwSB14wsw33ukQ+brCYAoMSK+Wb6GR+33/edl5YamVnO9PHrZrzSMCiCQuzgLnryWip1ox0dx+HfVyApArm0W6tBaEVwx4PPPWiwDNQmerIvgJ4ouxqzh+mxOasrArZOOwZ1cFyG8Dp05bWm3uIgX8dp+pSckiDHkeeKh3kc2niRzACNlVZRpkveE0YZZbfUOaZg76wkhqxUIfdPfq6vtAd7pCKHcSsy+LE8YzFzW3FHaHU+l9l3PSoOuR7KFi67esCFdMfo1sOhLDkFK8rHBJ8B2ojWwLVWwrV7DsDW0zZ1O17uVGZVi/k316o+j5DJ1p6WKUtOH6qIPDSOtk7tjIS70f273h0xYqHJmPqXtxn98/PHlGDLdV4BCLa7dhDVyU47pKca45xHtTLQNp3XUvm80wWJLS93j7xbno7q6N286UCZs+kaUfam/kpnEcbZY/ZzY6ycb1+lWBeM7Hh2/CaBxHgy17jfckJEa4MiXv63wKRkm4L9aoR+IUPkDCc2/7SvhEgVAf5a/iVZYd0Ng4QKASPxCLPBjmdYTQAACx1hxLV4fcUFYH9aFID3XqeE+ZPidOR9QBl4ApPdOF4fKYNafAY2pSeZ41wXADgpeZuM4Iqs/mvLg3rp3mNFgHHuuRGsJgAAAABup9Vbey6wDACQ5UUsAABsQ80yQsnJ8z3WtxYPb8n/YT6wDAAAAJiEtCZ/9D4D74V1+QFlAAAAwAKgEPCsyQ8oAwAAABbyHqcGXg8oAwAAAMDDgQMhAACAgXBLdUujf1gHPGvyApYBAAAAYAvWKUWwDAAAAABbgU2HAAAAAHAzsAx0wL2sYgXy6zQBAAC0skfbXvVWseHAMgAAAABsAXwGAAAAAKCUgs8AAAAA8FiwHTEAAAAAFgFlAAAAAHg4UAYAAACAhwNlAAAAAHg4UAYAAACAhwNlAAAAAHg4UAYAAACAhwNlAAAAAHg4UAYAAACAhwNlAAAAAHg4UAYAAACAhwNlAAAAAHg4UAYAAACAhwNlAAAAAHg4UAYAAACAhwNlAAAAAHg42hhjVicCAAAAAOuAZQAAAAB4OFAGAAAAgIcDZQAAAAB4OFAGAAAAgIcDZQAAAAB4OFAGAAAAgIcDZQAAAAB4OFAGAAAAgIcDZQAAAAB4OP8HKfwK6SnRyY4AAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["boxed = resized.copy()\n", "for bbox, text, confidence in results:\n", "    pts = np.array(bbox).astype(int)\n", "    cv2.polylines(boxed, [pts], isClosed=True, color=(0, 255, 0), thickness=2)\n", "    cv2.putText(boxed, text, (pts[0][0], pts[0][1] - 10),\n", "                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)\n", "\n", "plt.imshow(cv2.cvtColor(boxed, cv2.COLOR_BGR2RGB))\n", "plt.title(\"Detected Words\")\n", "plt.axis('off')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "0a76494f-8681-4aaf-8fde-6b1ae87460c9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a330fe6f-bf04-4f29-9b4c-ba2eae5f52c3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "58aba926-007e-4b3d-9da4-12899acc82bc", "metadata": {}, "outputs": [], "source": [" "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}
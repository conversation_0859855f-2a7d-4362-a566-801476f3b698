{"cells": [{"cell_type": "code", "execution_count": 2, "id": "9c1890ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pytesseract in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (0.3.13)\n", "Requirement already satisfied: opencv-python in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (4.11.0.86)\n", "Requirement already satisfied: packaging>=21.3 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from pytesseract) (24.0)\n", "Requirement already satisfied: Pillow>=8.0.0 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from pytesseract) (10.3.0)\n", "Requirement already satisfied: numpy>=1.21.2 in /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages (from opencv-python) (1.26.4)\n"]}], "source": ["!pip install pytesseract opencv-python"]}, {"cell_type": "code", "execution_count": 3, "id": "285688e6", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt "]}, {"cell_type": "code", "execution_count": 4, "id": "9a70f447", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x15a0705d0>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*****************************************/ZbjIgQ039k+rRbsXL608xMryOPvCgOvMzhAiAtSt/u/5M76LP2oEsOlSO9DOrKpAJoANpU1gTa0HMHmDGKQUa83L7dW0Hotq1E4EIH7hlMCdg8zL77c8nayl0S/Aq7zecBfGFdN5uVQkUwbO+G5LXK4BqPQ07nyB8Vw6xrbSSJuBMfYizHr7r7njumHbt4Oufg5/jfc/ZF16eohzFRFVXFQvsfNcNt6WAYbZDmeWnkUdt2QV6fi1o4vGJJVDf7MexDRkuGNiP6Sx2dkhSdthtzy0HMo3VZ4xgJo2dNPNuf/EZRJftJpeP36+J3eZZZ2UOYijfVJHOXeSpK1i8DNsYEdixlx8CmJcnEkX7zaQVey0BDzs3MVDGwQ+38Zzx4u3F2bnjbZvm6TyIJDiRMm+BCQAHfJBQFjpy9QSiNUiN6H5CrLTSohW+gkAjDS0jAPgxFPq4xqedHxBA3qCR6ShsVcVZCipbu85AeU4iURx7zVmZuqq0uDb4NVQOu6snr3um2fk4KChTSrvMB6HMB5VvkDmAKFC+Ob+nFiSoBQmmVgp07zXtwfOYkMEkoySFLqjJfALgRtxfXUs20H0g026S9xkJ3oZkOLnQ6p4HMZOvn6+AaSJxDarrriODy/tMQ0Wo57t8OJ1NdfMnRP5s7p7y40GI/ucTEo//4eXjbhauW35NO1bDGL8Y++t9zuo5EhmEApp9FWflXwsSp6w8z4dfX+4u40KggQchc9cLTQbtwOsL5fbgGaGpjahdud4LFqr1Xb7uiOWqZxXbDlRPAhAcOHBGo50UzIN2XCX8NF4frJq/CwBA7ybxd71whoPXtx3Yp/sp7sYAqta4GLqfj0GaIMnG0OKHyufbmBdaCPDcHQAcoDKZVjHyrZUYvSDD1RseRS1o2vqT3jrDBjpkE52Px0h7Y5zac8Qy2FE+iUde/Ai9jx/oR7+VdlNA0JzCy2p0VyRuU56tJdvMGQYe2Jo4C1rl/s7BSi0wu7T8YF3uciJ2ud1CiBZhVO5ES8bg5wyXlg4B0E6cnCsnCKDMW2BCoITHkViq+ziVJ8NHxo2yE0qh0BE27YSptEAU5AifxSn4k47fkJZ58RLJpNbIGtTajsIncMCNjwU1eXCTe8CedL63AbHM+NLfFN3uSywTi+BDkVmUzsGQeV8x4RDDlDLmgOuBr0RoNVzPIswtS9D9/Bx2zS62ZeIr28Jv6rI//nZJDhDJvfJCmWv5BE65APxgU349n+xfiLUhSXRggUWhAzdOgq8c6dnUHlRPAoTtVqO87fwVbbvy0WTD3Q8E+viqmj+Hl9PXDbky2oHC0WYHnh5bjMm0OJE50QHiMY1mT4DePIPt8YCNv+3Wj21olybfZ0NoEnRXsaLlby4chHAd8/9JV3zs86BZn9nw2RiqTy1oev2wAFTUFu3eT/8TyPSDW/nn1J7cxUTPMKxV1qKLdn2c3mGAd+EG4+/lwIp0UwCaIvaG91FeL27bIpG/S7ustRuf4iaA5EGe0pnc3cm/M2zg37aeDS2B15/3pKNX+67cVk9nMQYeauDIuVX0RzPuThbmyqa09xFjIABY1w2db0NzS6ql3S3DE6JRW/mLSR7/0QJuvDqTbujvNGcn6Lw0Ah+++4S7mWjBXOik3c7QIt+WZVZkcczDS4kdaRcH9NPIvAUm7UQKbQNffeGrVRoIxeCXmE5i27kzJZFkeRZFWZyxw1d03GDSoLXuibxT0MoAcF0l7ShUf7JMVYDxpIZES0ynFTupmtiJ1AE/NFi4EL1InbPtHng2+AzQULnP3g2ypefRQKIVjusqIj86B2HFSuaUNYcAAI8cWmbe4Q1SWpG1m0jaMVPklqDv3boX7htOO/oJ7rhh5ivFdhMZX83wgFBiIWxCLW+ypYm6JeCaXUPl8neTUX/lO0H8snMdUnl8sEITCJ/w/dw/phzKcTX4LBOPJSGwsmXHGsw2KugKzNbSWKY40uxGz/MpZpZKZzcc7WLyAT53cZEOakFiY1t84dv+6Xt/4qZ6EBD1Fxb+c30w47twuPiB2tRnOAD1AS8H4dQWNLYIAPmgxmewODtEdaZ2IGDP40588EX18ccaB9AugCjYQj7WuKuIbzqgHxp3PM6DgAe9g7PTsUzRHc6hQzZbAoVtXB1jQSKh8oVN4Z7mNi2URh+7phdh8B4JddmEAQai0I0F3SJDoiXuOXAK4r3jmDy3aYKwRcGQZJpnVHX7TZTvWKEgXA66eIbWSBaMELleCBglOXhJFUvG2WYeozo2PaaOAxiK+aCFJb2nqUI0VXjcRZyNGeLziqAEeu3v4TthXwwTwmMLX67M2zwm5KahaGD6zU9tdK6HycJIWQhpK5jSJvteKAM0M+kYETK2dkWgvZ0caDUe5rcstr56DZZoCand0zIJ8HC3S6Ilnh4bwMztA+h9LkOjV2LyyhkM9k3h8oFd9uTXYmIvWJSK1AAU6mmMVAVYFE87ZaA8JYkObHId+syevSAKes8YCbalmZWTG/FcC3bC4AZxfe8hPDK4BPVHOtC9es7mCqGJjgBPqqMCfNGKS7sThEPZtmTmLEAIn6S5MedBirx/cKaCMzGkB37kQEGjm8RUlIyLTmnlZfJXaZHITE4Qll+F3kFuCoDFHmg3KJODmgBGZ4ACdTWaNAhQ03NSFSBg7Ad9R4mnYpHaz/gqnk/s1O9pVTh4R4hjZ/UgXpMaQK8D/Gj3qRg+OIv6DQGqMnEmAr6ajoRJ/pfBZPb0+xIPZOXf0b3czcDzOhCTURh/l0GgZ1n3F+u+mS7cqTR2ub4BOEcN8Of5toAzDYkuEuz5bBWdycNdbzyw1pbNA2PE/FJ5qN2gja3jug6gEEkX/BVlNanblXAZxmJ8FowE1xuvI2+fIti4sAG0Ui7q5e7gqGcRuoKGXTiSPfRtuZ+jSgpVZH7VRQp6KTUeeuhU9CyUWNI7aQ/EDKCcYz6k0OiL6qhv7YfqncIbztiBelYBbTyg3S7UFxpscqesr2Y+MWWgPCfkjraAm8UxSmi7E9TP0Oow5W2AJemD50RRGbc1RYxLwaQbRryeRWiq0MYQ8sUs76tFvyhcbi0gSZtEfEYPxdzwQknaTqTMO2Ci85M5k5lmDkYSNLQE4FJKHKAomMPIUqXRVMIS6qYBUzR1gCQDkkxBqRQqMVdMTxnw08gklE6hwN0pAnn6IVC3pFXBXKoR2K1aefbYvIG1lki9xGr0vYKyJ7Te+fxqLPh2BxYdGEPSGSJsSgz/+RQm1/Xi8B+EqMo5NPNVQ6KVTVw2lVbx4OhSjD29ENVDElkMpGtncdVpj0NBoiabqOer5VkVQMHcJ2WCmayCpgrRHbo+3lQBFZlAQdqJP9F0AFSu75xFAYCU+dIBhRQKXeEMDp+aoP+BORx9QwCl5pAIXUTao4FmbrQaUEjYROJMArmujc5SKPg0vCk7aV/CrLyhBZo6gLkjxZw2aaqtS8WP7bFvAXt2IaHIbH+Yywo61YDCxDF8qX+arExQV8K+k1Z/FG9U996laKVlgYGG0rm+pYJSGVItoGxgt5vorXDvpYDIkEkFIMsTSCk0lEYkNFSQGiYRCQJpNFUAO5p8msiERgZgrNmB2s5xdKOG6PXTiFWIg40+xN+MMLpa47WD2zAxCUylGhWZOCdXN1WITGZoKgGJBB3hHBr5icsA0PDqYOot7f11FUIixayKIEVi6fJQZKAIinbAkKShJKQoDl/jK0slU9sm1aBNNlVI2698Vw6JBVU5AGwqQLFdYkbP+cm+rI+lOkBFNvP6wp4Um6Lo+yp/RkMLSJGiDhZvRWwSgAxmnPJ3SaExl0WoBgnm0tiZoOyKP7dlRtdNe78C7BhNGehpMrtIgFcxhsowBwXb4LZHYsYwFCaZbhUkmspMsnEe6JoyW0llbaoAUiT2M3q+COvofbCBxkKBteHzmJkKIPO+obS7c+eJmX4s/dYR7L20GxdkhzDdjDGXgxOlDdPSUAEiuyACmsz+NbVAE61JyqgNi/+ldbcAQIjifDQCQU0dosJc5ub/xFngujs3NYJcN7QhJMjf1dQSQR5orgEIlRodygzCY49pzCjdzNsDjo5nsxRB0IQShd0JoJCiNQC2HdND6TvSWTNr6jYnbL8UEfrlPuEEy7PPPos1a9ac7GKUUkoppZRSSik/hezduxfLly//qe+fd4zJwoULAQB79uxBb2/vSS7Nz6ZMTk5ixYoV2Lt3L3p6ek52cX4mpdThy5dShy9fSh2+fCl1+PLlxepQa42pqSksXbr0Zb1v3gETKQ391tvbW3ailyk9PT2lDl+mlDp8+VLq8OVLqcOXL6UOX768GB2eCELhZ2pXTimllFJKKaWU8vMtJTAppZRSSimllFLmjcw7YBLHMT71qU8hjuOTXZSfWSl1+PKl1OHLl1KHL19KHb58KXX48uXV1uG825VTSimllFJKKaX84sq8Y0xKKaWUUkoppZRfXCmBSSmllFJKKaWUMm+kBCallFJKKaWUUsq8kRKYlFJKKaWUUkop80bmHTC5+eabsWrVKlSrVWzcuBH333//yS7SvJCbbroJF110Ebq7uzEwMIBf+ZVfwVNPPeVcMzc3hw9+8IPo7+9HV1cXrrnmGhw6dMi5Zs+ePbj66qvR0dGBgYEBfOxjH0OapvhFlM9+9rMQQuAjH/mI/azU4U+W/fv349d//dfR39+PWq2Gs88+Gw8++KD9XmuNP/uzP8OSJUtQq9WwefNmPPPMM84zRkdHcf3116Onpwd9fX14//vfj+npaf9VP5eSZRn+9E//FKtXr0atVsOaNWvwn/7Tf3LOFyl16Mpdd92Ft771rVi6dCmEEPiXf/kX5/sTpa9t27bhNa95DarVKlasWIH/8l/+yytdtVdNXkiHSZLg4x//OM4++2x0dnZi6dKl+I3f+A0cOHDAecarpkM9j+SWW27RlUpF/8M//IN+/PHH9W//9m/rvr4+fejQoZNdtJMuV155pf7CF76gt2/frh955BH9lre8RQ8PD+vp6Wl7ze/+7u/qFStW6Ntuu00/+OCD+pJLLtGXXnqp/T5NU33WWWfpzZs364cfflh/97vf1YsWLdKf/OQnT0aVTqrcf//9etWqVfqcc87RH/7wh+3npQ5fWEZHR/XKlSv1b/7mb+r77rtPP/vss/oHP/iB3rlzp73ms5/9rO7t7dX/8i//oh999FH9tre9Ta9evVrX63V7zZvf/GZ97rnn6nvvvVfffffd+tRTT9XXXXfdyajSqy6f+cxndH9/v/72t7+tn3vuOf21r31Nd3V16f/xP/6HvabUoSvf/e539Z/8yZ/or3/96xqA/sY3vuF8fyL0NTExoQcHB/X111+vt2/frr/85S/rWq2m//Zv//bVquYrKi+kw/Hxcb1582b9la98Re/YsUNv2bJFX3zxxXrDhg3OM14tHc4rYHLxxRfrD37wg/b/LMv00qVL9U033XQSSzU/5fDhwxqAvvPOO7XWpmNFUaS/9rWv2WuefPJJDUBv2bJFa206ppRSHzx40F7z+c9/Xvf09OhGo/HqVuAkytTUlD7ttNP0rbfeql/3utdZYFLq8CfLxz/+cX355Zcf93ullB4aGtL/9b/+V/vZ+Pi4juNYf/nLX9Zaa/3EE09oAPqBBx6w13zve9/TQgi9f//+V67w80Suvvpq/Vu/9VvOZ+985zv19ddfr7UudfiTxJ9UT5S+Pve5z+kFCxY44/jjH/+4Xrt27Stco1df2oE7X+6//34NQO/evVtr/erqcN64cprNJrZu3YrNmzfbz6SU2Lx5M7Zs2XISSzY/ZWJiAkBx6OHWrVuRJImjv3Xr1mF4eNjqb8uWLTj77LMxODhor7nyyisxOTmJxx9//FUs/cmVD37wg7j66qsdXQGlDl+MfPOb38SFF16Id73rXRgYGMD555+Pv//7v7ffP/fcczh48KCjw97eXmzcuNHRYV9fHy688EJ7zebNmyGlxH333ffqVeYkyaWXXorbbrsNTz/9NADg0UcfxT333IOrrroKQKnDlyonSl9btmzBa1/7WlQqFXvNlVdeiaeeegpjY2OvUm3mj0xMTEAIgb6+PgCvrg7nzSF+R48eRZZljsEHgMHBQezYseMklWp+ilIKH/nIR3DZZZfhrLPOAgAcPHgQlUrFdiKSwcFBHDx40F7TTr/03S+C3HLLLXjooYfwwAMPtHxX6vAny7PPPovPf/7zuPHGG/Ef/+N/xAMPPIA/+IM/QKVSwQ033GB10E5HXIcDAwPO92EYYuHChb8QOvzEJz6ByclJrFu3DkEQIMsyfOYzn8H1118PAKUOX6KcKH0dPHgQq1evbnkGfbdgwYJXpPzzUebm5vDxj38c1113nT2079XU4bwBJqW8ePngBz+I7du345577jnZRfmZkr179+LDH/4wbr31VlSr1ZNdnJ9JUUrhwgsvxH/+z/8ZAHD++edj+/bt+Ju/+RvccMMNJ7l0Pxvy1a9+FV/60pfwT//0TzjzzDPxyCOP4CMf+QiWLl1a6rCUky5JkuDaa6+F1hqf//znT0oZ5o0rZ9GiRQiCoGUHxKFDhzA0NHSSSjX/5EMf+hC+/e1v44477sDy5cvt50NDQ2g2mxgfH3eu5/obGhpqq1/67uddtm7disOHD+OCCy5AGIYIwxB33nkn/uqv/gphGGJwcLDU4U+QJUuW4IwzznA+W79+Pfbs2QOg0MELjeOhoSEcPnzY+T5NU4yOjv5C6PBjH/sYPvGJT+A973kPzj77bLz3ve/FH/7hH+Kmm24CUOrwpcqJ0tcv+tgGClCye/du3HrrrZYtAV5dHc4bYFKpVLBhwwbcdttt9jOlFG677TZs2rTpJJZsfojWGh/60IfwjW98A7fffnsLXbZhwwZEUeTo76mnnsKePXus/jZt2oTHHnvM6VzU+fzJ5udRrrjiCjz22GN45JFH7M+FF16I66+/3v5d6vCF5bLLLmvZpv70009j5cqVAIDVq1djaGjI0eHk5CTuu+8+R4fj4+PYunWrveb222+HUgobN258FWpxcmV2dhZSuqY3CAIopQCUOnypcqL0tWnTJtx1111IksRec+utt2Lt2rW/EG4cAiXPPPMMfvjDH6K/v9/5/lXV4UsKlX2F5ZZbbtFxHOsvfvGL+oknntC/8zu/o/v6+pwdEL+o8oEPfED39vbqH/3oR3pkZMT+zM7O2mt+93d/Vw8PD+vbb79dP/jgg3rTpk1606ZN9nva6vqmN71JP/LII/r73/++Xrx48S/MVtd2wnflaF3q8CfJ/fffr8Mw1J/5zGf0M888o7/0pS/pjo4O/Y//+I/2ms9+9rO6r69P/+u//qvetm2bfvvb39526+b555+v77vvPn3PPffo00477ed2q6svN9xwg162bJndLvz1r39dL1q0SP/xH/+xvabUoStTU1P64Ycf1g8//LAGoP/7f//v+uGHH7Y7Rk6EvsbHx/Xg4KB+73vfq7dv365vueUW3dHR8XOzXfiFdNhsNvXb3vY2vXz5cv3II484cwzfYfNq6XBeAROttf6f//N/6uHhYV2pVPTFF1+s77333pNdpHkhANr+fOELX7DX1Ot1/Xu/93t6wYIFuqOjQ7/jHe/QIyMjznOef/55fdVVV+laraYXLVqkP/rRj+okSV7l2swf8YFJqcOfLN/61rf0WWedpeM41uvWrdN/93d/53yvlNJ/+qd/qgcHB3Ucx/qKK67QTz31lHPNsWPH9HXXXae7urp0T0+Pft/73qenpqZezWqcNJmcnNQf/vCH9fDwsK5Wq/qUU07Rf/Inf+JMAKUOXbnjjjva2r8bbrhBa33i9PXoo4/qyy+/XMdxrJctW6Y/+9nPvlpVfMXlhXT43HPPHXeOueOOO+wzXi0dCq1ZusFSSimllFJKKaWUkyjzJsaklFJKKaWUUkoppQQmpZRSSimllFLKvJESmJRSSimllFJKKfNGSmBSSimllFJKKaXMGymBSSmllFJKKaWUMm+kBCallFJKKaWUUsq8kRKYlFJKKaWUUkop80ZKYFJKKaWUUkoppcwbKYFJKaWUUkoppZQyb6QEJqWUUkoppZRSyryREpiUUkoppZRSSinzRkpgUkoppZRSSimlzBv5/wFJpH56kXI2OAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import cv2\n", "import pytesseract\n", "\n", "# Spécifie le chemin vers tesseract.exe si nécessaire (Windows)\n", "# pytesseract.pytesseract.tesseract_cmd = r'C:\\Program Files\\Tesseract-OCR\\tesseract.exe'\n", "\n", "# Charger l'image manuscrite\n", "image = cv2.imread(\"images/17.jpg\")\n", "\n", "# Convertir en niveaux de gris\n", "gray = cv2.cvtColor(image[400 : 500] , cv2.COLOR_BGR2GRAY)\n", "\n", "\n", "plt.imshow( gray)"]}, {"cell_type": "code", "execution_count": 5, "id": "5c86afb5-87c3-4a02-9b8e-c592463d56ad", "metadata": {}, "outputs": [], "source": ["# 2. Histogram Equalization (boost contrast)\n", "equalized = cv2.equalizeHist(gray)"]}, {"cell_type": "code", "execution_count": 6, "id": "a9cf2d6f-ed99-4407-917c-4a31e21e6d17", "metadata": {}, "outputs": [], "source": ["# 3. <PERSON><PERSON><PERSON>r to remove noise (tweakable)\n", "blurred = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(equalized, (3, 3), 0)"]}, {"cell_type": "code", "execution_count": 7, "id": "b7d34d98", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Appliquer un seuillage (optionnel mais utile)\n", "_, thresh = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)\n", "# Display the result\n", "plt.imshow(thresh, cmap='gray')\n", "plt.title(\"Preprocessed for EasyOCR\")\n", "plt.axis('off')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "id": "b0d3d649", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[0 0 0 ... 0 0 0]\n", " [0 0 0 ... 0 0 0]\n", " [0 0 0 ... 0 0 0]\n", " ...\n", " [0 0 0 ... 0 0 0]\n", " [0 0 0 ... 0 0 0]\n", " [0 0 0 ... 0 0 0]]\n"]}], "source": ["print(thresh)"]}, {"cell_type": "code", "execution_count": 9, "id": "908d6574", "metadata": {}, "outputs": [], "source": ["pytesseract.pytesseract.tesseract_cmd = '/opt/homebrew/bin/tesseract'\n"]}, {"cell_type": "code", "execution_count": null, "id": "0b909363-a3f0-46f3-8198-2f34f8367e19", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b8d0fbca-aeb8-4530-bdab-8dfded6c8854", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "id": "1fd1cf9f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Texte reconnu :\n", "0 Tab. Prgmentun 6259\n", "\n"]}], "source": ["\n", "# Utiliser Tesseract pour extraire le texte\n", "texte = pytesseract.image_to_string(thresh, lang='eng')  # ou 'fra' pour le français\n", "\n", "print(\"Texte reconnu :\")\n", "print(texte)"]}, {"cell_type": "code", "execution_count": null, "id": "99d242fe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "71f8ef0a-455d-47e9-a750-c564749d5728", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}
from transformers import TrOCRProcessor, VisionEncoderDecoderModel
from PIL import Image
from io import BytesIO

# 1) Suppose you have a dict that maps keys to either:
#    • a local file-path string
#    • raw image bytes
#    • or even a PIL.Image.Image object
my_images = {
    "17": "data/p/17.jpg",
    # "18": open("data/p/18.jpg", "rb").read(),
    # "19": Image.open("data/p/19.jpg"),
}

def load_image(source):
    """
    source may be:
     - str: treated as a local path
     - bytes/bytearray: wrapped in a BytesIO
     - PIL.Image.Image: returned as-is
    """
    if isinstance(source, Image.Image):
        return source
    elif isinstance(source, (bytes, bytearray)):
        return Image.open(BytesIO(source))
    elif isinstance(source, str):
        return Image.open(source)
    else:
        raise ValueError(f"Unsupported image source type: {type(source)}")

# 2) Pick your image from the dict
raw = my_images["17"]
image = load_image(raw)

# 3) Run through TrOCR
processor = TrOCRProcessor.from_pretrained("microsoft/trocr-base-handwritten")
model     = VisionEncoderDecoderModel.from_pretrained("microsoft/trocr-base-handwritten")

pixel_values = processor(images=image, return_tensors="pt").pixel_values
generated_ids = model.generate(pixel_values)
generated_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]

print("OCR result:", generated_text)

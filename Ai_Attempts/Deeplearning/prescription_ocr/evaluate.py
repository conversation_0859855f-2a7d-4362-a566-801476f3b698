import os
import csv
import cv2
import easyocr
import pandas as pd
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

def evaluate_model(test_folder, test_label_file, model_path="models/medicine_recognizer.pth"):
    # Initialize EasyOCR reader
    reader = easyocr.Reader(['en'])
    
    # Load custom recognizer if available
    if os.path.exists(model_path):
        reader.recognizer.load(model_path)
    
    # Load test data
    test_samples = []
    with open(test_label_file, 'r') as f:
        reader_csv = csv.reader(f)
        for row in reader_csv:
            img_name, true_label = row
            img_path = os.path.join(test_folder, img_name)
            if os.path.exists(img_path):
                test_samples.append((img_path, true_label))
    
    # Evaluate on test set
    y_true = []
    y_pred = []
    
    for img_path, true_label in test_samples:
        # Read image
        image = cv2.imread(img_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Recognize text
        results = reader.recognize(image)
        pred_label = results[0][1] if results else ""
        
        y_true.append(true_label.lower())
        y_pred.append(pred_label.lower())
    
    # Calculate metrics
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, average='micro')
    recall = recall_score(y_true, y_pred, average='micro')
    f1 = f1_score(y_true, y_pred, average='micro')
    
    print(f"Evaluation Results:")
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")
    print(f"F1 Score: {f1:.4f}")
    
    # Save results
    results = pd.DataFrame({
        'image': [s[0] for s in test_samples],
        'true_label': y_true,
        'pred_label': y_pred
    })
    os.makedirs("outputs", exist_ok=True)
    results.to_csv("outputs/evaluation_results.csv", index=False)

if __name__ == "__main__":
    test_folder = os.path.join("data/dataset", "Testing", "testing_words")
    test_label_file = os.path.join("data/dataset", "Testing", "testing_labels.csv")

    
    evaluate_model(test_folder, test_label_file)
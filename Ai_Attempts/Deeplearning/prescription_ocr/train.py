import os
import csv
import cv2
import torch
import easyocr
import numpy as np
from tqdm import tqdm
from sklearn.model_selection import train_test_split
from torch.utils.data import Dataset, DataLoader

class MedicineWordDataset(Dataset):
    def __init__(self, image_folder, label_file, transform=None):
        self.image_folder = image_folder
        self.transform = transform
        self.samples = []
        
        with open(label_file, 'r') as f:
            reader = csv.reader(f)
            for row in reader:
                # Handle different CSV formats
                if len(row) >= 2:  # At least 2 columns
                    img_name = row[0].strip()  # First column is image name
                    label = row[1].strip()     # Second column is label
                    img_path = os.path.join(image_folder, img_name)
                    if os.path.exists(img_path):
                        self.samples.append((img_path, label))
                elif len(row) == 1:  # Handle case where filename and label might be in one column
                    parts = row[0].split(',')  # Try splitting by comma
                    if len(parts) >= 2:
                        img_name = parts[0].strip()
                        label = parts[1].strip()
                        img_path = os.path.join(image_folder, img_name)
                        if os.path.exists(img_path):
                            self.samples.append((img_path, label))
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label = self.samples[idx]
        image = cv2.imread(img_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        if self.transform:
            image = self.transform(image)
            
        return image, label

def fine_tune_ocr(train_folder, train_label_file, val_folder, val_label_file, epochs=10):
    # Initialize EasyOCR reader
    reader = easyocr.Reader(['en'], recognizer=True)
    
    # Prepare datasets
    train_dataset = MedicineWordDataset(train_folder, train_label_file)
    val_dataset = MedicineWordDataset(val_folder, val_label_file)
    
    # Fine-tune the recognizer
    print("Starting fine-tuning...")
    recognizer = reader.recognizer
    
    # Your fine-tuning code here...
    # (This would include your actual training loop)
    
    print("Fine-tuning completed. Saving model...")
    
    # Correct way to save the model
    if hasattr(recognizer, 'module'):  # If it's DataParallel
        torch.save(recognizer.module.state_dict(), 'models/medicine_recognizer.pth')
    else:
        torch.save(recognizer.state_dict(), 'models/medicine_recognizer.pth')
    
    print("Model saved to models/medicine_recognizer.pth")

if __name__ == "__main__":
    # Paths to your data
    data_dir = "data/dataset"  # Changed from the long name
    train_folder = os.path.join(data_dir, "Training", "training_words")
    train_label_file = os.path.join(data_dir, "Training", "training_labels.csv")
    val_folder = os.path.join(data_dir, "Validation", "validation_words")
    val_label_file = os.path.join(data_dir, "Validation", "validation_labels.csv")
    
    # Create models directory if not exists
    os.makedirs("models", exist_ok=True)
    
    # Start fine-tuning
    fine_tune_ocr(train_folder, train_label_file, val_folder, val_label_file, epochs=10)
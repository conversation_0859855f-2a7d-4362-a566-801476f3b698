import os
import cv2
import easyocr
import torch
import pandas as pd
from tqdm import tqdm

# Enhanced Medicine Lexicon
MEDICINE_LEXICON = {
    'paracetamol', 'ibuprofen', 'pand', 'pan40', 'pan20',
    'dolo', 'aceclofenac', 'rabeprazole', 'augmentin', 'engelam',
    'hexigel', 'montelukast', 'ondansetron', 'levocetirizine',
    # Common misspellings
    'paracetamal', 'ibuprofin', 'pand40', 'pand20', 'aujent',
    'enzel', 'hexiq', 'pan4o', 'pan0', 'pand4'
}

def preprocess_image(image_path):
    """Enhance image quality for better OCR"""
    img = cv2.imread(image_path)
    if img is None:
        return None
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Contrast enhancement
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    
    # Denoising
    denoised = cv2.fastNlMeansDenoising(enhanced, h=10)
    
    return denoised

def is_valid_image(filepath):
    """Check if file exists and is a valid image"""
    if not os.path.exists(filepath):
        return False
    try:
        img = cv2.imread(filepath)
        return img is not None
    except:
        return False

def load_custom_recognizer(reader, model_path):
    """Load fine-tuned recognizer weights"""
    if os.path.exists(model_path):
        state_dict = torch.load(model_path)
        if hasattr(reader.recognizer, 'module'):
            reader.recognizer.module.load_state_dict(state_dict)
        else:
            reader.recognizer.load_state_dict(state_dict)
    return reader

def clean_medicine_name(text):
    """Standardize medicine names for matching"""
    text = text.lower()
    # Remove numbers and special characters
    text = ''.join([c for c in text if c.isalpha()])
    return text

def fix_common_errors(text):
    """Correct common OCR mistakes"""
    corrections = {
        'winn jdoloneatncivcon': 'paracetamol',
        'pand4': 'pand40',
        'pan4o': 'pand40',
        'pan0': 'pand40',
        'ibupro': 'ibuprofen',
        'aujent': 'augmentin',
        'enzel': 'engelam',
        'hexiq': 'hexigel'
    }
    text_lower = text.lower()
    for wrong, right in corrections.items():
        if wrong in text_lower:
            return right.capitalize()
    return text

def extract_medicines_from_prescription(image_path, reader):
    """Extract and filter medicine names"""
    try:
        # Preprocess image
        processed_img = preprocess_image(image_path)
        if processed_img is None:
            raise ValueError("Image preprocessing failed")
        
        # OCR with optimized settings
        results = reader.readtext(processed_img,
                                batch_size=4,
                                decoder='beamsearch',
                                beamWidth=10,
                                detail=0)
        
        # Filter and clean results
        medicines = set()
        for text in results:
            corrected_text = fix_common_errors(text)
            cleaned = clean_medicine_name(corrected_text)
            
            # Check against lexicon
            for med in MEDICINE_LEXICON:
                if med in cleaned:
                    medicines.add(corrected_text.capitalize())
        
        return sorted(medicines)
    except Exception as e:
        raise ValueError(f"Processing failed: {str(e)}")

def process_prescriptions_folder(folder_path, output_file):
    """Process all prescriptions in folder"""
    # Initialize reader with standard parameters
    reader = easyocr.Reader(['en'],
                          gpu=False,
                          model_storage_directory='models',
                          download_enabled=True)
    
    # Load custom model if available
    model_path = "models/medicine_recognizer.pth"
    if os.path.exists(model_path):
        reader = load_custom_recognizer(reader, model_path)
    
    # Get valid image files
    valid_extensions = ('.png', '.jpg', '.jpeg')
    files = [f for f in sorted(os.listdir(folder_path))
             if f.lower().endswith(valid_extensions)]
    
    # Process files
    results = []
    error_log = []
    
    for file in tqdm(files, desc="Processing prescriptions"):
        file_path = os.path.join(folder_path, file)
        
        if not is_valid_image(file_path):
            error_log.append(f"Invalid image: {file}")
            continue
            
        try:
            medicines = extract_medicines_from_prescription(file_path, reader)
            results.append({
                'prescription': file,
                'extracted_medicines': ', '.join(medicines),
                'medicine_count': len(medicines)
            })
        except Exception as e:
            error_log.append(f"{file}: {str(e)}")
            results.append({
                'prescription': file,
                'extracted_medicines': 'ERROR',
                'medicine_count': 0
            })
    
    # Save results
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    pd.DataFrame(results).to_csv(output_file, index=False)
    
    # Save error log
    if error_log:
        error_file = output_file.replace('.csv', '_errors.log')
        with open(error_file, 'w') as f:
            f.write("\n".join(error_log))
        print(f"\nEncountered {len(error_log)} errors. See {error_file}")

    print(f"\nSuccessfully processed {len(results)-len(error_log)}/{len(files)} prescriptions")
    print(f"Results saved to {output_file}")

if __name__ == "__main__":
    process_prescriptions_folder(
        folder_path="data/p",
        output_file="outputs/extracted_medicines.csv"
    )
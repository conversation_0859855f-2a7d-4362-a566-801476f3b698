{"cells": [{"cell_type": "code", "execution_count": null, "id": "26e7d02a", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["from transformers import TrOCRProcessor, VisionEncoderDecoderModel\n", "from PIL import Image\n", "import requests\n", "\n", "# load image from the IAM database\n", "url = 'https://fki.tic.heia-fr.ch/static/img/a01-122-02-00.jpg'\n", "image = Image.open(requests.get(url, stream=True).raw).convert(\"RGB\")\n", "\n", "processor = TrOCRProcessor.from_pretrained('microsoft/trocr-base-handwritten')\n", "model = VisionEncoderDecoderModel.from_pretrained('microsoft/trocr-base-handwritten')\n", "pixel_values = processor(images=image, return_tensors=\"pt\").pixel_values\n", "\n", "generated_ids = model.generate(pixel_values)\n", "generated_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}
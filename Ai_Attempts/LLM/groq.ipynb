{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7Bs1RigciER3", "outputId": "ba32710f-7e36-4420-a6b2-df6b18b46657"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: langchain in /usr/local/lib/python3.11/dist-packages (0.3.25)\n", "Collecting groq\n", "  Downloading groq-0.26.0-py3-none-any.whl.metadata (15 kB)\n", "Collecting langchain-community\n", "  Downloading langchain_community-0.3.24-py3-none-any.whl.metadata (2.5 kB)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.58 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.63)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.8 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.8)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.17 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.43)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.11.5)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.0.41)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.11/dist-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from groq) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from groq) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from groq) (0.28.1)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from groq) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.10 in /usr/local/lib/python3.11/dist-packages (from groq) (4.13.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (3.11.15)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (9.1.2)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)\n", "Collecting pydantic-settings<3.0.0,>=2.4.0 (from langchain-community)\n", "  Downloading pydantic_settings-2.9.1-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting httpx-sse<1.0.0,>=0.4.0 (from langchain-community)\n", "  Downloading httpx_sse-0.4.0-py3-none-any.whl.metadata (9.0 kB)\n", "Requirement already satisfied: numpy>=1.26.2 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (2.0.2)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.6.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.4.4)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (0.3.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.20.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.5.0->groq) (3.10)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading marshmallow-3.26.1-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->groq) (0.16.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.58->langchain) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.58->langchain) (24.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (3.10.18)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.4.1)\n", "Collecting python-dotenv>=0.21.0 (from pydantic-settings<3.0.0,>=2.4.0->langchain-community)\n", "  Downloading python_dotenv-1.1.0-py3-none-any.whl.metadata (24 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (2.4.0)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.2.2)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.11/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.58->langchain) (3.0.0)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading mypy_extensions-1.1.0-py3-none-any.whl.metadata (1.1 kB)\n", "Downloading groq-0.26.0-py3-none-any.whl (129 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m129.6/129.6 kB\u001b[0m \u001b[31m9.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_community-0.3.24-py3-none-any.whl (2.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.5/2.5 MB\u001b[0m \u001b[31m55.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Downloading httpx_sse-0.4.0-py3-none-any.whl (7.8 kB)\n", "Downloading pydantic_settings-2.9.1-py3-none-any.whl (44 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.4/44.4 kB\u001b[0m \u001b[31m2.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading marshmallow-3.26.1-py3-none-any.whl (50 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.9/50.9 kB\u001b[0m \u001b[31m3.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading python_dotenv-1.1.0-py3-none-any.whl (20 kB)\n", "Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Downloading mypy_extensions-1.1.0-py3-none-any.whl (5.0 kB)\n", "Installing collected packages: python-dotenv, mypy-extensions, marshmallow, httpx-sse, typing-inspect, pydantic-settings, groq, dataclasses-json, langchain-community\n", "Successfully installed dataclasses-json-0.6.7 groq-0.26.0 httpx-sse-0.4.0 langchain-community-0.3.24 marshmallow-3.26.1 mypy-extensions-1.1.0 pydantic-settings-2.9.1 python-dotenv-1.1.0 typing-inspect-0.9.0\n"]}], "source": ["pip install langchain groq langchain-community"]}, {"cell_type": "code", "source": ["pip install python-dotenv\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "h-oi8uWBif73", "outputId": "dbf4a8c5-9c1b-4d7b-c96f-9608a6f079f6"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: python-dotenv in /usr/local/lib/python3.11/dist-packages (1.1.0)\n"]}]}, {"cell_type": "code", "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_groq import ChatGroq  # Use groq-specific integration\n", "import os\n", "from dotenv import load_dotenv\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "\n", "# Initialize Groq LLM via LangChain\n", "llm = ChatGroq(model=\"llama-3.3-70b-versatile\")\n", "\n", "# Create a prompt template\n", "prompt = PromptTemplate.from_template(\"Explain the concept of {topic} in simple terms.\")\n", "\n", "# Chain the prompt to the LLM and output parser\n", "chain = prompt | llm | StrOutputParser()\n", "\n", "# Invoke the chain\n", "response = chain.invoke({\"topic\": \"Quantum Computing\"})\n", "print(response)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lKiNNw-9i0eC", "outputId": "44298d46-724e-4519-9f8a-9c4f40cb803d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["**Introduction to Quantum Computing**\n", "\n", "Quantum Computing is a new and exciting field that uses the principles of quantum mechanics to perform calculations and operations on data. It's a different approach to computing that has the potential to solve complex problems much faster than classical computers.\n", "\n", "**Classical vs. Quantum Computing**\n", "\n", "To understand quantum computing, let's first look at how classical computers work. Classical computers use \"bits\" to store and process information. A bit can have a value of either 0 or 1. These bits are used to perform calculations and operations, but they can only be in one state at a time.\n", "\n", "Quantum computers, on the other hand, use \"qubits\" (quantum bits). Qubits are special because they can exist in multiple states at the same time, which is known as a \"superposition.\" This means a qubit can be both 0 and 1 simultaneously, allowing for many more possibilities and calculations to be performed.\n", "\n", "**Key Concepts**\n", "\n", "Here are some key concepts that make quantum computing unique:\n", "\n", "* **Superposition**: Qubits can exist in multiple states at the same time.\n", "* **Entanglement**: Qubits can be connected in a way that allows them to affect each other, even if they're separated by large distances.\n", "* **Quantum parallelism**: Quantum computers can perform many calculations simultaneously, thanks to the principles of superposition and entanglement.\n", "\n", "**How Quantum Computing Works**\n", "\n", "Imagine you have a combination lock with 10 numbers. A classical computer would try each number one by one, starting from 0 and going up to 9. A quantum computer, on the other hand, can try all 10 numbers at the same time, thanks to the principles of superposition and entanglement.\n", "\n", "**Potential Applications**\n", "\n", "Quantum computing has many potential applications, including:\n", "\n", "* **Cryptography**: Quantum computers can break many encryption algorithms currently in use, but they can also be used to create unbreakable encryption methods.\n", "* **Optimization**: Quantum computers can be used to optimize complex systems, such as logistics or finance.\n", "* **Simulation**: Quantum computers can simulate complex systems, such as molecules or materials, which can lead to breakthroughs in fields like medicine and energy.\n", "\n", "**Conclusion**\n", "\n", "Quantum computing is a new and exciting field that has the potential to revolutionize the way we approach complex problems. By using the principles of quantum mechanics, quantum computers can perform calculations and operations that are beyond the capabilities of classical computers. While it's still a developing field, the potential applications of quantum computing are vast and exciting.\n"]}]}, {"cell_type": "code", "source": ["pip install langchain_groq"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ji7GluzYjIYZ", "outputId": "3c6a0bc2-c84f-41a3-d2f7-477b7832f48d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain_groq\n", "  Downloading langchain_groq-0.3.2-py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.49 in /usr/local/lib/python3.11/dist-packages (from langchain_groq) (0.3.63)\n", "Requirement already satisfied: groq<1,>=0.4.1 in /usr/local/lib/python3.11/dist-packages (from langchain_groq) (0.26.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain_groq) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain_groq) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain_groq) (0.28.1)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain_groq) (2.11.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain_groq) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.10 in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain_groq) (4.13.2)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.126 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.49->langchain_groq) (0.3.43)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.49->langchain_groq) (9.1.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.49->langchain_groq) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.49->langchain_groq) (6.0.2)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.49->langchain_groq) (24.2)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.5.0->groq<1,>=0.4.1->langchain_groq) (3.10)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain_groq) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain_groq) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain_groq) (0.16.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.11/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.49->langchain_groq) (3.0.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.126->langchain-core<1.0.0,>=0.3.49->langchain_groq) (3.10.18)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.126->langchain-core<1.0.0,>=0.3.49->langchain_groq) (2.32.3)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.126->langchain-core<1.0.0,>=0.3.49->langchain_groq) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.126->langchain-core<1.0.0,>=0.3.49->langchain_groq) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->groq<1,>=0.4.1->langchain_groq) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->groq<1,>=0.4.1->langchain_groq) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->groq<1,>=0.4.1->langchain_groq) (0.4.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langsmith<0.4,>=0.1.126->langchain-core<1.0.0,>=0.3.49->langchain_groq) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langsmith<0.4,>=0.1.126->langchain-core<1.0.0,>=0.3.49->langchain_groq) (2.4.0)\n", "Downloading langchain_groq-0.3.2-py3-none-any.whl (15 kB)\n", "Installing collected packages: langchain_groq\n", "Successfully installed langchain_groq-0.3.2\n"]}]}, {"cell_type": "code", "source": ["!pip install torch torchvision torchaudio\n", "!pip install transformers\n", "!pip install langchain groq langchain-community\n", "!pip install easyocr\n", "!pip install python-dotenv\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hn8tQyP0jLZV", "outputId": "6b4feab0-1695-42a2-f512-0246dbf0cce6"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: torch in /usr/local/lib/python3.11/dist-packages (2.6.0+cu124)\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.11/dist-packages (0.21.0+cu124)\n", "Requirement already satisfied: torchaudio in /usr/local/lib/python3.11/dist-packages (2.6.0+cu124)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch) (4.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch) (3.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch) (2025.3.2)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch)\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.4.127 (from torch)\n", "  Downloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.4.127 (from torch)\n", "  Downloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch)\n", "  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==******** (from torch)\n", "  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==******** (from torch)\n", "  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==********** (from torch)\n", "  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==******** (from torch)\n", "  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch)\n", "  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch) (12.4.127)\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch)\n", "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch) (1.3.0)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from torchvision) (2.0.2)\n", "Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in /usr/local/lib/python3.11/dist-packages (from torchvision) (11.2.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch) (3.0.2)\n", "Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m105.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m88.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m52.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n", "\u001b[2K   \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━\u001b[0m \u001b[32m425.2/664.8 MB\u001b[0m \u001b[31m2.6 MB/s\u001b[0m eta \u001b[36m0:01:33\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m12.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m6.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m91.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h\u001b[31mERROR: THESE PACKAGES DO NOT MATCH THE HASHES FROM THE REQUIREMENTS FILE. If you have updated the package versions, please update the hashes. Otherwise, examine the package contents carefully; someone may have tampered with them.\n", "    unknown package:\n", "        Expected sha256 165764f44ef8c61fcdfdfdbe769d687e06374059fbb388b6c89ecb0e28793a6f\n", "             Got        21b5ccc75c19a7b219e2e9b8c9eaf90b20eaef93d7fa2413b529b0e2559c7956\n", "\u001b[0m\u001b[31m\n", "\u001b[0mRequirement already satisfied: transformers in /usr/local/lib/python3.11/dist-packages (4.52.3)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from transformers) (3.18.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.32.2)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2.0.2)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2024.11.6)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.21.1)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.5.3)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers) (4.67.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (2025.3.2)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (4.13.2)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (1.1.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2025.4.26)\n", "Requirement already satisfied: langchain in /usr/local/lib/python3.11/dist-packages (0.3.25)\n", "Requirement already satisfied: groq in /usr/local/lib/python3.11/dist-packages (0.26.0)\n", "Requirement already satisfied: langchain-community in /usr/local/lib/python3.11/dist-packages (0.3.24)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.58 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.63)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.8 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.8)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.17 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.43)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.11.5)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.0.41)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.11/dist-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from groq) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from groq) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from groq) (0.28.1)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from groq) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.10 in /usr/local/lib/python3.11/dist-packages (from groq) (4.13.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (3.11.15)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (9.1.2)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (0.6.7)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (2.9.1)\n", "Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (0.4.0)\n", "Requirement already satisfied: numpy>=1.26.2 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (2.0.2)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.6.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.4.4)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (0.3.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.20.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.5.0->groq) (3.10)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.11/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (0.9.0)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->groq) (0.16.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.58->langchain) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.58->langchain) (24.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (3.10.18)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.4.1)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in /usr/local/lib/python3.11/dist-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain-community) (1.1.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (2.4.0)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.2.2)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.11/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.58->langchain) (3.0.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.11/dist-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community) (1.1.0)\n", "Collecting easyocr\n", "  Downloading easyocr-1.7.2-py3-none-any.whl.metadata (10 kB)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.11/dist-packages (from easyocr) (2.6.0+cu124)\n", "Requirement already satisfied: torchvision>=0.5 in /usr/local/lib/python3.11/dist-packages (from easyocr) (0.21.0+cu124)\n", "Requirement already satisfied: opencv-python-headless in /usr/local/lib/python3.11/dist-packages (from easyocr) (*********)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.11/dist-packages (from easyocr) (1.15.3)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from easyocr) (2.0.2)\n", "Requirement already satisfied: Pillow in /usr/local/lib/python3.11/dist-packages (from easyocr) (11.2.1)\n", "Requirement already satisfied: scikit-image in /usr/local/lib/python3.11/dist-packages (from easyocr) (0.25.2)\n", "Collecting python-bidi (from easyocr)\n", "  Downloading python_bidi-0.6.6-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\n", "Requirement already satisfied: PyYAML in /usr/local/lib/python3.11/dist-packages (from easyocr) (6.0.2)\n", "Requirement already satisfied: S<PERSON><PERSON>y in /usr/local/lib/python3.11/dist-packages (from easyocr) (2.1.1)\n", "Collecting pyclipper (from easyocr)\n", "  Downloading pyclipper-1.3.0.post6-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.0 kB)\n", "Collecting ninja (from easyocr)\n", "  Downloading ninja-********-py3-none-manylinux_2_12_x86_64.manylinux2010_x86_64.whl.metadata (5.0 kB)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch->easyocr) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch->easyocr) (4.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch->easyocr) (3.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch->easyocr) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch->easyocr) (2025.3.2)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch->easyocr)\n", "  Using cached nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.4.127 (from torch->easyocr)\n", "  Using cached nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.4.127 (from torch->easyocr)\n", "  Using cached nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch->easyocr)\n", "  Using cached nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==******** (from torch->easyocr)\n", "  Using cached nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==******** (from torch->easyocr)\n", "  Using cached nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==********** (from torch->easyocr)\n", "  Using cached nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==******** (from torch->easyocr)\n", "  Using cached nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch->easyocr)\n", "  Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch->easyocr) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch->easyocr) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch->easyocr) (12.4.127)\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch->easyocr)\n", "  Using cached nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch->easyocr) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch->easyocr) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch->easyocr) (1.3.0)\n", "Requirement already satisfied: imageio!=2.35.0,>=2.33 in /usr/local/lib/python3.11/dist-packages (from scikit-image->easyocr) (2.37.0)\n", "Requirement already satisfied: tifffile>=2022.8.12 in /usr/local/lib/python3.11/dist-packages (from scikit-image->easyocr) (2025.5.26)\n", "Requirement already satisfied: packaging>=21 in /usr/local/lib/python3.11/dist-packages (from scikit-image->easyocr) (24.2)\n", "Requirement already satisfied: lazy-loader>=0.4 in /usr/local/lib/python3.11/dist-packages (from scikit-image->easyocr) (0.4)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch->easyocr) (3.0.2)\n", "Downloading easyocr-1.7.2-py3-none-any.whl (2.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.9/2.9 MB\u001b[0m \u001b[31m76.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hUsing cached nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n", "Using cached nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n", "Using cached nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n", "Using cached nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n", "Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m1.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hUsing cached nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n", "Using cached nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n", "Using cached nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n", "Using cached nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n", "Using cached nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "Downloading ninja-********-py3-none-manylinux_2_12_x86_64.manylinux2010_x86_64.whl (422 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m422.8/422.8 kB\u001b[0m \u001b[31m617.6 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pyclipper-1.3.0.post6-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (969 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m969.6/969.6 kB\u001b[0m \u001b[31m56.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading python_bidi-0.6.6-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (292 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m292.9/292.9 kB\u001b[0m \u001b[31m22.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: python-bidi, pyclipper, nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, ninja, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, easyocr\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n", "  Attempting uninstall: nvidia-curand-cu12\n", "    Found existing installation: nvidia-curand-cu12 *********\n", "    Uninstalling nvidia-curand-cu12-*********:\n", "      Successfully uninstalled nvidia-curand-cu12-*********\n", "  Attempting uninstall: nvidia-cufft-cu12\n", "    Found existing installation: nvidia-cufft-cu12 *********\n", "    Uninstalling nvidia-cufft-cu12-*********:\n", "      Successfully uninstalled nvidia-cufft-cu12-*********\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cublas-cu12\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n", "  Attempting uninstall: nvidia-cusparse-cu12\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n", "  Attempting uninstall: nvidia-cudnn-cu12\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n", "  Attempting uninstall: nvidia-cusolver-cu12\n", "    Found existing installation: nvidia-cusolver-cu12 *********\n", "    Uninstalling nvidia-cusolver-cu12-*********:\n", "      Successfully uninstalled nvidia-cusolver-cu12-*********\n", "Successfully installed easyocr-1.7.2 ninja-******** nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.4.127 nvidia-cuda-nvrtc-cu12-12.4.127 nvidia-cuda-runtime-cu12-12.4.127 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-nvjitlink-cu12-12.4.127 pyclipper-1.3.0.post6 python-bidi-0.6.6\n", "Requirement already satisfied: python-dotenv in /usr/local/lib/python3.11/dist-packages (1.1.0)\n"]}]}, {"cell_type": "code", "source": ["import easyocr\n", "from PIL import Image\n", "\n", "def extract_text_from_image(image_path: str):\n", "    reader = easyocr.Reader(['en'])\n", "    result = reader.readtext(image_path, detail=0)\n", "    return \"\\n\".join(result)\n"], "metadata": {"id": "1NFuomKDmRV3"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_groq import ChatGroq\n", "import os\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "llm = ChatGroq(model=\"llama-3.3-70b-versatile\")\n", "\n", "prompt = PromptTemplate.from_template(\n", "    \"\"\"Extract all medicine names and dosages from the following handwritten prescription text:\n", "\n", "    {text}\n", "    correct or complete the medication name from the given text,\n", "    remove any non relevant detail like email, header of the prescription as well as the footer remove them from the final output\n", "    the output should contain only correct medication name\n", "    Return only a list in this format:\n", "    - MedicineName1: Dosage info\n", "    - MedicineName2: Dosage info\n", "    \"\"\"\n", ")\n", "\n", "chain = prompt | llm | StrOutputParser()\n"], "metadata": {"id": "14AFsnibm7my"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def process_prescription(image_path):\n", "    # Step 1: OCR\n", "    ocr_text = extract_text_from_image(image_path)\n", "    #print(\"Extracted OCR Text:\", ocr_text)\n", "\n", "    # Step 2: Ask LLM to extract medicines\n", "    result = chain.invoke({\"text\": ocr_text})\n", "    return result\n", "\n", "# Example\n", "output = process_prescription(\"17.jpg\")\n", "print(output)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jTDAlZ9nnKnp", "outputId": "2cbd1687-0330-4dc3-fd4e-dd46f16095f0"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["WARNING:easyocr.easyocr:Neither CUDA nor MPS are available - defaulting to CPU. Note: This module is much faster with a GPU.\n"]}, {"output_type": "stream", "name": "stdout", "text": ["- Augmentin: 625 mg\n", "- Enzalutamide: (no dosage info)\n", "- Hexigel: (no dosage info)\n", "- Pan D: (no dosage info)\n"]}]}, {"cell_type": "code", "source": ["pip install pytesseract pillow\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jpEAeUOYnQuD", "outputId": "6f862a87-0f34-4171-c789-71b9cba51a6d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting pytesseract\n", "  Downloading pytesseract-0.3.13-py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: pillow in /usr/local/lib/python3.11/dist-packages (11.2.1)\n", "Requirement already satisfied: packaging>=21.3 in /usr/local/lib/python3.11/dist-packages (from pytesseract) (24.2)\n", "Downloading pytesseract-0.3.13-py3-none-any.whl (14 kB)\n", "Installing collected packages: pytesseract\n", "Successfully installed pytesseract-0.3.13\n"]}]}, {"cell_type": "code", "source": ["from PIL import Image\n", "import pytesseract\n", "\n", "# Optional: Set path if tesseract is not in PATH\n", "# pytesseract.pytesseract.tesseract_cmd = \"/opt/homebrew/bin/tesseract\"\n", "\n", "def extract_text_with_tesseract(image_path):\n", "    img = Image.open(image_path)\n", "    text = pytesseract.image_to_string(img)\n", "    return text\n", "\n", "# Example usage\n", "image_path = \"17.jpg\"  # Replace with your image path\n", "ocr_text = extract_text_with_tesseract(image_path)\n", "print(\"Extracted Text:\\n\", ocr_text)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "12HcMGFmp94i", "outputId": "05652042-eef9-47ad-f412-e5ea113233ca"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Extracted Text:\n", " Smile Designing | Teeth Whitening THE CH ITE TUSK.\n", "\n", "Dental Implants | General Dentistry @O/whitetuskdental\n", "g)e! a <PERSON>e chan <PERSON>e\n", "ie (arg\n", "/\n", "\n", "hb. <PERSON> a\n", "\n", "a (eh erat x Slogs\n", "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>n\n", "\n", "ae\n", "\n", "faxD 4\n", "\n", "ae\n", "afer | ea\n", "\n", "ae Hexcgel ee 7 a\n", "(YL BABSOPE\n", "\n", "a —| X tveek\n", "\n", "Ph: +91 8108112511 | Web: www.thewhitetusk.com | Email: info@<EMAIL>\n", "\n", " \n", "\n", " \n", "\f\n"]}]}, {"cell_type": "code", "source": ["print( chain.invoke({\"text\": ocr_text}))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uzkagpWDqD70", "outputId": "7b8dc0b5-af20-4d9d-8cf2-721960da9200"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["- Enalapril: No dosage info \n", "- Hexigel: No dosage info\n"]}]}, {"cell_type": "code", "source": ["from langchain_community.document_loaders import ImageToTextLoader\n", "\n", "# Path to your image\n", "image_path = \"17.jpg\"\n", "\n", "# Create the loader\n", "loader = ImageToTextLoader(image_path)\n", "\n", "# Load and extract text as Document(s)\n", "docs = loader.load()\n", "\n", "# Access the extracted text\n", "print(docs[0].page_content)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 500}, "id": "KXaK5mpoqWMc", "outputId": "bb82a145-fa92-401a-99ad-231ad3c22764"}, "execution_count": null, "outputs": [{"output_type": "error", "ename": "ImportError", "evalue": "cannot import name 'ImageToTextLoader' from 'langchain_community.document_loaders' (/usr/local/lib/python3.11/dist-packages/langchain_community/document_loaders/__init__.py)", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "\u001b[0;32m<ipython-input-26-2c051d9b8a61>\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0;32mfrom\u001b[0m \u001b[0mlangchain_community\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdocument_loaders\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mImageToTextLoader\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0;31m# Path to your image\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0mimage_path\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m\"17.jpg\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mImportError\u001b[0m: cannot import name 'ImageToTextLoader' from 'langchain_community.document_loaders' (/usr/local/lib/python3.11/dist-packages/langchain_community/document_loaders/__init__.py)", "", "\u001b[0;31m---------------------------------------------------------------------------\u001b[0;32m\nNOTE: If your import is failing due to a missing package, you can\nmanually install dependencies using either !pip or !apt.\n\nTo view examples of installing some common dependencies, click the\n\"Open Examples\" button below.\n\u001b[0;31m---------------------------------------------------------------------------\u001b[0m\n"], "errorDetails": {"actions": [{"action": "open_url", "actionText": "Open Examples", "url": "/notebooks/snippets/importing_libraries.ipynb"}]}}]}, {"cell_type": "code", "source": ["!pip install langchain langchain-community pytesseract pillow\n", "!brew install tesseract  # macOS\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1DlhxOAVrPIX", "outputId": "2969bec8-e538-4a21-8f7f-e418f40698a5"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: langchain in /usr/local/lib/python3.11/dist-packages (0.3.25)\n", "Requirement already satisfied: langchain-community in /usr/local/lib/python3.11/dist-packages (0.3.24)\n", "Requirement already satisfied: pytesseract in /usr/local/lib/python3.11/dist-packages (0.3.13)\n", "Requirement already satisfied: pillow in /usr/local/lib/python3.11/dist-packages (11.2.1)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.58 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.63)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.8 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.8)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.17 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.43)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.11.5)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.0.41)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.11/dist-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (3.11.15)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (9.1.2)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (0.6.7)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (2.9.1)\n", "Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (0.4.0)\n", "Requirement already satisfied: numpy>=1.26.2 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (2.0.2)\n", "Requirement already satisfied: packaging>=21.3 in /usr/local/lib/python3.11/dist-packages (from pytesseract) (24.2)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.6.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.4.4)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (0.3.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.20.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.11/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (0.9.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.58->langchain) (1.33)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.58->langchain) (4.13.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (3.10.18)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.4.1)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in /usr/local/lib/python3.11/dist-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain-community) (1.1.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (2025.4.26)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.2.2)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (4.9.0)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (0.16.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.11/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.58->langchain) (3.0.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.11/dist-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community) (1.1.0)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (1.3.1)\n", "/bin/bash: line 1: brew: command not found\n"]}]}, {"cell_type": "code", "source": ["pip install --upgrade --quiet \"unstructured[all-docs]\""], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dRn0jJHYrVfh", "outputId": "f2c02871-3f8a-4c36-9c70-44d4ab140c39"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/981.5 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m42.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m48.2/48.2 kB\u001b[0m \u001b[31m3.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m17.6/17.6 MB\u001b[0m \u001b[31m74.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m16.4/16.4 MB\u001b[0m \u001b[31m89.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m244.3/244.3 kB\u001b[0m \u001b[31m19.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m472.8/472.8 kB\u001b[0m \u001b[31m33.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m48.1/48.1 kB\u001b[0m \u001b[31m3.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m112.5/112.5 kB\u001b[0m \u001b[31m8.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m590.6/590.6 kB\u001b[0m \u001b[31m36.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m526.1/526.1 kB\u001b[0m \u001b[31m37.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.6/5.6 MB\u001b[0m \u001b[31m93.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.2/1.2 MB\u001b[0m \u001b[31m58.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.5/2.5 MB\u001b[0m \u001b[31m85.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m304.2/304.2 kB\u001b[0m \u001b[31m20.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m167.6/167.6 kB\u001b[0m \u001b[31m13.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.1/3.1 MB\u001b[0m \u001b[31m83.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m69.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m195.8/195.8 kB\u001b[0m \u001b[31m14.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m169.4/169.4 kB\u001b[0m \u001b[31m13.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m3.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m114.6/114.6 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.9/2.9 MB\u001b[0m \u001b[31m78.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m5.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for langdetect (setup.py) ... \u001b[?25l\u001b[?25hdone\n"]}]}, {"cell_type": "code", "source": ["from langchain_community.document_loaders.image import UnstructuredImageLoader\n", "\n", "loader = UnstructuredImageLoader(\"17.jpg\")\n", "data = loader.load()\n", "\n", "print(data[0].page_content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RESXrIfRrzKs", "outputId": "7a9d6df5-9b7c-4d84-b529-c5354ac3cb30"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["THE wa ITE TUSK.\n", "\n", "Smile Designing | Teeth Whitening\n", "\n", "Dental Implants | General Dentistry @@/whitetuskdental FarD 4 eae ase ee Age Hexcgel joo yer (YLRABSOAPE\n", "\n", "aa\n", "\n", "A)\n", "\n", "<PERSON> tureek\n", "\n", "Ph: +91 8108112511 | Web: www.thewhitetusk.com | Email: info@<EMAIL>\n"]}]}, {"cell_type": "code", "source": ["!pip install transformers torchvision torch pillow\n", "!pip install langchain langchain-community langchain-groq python-dotenv\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EQdodLi7sCU3", "outputId": "b4cfa184-79be-47b9-bde8-d76f803b0d25"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: transformers in /usr/local/lib/python3.11/dist-packages (4.52.3)\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.11/dist-packages (0.21.0+cu124)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.11/dist-packages (2.6.0+cu124)\n", "Requirement already satisfied: pillow in /usr/local/lib/python3.11/dist-packages (11.2.1)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from transformers) (3.18.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.32.2)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2.0.2)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2024.11.6)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.21.1)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.5.3)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers) (4.67.1)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch) (4.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch) (3.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch) (2025.3.2)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch) (12.4.127)\n", "Requirement already satisfied: nvidia-cudnn-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch) (********)\n", "Requirement already satisfied: nvidia-cublas-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch) (********)\n", "Requirement already satisfied: nvidia-cufft-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch) (********)\n", "Requirement already satisfied: nvidia-curand-cu12==********** in /usr/local/lib/python3.11/dist-packages (from torch) (**********)\n", "Requirement already satisfied: nvidia-cusolver-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch) (********)\n", "Requirement already satisfied: nvidia-cusparse-cu12==********** in /usr/local/lib/python3.11/dist-packages (from torch) (**********)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch) (12.4.127)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch) (12.4.127)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch) (1.3.0)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (1.1.2)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch) (3.0.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2025.4.26)\n", "Requirement already satisfied: langchain in /usr/local/lib/python3.11/dist-packages (0.3.25)\n", "Requirement already satisfied: langchain-community in /usr/local/lib/python3.11/dist-packages (0.3.24)\n", "Requirement already satisfied: langchain-groq in /usr/local/lib/python3.11/dist-packages (0.3.2)\n", "Requirement already satisfied: python-dotenv in /usr/local/lib/python3.11/dist-packages (1.1.0)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.58 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.63)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.8 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.8)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.17 in /usr/local/lib/python3.11/dist-packages (from langchain) (0.3.43)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.11.5)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.0.41)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.11/dist-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.11/dist-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (3.11.15)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (9.1.2)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (0.6.7)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (2.9.1)\n", "Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (0.4.0)\n", "Requirement already satisfied: numpy>=1.26.2 in /usr/local/lib/python3.11/dist-packages (from langchain-community) (2.0.2)\n", "Requirement already satisfied: groq<1,>=0.4.1 in /usr/local/lib/python3.11/dist-packages (from langchain-groq) (0.26.0)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.6.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.4.4)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (0.3.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.20.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.11/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (0.9.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain-groq) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain-groq) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain-groq) (0.28.1)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain-groq) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.10 in /usr/local/lib/python3.11/dist-packages (from groq<1,>=0.4.1->langchain-groq) (4.13.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.58->langchain) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.11/dist-packages (from langchain-core<1.0.0,>=0.3.58->langchain) (24.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (3.10.18)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from langsmith<0.4,>=0.1.17->langchain) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.4.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2->langchain) (2025.4.26)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.2.2)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain-groq) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->groq<1,>=0.4.1->langchain-groq) (0.16.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.11/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.58->langchain) (3.0.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.11/dist-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community) (1.1.0)\n"]}]}, {"cell_type": "code", "source": ["from PIL import Image\n", "from transformers import TrOCRProcessor, VisionEncoderDecoderModel\n", "\n", "def extract_text_with_trocr(image_path: str) -> str:\n", "    processor = TrOCRProcessor.from_pretrained(\"microsoft/trocr-large-handwritten\")\n", "    model = VisionEncoderDecoderModel.from_pretrained(\"microsoft/trocr-large-handwritten\")\n", "\n", "    image = Image.open(image_path).convert(\"RGB\")\n", "    pixel_values = processor(images=image, return_tensors=\"pt\").pixel_values\n", "\n", "    generated_ids = model.generate(pixel_values)\n", "    generated_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]\n", "\n", "    return generated_text\n"], "metadata": {"id": "OZnrUQzDuuY1"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_groq import ChatGroq\n", "import os\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "llm = ChatGroq(model=\"llama-3.3-70b-versatile\")\n", "\n", "prompt = PromptTemplate.from_template(\"\"\"\n", "Extract medication names and dosages from this prescription text:\n", "\n", "{text}\n", "\n", "Return as structured list:\n", "- Medication: <PERSON><PERSON><PERSON>\n", "\"\"\")\n", "\n", "chain = prompt | llm | StrOutputParser()\n"], "metadata": {"id": "NZDpmXYzu3uN"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def analyze_prescription(image_path):\n", "    # Step 1: OCR\n", "    text = extract_text_with_trocr(image_path)\n", "    print(\"📝 OCR Extracted Text:\\n\", text)\n", "\n", "    # Step 2: <PERSON><PERSON><PERSON><PERSON> LLM Extraction\n", "    result = chain.invoke({\"text\": text})\n", "    print(\"\\n💊 Extracted Medications:\\n\", result)\n"], "metadata": {"id": "NCPjWpxovFnt"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["if __name__ == \"__main__\":\n", "    analyze_prescription(\"17.jpg\")  # Replace with your image path\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bXSXAIc5vJlc", "outputId": "3891d082-e6a5-4753-c8ce-53feef70e980"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Some weights of VisionEncoderDecoderModel were not initialized from the model checkpoint at microsoft/trocr-large-handwritten and are newly initialized: ['encoder.pooler.dense.bias', 'encoder.pooler.dense.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📝 OCR Extracted Text:\n", " 1903\n", "\n", "💊 Extracted Medications:\n", " There is no prescription text provided. The only information given is the number \"1903\", which does not contain any medication names or dosages. If you could provide the actual prescription text, I would be happy to assist you in extracting the medication names and dosages.\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "GOsW7lWEvNTb"}, "execution_count": null, "outputs": []}]}
{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.11", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kaggle": {"accelerator": "none", "dataSources": [{"sourceId": 11870605, "sourceType": "datasetVersion", "datasetId": 7459829}], "dockerImageVersionId": 31041, "isInternetEnabled": true, "language": "python", "sourceType": "notebook", "isGpuEnabled": false}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": "# Clean install compatible versions\n%pip uninstall -y transformers accelerate peft\n%pip install --no-cache-dir transformers==4.36.2 accelerate==0.21.0 datasets\n", "metadata": {"_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "trusted": true, "execution": {"iopub.status.busy": "2025-05-25T10:39:00.648350Z", "iopub.execute_input": "2025-05-25T10:39:00.648620Z", "iopub.status.idle": "2025-05-25T10:40:09.252209Z", "shell.execute_reply.started": "2025-05-25T10:39:00.648599Z", "shell.execute_reply": "2025-05-25T10:40:09.251263Z"}}, "outputs": [{"name": "stdout", "text": "\u001b[33mWARNING: Skipping transformers as it is not installed.\u001b[0m\u001b[33m\n\u001b[0m\u001b[33mWARNING: Skipping accelerate as it is not installed.\u001b[0m\u001b[33m\n\u001b[0m\u001b[33mWARNING: Skipping peft as it is not installed.\u001b[0m\u001b[33m\n\u001b[0mNote: you may need to restart the kernel to use updated packages.\nCollecting transformers==4.36.2\n  Downloading transformers-4.36.2-py3-none-any.whl.metadata (126 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m126.8/126.8 kB\u001b[0m \u001b[31m7.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hCollecting accelerate==0.21.0\n  Downloading accelerate-0.21.0-py3-none-any.whl.metadata (17 kB)\nRequirement already satisfied: datasets in /usr/local/lib/python3.11/dist-packages (3.6.0)\nRequirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from transformers==4.36.2) (3.18.0)\nRequirement already satisfied: huggingface-hub<1.0,>=0.19.3 in /usr/local/lib/python3.11/dist-packages (from transformers==4.36.2) (0.31.1)\nRequirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from transformers==4.36.2) (1.26.4)\nRequirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers==4.36.2) (25.0)\nRequirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers==4.36.2) (6.0.2)\nRequirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers==4.36.2) (2024.11.6)\nRequirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers==4.36.2) (2.32.3)\nCollecting tokenizers<0.19,>=0.14 (from transformers==4.36.2)\n  Downloading tokenizers-0.15.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)\nRequirement already satisfied: safetensors>=0.3.1 in /usr/local/lib/python3.11/dist-packages (from transformers==4.36.2) (0.5.3)\nRequirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers==4.36.2) (4.67.1)\nRequirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from accelerate==0.21.0) (7.0.0)\nRequirement already satisfied: torch>=1.10.0 in /usr/local/lib/python3.11/dist-packages (from accelerate==0.21.0) (2.6.0+cu124)\nRequirement already satisfied: pyarrow>=15.0.0 in /usr/local/lib/python3.11/dist-packages (from datasets) (19.0.1)\nRequirement already satisfied: dill<0.3.9,>=0.3.0 in /usr/local/lib/python3.11/dist-packages (from datasets) (0.3.8)\nRequirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (from datasets) (2.2.3)\nRequirement already satisfied: xxhash in /usr/local/lib/python3.11/dist-packages (from datasets) (3.5.0)\nRequirement already satisfied: multiprocess<0.70.17 in /usr/local/lib/python3.11/dist-packages (from datasets) (0.70.16)\nCollecting fsspec<=2025.3.0,>=2023.1.0 (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets)\n  Downloading fsspec-2025.3.0-py3-none-any.whl.metadata (11 kB)\nRequirement already satisfied: aiohttp!=4.0.0a0,!=4.0.0a1 in /usr/local/lib/python3.11/dist-packages (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets) (3.11.18)\nRequirement already satisfied: typing-extensions>=******* in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.19.3->transformers==4.36.2) (4.13.2)\nRequirement already satisfied: hf-xet<2.0.0,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.19.3->transformers==4.36.2) (1.1.0)\nRequirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.36.2) (1.3.8)\nRequirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.36.2) (1.2.4)\nRequirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.36.2) (0.1.1)\nRequirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.36.2) (2025.1.0)\nRequirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.36.2) (2022.1.0)\nRequirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.36.2) (2.4.1)\nRequirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.36.2) (3.4.2)\nRequirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.36.2) (3.10)\nRequirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.36.2) (2.4.0)\nRequirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.36.2) (2025.4.26)\nRequirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=1.10.0->accelerate==0.21.0) (3.4.2)\nRequirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10.0->accelerate==0.21.0) (3.1.6)\nRequirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10.0->accelerate==0.21.0) (12.4.127)\nRequirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10.0->accelerate==0.21.0) (12.4.127)\nRequirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10.0->accelerate==0.21.0) (12.4.127)\nCollecting nvidia-cudnn-cu12==******** (from torch>=1.10.0->accelerate==0.21.0)\n  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-cublas-cu12==******** (from torch>=1.10.0->accelerate==0.21.0)\n  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cufft-cu12==******** (from torch>=1.10.0->accelerate==0.21.0)\n  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-curand-cu12==********** (from torch>=1.10.0->accelerate==0.21.0)\n  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cusolver-cu12==******** (from torch>=1.10.0->accelerate==0.21.0)\n  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-cusparse-cu12==********** (from torch>=1.10.0->accelerate==0.21.0)\n  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\nRequirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10.0->accelerate==0.21.0) (0.6.2)\nRequirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10.0->accelerate==0.21.0) (2.21.5)\nRequirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10.0->accelerate==0.21.0) (12.4.127)\nCollecting nvidia-nvjitlink-cu12==12.4.127 (from torch>=1.10.0->accelerate==0.21.0)\n  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nRequirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10.0->accelerate==0.21.0) (3.2.0)\nRequirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10.0->accelerate==0.21.0) (1.13.1)\nRequirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=1.10.0->accelerate==0.21.0) (1.3.0)\nRequirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets) (2.9.0.post0)\nRequirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets) (2025.2)\nRequirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets) (2025.2)\nRequirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets) (2.6.1)\nRequirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets) (1.3.2)\nRequirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets) (25.3.0)\nRequirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets) (1.6.0)\nRequirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets) (6.4.3)\nRequirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets) (0.3.1)\nRequirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets) (1.20.0)\nRequirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas->datasets) (1.17.0)\nRequirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=1.10.0->accelerate==0.21.0) (3.0.2)\nRequirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.17->transformers==4.36.2) (2024.2.0)\nRequirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.17->transformers==4.36.2) (2022.1.0)\nRequirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy>=1.17->transformers==4.36.2) (1.3.0)\nRequirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy>=1.17->transformers==4.36.2) (2024.2.0)\nRequirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy>=1.17->transformers==4.36.2) (2024.2.0)\nDownloading transformers-4.36.2-py3-none-any.whl (8.2 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.2/8.2 MB\u001b[0m \u001b[31m112.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n\u001b[?25hDownloading accelerate-0.21.0-py3-none-any.whl (244 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m244.2/244.2 kB\u001b[0m \u001b[31m173.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading fsspec-2025.3.0-py3-none-any.whl (193 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m193.6/193.6 kB\u001b[0m \u001b[31m308.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading tokenizers-0.15.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.6 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.6/3.6 MB\u001b[0m \u001b[31m350.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m311.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m313.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m349.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m271.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m326.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m328.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m314.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n\u001b[?25hInstalling collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cublas-cu12, fsspec, nvidia-cusparse-cu12, nvidia-cudnn-cu12, tokenizers, nvidia-cusolver-cu12, transformers, accelerate\n  Attempting uninstall: nvidia-nvjitlink-cu12\n    Found existing installation: nvidia-nvjitlink-cu12 12.9.41\n    Uninstalling nvidia-nvjitlink-cu12-12.9.41:\n      Successfully uninstalled nvidia-nvjitlink-cu12-12.9.41\n  Attempting uninstall: nvidia-curand-cu12\n    Found existing installation: nvidia-curand-cu12 10.3.10.19\n    Uninstalling nvidia-curand-cu12-10.3.10.19:\n      Successfully uninstalled nvidia-curand-cu12-10.3.10.19\n  Attempting uninstall: nvidia-cufft-cu12\n    Found existing installation: nvidia-cufft-cu12 11.4.0.6\n    Uninstalling nvidia-cufft-cu12-11.4.0.6:\n      Successfully uninstalled nvidia-cufft-cu12-11.4.0.6\n  Attempting uninstall: nvidia-cublas-cu12\n    Found existing installation: nvidia-cublas-cu12 12.9.0.13\n    Uninstalling nvidia-cublas-cu12-12.9.0.13:\n      Successfully uninstalled nvidia-cublas-cu12-12.9.0.13\n  Attempting uninstall: fsspec\n    Found existing installation: fsspec 2025.3.2\n    Uninstalling fsspec-2025.3.2:\n      Successfully uninstalled fsspec-2025.3.2\n  Attempting uninstall: nvidia-cusparse-cu12\n    Found existing installation: nvidia-cusparse-cu12 12.5.9.5\n    Uninstalling nvidia-cusparse-cu12-12.5.9.5:\n      Successfully uninstalled nvidia-cusparse-cu12-12.5.9.5\n  Attempting uninstall: nvidia-cudnn-cu12\n    Found existing installation: nvidia-cudnn-cu12 ********\n    Uninstalling nvidia-cudnn-cu12-********:\n      Successfully uninstalled nvidia-cudnn-cu12-********\n  Attempting uninstall: tokenizers\n    Found existing installation: tokenizers 0.21.1\n    Uninstalling tokenizers-0.21.1:\n      Successfully uninstalled tokenizers-0.21.1\n  Attempting uninstall: nvidia-cusolver-cu12\n    Found existing installation: nvidia-cusolver-cu12 *********\n    Uninstalling nvidia-cusolver-cu12-*********:\n      Successfully uninstalled nvidia-cusolver-cu12-*********\n\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\ncesium 0.12.4 requires numpy<3.0,>=2.0, but you have numpy 1.26.4 which is incompatible.\nsentence-transformers 3.4.1 requires transformers<5.0.0,>=4.41.0, but you have transformers 4.36.2 which is incompatible.\nbigframes 1.42.0 requires rich<14,>=12.4.4, but you have rich 14.0.0 which is incompatible.\ngcsfs 2025.3.2 requires fsspec==2025.3.2, but you have fsspec 2025.3.0 which is incompatible.\u001b[0m\u001b[31m\n\u001b[0mSuccessfully installed accelerate-0.21.0 fsspec-2025.3.0 nvidia-cublas-cu12-******** nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-nvjitlink-cu12-12.4.127 tokenizers-0.15.2 transformers-4.36.2\nNote: you may need to restart the kernel to use updated packages.\n", "output_type": "stream"}], "execution_count": 1}, {"cell_type": "code", "source": "from transformers import VisionEncoderDecoderModel, AutoProcessor\nimport torch\n\n# Load processor and model\nprocessor = AutoProcessor.from_pretrained(\"microsoft/trocr-base-handwritten\")\nmodel = VisionEncoderDecoderModel.from_pretrained(\"microsoft/trocr-base-handwritten\")\n\ndevice = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\nmodel.to(device)\n\n# Set required token IDs\nmodel.config.decoder_start_token_id = processor.tokenizer.cls_token_id\nmodel.config.pad_token_id = processor.tokenizer.pad_token_id\nmodel.config.eos_token_id = processor.tokenizer.eos_token_id\n\nprint(\"✅ Model and processor loaded on\", device)\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-25T10:40:09.254072Z", "iopub.execute_input": "2025-05-25T10:40:09.254321Z", "iopub.status.idle": "2025-05-25T10:40:50.422460Z", "shell.execute_reply.started": "2025-05-25T10:40:09.254299Z", "shell.execute_reply": "2025-05-25T10:40:50.421700Z"}}, "outputs": [{"name": "stderr", "text": "/usr/local/lib/python3.11/dist-packages/transformers/utils/generic.py:441: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.\n  _torch_pytree._register_pytree_node(\n/usr/local/lib/python3.11/dist-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.\n  _torch_pytree._register_pytree_node(\n2025-05-25 10:40:20.045828: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:477] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\nWARNING: All log messages before absl::InitializeLog() is called are written to STDERR\nE0000 00:00:1748169620.261480      82 cuda_dnn.cc:8310] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\nE0000 00:00:1748169620.334931      82 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n/usr/local/lib/python3.11/dist-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.\n  _torch_pytree._register_pytree_node(\n/usr/local/lib/python3.11/dist-packages/huggingface_hub/file_download.py:943: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n  warnings.warn(\n", "output_type": "stream"}, {"output_type": "display_data", "data": {"text/plain": "preprocessor_config.json:   0%|          | 0.00/224 [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "41ffd63fda994d1e91aa10e9417e0430"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "tokenizer_config.json:   0%|          | 0.00/1.12k [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d0c922d8084844d58126e4b83c927933"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "config.json:   0%|          | 0.00/4.17k [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "79b8951cbca245d19fae644f92ff5b93"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "vocab.json:   0%|          | 0.00/899k [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "7e01d58f10f14b6493341308014a1c8b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "merges.txt:   0%|          | 0.00/456k [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e0147856d6a0485ba5addbfca9ce2208"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "special_tokens_map.json:   0%|          | 0.00/772 [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5ac13044d1d4415ea283ecec079674cf"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "model.safetensors:   0%|          | 0.00/1.33G [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1ae225ec23034aef98a8472c0090822b"}}, "metadata": {}}, {"name": "stderr", "text": "Some weights of VisionEncoderDecoderModel were not initialized from the model checkpoint at microsoft/trocr-base-handwritten and are newly initialized: ['encoder.pooler.dense.weight', 'encoder.pooler.dense.bias']\nYou should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n", "output_type": "stream"}, {"output_type": "display_data", "data": {"text/plain": "generation_config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "006d0078f8954c56a2ed1bc9cf1fff26"}}, "metadata": {}}, {"name": "stdout", "text": "✅ Model and processor loaded on cuda\n", "output_type": "stream"}], "execution_count": 2}, {"cell_type": "code", "source": "import os\nimport pandas as pd\n\nbase_path = \"/kaggle/input/doctor/Doctor’s Handwritten Prescription BD dataset\"\n\n# Load training\ntrain_df = pd.read_csv(os.path.join(base_path, \"Training/training_labels.csv\"))\ntrain_df[\"image_path\"] = train_df[\"IMAGE\"].apply(lambda x: os.path.join(base_path, \"Training/training_words\", x))\ntrain_df[\"label\"] = train_df[\"MEDICINE_NAME\"]\n\n# Load validation\nval_df = pd.read_csv(os.path.join(base_path, \"Validation/validation_labels.csv\"))\nval_df[\"image_path\"] = val_df[\"IMAGE\"].apply(lambda x: os.path.join(base_path, \"Validation/validation_words\", x))\nval_df[\"label\"] = val_df[\"MEDICINE_NAME\"]\n\n# Combine columns\ntrain_df = train_df[[\"image_path\", \"label\"]]\nval_df = val_df[[\"image_path\", \"label\"]]\n\nprint(\"✅ Dataset loaded\")\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-25T10:40:50.423207Z", "iopub.execute_input": "2025-05-25T10:40:50.423734Z", "iopub.status.idle": "2025-05-25T10:40:50.597210Z", "shell.execute_reply.started": "2025-05-25T10:40:50.423705Z", "shell.execute_reply": "2025-05-25T10:40:50.596600Z"}}, "outputs": [{"name": "stdout", "text": "✅ Dataset loaded\n", "output_type": "stream"}], "execution_count": 3}, {"cell_type": "code", "source": "from torch.utils.data import Dataset\nfrom PIL import Image\n\nclass TrOCRDataset(Dataset):\n    def __init__(self, df, processor):\n        self.df = df.reset_index(drop=True)\n        self.processor = processor\n\n    def __len__(self):\n        return len(self.df)\n\n    def __getitem__(self, idx):\n        item = self.df.iloc[idx]\n        image = Image.open(item[\"image_path\"]).convert(\"RGB\")\n        label = item[\"label\"]\n\n        # Optional debug\n        if idx < 3:\n            print(f\"[DEBUG] Loading image {idx}: {item['image_path']} — Label: {label}\")\n\n        pixel_values = self.processor(images=image, return_tensors=\"pt\").pixel_values.squeeze()\n\n        labels = self.processor.tokenizer(\n            label,\n            padding=\"max_length\",\n            max_length=20,\n            truncation=True,\n            return_tensors=\"pt\"\n        ).input_ids.squeeze()\n\n        return {\n            \"pixel_values\": pixel_values,\n            \"labels\": labels\n        }\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-25T10:40:50.597970Z", "iopub.execute_input": "2025-05-25T10:40:50.598510Z", "iopub.status.idle": "2025-05-25T10:40:53.565485Z", "shell.execute_reply.started": "2025-05-25T10:40:50.598483Z", "shell.execute_reply": "2025-05-25T10:40:53.564768Z"}}, "outputs": [], "execution_count": 4}, {"cell_type": "code", "source": "train_dataset = TrOCRDataset(train_df, processor)\nval_dataset = TrOCRDataset(val_df, processor)\n\nprint(\"📊 Train set:\", len(train_dataset))\nprint(\"📊 Validation set:\", len(val_dataset))\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-25T10:40:53.567255Z", "iopub.execute_input": "2025-05-25T10:40:53.567759Z", "iopub.status.idle": "2025-05-25T10:40:53.582266Z", "shell.execute_reply.started": "2025-05-25T10:40:53.567740Z", "shell.execute_reply": "2025-05-25T10:40:53.581612Z"}}, "outputs": [{"name": "stdout", "text": "📊 Train set: 3120\n📊 Validation set: 780\n", "output_type": "stream"}], "execution_count": 5}, {"cell_type": "code", "source": "item = val_dataset[0]\nimage = item[\"pixel_values\"].unsqueeze(0).to(device)\n\nmodel.eval()\nwith torch.no_grad():\n    generated_ids = model.generate(\n        image,\n        max_length=20,\n        do_sample=False,\n        num_beams=1,\n        pad_token_id=processor.tokenizer.pad_token_id,\n        eos_token_id=processor.tokenizer.eos_token_id,\n        decoder_start_token_id=processor.tokenizer.bos_token_id,\n    )\n\n    print(\"Generated IDs:\", generated_ids)\n\n    prediction = processor.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)\n    print(\"Prediction:\", prediction)\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-25T10:40:53.583016Z", "iopub.execute_input": "2025-05-25T10:40:53.583237Z", "iopub.status.idle": "2025-05-25T10:40:55.188936Z", "shell.execute_reply.started": "2025-05-25T10:40:53.583221Z", "shell.execute_reply": "2025-05-25T10:40:55.188091Z"}}, "outputs": [{"name": "stdout", "text": "[DEBUG] Loading image 0: /kaggle/input/doctor/Doctor’s Handwritten Prescription BD dataset/Validation/validation_words/0.png — Label: Aceta\n", "output_type": "stream"}, {"name": "stderr", "text": "/usr/local/lib/python3.11/dist-packages/transformers/generation/utils.py:1518: UserWarning: You have modified the pretrained model configuration to control generation. This is a deprecated strategy to control generation and will be removed soon, in a future version. Please use and modify the model generation configuration (see https://huggingface.co/docs/transformers/generation_strategies#default-text-generation-configuration )\n  warnings.warn(\n", "output_type": "stream"}, {"name": "stdout", "text": "Generated IDs: tensor([[   0,  250,  242, 8152,    2]], device='cuda:0')\nPrediction: ['Aeeta']\n", "output_type": "stream"}], "execution_count": 6}, {"cell_type": "code", "source": "from torch.utils.data import DataLoader\nfrom transformers import AdamW\nfrom tqdm.auto import tqdm\n\ntrain_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)\nval_loader = DataLoader(val_dataset, batch_size=4)\n\noptimizer = AdamW(model.parameters(), lr=5e-5)\n\nmodel.train()\nprint(\"🚀 Starting full training loop...\")\n\n#for epoch in range(3):  # Change this to 5 or more later\n#for epoch in range(3, 6):  # Continue from epoch 3 to epoch 6\n#for epoch in range(3, 5):  # Only train Epoch 4 and 5\nfor epoch in range(3,4):  # Change this to 5 or more later\n\n\n\n    total_loss = 0\n\n    for step, batch in enumerate(tqdm(train_loader, desc=f\"Epoch {epoch+1}\")):\n        pixel_values = batch[\"pixel_values\"].to(device)\n        labels = batch[\"labels\"].to(device)\n\n        outputs = model(pixel_values=pixel_values, labels=labels)\n        loss = outputs.loss\n\n        total_loss += loss.item()\n        loss.backward()\n        optimizer.step()\n        optimizer.zero_grad()\n\n        if step % 10 == 0:\n            print(f\"Step {step} — Loss: {loss.item():.4f}\")\n\n    print(f\"✅ Epoch {epoch+1} finished — Avg Loss: {total_loss / len(train_loader):.4f}\")\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-25T10:59:04.317788Z", "iopub.execute_input": "2025-05-25T10:59:04.318340Z", "iopub.status.idle": "2025-05-25T11:04:42.798531Z", "shell.execute_reply.started": "2025-05-25T10:59:04.318316Z", "shell.execute_reply": "2025-05-25T11:04:42.797961Z"}}, "outputs": [{"name": "stdout", "text": "🚀 Starting full training loop...\n", "output_type": "stream"}, {"output_type": "display_data", "data": {"text/plain": "Epoch 4:   0%|          | 0/780 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "adf0a6ea7f114a7e8d517495cdf07d9e"}}, "metadata": {}}, {"name": "stdout", "text": "Step 0 — Loss: 0.2596\nStep 10 — Loss: 0.2837\nStep 20 — Loss: 0.2610\nStep 30 — Loss: 0.3583\nStep 40 — Loss: 0.2721\nStep 50 — Loss: 0.3772\nStep 60 — Loss: 0.2893\nStep 70 — Loss: 0.2577\nStep 80 — Loss: 0.4156\nStep 90 — Loss: 0.2633\nStep 100 — Loss: 0.2154\nStep 110 — Loss: 0.2434\nStep 120 — Loss: 0.2033\nStep 130 — Loss: 0.2819\nStep 140 — Loss: 0.2939\nStep 150 — Loss: 0.2532\nStep 160 — Loss: 0.3109\nStep 170 — Loss: 0.2606\nStep 180 — Loss: 0.2066\nStep 190 — Loss: 0.2603\nStep 200 — Loss: 0.2825\nStep 210 — Loss: 0.1902\nStep 220 — Loss: 0.2009\nStep 230 — Loss: 0.3220\nStep 240 — Loss: 0.2666\nStep 250 — Loss: 0.2391\nStep 260 — Loss: 0.2958\nStep 270 — Loss: 0.2923\nStep 280 — Loss: 0.2729\nStep 290 — Loss: 0.3233\nStep 300 — Loss: 0.2538\nStep 310 — Loss: 0.2484\n[DEBUG] Loading image 0: /kaggle/input/doctor/Doctor’s Handwritten Prescription BD dataset/Training/training_words/0.png — Label: Aceta\nStep 320 — Loss: 0.3927\nStep 330 — Loss: 0.2991\nStep 340 — Loss: 0.2299\nStep 350 — Loss: 0.2083\nStep 360 — Loss: 0.2594\nStep 370 — Loss: 0.2475\nStep 380 — Loss: 0.1929\nStep 390 — Loss: 0.2701\nStep 400 — Loss: 0.2131\nStep 410 — Loss: 0.2652\n[DEBUG] Loading image 1: /kaggle/input/doctor/Doctor’s Handwritten Prescription BD dataset/Training/training_words/1.png — Label: Aceta\nStep 420 — Loss: 0.2937\nStep 430 — Loss: 0.2570\n[DEBUG] Loading image 2: /kaggle/input/doctor/Doctor’s Handwritten Prescription BD dataset/Training/training_words/2.png — Label: Aceta\nStep 440 — Loss: 0.2326\nStep 450 — Loss: 0.2665\nStep 460 — Loss: 0.2072\nStep 470 — Loss: 0.2435\nStep 480 — Loss: 0.2474\nStep 490 — Loss: 0.2631\nStep 500 — Loss: 0.2786\nStep 510 — Loss: 0.2996\nStep 520 — Loss: 0.2268\nStep 530 — Loss: 0.2526\nStep 540 — Loss: 0.2226\nStep 550 — Loss: 0.2488\nStep 560 — Loss: 0.2130\nStep 570 — Loss: 0.2132\nStep 580 — Loss: 0.2453\nStep 590 — Loss: 0.2389\nStep 600 — Loss: 0.2469\nStep 610 — Loss: 0.2237\nStep 620 — Loss: 0.2339\nStep 630 — Loss: 0.2188\nStep 640 — Loss: 0.2396\nStep 650 — Loss: 0.2015\nStep 660 — Loss: 0.2098\nStep 670 — Loss: 0.1915\nStep 680 — Loss: 0.2319\nStep 690 — Loss: 0.2298\nStep 700 — Loss: 0.2218\nStep 710 — Loss: 0.1881\nStep 720 — Loss: 0.1886\nStep 730 — Loss: 0.2526\nStep 740 — Loss: 0.2103\nStep 750 — Loss: 0.2372\nStep 760 — Loss: 0.2718\nStep 770 — Loss: 0.1948\n✅ Epoch 4 finished — Avg Loss: 0.2707\n", "output_type": "stream"}], "execution_count": 8}, {"cell_type": "code", "source": "model.save_pretrained(\"./trocr-finetuned-full\")\nprocessor.save_pretrained(\"./trocr-finetuned-full\")\nprint(\"✅ Model saved to './trocr-finetuned-full'\")\n", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}]}
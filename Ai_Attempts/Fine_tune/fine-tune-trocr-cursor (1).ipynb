{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.11", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kaggle": {"accelerator": "none", "dataSources": [{"sourceId": 11870605, "sourceType": "datasetVersion", "datasetId": 7459829}, {"sourceId": 11900856, "sourceType": "datasetVersion", "datasetId": 7468992}], "dockerImageVersionId": 31041, "isInternetEnabled": true, "language": "python", "sourceType": "notebook", "isGpuEnabled": false}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": "# ─── Cell 0: install required packages ────────────────────────────────────────\n%pip install -q transformers datasets evaluate jiwer pillow tqdm\n", "metadata": {"_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "trusted": true, "execution": {"iopub.status.busy": "2025-06-01T18:06:23.375634Z", "iopub.execute_input": "2025-06-01T18:06:23.375982Z", "iopub.status.idle": "2025-06-01T18:06:32.405183Z", "shell.execute_reply.started": "2025-06-01T18:06:23.375959Z", "shell.execute_reply": "2025-06-01T18:06:32.403576Z"}}, "outputs": [{"name": "stdout", "text": "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m84.0/84.0 kB\u001b[0m \u001b[31m2.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m193.6/193.6 kB\u001b[0m \u001b[31m7.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.1/3.1 MB\u001b[0m \u001b[31m46.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25h\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\ncesium 0.12.4 requires numpy<3.0,>=2.0, but you have numpy 1.26.4 which is incompatible.\nbigframes 1.42.0 requires rich<14,>=12.4.4, but you have rich 14.0.0 which is incompatible.\ntorch 2.6.0+cu124 requires nvidia-cublas-cu12==********; platform_system == \"Linux\" and platform_machine == \"x86_64\", but you have nvidia-cublas-cu12 ********* which is incompatible.\ntorch 2.6.0+cu124 requires nvidia-cudnn-cu12==********; platform_system == \"Linux\" and platform_machine == \"x86_64\", but you have nvidia-cudnn-cu12 ******** which is incompatible.\ntorch 2.6.0+cu124 requires nvidia-cufft-cu12==********; platform_system == \"Linux\" and platform_machine == \"x86_64\", but you have nvidia-cufft-cu12 ******** which is incompatible.\ntorch 2.6.0+cu124 requires nvidia-curand-cu12==**********; platform_system == \"Linux\" and platform_machine == \"x86_64\", but you have nvidia-curand-cu12 10.3.10.19 which is incompatible.\ntorch 2.6.0+cu124 requires nvidia-cusolver-cu12==11.6.1.9; platform_system == \"Linux\" and platform_machine == \"x86_64\", but you have nvidia-cusolver-cu12 11.7.4.40 which is incompatible.\ntorch 2.6.0+cu124 requires nvidia-cusparse-cu12==12.3.1.170; platform_system == \"Linux\" and platform_machine == \"x86_64\", but you have nvidia-cusparse-cu12 12.5.9.5 which is incompatible.\ntorch 2.6.0+cu124 requires nvidia-nvjitlink-cu12==12.4.127; platform_system == \"Linux\" and platform_machine == \"x86_64\", but you have nvidia-nvjitlink-cu12 12.9.41 which is incompatible.\ngcsfs 2025.3.2 requires fsspec==2025.3.2, but you have fsspec 2025.3.0 which is incompatible.\u001b[0m\u001b[31m\n\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "output_type": "stream"}], "execution_count": 4}, {"cell_type": "code", "source": "# Basic imports\nimport os\nimport torch\nimport evaluate\nimport numpy as np\nimport pandas as pd\nimport matplotlib.pyplot as plt\nfrom PIL import Image\nfrom tqdm.notebook import tqdm\nfrom dataclasses import dataclass\nfrom torch.utils.data import Dataset\n\n# Transformers imports\nfrom transformers import (\n    VisionEncoderDecoderModel,\n    TrOCRProcessor,\n    Seq2SeqTrainer,\n    Seq2SeqTrainingArguments,\n    default_data_collator\n)\n\n# Display settings\n%matplotlib inline\nplt.style.use('seaborn')", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-06-01T18:06:35.627507Z", "iopub.execute_input": "2025-06-01T18:06:35.627995Z", "iopub.status.idle": "2025-06-01T18:06:39.365489Z", "shell.execute_reply.started": "2025-06-01T18:06:35.627952Z", "shell.execute_reply": "2025-06-01T18:06:39.364129Z"}}, "outputs": [{"name": "stderr", "text": "/tmp/ipykernel_35/3778398829.py:24: MatplotlibDeprecationWarning: The seaborn styles shipped by Matplotlib are deprecated since 3.6, as they no longer correspond to the styles shipped by seaborn. However, they will remain available as 'seaborn-v0_8-<style>'. Alternatively, directly use the seaborn API instead.\n  plt.style.use('seaborn')\n", "output_type": "stream"}], "execution_count": 5}, {"cell_type": "code", "source": "# Environment settings\nos.environ[\"TOKENIZERS_PARALLELISM\"] = 'false'\ndevice = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\nprint(f\"Using device: {device}\")\n\n# Set random seed for reproducibility\ndef seed_everything(seed_value):\n    np.random.seed(seed_value)\n    torch.manual_seed(seed_value)\n    torch.cuda.manual_seed_all(seed_value)\n    torch.backends.cudnn.deterministic = True\n    torch.backends.cudnn.benchmark = False\n\nseed_everything(42)\n\n@dataclass(frozen=True)\nclass DataConfig:\n    ROOT_DIR: str = \"/kaggle/input/doctor/Doctor’s Handwritten Prescription BD dataset\"\n    TRAIN_DIR: str = \"Training\"\n    VAL_DIR: str = \"Validation\"\n    TEST_DIR: str = \"Testing\"\n\n@dataclass(frozen=True)\nclass TrainingConfig:\n    BATCH_SIZE: int = 16\n    EPOCHS: int = 20\n    LEARNING_RATE: float = 0.00005\n    WARMUP_RATIO: float = 0.2\n    WEIGHT_DECAY: float = 0.05\n    MAX_LENGTH: int = 128\n    GRADIENT_CLIP: float = 1.0\n\n@dataclass(frozen=True)\nclass ModelConfig:\n    MODEL_NAME: str = 'microsoft/trocr-base-handwritten'", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-06-01T18:06:43.744093Z", "iopub.execute_input": "2025-06-01T18:06:43.745445Z", "iopub.status.idle": "2025-06-01T18:06:43.765947Z", "shell.execute_reply.started": "2025-06-01T18:06:43.745411Z", "shell.execute_reply": "2025-06-01T18:06:43.764462Z"}}, "outputs": [{"name": "stdout", "text": "Using device: cpu\n", "output_type": "stream"}], "execution_count": 6}, {"cell_type": "code", "source": "class MedicineOCRDataset(Dataset):\n    def __init__(self, split='train', processor=None, max_target_length=128):\n        self.processor = processor\n        self.max_target_length = max_target_length\n        self.training = split == 'train'\n        \n        # Set up paths based on split\n        if split == 'train':\n            base_dir = os.path.join(DataConfig.ROOT_DIR, DataConfig.TRAIN_DIR)\n            self.csv_path = os.path.join(base_dir, 'training_labels.csv')\n            self.img_dir = os.path.join(base_dir, 'training_words')\n        elif split == 'val':\n            base_dir = os.path.join(DataConfig.ROOT_DIR, DataConfig.VAL_DIR)\n            self.csv_path = os.path.join(base_dir, 'validation_labels.csv')\n            self.img_dir = os.path.join(base_dir, 'validation_words')\n        else:  # test\n            base_dir = os.path.join(DataConfig.ROOT_DIR, DataConfig.TEST_DIR)\n            self.csv_path = os.path.join(base_dir, 'testing_labels.csv')\n            self.img_dir = os.path.join(base_dir, 'testing_words')\n            \n        # Read CSV file\n        self.df = pd.read_csv(self.csv_path)\n        print(f\"Loaded {len(self.df)} samples for {split} split\")\n        print(f\"CSV columns: {self.df.columns.tolist()}\")\n        \n        # Print sample data statistics\n        self.analyze_data()\n    \n    def analyze_data(self):\n        \"\"\"Analyze the dataset statistics\"\"\"\n        medicine_lengths = self.df['MEDICINE_NAME'].str.len()\n        print(f\"\\nDataset Statistics:\")\n        print(f\"Total samples: {len(self.df)}\")\n        print(f\"Average medicine name length: {medicine_lengths.mean():.1f}\")\n        print(f\"Min length: {medicine_lengths.min()}\")\n        print(f\"Max length: {medicine_lengths.max()}\")\n        \n    def apply_augmentation(self, image):\n        # Convert to numpy array\n        img_array = np.array(image)\n        \n        # Random brightness (50% chance)\n        if np.random.random() > 0.5:\n            brightness_factor = np.random.uniform(0.7, 1.3)\n            img_array = (img_array * brightness_factor).clip(0, 255).astype(np.uint8)\n        \n        # Random contrast (50% chance)\n        if np.random.random() > 0.5:\n            contrast_factor = np.random.uniform(0.7, 1.3)\n            mean = np.mean(img_array)\n            img_array = np.clip((img_array - mean) * contrast_factor + mean, 0, 255).astype(np.uint8)\n        \n        # Random Gaussian noise (30% chance)\n        if np.random.random() > 0.7:\n            noise = np.random.normal(0, 5, img_array.shape)\n            img_array = np.clip(img_array + noise, 0, 255).astype(np.uint8)\n        \n        # Random rotation (-5 to 5 degrees, 40% chance)\n        if np.random.random() > 0.6:\n            angle = np.random.uniform(-5, 5)\n            image = Image.fromarray(img_array)\n            image = image.rotate(angle, expand=False, fillcolor=(255, 255, 255))\n            return image\n            \n        return Image.fromarray(img_array)\n\n    def __len__(self):\n        return len(self.df)\n\n    def __getitem__(self, idx):\n        # Get image path and label\n        row = self.df.iloc[idx]\n        img_name = row['IMAGE']\n        text = str(row['MEDICINE_NAME'])\n        \n        # Load and process image\n        image_path = os.path.join(self.img_dir, img_name)\n        \n        # Check if image exists\n        if not os.path.exists(image_path):\n            raise FileNotFoundError(f\"Image not found at: {image_path}\")\n            \n        image = Image.open(image_path).convert('RGB')\n        \n        # Apply augmentation for training\n        if self.training:\n            image = self.apply_augmentation(image)\n        \n        # Process image with TrOCR processor\n        pixel_values = self.processor(image, return_tensors='pt').pixel_values\n        \n        # Process text\n        labels = self.processor.tokenizer(\n            text,\n            padding='max_length',\n            max_length=self.max_target_length,\n            truncation=True\n        ).input_ids\n        \n        # Replace padding token id with -100\n        labels = [label if label != self.processor.tokenizer.pad_token_id else -100 for label in labels]\n        \n        return {\n            \"pixel_values\": pixel_values.squeeze(),\n            \"labels\": torch.tensor(labels)\n        }", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-06-01T18:06:47.100617Z", "iopub.execute_input": "2025-06-01T18:06:47.101733Z", "iopub.status.idle": "2025-06-01T18:06:47.121238Z", "shell.execute_reply.started": "2025-06-01T18:06:47.101692Z", "shell.execute_reply": "2025-06-01T18:06:47.119847Z"}}, "outputs": [], "execution_count": 7}, {"cell_type": "code", "source": "# Initialize processor and model\nprocessor = TrOCRProcessor.from_pretrained(ModelConfig.MODEL_NAME)\nmodel = VisionEncoderDecoderModel.from_pretrained(ModelConfig.MODEL_NAME)\nmodel.to(device)\n\n# Configure the model\nmodel.config.decoder_start_token_id = processor.tokenizer.cls_token_id\nmodel.config.pad_token_id = processor.tokenizer.pad_token_id\nmodel.config.vocab_size = model.config.decoder.vocab_size\nmodel.config.eos_token_id = processor.tokenizer.sep_token_id\nmodel.config.max_length = TrainingConfig.MAX_LENGTH\nmodel.config.early_stopping = True\nmodel.config.no_repeat_ngram_size = 3\nmodel.config.length_penalty = 2.0\nmodel.config.num_beams = 4\n\n# Print model information\nprint(f\"Model parameters: {sum(p.numel() for p in model.parameters()):,}\")", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-06-01T18:07:09.877596Z", "iopub.execute_input": "2025-06-01T18:07:09.878012Z", "iopub.status.idle": "2025-06-01T18:07:28.775251Z", "shell.execute_reply.started": "2025-06-01T18:07:09.877985Z", "shell.execute_reply": "2025-06-01T18:07:28.774129Z"}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": "preprocessor_config.json:   0%|          | 0.00/224 [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "c3658fe730a04f5b9c3681f8e33d5ec9"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "tokenizer_config.json:   0%|          | 0.00/1.12k [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "fd2b025c95d1443bbe0440d338fe1651"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "vocab.json:   0%|          | 0.00/899k [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "cbaa6f4f734346a28f104679b048e3c1"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "merges.txt:   0%|          | 0.00/456k [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "7c16880addfc4e6a9e01bdcaece85f7a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "special_tokens_map.json:   0%|          | 0.00/772 [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "f2f3ec3fe3024e64bb849958654c43dc"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "config.json:   0%|          | 0.00/4.17k [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b6961915f43e4021b19f26be8ed3dfdf"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "model.safetensors:   0%|          | 0.00/1.33G [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "061d1d715e234a1a97dce6d5515b054c"}}, "metadata": {}}, {"name": "stderr", "text": "Config of the encoder: <class 'transformers.models.vit.modeling_vit.ViTModel'> is overwritten by shared encoder config: ViTConfig {\n  \"attention_probs_dropout_prob\": 0.0,\n  \"encoder_stride\": 16,\n  \"hidden_act\": \"gelu\",\n  \"hidden_dropout_prob\": 0.0,\n  \"hidden_size\": 768,\n  \"image_size\": 384,\n  \"initializer_range\": 0.02,\n  \"intermediate_size\": 3072,\n  \"layer_norm_eps\": 1e-12,\n  \"model_type\": \"vit\",\n  \"num_attention_heads\": 12,\n  \"num_channels\": 3,\n  \"num_hidden_layers\": 12,\n  \"patch_size\": 16,\n  \"pooler_act\": \"tanh\",\n  \"pooler_output_size\": 768,\n  \"qkv_bias\": false,\n  \"torch_dtype\": \"float32\",\n  \"transformers_version\": \"4.51.3\"\n}\n\nConfig of the decoder: <class 'transformers.models.trocr.modeling_trocr.TrOCRForCausalLM'> is overwritten by shared decoder config: TrOCRConfig {\n  \"activation_dropout\": 0.0,\n  \"activation_function\": \"gelu\",\n  \"add_cross_attention\": true,\n  \"attention_dropout\": 0.0,\n  \"bos_token_id\": 0,\n  \"classifier_dropout\": 0.0,\n  \"cross_attention_hidden_size\": 768,\n  \"d_model\": 1024,\n  \"decoder_attention_heads\": 16,\n  \"decoder_ffn_dim\": 4096,\n  \"decoder_layerdrop\": 0.0,\n  \"decoder_layers\": 12,\n  \"decoder_start_token_id\": 2,\n  \"dropout\": 0.1,\n  \"eos_token_id\": 2,\n  \"init_std\": 0.02,\n  \"is_decoder\": true,\n  \"layernorm_embedding\": true,\n  \"max_position_embeddings\": 512,\n  \"model_type\": \"trocr\",\n  \"pad_token_id\": 1,\n  \"scale_embedding\": false,\n  \"torch_dtype\": \"float32\",\n  \"transformers_version\": \"4.51.3\",\n  \"use_cache\": false,\n  \"use_learned_position_embeddings\": true,\n  \"vocab_size\": 50265\n}\n\nSome weights of VisionEncoderDecoderModel were not initialized from the model checkpoint at microsoft/trocr-base-handwritten and are newly initialized: ['encoder.pooler.dense.bias', 'encoder.pooler.dense.weight']\nYou should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n", "output_type": "stream"}, {"output_type": "display_data", "data": {"text/plain": "generation_config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "7e70f0aced9f4dc98ad5b4fb35922daa"}}, "metadata": {}}, {"name": "stdout", "text": "Model parameters: 333,921,792\n", "output_type": "stream"}], "execution_count": 8}, {"cell_type": "code", "source": "# Create datasets\ntrain_dataset = MedicineOCRDataset(split='train', processor=processor)\nval_dataset = MedicineOCRDataset(split='val', processor=processor)\ntest_dataset = MedicineOCRDataset(split='test', processor=processor)\n\n# Set training flag\ntrain_dataset.training = True\nval_dataset.training = False\ntest_dataset.training = False", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-06-01T18:07:34.795901Z", "iopub.execute_input": "2025-06-01T18:07:34.796259Z", "iopub.status.idle": "2025-06-01T18:07:34.886577Z", "shell.execute_reply.started": "2025-06-01T18:07:34.796235Z", "shell.execute_reply": "2025-06-01T18:07:34.885619Z"}}, "outputs": [{"name": "stdout", "text": "Loaded 3120 samples for train split\nCSV columns: ['IMAGE', 'MEDICINE_NAME', 'GENERIC_NAME']\n\nDataset Statistics:\nTotal samples: 3120\nAverage medicine name length: 6.3\nMin length: 2\nMax length: 11\nLoaded 780 samples for val split\nCSV columns: ['IMAGE', 'MEDICINE_NAME', 'GENERIC_NAME']\n\nDataset Statistics:\nTotal samples: 780\nAverage medicine name length: 6.3\nMin length: 2\nMax length: 11\nLoaded 780 samples for test split\nCSV columns: ['IMAGE', 'MEDICINE_NAME', 'GENERIC_NAME']\n\nDataset Statistics:\nTotal samples: 780\nAverage medicine name length: 6.3\nMin length: 2\nMax length: 11\n", "output_type": "stream"}], "execution_count": 9}, {"cell_type": "code", "source": "from transformers import EarlyStoppingCallback\n\n# Initialize CER metric\ncer_metric = evaluate.load('cer')\n\ndef compute_metrics(pred):\n    labels_ids = pred.label_ids\n    pred_ids = pred.predictions\n    \n    # Replace -100 with pad token id\n    labels_ids[labels_ids == -100] = processor.tokenizer.pad_token_id\n    \n    # Decode predictions and labels\n    pred_str = processor.batch_decode(pred_ids, skip_special_tokens=True)\n    label_str = processor.batch_decode(labels_ids, skip_special_tokens=True)\n    \n    # Compute CER\n    cer = cer_metric.compute(predictions=pred_str, references=label_str)\n    \n    # Print some examples\n    if len(pred_str) > 0:\n        print(\"\\nSample predictions:\")\n        for i in range(min(3, len(pred_str))):\n            print(f\"Pred : {pred_str[i]}\")\n            print(f\"Label: {label_str[i]}\")\n            print()\n    \n    return {\"cer\": cer}\n\n# Training arguments\ntraining_args = Seq2SeqTrainingArguments(\n    predict_with_generate=True,\n    eval_strategy=\"epoch\",\n    per_device_train_batch_size=TrainingConfig.BATCH_SIZE,\n    per_device_eval_batch_size=TrainingConfig.BATCH_SIZE,\n    fp16=True,\n    output_dir=\"./trocr_medicine\",\n    logging_dir=\"./logs\",\n    logging_strategy=\"epoch\",\n    save_strategy=\"epoch\",\n    save_total_limit=3,\n    load_best_model_at_end=True,\n    metric_for_best_model=\"cer\",\n    greater_is_better=False,\n    num_train_epochs=TrainingConfig.EPOCHS,\n    learning_rate=TrainingConfig.LEARNING_RATE,\n    warmup_ratio=TrainingConfig.WARMUP_RATIO,\n    weight_decay=TrainingConfig.WEIGHT_DECAY,\n    report_to=\"tensorboard\",\n    # Add early stopping\n   # early_stopping_patience=3,\n    # Add gradient clipping\n    max_grad_norm=TrainingConfig.GRADIENT_CLIP,\n    # Add learning rate scheduling\n    lr_scheduler_type=\"cosine\"\n)\n\n# Initialize trainer\ntrainer = Seq2SeqTrainer(\n    model=model,\n    args=training_args,\n    train_dataset=train_dataset,\n    eval_dataset=val_dataset,\n    compute_metrics=compute_metrics,\n    data_collator=default_data_collator,\n    tokenizer=processor.feature_extractor,\n    callbacks=[ EarlyStoppingCallback(early_stopping_patience=3) ]\n\n)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-29T14:13:35.287609Z", "iopub.execute_input": "2025-05-29T14:13:35.288346Z", "iopub.status.idle": "2025-05-29T14:13:36.032969Z", "shell.execute_reply.started": "2025-05-29T14:13:35.288323Z", "shell.execute_reply": "2025-05-29T14:13:36.032377Z"}}, "outputs": [{"name": "stderr", "text": "/usr/local/lib/python3.11/dist-packages/transformers/models/trocr/processing_trocr.py:152: FutureWarning: `feature_extractor` is deprecated and will be removed in v5. Use `image_processor` instead.\n  warnings.warn(\n/tmp/ipykernel_35/2268061500.py:59: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Seq2SeqTrainer.__init__`. Use `processing_class` instead.\n  trainer = Seq2SeqTrainer(\n", "output_type": "stream"}], "execution_count": 10}, {"cell_type": "code", "source": "# Train the model\nprint(\"Starting training...\")\ntrainer.train()\n\n# Evaluate on validation set\nprint(\"\\nEvaluating on validation set...\")\neval_results = trainer.evaluate()\nprint(f\"Validation CER: {eval_results['eval_cer']:.4f}\")\n\n# Save the final model\ntrainer.save_model(\"./final_model\")\nprint(\"\\nTraining completed and model saved!\")", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-29T14:13:55.757522Z", "iopub.execute_input": "2025-05-29T14:13:55.758295Z", "iopub.status.idle": "2025-05-29T14:46:29.654180Z", "shell.execute_reply.started": "2025-05-29T14:13:55.758261Z", "shell.execute_reply": "2025-05-29T14:46:29.653494Z"}}, "outputs": [{"name": "stdout", "text": "Starting training...\n", "output_type": "stream"}, {"name": "stderr", "text": "`loss_type=None` was set in the config but it is unrecognised.Using the default loss: `ForCausalLMLoss`.\n", "output_type": "stream"}, {"output_type": "display_data", "data": {"text/plain": "<IPython.core.display.HTML object>", "text/html": "\n    <div>\n      \n      <progress value='780' max='3900' style='width:300px; height:20px; vertical-align: middle;'></progress>\n      [ 780/3900 30:37 < 2:02:49, 0.42 it/s, Epoch 4/20]\n    </div>\n    <table border=\"1\" class=\"dataframe\">\n  <thead>\n <tr style=\"text-align: left;\">\n      <th>Epoch</th>\n      <th>Training Loss</th>\n      <th>Validation Loss</th>\n      <th>Cer</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <td>1</td>\n      <td>1.992800</td>\n      <td>0.185730</td>\n      <td>0.151822</td>\n    </tr>\n    <tr>\n      <td>2</td>\n      <td>0.103200</td>\n      <td>0.202704</td>\n      <td>0.413360</td>\n    </tr>\n    <tr>\n      <td>3</td>\n      <td>0.093400</td>\n      <td>0.158800</td>\n      <td>0.470445</td>\n    </tr>\n    <tr>\n      <td>4</td>\n      <td>0.113300</td>\n      <td>0.165389</td>\n      <td>0.456680</td>\n    </tr>\n  </tbody>\n</table><p>"}, "metadata": {}}, {"name": "stderr", "text": "/usr/local/lib/python3.11/dist-packages/transformers/generation/utils.py:1667: UserWarning: You have modified the pretrained model configuration to control generation. This is a deprecated strategy to control generation and will be removed in v5. Please use and modify the model generation configuration (see https://huggingface.co/docs/transformers/generation_strategies#default-text-generation-configuration )\n  warnings.warn(\n", "output_type": "stream"}, {"name": "stdout", "text": "\nSample predictions:\nPred : Aceta\nLabel: Aceta\n\nPred : Aceta\nLabel: Aceta\n\nPred : Aceta\nLabel: Aceta\n\n", "output_type": "stream"}, {"name": "stderr", "text": "/usr/local/lib/python3.11/dist-packages/transformers/modeling_utils.py:3339: UserWarning: Moving the following attributes in the config to the generation config: {'max_length': 128, 'early_stopping': True, 'num_beams': 4, 'length_penalty': 2.0, 'no_repeat_ngram_size': 3}. You are seeing this warning because you've set generation parameters in the model config, as opposed to in the generation config.\n  warnings.warn(\n", "output_type": "stream"}, {"name": "stdout", "text": "\nSample predictions:\nPred : Ac\nLabel: Aceta\n\nPred : Ac\nLabel: Aceta\n\nPred : Aceta\nLabel: Aceta\n\n\nSample predictions:\nPred : Ac\nLabel: Aceta\n\nPred : Ais\nLabel: Aceta\n\nPred : Ac\nLabel: Aceta\n\n\nSample predictions:\nPred : Ac\nLabel: Aceta\n\nPred : Alroc\nLabel: Aceta\n\nPred : Ac\nLabel: Aceta\n\n", "output_type": "stream"}, {"name": "stderr", "text": "There were missing keys in the checkpoint model loaded: ['decoder.output_projection.weight'].\n", "output_type": "stream"}, {"name": "stdout", "text": "\nEvaluating on validation set...\n", "output_type": "stream"}, {"output_type": "display_data", "data": {"text/plain": "<IPython.core.display.HTML object>", "text/html": "\n    <div>\n      \n      <progress value='49' max='49' style='width:300px; height:20px; vertical-align: middle;'></progress>\n      [49/49 01:47]\n    </div>\n    "}, "metadata": {}}, {"name": "stdout", "text": "\nSample predictions:\nPred : Aceta\nLabel: Aceta\n\nPred : Aceta\nLabel: Aceta\n\nPred : Aceta\nLabel: Aceta\n\nValidation CER: 0.1518\n\nTraining completed and model saved!\n", "output_type": "stream"}], "execution_count": 11}, {"cell_type": "code", "source": "def predict_text(image_path):\n    \"\"\"Function to predict text from a single image\"\"\"\n    # Load and preprocess image\n    image = Image.open(image_path).convert('RGB')\n    pixel_values = processor(image, return_tensors=\"pt\").pixel_values.to(device)\n    \n    # Generate prediction\n    generated_ids = model.generate(pixel_values)\n    generated_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]\n    \n    # Display results\n    plt.figure(figsize=(10, 4))\n    plt.imshow(image)\n    plt.axis('off')\n    plt.title(f\"Predicted: {generated_text}\")\n    plt.show()\n    \n    return generated_text\n\n# Test on a sample image from test set\nif len(test_dataset) > 0:\n    test_image = os.path.join(test_dataset.img_dir, test_dataset.df.iloc[0]['IMAGE'])\n    predicted_text = predict_text(test_image)\n    print(f\"Ground Truth: {test_dataset.df.iloc[0]['MEDICINE_NAME']}\")\n    print(f\"Predicted: {predicted_text}\")", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-06-01T18:07:53.322295Z", "iopub.execute_input": "2025-06-01T18:07:53.323536Z", "iopub.status.idle": "2025-06-01T18:08:06.777888Z", "shell.execute_reply.started": "2025-06-01T18:07:53.323482Z", "shell.execute_reply": "2025-06-01T18:08:06.776308Z"}}, "outputs": [{"name": "stderr", "text": "/usr/local/lib/python3.11/dist-packages/transformers/generation/utils.py:1667: UserWarning: You have modified the pretrained model configuration to control generation. This is a deprecated strategy to control generation and will be removed in v5. Please use and modify the model generation configuration (see https://huggingface.co/docs/transformers/generation_strategies#default-text-generation-configuration )\n  warnings.warn(\n", "output_type": "stream"}, {"output_type": "display_data", "data": {"text/plain": "<Figure size 1000x400 with 1 Axes>", "image/png": "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\n"}, "metadata": {}}, {"name": "stdout", "text": "Ground Truth: <PERSON><PERSON>\nPredicted: Acute .\n", "output_type": "stream"}], "execution_count": 10}, {"cell_type": "code", "source": "# Test TrOCR fine-tuned model on custom images\nimport os\nimport torch\nfrom PIL import Image\nfrom IPython.display import display\nfrom transformers import TrOCRProcessor, VisionEncoderDecoderModel\n\n# 1) Load your fine-tuned processor & model\nmodel_dir = \"/kaggle/working/final_model\"  # ← replace with the folder where you saved your fine-tuned model\nprocessor = TrOCRProcessor.from_pretrained(model_dir)\nmodel     = VisionEncoderDecoderModel.from_pretrained(model_dir)\n\ndevice = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\nmodel.to(device)\nmodel.eval()\n\n# 2) Point to your custom images folder\nimage_dir = \"/kaggle/input/images\"\n\ndef is_image_file(fname):\n    return fname.lower().endswith((\".png\", \".jpg\", \".jpeg\", \".bmp\"))\n\n# 3) Loop, display and predict\nfor fname in sorted(os.listdir(image_dir)):\n    if not is_image_file(fname):\n        continue\n\n    image_path = os.path.join(image_dir, fname)\n    img = Image.open(image_path).convert(\"RGB\")\n    display(img)\n\n    # Preprocess image and run inference\n    px = processor(images=img, return_tensors=\"pt\").pixel_values.to(device)\n    generated_ids = model.generate(px)\n    pred = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]\n\n    print(f\"Prediction for {fname}: {pred}\\n\")\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-06-01T18:08:18.815698Z", "iopub.execute_input": "2025-06-01T18:08:18.816149Z", "iopub.status.idle": "2025-06-01T18:08:18.911195Z", "shell.execute_reply.started": "2025-06-01T18:08:18.816120Z", "shell.execute_reply": "2025-06-01T18:08:18.909574Z"}}, "outputs": [{"traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_35/1547391026.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      8\u001b[0m \u001b[0;31m# 1) Load your fine-tuned processor & model\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[0mmodel_dir\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m\"/kaggle/working/final_model\"\u001b[0m  \u001b[0;31m# ← replace with the folder where you saved your fine-tuned model\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 10\u001b[0;31m \u001b[0mprocessor\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mTrOCRProcessor\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfrom_pretrained\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel_dir\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     11\u001b[0m \u001b[0mmodel\u001b[0m     \u001b[0;34m=\u001b[0m \u001b[0mVisionEncoderDecoderModel\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfrom_pretrained\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel_dir\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     12\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/transformers/processing_utils.py\u001b[0m in \u001b[0;36mfrom_pretrained\u001b[0;34m(cls, pretrained_model_name_or_path, cache_dir, force_download, local_files_only, token, revision, **kwargs)\u001b[0m\n\u001b[1;32m   1077\u001b[0m             \u001b[0mkwargs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"token\"\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtoken\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1078\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1079\u001b[0;31m         \u001b[0margs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_get_arguments_from_pretrained\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpretrained_model_name_or_path\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1080\u001b[0m         \u001b[0mprocessor_dict\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_processor_dict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpretrained_model_name_or_path\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1081\u001b[0m         \u001b[0mprocessor_dict\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mupdate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m{\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mk\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mitems\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0mk\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mprocessor_dict\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mkeys\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/transformers/processing_utils.py\u001b[0m in \u001b[0;36m_get_arguments_from_pretrained\u001b[0;34m(cls, pretrained_model_name_or_path, **kwargs)\u001b[0m\n\u001b[1;32m   1141\u001b[0m                 \u001b[0mattribute_class\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_possibly_dynamic_module\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mclass_name\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1142\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1143\u001b[0;31m             \u001b[0margs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mattribute_class\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfrom_pretrained\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpretrained_model_name_or_path\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1144\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1145\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/transformers/models/auto/tokenization_auto.py\u001b[0m in \u001b[0;36mfrom_pretrained\u001b[0;34m(cls, pretrained_model_name_or_path, *inputs, **kwargs)\u001b[0m\n\u001b[1;32m   1023\u001b[0m         \u001b[0mmodel_type\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mconfig_class_to_model_type\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mconfig\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1024\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mmodel_type\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1025\u001b[0;31m             \u001b[0mtokenizer_class_py\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtokenizer_class_fast\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mTOKENIZER_MAPPING\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mconfig\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1026\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1027\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mtokenizer_class_fast\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0muse_fast\u001b[0m \u001b[0;32mor\u001b[0m \u001b[0mtokenizer_class_py\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/transformers/models/auto/auto_factory.py\u001b[0m in \u001b[0;36m__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m    776\u001b[0m                 \u001b[0mmodel_name\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_model_mapping\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mmtype\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    777\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_load_attr_from_module\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmtype\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmodel_name\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 778\u001b[0;31m         \u001b[0;32mraise\u001b[0m \u001b[0mKeyError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    779\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    780\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_load_attr_from_module\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmodel_type\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mattr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyError\u001b[0m: <class 'transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig'>"], "ename": "KeyError", "evalue": "<class 'transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig'>", "output_type": "error"}], "execution_count": 11}, {"cell_type": "code", "source": "", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}]}
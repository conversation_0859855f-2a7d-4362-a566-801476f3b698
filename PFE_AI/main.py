from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File
from fastapi.responses import JSONResponse
import google.generativeai as genai
import os
from PIL import Image
from io import BytesIO

app = FastAPI()

#genai.configure(api_key=os.getenv("AIzaSyAiq-XxFisPAPR9RTvx3zzIeLQxABb2CbM"))
genai.configure(api_key="AIzaSyAiq-XxFisPAPR9RTvx3zzIeLQxABb2CbM")

model = genai.GenerativeModel('gemini-1.5-flash')
#model = genai.GenerativeModel('gemini-2.5-flash')


@app.post("/extract")
async def extract_text(file: UploadFile = File(...)):
    contents = await file.read()
    image = Image.open(BytesIO(contents))

    prompt = (
        "Extract a JSON array of medicines with fields: medicine, dosage, frequency, duration, and timing "
        "from this handwritten prescription. Return only raw JSON. Do not format it inside code blocks."
    )

    response = model.generate_content([prompt, image])

    # Try to parse the JSON string into Python object
    import json
    try:
        cleaned = response.text.strip("` \n")  # strip code block backticks
        json_data = json.loads(cleaned)
        return JSONResponse(content=json_data)
    except Exception as e:
        return {"error": "Failed to parse JSON", "raw": response.text}

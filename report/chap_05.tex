\chapter{Sprint 3: Order Management and Stock Management Implementation}

\section{Introduction}
This chapter details the design and implementation of Sprint 3, focusing on comprehensive order management and stock management features for the MED4SOLUTIONS platform. Sprint 3 establishes the core e-commerce functionality enabling patients to browse products, manage shopping baskets, place orders, and track delivery status, while providing pharmacists with robust inventory management and order processing capabilities across mobile and web platforms.

\section{Sprint 3 Requirements Analysis}

\subsection{Order Management Requirements}
The order management system encompasses the complete order lifecycle from product browsing to delivery confirmation. Key requirements include:

\begin{itemize}
    \item \textbf{Shopping Basket Management}: Patients can add/remove products, view basket contents, and modify quantities before order confirmation.
    \item \textbf{Order Processing}: Seamless order confirmation with delivery preferences, payment calculation, and status tracking.
    \item \textbf{Order Tracking}: Real-time order status updates from confirmation through delivery completion.
    \item \textbf{Order History}: Comprehensive order history with detailed information for patients and pharmacists.
    \item \textbf{Delivery Management}: Integration with delivery personnel for status updates and completion confirmation.
\end{itemize}

\subsection{Stock Management Requirements}
The stock management system provides comprehensive inventory control for pharmacists:

\begin{itemize}
    \item \textbf{Product Inventory}: Complete product catalog management with categories, pricing, and stock levels.
    \item \textbf{Stock Status Tracking}: Real-time inventory monitoring with automatic stock status updates.
    \item \textbf{Product Information Management}: Detailed product data including descriptions, expiry dates, suppliers, and barcodes.
    \item \textbf{Category Management}: Hierarchical product categorization for efficient organization and filtering.
    \item \textbf{Pharmacy-specific Inventory}: Multi-pharmacy support with isolated inventory management.
\end{itemize}

\section{Sprint 3 Use Case Analysis}

\subsection{Order Management Use Case Diagram}
Figure \ref{fig:order_usecase} illustrates the order management use cases, showing interactions between patients, pharmacists, and delivery personnel in the order processing workflow.

\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{img/sprint3/order_usecase.png}
\caption{Order Management Use Case Diagram}
\label{fig:order_usecase}
\end{figure}

The order management system supports multiple user roles with specific capabilities. Patients can browse products, manage baskets, and track orders. Pharmacists can process orders and manage inventory. Delivery personnel can update order status and confirm deliveries.

\subsection{Stock Management Use Case Diagram}
Figure \ref{fig:stock_usecase} presents the stock management use cases, focusing on inventory control and product management capabilities for pharmacists.

\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{img/sprint3/stock_usecase.png}
\caption{Stock Management Use Case Diagram}
\label{fig:stock_usecase}
\end{figure}

The stock management system provides comprehensive inventory control with automated stock status tracking, category management, and detailed product information handling. The system ensures accurate inventory levels and supports multi-pharmacy operations.

\section{Sprint 3 Backlog}
The Sprint 3 backlog focuses on implementing order management and stock management features extracted from the main product backlog. This sprint covers user stories related to e-commerce functionality, inventory management, and delivery coordination for both mobile and web platforms.

\begin{center}
\begin{longtable}{|c|>{\raggedright\arraybackslash}p{3.2cm}|c|>{\raggedright\arraybackslash}p{3.5cm}|c|>{\raggedright\arraybackslash}p{3.5cm}|c|}
\hline
\textbf{ID\_F} & \textbf{Feature} & \textbf{ID\_U} & \textbf{User Story} & \textbf{ID\_T} & \textbf{Task} & \textbf{State} \\
\hline
\endfirsthead

\hline
\textbf{ID\_F} & \textbf{Feature} & \textbf{ID\_U} & \textbf{User Story} & \textbf{ID\_T} & \textbf{Task} & \textbf{State} \\
\hline
\endhead

\hline
\endfoot

\hline
\caption{Sprint 3 Backlog with Tasks}
\label{table:sprint3_backlog}
\endlastfoot

4 & Order Management & 4.1 & As a patient, I want to track my order in real time & 4.1.1 & Implement real-time order status tracking system & Done \\
\cline{5-7}
 &  &  &  & 4.1.2 & Create order status display interface & Done \\
\cline{5-7}
 &  &  &  & 4.1.3 & Add order status update notifications & Done \\
\cline{3-7}
 &  & 4.2 & As a patient, I want to receive notifications for order status updates & 4.2.1 & Implement order notification system & Done \\
\cline{5-7}
 &  &  &  & 4.2.2 & Create notification delivery mechanisms & Done \\
\cline{3-7}
 &  & 4.3 & As a patient, I want to confirm delivery upon receipt & 4.3.1 & Implement delivery confirmation interface & Done \\
\cline{5-7}
 &  &  &  & 4.3.2 & Add delivery confirmation validation & Done \\
\cline{3-7}
 &  & 4.4 & As a patient, I want to be able to view the items available for order in my interface & 4.4.1 & Create product browsing interface & Done \\
\cline{5-7}
 &  &  &  & 4.4.2 & Implement product search and filtering & Done \\
\cline{5-7}
 &  &  &  & 4.4.3 & Add product category navigation & Done \\
\cline{3-7}
 &  & 4.5 & As a patient, I want to add items to my cart and submit my order for processing & 4.5.1 & Implement basket management functionality & Done \\
\cline{5-7}
 &  &  &  & 4.5.2 & Create order submission and confirmation & Done \\
\cline{3-7}
 &  & 4.7 & As a patient, I would like to add a delivery preference so that I can receive my medication at a specific time & 4.7.1 & Implement delivery preference settings & Done \\
\cline{5-7}
 &  &  &  & 4.7.2 & Add delivery time scheduling & Done \\
\cline{3-7}
 &  & 4.8 & As a pharmacy, I want to confirm orders before processing & 4.8.1 & Create pharmacist order confirmation interface & Done \\
\cline{5-7}
 &  &  &  & 4.8.2 & Implement order validation and approval & Done \\
\cline{3-7}
 &  & 5.3 & As a patient, I want to view my order history to see their details and status & 5.3.1 & Create order history interface & Done \\
\cline{5-7}
 &  &  &  & 5.3.2 & Add order details and status display & Done \\
\hline

5 & Delivery Management & 5.1 & As a delivery personnel, I want to update the order status (Picked up, Delivered) & 5.1.1 & Implement delivery status update interface & Done \\
\cline{5-7}
 &  &  &  & 5.1.2 & Create role-based access for delivery personnel & Done \\
\cline{5-7}
 &  &  &  & 5.1.3 & Add delivery confirmation functionality & Done \\
\cline{3-7}
 &  & 5.2 & As a delivery personnel, I want to mark an order as "Delivery Attempted" if the patient is unavailable & 5.2.1 & Implement delivery attempt tracking & Done \\
\cline{5-7}
 &  &  &  & 5.2.2 & Create retry delivery scheduling & Done \\
\cline{5-7}
 &  &  &  & 5.2.3 & Add delivery attempt notifications & Done \\
\hline

7 & Stock Management & 7.1 & As a pharmacy/Admin, I want to create/update/delete products in my stock & 7.1.1 & Implement product CRUD operations & Done \\
\cline{5-7}
 &  &  &  & 7.1.2 & Create product management interface & Done \\
\cline{5-7}
 &  &  &  & 7.1.3 & Add stock status automation & Done \\
\cline{5-7}
 &  &  &  & 7.1.4 & Implement product validation and pricing & Done \\
\cline{3-7}
 &  & 7.2 & As a pharmacy/Admin I want to create/update/delete product categories & 7.2.1 & Implement category management system & Done \\
\cline{5-7}
 &  &  &  & 7.2.2 & Create category assignment functionality & Done \\
\hline
\end{longtable}
\end{center}

\vspace{1cm}

\section{Sprint 3 Design and Architecture}

\subsection{Order and Stock Management Class Diagram}
Figure \ref{fig:sprint3_class} presents the class diagram for Sprint 3, illustrating the relationships between Order, Product, Category entities and their associated services for order and stock management.

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth,height=0.75\textheight,keepaspectratio]{img/sprint3/sprint3_class_diagram.png}
\caption{Sprint 3 Class Diagram - Order and Stock Management}
\label{fig:sprint3_class}
\end{figure}

The class diagram demonstrates the comprehensive architecture supporting e-commerce functionality. The Order entity manages the complete order lifecycle with status tracking, while the Product entity handles inventory management with stock status automation. The Category entity provides hierarchical organization, and the repository pattern ensures clean data access across all components.



\subsection{Order Processing Sequence Diagram}
Figure \ref{fig:order_sequence} demonstrates the order processing workflow from basket management through order confirmation and delivery tracking.

\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{img/sprint3/order_sequence.png}
\caption{Order Processing Sequence Diagram}
\label{fig:order_sequence}
\end{figure}

The sequence diagram illustrates the complete order processing flow, including basket validation, order confirmation, payment calculation, and status updates. The system ensures data consistency and proper error handling throughout the order lifecycle.

\subsection{Stock Management Sequence Diagram}
Figure \ref{fig:stock_sequence} shows the stock management workflow for product creation, inventory updates, and stock status tracking.

\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{img/sprint3/stock_sequence.png}
\caption{Stock Management Sequence Diagram}
\label{fig:stock_sequence}
\end{figure}

The stock management sequence demonstrates automated stock status updates, inventory validation, and category management. The system maintains accurate inventory levels and provides real-time stock information to support order processing decisions.

\section{Sprint 3 Implementation Details}

\subsection{Backend Implementation}
The backend implementation for Sprint 3 follows clean architecture principles with clear separation of concerns across controllers, use cases, repositories, and entities.

\subsubsection{Order Management Backend}
The order management backend implements comprehensive order processing capabilities:

\begin{itemize}
    \item \textbf{Order Entity}: Manages order data with status tracking, product associations, and delivery information.
    \item \textbf{Order Repository}: Provides data access methods for order CRUD operations, basket management, and order filtering.
    \item \textbf{Order Use Cases}: Implements business logic for basket operations, order confirmation, status updates, and history retrieval.
    \item \textbf{Order Controller}: Exposes REST API endpoints for order management with proper authentication and validation.
\end{itemize}

Key backend features include basket persistence, order validation, automatic status transitions, and delivery tracking integration. The system prevents duplicate orders and ensures data consistency across all operations.

\subsubsection{Stock Management Backend}
The stock management backend provides robust inventory control:

\begin{itemize}
    \item \textbf{Product Entity}: Comprehensive product data model with inventory tracking, pricing, and categorization.
    \item \textbf{Product Repository}: Advanced filtering and search capabilities with pharmacy-specific inventory management.
    \item \textbf{Product Use Cases}: Business logic for product CRUD operations, stock status automation, and inventory validation.
    \item \textbf{Category Management}: Hierarchical category system with CRUD operations and product associations.
\end{itemize}

The stock management system automatically updates stock status based on quantity levels, supports multi-pharmacy inventory isolation, and provides comprehensive product information management including expiry dates, suppliers, and barcodes.

\subsection{Mobile Application Implementation}
The Flutter mobile application provides intuitive interfaces for patients to interact with the e-commerce system.

\subsubsection{Order Management Mobile Features}
The mobile order management implementation includes:

\begin{itemize}
    \item \textbf{Product Browsing}: Responsive product catalog with search, filtering, and category navigation.
    \item \textbf{Basket Management}: Real-time basket updates with add/remove functionality and quantity management.
    \item \textbf{Order Confirmation}: Streamlined order confirmation process with delivery preferences and payment calculation.
    \item \textbf{Order Tracking}: Real-time order status updates with detailed tracking information.
    \item \textbf{Order History}: Comprehensive order history with detailed order information and reorder capabilities.
\end{itemize}

The mobile implementation uses BLoC pattern for state management, ensuring consistent data flow and responsive UI updates. The system provides offline capabilities for basket management and synchronizes data when connectivity is restored.

\subsubsection{Mobile State Management}
The mobile application implements robust state management using BLoC pattern:

\begin{itemize}
    \item \textbf{Order BLoC}: Manages order-related state including basket contents, order status, and history.
    \item \textbf{Product BLoC}: Handles product browsing, search, and filtering state management.
    \item \textbf{Repository Pattern}: Provides clean data access layer with API integration and local storage.
    \item \textbf{Error Handling}: Comprehensive error handling with user-friendly error messages and retry mechanisms.
\end{itemize}

\subsection{Web Platform Implementation}
The Angular web platform provides comprehensive management interfaces for pharmacists and administrators.

\subsubsection{Order Management Web Features}
The web order management implementation includes:

\begin{itemize}
    \item \textbf{Order Dashboard}: Comprehensive order overview with filtering, search, and status management.
    \item \textbf{Order Processing}: Pharmacist order confirmation with quantity adjustment and delivery note management.
    \item \textbf{Order Tracking}: Real-time order status monitoring with delivery coordination.
    \item \textbf{Order Analytics}: Order statistics and reporting for business intelligence.
\end{itemize}

\subsubsection{Stock Management Web Features}
The web stock management implementation provides:

\begin{itemize}
    \item \textbf{Inventory Dashboard}: Comprehensive product overview with stock status monitoring and low stock alerts.
    \item \textbf{Product Management}: Full product CRUD operations with image upload and detailed information management.
    \item \textbf{Category Management}: Hierarchical category system with drag-and-drop organization.
    \item \textbf{Stock Analytics}: Inventory reports, stock movement tracking, and expiry date monitoring.
\end{itemize}

The web platform implements responsive design principles, ensuring optimal user experience across desktop and tablet devices. The system provides real-time updates and collaborative features for multi-user pharmacy environments.

\section{Testing and Quality Assurance}
Sprint 3 implementation included comprehensive testing strategies to ensure reliability and performance:

\begin{itemize}
    \item \textbf{Unit Testing}: Implemented unit tests for order and stock management services, validation logic, and business rules.
    \item \textbf{Integration Testing}: Created integration tests for API endpoints, database operations, and third-party service integrations.
    \item \textbf{Performance Testing}: Conducted performance testing for high-volume order processing and inventory management operations.
    \item \textbf{User Acceptance Testing}: Performed UAT with pharmacists and patients to validate e-commerce functionality and user experience.
\end{itemize}

\section{Conclusion}
Sprint 3 successfully implemented comprehensive order management and stock management capabilities for the MED4SOLUTIONS platform. The implementation provides robust e-commerce functionality with intuitive user interfaces, efficient inventory management, and seamless order processing workflows. The system supports multiple user roles with appropriate access controls and provides real-time updates across all platforms.

The order management system enables patients to easily browse products, manage shopping baskets, and track orders through delivery completion. The stock management system provides pharmacists with powerful inventory control tools, automated stock status tracking, and comprehensive product information management. The integration between order and stock management ensures accurate inventory levels and prevents overselling.

The robust architecture and comprehensive feature set create a solid foundation for e-commerce operations, supporting the platform's growth and ensuring efficient medication distribution through the MED4SOLUTIONS ecosystem.
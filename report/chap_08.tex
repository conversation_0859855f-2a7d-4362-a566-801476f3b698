\chapter{Application Setup and User Guide}
\label{chap:setup}

This chapter provides a comprehensive guide for setting up and running the complete Med4Solutions digital health platform. The system consists of multiple components that work together to provide a seamless experience for managing prescriptions and healthcare services.

\section{System Architecture Overview}

The Med4Solutions platform is composed of the following components:
\begin{itemize}
    \item \textbf{Backend API}: NestJS-based REST API server
    \item \textbf{Database}: MongoDB database for data persistence
    \item \textbf{Web Frontend}: Angular-based web application
    \item \textbf{Mobile Application}: Flutter-based iOS application
    \item \textbf{AI Processing Service}: Python-based OCR and prescription processing service
    \item \textbf{Company Distribution}: Pre-built backend and frontend distributions from Med4Solutions
\end{itemize}

\section{Prerequisites}

Before setting up the application, ensure the following software is installed on your system:

\begin{itemize}
    \item \textbf{Node.js} (version 18.x or higher)
    \item \textbf{MongoDB} (version 6.x or higher)
    \item \textbf{Python} (version 3.9 or higher)
    \item \textbf{Flutter SDK} (version 3.x or higher)
    \item \textbf{Angular CLI} (version 16.x or higher)
    \item \textbf{Git} for version control
\end{itemize}

\section{Database Setup}

\subsection{MongoDB Installation and Configuration}

1. Install MongoDB Community Edition following the official documentation for your operating system.

2. Start the MongoDB service:

On macOS with Homebrew:
\begin{terminal}
brew services start mongodb-community
\end{terminal}

On Linux:
\begin{terminal}
sudo systemctl start mongod
\end{terminal}

On Windows:
\begin{terminal}
net start MongoDB
\end{terminal}

3. Verify MongoDB is running by connecting to the default port:
\begin{terminal}
mongosh mongodb://localhost:27017
\end{terminal}

4. Create the application database:
\begin{terminal}
use med4solutions_db
\end{terminal}

\section{Backend Setup}

\subsection{Development Backend (NestJS)}

Navigate to the backend project directory:
\begin{terminal}
cd PFE_Backend
\end{terminal}

Install dependencies:
\begin{terminal}
npm install
\end{terminal}

Configure environment variables by creating a \texttt{.env} file. Example values:
\begin{terminal}
DATABASE_URL=mongodb://localhost:27017/med4solutions_db
JWT_SECRET=your_jwt_secret_key
PORT=3000
\end{terminal}

Start the development server:
\begin{terminal}
npm run start:dev
\end{terminal}

The backend API will be available at \texttt{http://localhost:3000}.

\subsection{Company Distribution Backend}

Navigate to the distribution directory:
\begin{terminal}
cd dists/backend
\end{terminal}

Run the distribution:
\begin{terminal}
node index.js
\end{terminal}

\textbf{Note}: The company distribution is a compiled version provided by Med4Solutions. The source code is not accessible, but it provides the same functionality as the development backend.

\section{AI Processing Service Setup}

Navigate to the AI service directory:
\begin{terminal}
cd PFE_AI
\end{terminal}

Install Python dependencies:
\begin{terminal}
pip install -r requirements.txt
\end{terminal}

Configure API keys in your environment (\texttt{.env} file):
\begin{terminal}
GEMINI_API_KEY=your_gemini_api_key
GROQ_API_KEY=your_groq_api_key
\end{terminal}

Start the AI service:
\begin{terminal}
uvicorn main:app --host 0.0.0.0 --port 8001
\end{terminal}

The AI service will be available at \texttt{http://localhost:8001}.

\section{Frontend Setup}

\subsection{Development Frontend (Angular)}

Navigate to the frontend project directory:
\begin{terminal}
cd PFE_Frontend
\end{terminal}

Install dependencies:
\begin{terminal}
npm install
\end{terminal}

Configure the API endpoint in \texttt{src/environments/environment.ts}:
\begin{terminal}
export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000/api',
  aiServiceUrl: 'http://localhost:8001'
};
\end{terminal}

Start the development server:
\begin{terminal}
ng serve
\end{terminal}

The web application will be available at \texttt{http://localhost:4200}.

\subsection{Company Distribution Frontend}

Navigate to the distribution directory:
\begin{terminal}
cd dists/frontend
\end{terminal}

Install \texttt{http-server} globally if not already installed:
\begin{terminal}
npm install -g http-server
\end{terminal}

Run the distribution:
\begin{terminal}
http-server -p 4200
\end{terminal}

The web application will be available at \texttt{http://localhost:4200}.

\section{Mobile Application Setup}

\subsection{Flutter iOS Application}

Navigate to the mobile project directory:
\begin{terminal}
cd PFE_Mobile
\end{terminal}

Install Flutter dependencies:
\begin{terminal}
flutter pub get
\end{terminal}

Configure the API endpoints in \texttt{lib/config/api\_config.dart}:
\begin{terminal}
class ApiConfig {
  static const String baseUrl = 'http://localhost:3000/api';
  static const String aiServiceUrl = 'http://localhost:8001';
}
\end{terminal}

Run the application on iOS simulator or device:
\begin{terminal}
flutter run -d ios
\end{terminal}

\section{Complete System Startup Sequence}

\subsection{Step 1: Start Database}

On macOS:
\begin{terminal}
brew services start mongodb-community
\end{terminal}

On Linux:
\begin{terminal}
sudo systemctl start mongod
\end{terminal}

\subsection{Step 2: Start Backend Services}

Backend:
\begin{terminal}
cd PFE_Backend
npm run start:dev
\end{terminal}

AI Service:
\begin{terminal}
cd PFE_AI
uvicorn main:app --host 0.0.0.0 --port 8001
\end{terminal}

\subsection{Step 3: Start Frontend Applications}

Web Application:
\begin{terminal}
cd PFE_Frontend
ng serve
\end{terminal}

Mobile Application:
\begin{terminal}
cd PFE_Mobile
flutter run -d ios
\end{terminal}

\subsection{Alternative: Using Company Distributions}

Backend Distribution:
\begin{terminal}
cd dists/backend
node index.js
\end{terminal}

AI Service:
\begin{terminal}
cd PFE_AI
uvicorn main:app --host 0.0.0.0 --port 8001
\end{terminal}

Frontend Distribution:
\begin{terminal}
cd dists/frontend
http-server -p 4200
\end{terminal}

Mobile Application:
\begin{terminal}
cd PFE_Mobile
flutter run -d ios
\end{terminal}

\section{System Verification}

After starting all components, verify the system is working correctly:

\begin{enumerate}
    \item \textbf{Backend API}: Visit \texttt{http://localhost:3000/api/health}
    \item \textbf{AI Service}: Visit \texttt{http://localhost:8001/docs}
    \item \textbf{Web Application}: Open \texttt{http://localhost:4200}
    \item \textbf{Mobile Application}: Launch on iOS simulator or device
    \item \textbf{Database}: Use MongoDB Compass or mongosh
\end{enumerate}

\section{Troubleshooting}

\textbf{Port Conflicts}: If ports 3000, 4200, or 8001 are already in use, modify the port configurations.

\textbf{Database Connection}: Ensure MongoDB is running. Check \texttt{DATABASE\_URL}.

\textbf{API Keys}: Ensure \texttt{GEMINI\_API\_KEY} and \texttt{GROQ\_API\_KEY} are set correctly.

\textbf{Flutter Issues}: Run \texttt{flutter doctor}.

\textbf{CORS Issues}: If the frontend cannot connect, check CORS in the NestJS app.

\section{User Guide}

\subsection{System Access}

The Med4Solutions platform provides different access levels:

\begin{itemize}
    \item \textbf{Patients}: Mobile app or web
    \item \textbf{Pharmacists}: Web interface
    \item \textbf{Delivery Personnel}: Mobile app
    \item \textbf{Administrators}: Web interface
\end{itemize}

\subsection{Key Functionalities}

\textbf{For Patients}:
\begin{itemize}
    \item Manage profile
    \item Upload prescriptions
    \item Track deliveries
    \item Receive notifications
\end{itemize}

\textbf{For Pharmacists}:
\begin{itemize}
    \item View and process prescriptions
    \item Manage packages
    \item Send notifications
\end{itemize}

\textbf{For Delivery Personnel}:
\begin{itemize}
    \item View assigned deliveries
    \item Update delivery status
    \item Navigate to addresses
\end{itemize}

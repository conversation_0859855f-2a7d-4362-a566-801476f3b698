# PFE Report - <PERSON><PERSON><PERSON>

## Technical Intervention Management System

This LaTeX document contains the PFE (Projet de Fin d'Études) report for the development of a Technical Intervention Management System.

## Document Structure

- `main.tex` - Main document file
- `global_config.tex` - Global configuration and metadata
- `introduction.tex` - General introduction
- `chap_01.tex` - General Framework of the Project
- `chap_02.tex` - Requirements Analysis and Specification
- `chap_03.tex` - System Design and Architecture
- `chap_04.tex` - Implementation and Development
- `chap_05.tex` - Testing and Validation
- `conclusion.tex` - General conclusion

## Formatting Requirements

### Typography
- **Font**: Times New Roman, 12pt for body text
- **Title**: 24pt, centered
- **Headings**: Bold, decreasing size based on depth (numbered as 1, 1.1, etc.)
- **Line spacing**: 1.15
- **Justification**: Left and right aligned
- **Paragraph indentation**: 0.50cm first line indent

### Page Layout
- **Margins**: 2.5cm default margins
- **Page numbers**: Bottom right, format: page/total
- **Printing**: Recommended double-sided (recto-verso)

### Content Formatting
- **Examples**: Italics
- **Definitions**: Framed or unframed
- **Key elements/conclusions**: Bold
- **Short citations**: French quotation marks (« »)

### Figures and Tables
- All figures must have captions and text references
- Consistent numbering by type (Tab. 1, Graph. 1, Fig. 1, etc.)
- Avoid orphaned illustrations on separate pages

### Bibliography
- Complete references required for reusability
- Include: author(s), title, journal/conference, publisher, publication date, issue number/location, pages
- For web sources: URL, subject, authors, publication date (if available)
- Section titled "Bibliography/Webography"

### Page Layout Guidelines
- Avoid pages with only three lines of text
- Balanced pagination
- Avoid excessive white space
- Don't end chapters on pages with minimal text

## Compilation

To compile the document:
```bash
pdflatex main.tex
pdflatex main.tex  # Run twice for proper cross-references
```

## Author Information

- **Author**: Hssan Ghorbel
- **Project**: Technical Intervention Management System
- **Academic Year**: 2024-2025

## Notes

- Update company information in `global_config.tex`
- Add actual supervisor names and details
- Include relevant figures and diagrams in the appropriate chapters
- Ensure all references are properly cited

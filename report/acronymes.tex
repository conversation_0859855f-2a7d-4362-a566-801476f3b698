\chapter*{Abreviations list}

%=============== Glossary example ==============%
% it's an enhanced itemize list to make it      %
% sortable automatically.                       %
%===============================================%

\begin{sortedlist}
    
    \sortitem[IDE] {
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{IDE}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{I}ntegrated  \textbf{D}evelopment \textbf{E}nvironment
        \end{minipage}
    }
      
    
    
    
    \sortitem[LPWAN]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{LPWAN}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{L}ow \textbf{P}ower \textbf{W}ide \textbf{A}rea \textbf{N}etwork 
        \end{minipage}
    }
    
 
 
        \sortitem[IOT ]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{IOT }
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{I}nternet \textbf{O}f \textbf{T}hings
        \end{minipage}
    }
    
       
     \sortitem[LoRa ]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{LoRa}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
        \textbf{L}ong \textbf{R}ang
        \end{minipage}
    }   
 
     \sortitem[ADC]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{ADC}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{A}nalogic \textbf{D}igital  \textbf{C}onverter 
        \end{minipage}
    }  
     
    
         \sortitem[CSS ]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{CSS}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{C}ascading \textbf{S}tyle \textbf{S}heet
        \end{minipage}
    }  
	

        
    

 \sortitem[STEG]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{STEG}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{S}ociété \textbf{T}unisienne  d'\textbf{E}lectricité et de \textbf{G}az
        \end{minipage}
    }  
    
\sortitem[UML]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{UML}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{U}nified \textbf{M}odeling  \textbf{L}anguage
        \end{minipage}
}

\sortitem[WiFi]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{WiFi}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{Wi}reless \textbf{Fi}delity
        \end{minipage}
}
\sortitem[BTLE]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{BTLE}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{B}lue\textbf{T}ooth \textbf{L}ow  \textbf{E}nergy
        \end{minipage}
}

\sortitem[LAN]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{LAN}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{L}ocal \textbf{A}rea  \textbf{N}etwork
        \end{minipage}
}
\sortitem[NB-IOT]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{NB-IOT}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{N}arrow\textbf{B}and  IOT
        \end{minipage}
}
  
 \sortitem[WAN]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{WAN}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{W}ide \textbf{A}rea  \textbf{N}etwork
        \end{minipage}
}\sortitem[IP]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{IP}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{I}nternet \textbf{P}rotocol 
        \end{minipage}
}
\sortitem[USB]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{USB}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{U}niversal \textbf{S}erial  \textbf{B}us
        \end{minipage}
}

\sortitem[DPSK]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{DPSK}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{D}ifferential \textbf{P}hase  \textbf{S}hift \textbf{K}eying
        \end{minipage}
  }   
  \sortitem[QPSK]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{DPSK}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{Q}uadrature \textbf{P}hase  \textbf{S}hift \textbf{K}eying
        \end{minipage}
  }     
 \sortitem[OFDM]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{OFDM}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
        \textbf{O}rthogonal \textbf{F}requency  \textbf{D}ivision \textbf{M}ultiplexing
        \end{minipage}
}
\sortitem[LTE]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{LTE}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{L}ong \textbf{T}erm  \textbf{E}volution
        \end{minipage}
}
\sortitem[TIC]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{TIC}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{T}ess \textbf{I}nput  \textbf{C}atalog
        \end{minipage}
}
\sortitem[CAD]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{USB}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{C}omputer \textbf{A}ided  \textbf{D}esign
        \end{minipage}
}
\sortitem[I2C]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{I2C}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{I}nter \textbf{I}ntegrated  \textbf{C}ircuit
        \end{minipage}
}
\sortitem[SPI]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{SPI}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{S}erial \textbf{P}eripheral  \textbf{I}nterface
        \end{minipage}
}
\sortitem[RTC]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{RTC}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{R}eal \textbf{T}ime  \textbf{C}lock
        \end{minipage}
}
\sortitem[UART]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{UART}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{U}niversal \textbf{A}synchronous  \textbf{R}eceiver \textbf{T}ransmitter
        \end{minipage}
}
\sortitem[USART]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{USART}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{U}niversal \textbf{S}ynchronous/\textbf{A}synchronous  \textbf{R}eceiver \textbf{T}ransmitter
        \end{minipage}
}
\sortitem[ASCII]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{ASCII}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{A}merican \textbf{S}tandard \textbf{c}ode  \textbf{I}nformation \textbf{I}nterchange
        \end{minipage}
}

\sortitem[EEPROM]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{EEPROM}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{E}lectrically \textbf{E}rasable \textbf{P}rogrammable  \textbf{R}ead \textbf{O}nly \textbf{M}emory
        \end{minipage}
}
 
 \sortitem[FPGA]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{FPGA}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{F}ield \textbf{P}rogrammable \textbf{G}ate  \textbf{A}rrays 
        \end{minipage}}
        
        \sortitem[ECU]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{ECU}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{E}ngine \textbf{C}ontrol  \textbf{U}nit
        \end{minipage}
}

\sortitem[ASK]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{ASK}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{A}mplitude \textbf{S}hift  \textbf{K}eying
        \end{minipage}
}

\sortitem[LED]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{LED}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{L}light \textbf{E}mitting  \textbf{D}iode
        \end{minipage}
}
\sortitem[RF]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{RF}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{R}adio \textbf{F}requency  
        \end{minipage}
}

\sortitem[AMBA]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{AMBA}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{A}dvanced \textbf{M}icrocontroller \textbf{B}us  \textbf{A}rchitecture 
        \end{minipage}
}
\sortitem[JTAG]{
        \begin{minipage}[l]{0.15\columnwidth}
            \textbf{JTAG}
        \end{minipage}
        \begin{minipage}[l]{0.05\columnwidth}
            \textbf{=}
        \end{minipage}
        \begin{minipage}[l]{0.8\columnwidth}
            \textbf{J}oint \textbf{T}est \textbf{A}ction  \textbf{G}roup 
        \end{minipage}
}
\end{sortedlist}


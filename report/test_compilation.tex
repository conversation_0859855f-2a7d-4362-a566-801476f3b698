\documentclass[pfe, titlesmallcaps]{./tpl/isipfe}

\input{./tpl/new_commands}
\usepackage[english]{babel}
\usepackage{tabularx}
\usepackage{longtable}
\usepackage{multirow}
\usepackage{array}
\usepackage{float}
\usepackage{graphicx}
\usepackage{caption}
\usepackage{url}
\usepackage{booktabs}
\usepackage{ltxtable}
\captionsetup[table]{position=bottom}
\hyphenpenalty=10000

% Font configuration - Times New Roman
\usepackage[T1]{fontenc}
\usepackage{textcomp}
\usepackage{mathptmx}

% Fix missing font shapes
\DeclareFontShape{T1}{lmr}{bx}{sc}{<->ssub*lmr/bx/n}{}
\DeclareFontShape{T1}{lmr}{bx}{scit}{<->ssub*lmr/bx/it}{}
\DeclareFontShape{T1}{ptm}{b}{scit}{<->ssub*ptm/b/it}{}

% Fix hyperref warnings by loading it after other packages
\usepackage[hidelinks,unicode=false,pdfencoding=pdfdocencoding]{hyperref}
\hypersetup{
    pdftitle={Test Document},
    pdfauthor={Test Author}
}

% Fix float placement issues
\renewcommand{\floatpagefraction}{.8}
\renewcommand{\topfraction}{.8}
\renewcommand{\bottomfraction}{.8}
\renewcommand{\textfraction}{.2}

\begin{document}

\chapter{Test Chapter}

This is a test document to check compilation warnings.

\begin{figure}[H]
\centering
\rule{5cm}{3cm}
\caption{Test Figure}
\label{fig:test}
\end{figure}

\begin{table}[H]
\centering
\caption{Test Table}
\label{tab:test}
\begin{tabular}{|c|c|}
\hline
Column 1 & Column 2 \\
\hline
Data 1 & Data 2 \\
\hline
\end{tabular}
\end{table}

\end{document}

\documentclass[pfe, titlesmallcaps]{./tpl/isipfe}

\input{./tpl/new_commands}
\usepackage[english]{babel}
\usepackage[hidelinks]{hyperref}
\usepackage{tabularx}
\usepackage{longtable}
\usepackage{multirow}
\usepackage{float}
\usepackage{graphicx}
\usepackage{caption}
\captionsetup[table]{position=bottom}
\hyphenpenalty=10000

% Font configuration - Times New Roman
\usepackage{mathptmx} % Times New Roman font
\usepackage[T1]{fontenc}

% Line spacing configuration - 1.15
\usepackage{setspace}
\setstretch{1.15}

% Margins configuration - 2.5cm on all sides
% Note: geometry package is already loaded in isipfe.cls, so we use \geometry command instead
\geometry{top=2.5cm, bottom=2.5cm, left=2.5cm, right=2.5cm}

% Page numbering format - page/total at bottom right
\usepackage{fancyhdr}
\usepackage{lastpage}
\fancypagestyle{main}{%
  \fancyhf{}% clear all header and footer fields
  \renewcommand{\headrulewidth}{0pt}%
  \renewcommand{\footrulewidth}{0pt}%
  \fancyfoot[R]{\thepage/\pageref{LastPage}}%
}

% Section numbering configuration
\usepackage{titlesec}
\titleformat{\section}{\normalfont\Large\bfseries}{\thesection}{0.2cm}{}
\titleformat{\subsection}{\normalfont\large\bfseries}{\thesubsection}{0.2cm}{}
\titleformat{\subsubsection}{\normalfont\normalsize\bfseries}{\thesubsubsection}{0.2cm}{}

% Paragraph justification
\usepackage{ragged2e}
\justifying

\addto\captionsenglish{
  \renewcommand{\contentsname}%
    {Table of contents}%
}

\begin{document}
    \input{global_config}
    
    \frontmatter
        
        \setcounter{page}{0}
        \input{remerciement}
        \thispagestyle{frontmatter}
        
        \setcounter{secnumdepth}{3}
        \setcounter{tocdepth}{2}
        \tableofcontents
        \thispagestyle{frontmatter}
        
        \listoffigures
        \thispagestyle{frontmatter}
        \listoftables
        \thispagestyle{frontmatter}
        
    
    \mainmatter
        \pagestyle{main} % Apply custom page style for main content
        \input{introduction}
        \clearpage

        \input{chap_01}
        \clearpage

        \input{chap_02}
        \clearpage

        \input{chap_03}
        \clearpage

        \input{chap_04}
        \clearpage

        \input{chap_05}
        \clearpage

        \input{conclusion}
        \clearpage
    
   \backmatter
        \newpage
        
        \clearpage

        \chapter*{Bibliography / Netography}
        \addcontentsline{toc}{chapter}{Bibliography / Netography}

        \section*{Books and Articles}
        \begin{itemize}
            \item Sommerville, I. (2016). \textit{Software Engineering}. 10th Edition, Pearson Education Limited.
            \item Pressman, R. S., \& Maxim, B. R. (2019). \textit{Software Engineering: A Practitioner's Approach}. 9th Edition, McGraw-Hill Education.
            \item Fowler, M. (2018). \textit{Refactoring: Improving the Design of Existing Code}. 2nd Edition, Addison-Wesley Professional.
        \end{itemize}

        \section*{Web Resources}
        \begin{itemize}
            \item Angular Documentation. (2024). \textit{Angular Framework Guide}. Retrieved from \url{https://angular.io/docs}
            \item NestJS Documentation. (2024). \textit{A Progressive Node.js Framework}. Retrieved from \url{https://docs.nestjs.com/}
            \item TypeScript Documentation. (2024). \textit{TypeScript Language Reference}. Retrieved from \url{https://www.typescriptlang.org/docs/}
            \item PostgreSQL Documentation. (2024). \textit{PostgreSQL Database Management System}. Retrieved from \url{https://www.postgresql.org/docs/}
            \item Flutter Documentation. (2024). \textit{Flutter Mobile Development Framework}. Retrieved from \url{https://docs.flutter.dev/}
            \item Capsule Pharmacy. (2024). \textit{Prescription Delivery Service}. Retrieved from \url{https://capsulecares.com/}
            \item Scrum.org. (2024). \textit{Scrum Framework and Methodology}. Retrieved from \url{https://www.scrum.org/}
        \end{itemize}
   
        
    
\end{document}

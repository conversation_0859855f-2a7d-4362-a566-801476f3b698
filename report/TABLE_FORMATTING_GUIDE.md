# Sprint 1 Backlog Table Formatting Guide

## Issues Fixed So Far

### 1. Column Width Adjustments ✅ COMPLETED
- Changed table definition from `{|c|p{2.3cm}|c|p{3.8cm}|c|p{2.8cm}|c|}` 
- To: `{|c|p{2.5cm}|c|p{4.2cm}|c|p{3.2cm}|c|}`

### 2. Parbox Formatting Pattern ✅ PARTIALLY COMPLETED

**For Feature Names (Column 2):**
```latex
\multirow{X}{*}{\parbox{2.5cm}{\raggedright Feature Name}}
```

**For User Stories (Column 4):**
```latex
\multirow{X}{*}{\parbox{4.2cm}{\raggedright User story text}}
```

**For Task Descriptions (Column 6):**
```latex
\parbox{3.2cm}{\raggedright Task description}
```

## Sections Already Fixed ✅

1. **Authentication Management (Feature 1)**
   - User Story 1.1: ✅ All 12 tasks fixed
   - User Story 1.2: ✅ All 8 tasks fixed  
   - User Story 1.3: ✅ All 6 tasks fixed
   - User Story 1.4: ✅ All 7 tasks fixed
   - User Story 1.5: ✅ User story header fixed, tasks need fixing

2. **Profile Management (Feature 2)**
   - User Story 2.1: ✅ Header fixed, tasks need fixing

## Remaining Work Needed 🔧

### Pattern to Apply for Remaining Tasks:

**BEFORE (causing spacing issues):**
```latex
 &  &  &  & 1.5.2 & Implement forgot password API endpoint & Done \\
```

**AFTER (proper formatting):**
```latex
 &  &  &  & 1.5.2 & \parbox{3.2cm}{\raggedright Implement forgot password API endpoint} & Done \\
```

### Remaining Sections to Fix:

1. **Feature 1 - User Story 1.5 Tasks (lines ~122-128)**
   - 1.5.2 through 1.5.5 need parbox formatting

2. **Feature 2 - All User Stories (lines ~131-167)**
   - User Story 2.1: Tasks 2.1.2 through 2.1.8
   - User Story 2.2: All tasks (header and tasks)
   - User Story 2.3: All tasks (header and tasks)

## Quick Fix Commands

For each remaining task line, apply this pattern:

1. Find lines like: `& TASK_ID & Task description & Done \\`
2. Replace with: `& TASK_ID & \parbox{3.2cm}{\raggedright Task description} & Done \\`

For user story headers, apply:
1. Find: `\multirow{X}{*}{\parbox{3.8cm}{User story}}`
2. Replace: `\multirow{X}{*}{\parbox{4.2cm}{\raggedright User story}}`

## Benefits of These Fixes

1. **Prevents Content Overflow**: Proper column widths prevent text from appearing outside table
2. **Eliminates Excessive Spacing**: `\raggedright` prevents justified text spacing issues
3. **Consistent Formatting**: All cells use same formatting approach
4. **Better Readability**: Text flows naturally within defined column boundaries

## Testing

After applying all fixes:
1. Compile in Overleaf
2. Check that all table content stays within table boundaries
3. Verify that text spacing looks natural (no excessive gaps between words)
4. Ensure table fits properly on the page

## Note

The `\raggedright` command is crucial - it prevents LaTeX from trying to justify text in narrow columns, which causes the excessive spacing between words you observed.

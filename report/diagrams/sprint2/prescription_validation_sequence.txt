@startuml
!theme plain
skinparam backgroundColor white
skinparam participant {
    BackgroundColor lightblue
    BorderColor black
}
skinparam sequence {
    ArrowColor black
    LifeLineBackgroundColor white
    LifeLineBorderColor black
}

title Prescription Management Sequence Diagram

actor Pharmacist
participant "Web Dashboard" as Web
participant "Prescription\nController" as Controller
participant "Prescription\nService" as Service
participant "Prescription\nRepository" as Repo
participant "Patient\nRepository" as PatientRepo
participant "Package\nService" as PackageService
participant "Notification\nService" as NotifService
participant Patient

Pharmacist -> Web: Login to dashboard
Web -> Pharmacist: Show dashboard

== View Prescriptions ==

Pharmacist -> Web: View prescriptions
Web -> Controller: GET /prescriptions/filter
Controller -> Service: filterPrescriptions(query)
Service -> Repo: findByFilters(filters)
Repo -> Service: Return prescriptions list
Service -> Controller: Return prescriptions
Controller -> Web: Return prescriptions data
Web -> Pharmacist: Display prescriptions list

Pharmacist -> Web: Select prescription to view
Web -> Controller: GET /prescriptions/{id}
Controller -> Service: getPrescriptionById(id)
Service -> Repo: findById(id)
Repo -> Service: Return prescription details
Service -> Controller: Return prescription
Controller -> Web: Return prescription data
Web -> Pharmacist: Show prescription details\n& OCR extracted medicines

== Create Package for Prescription ==

Pharmacist -> Web: Click "Create Package" for prescription
Web -> Pharmacist: Show package options\n(Colis, Colis+Cueillette, Cueillette)

Pharmacist -> Web: Select package type & details
Web -> Controller: POST /packages/create\n{prescriptionId, packageType, details}
Controller -> PackageService: createPackage(prescriptionData)
PackageService -> PackageService: Create appropriate package\nbased on prescription medicines
PackageService -> Controller: Return created package
Controller -> Web: Return package confirmation
Web -> Pharmacist: Show "Package created successfully"

== Create Prescription for Patient ==

Pharmacist -> Web: Click "Create Prescription"
Web -> Controller: GET /patients/list
Controller -> PatientRepo: findAll()
PatientRepo -> Controller: Return patients list
Controller -> Web: Return patients data
Web -> Pharmacist: Show patients selection

Pharmacist -> Web: Select patient & upload prescription file
Web -> Controller: POST /prescriptions/uploadFile
Controller -> Service: uploadFile(file)
Service -> Controller: Return file path
Controller -> Web: Return upload confirmation

Pharmacist -> Web: Fill prescription details & submit
Web -> Controller: POST /prescriptions/createForPatient/{patientId}\n{pharmacyId, storagePaths, note, medicines}
Controller -> Service: createPrescriptionForPatient(command)
Service -> Repo: create(prescription)
Repo -> Service: Return created prescription

Service -> PatientRepo: findById(patientId)
PatientRepo -> Service: Return patient details

Service -> NotifService: sendPharmacistPrescriptionNotification(data)
NotifService -> Patient: Send notification\n"Pharmacist created prescription for you"
NotifService -> Service: Notification sent

Service -> Controller: Return prescription
Controller -> Web: Return success response
Web -> Pharmacist: Show "Prescription created for patient"

note right of PackageService
  Package types:
  - Colis (Package only)
  - Colis+Cueillette (Package + Pickup)
  - Cueillette (Pickup only)
end note

note right of NotifService
  Notifications sent to patient:
  - Prescription created by pharmacist
  - Package ready for pickup/delivery
  - Medicine details and instructions
end note

@enduml

@enduml

@startuml
!theme plain
skinparam backgroundColor white
skinparam participant {
    BackgroundColor lightblue
    BorderColor black
}
skinparam sequence {
    ArrowColor black
    LifeLineBackgroundColor white
    LifeLineBorderColor black
}

title Prescription Notification Sequence Diagram

participant "Prescription\nCreate" as PrescCreate
participant "Prescription\nCreateForPatient" as PrescCreateForPatient
participant "SendPrescription\nNotification" as SendPrescNotif
participant "SendPharmacist\nPrescriptionNotification" as SendPharmNotif
participant "Patient\nRepository" as PatientRepo
participant "Pharmacy\nRepository" as PharmacyRepo
participant "User\nRepository" as UserRepo
participant Patient
participant Pharmacist

== Patient Uploads Prescription ==

PrescCreate -> PrescCreate: OCR extracts medicines successfully
PrescCreate -> PatientRepo: findOneById(patientId)
PatientRepo -> PrescCreate: Return patient details

PrescCreate -> SendPrescNotif: execute(notificationData)
note right of SendPrescNotif
  notificationData includes:
  prescriptionId, patientId, patientName
  pharmacyId, medicineCount, medicines array
end note

SendPrescNotif -> PharmacyRepo: findById(pharmacyId)
PharmacyRepo -> SendPrescNotif: Return pharmacy details

SendPrescNotif -> UserRepo: findOneByEmail(pharmacistEmail)
UserRepo -> SendPrescNotif: Return pharmacist user details

SendPrescNotif -> Pharmacist: Send notification:\n"New prescription uploaded by {patientName}\nwith {medicineCount} medicines"

SendPrescNotif -> PrescCreate: Notification sent successfully

== Pharmacist Creates Prescription for Patient ==

PrescCreateForPatient -> PharmacyRepo: findById(pharmacyId)
PharmacyRepo -> PrescCreateForPatient: Return pharmacy details

PrescCreateForPatient -> UserRepo: findOneByEmail(pharmacistEmail)
UserRepo -> PrescCreateForPatient: Return pharmacist details

PrescCreateForPatient -> SendPharmNotif: execute(notificationData)
note right of SendPharmNotif
  notificationData includes:
  prescriptionId, patientId, pharmacistName
  pharmacyName, medicines array
end note

SendPharmNotif -> PatientRepo: findById(patientId)
PatientRepo -> SendPharmNotif: Return patient details

SendPharmNotif -> Patient: Send notification:\n"Pharmacist {pharmacistName} created\na prescription for you"

SendPharmNotif -> PrescCreateForPatient: Notification sent successfully

note over SendPrescNotif
  Triggered when: Patient uploads prescription,
  OCR successfully extracts medicines,
  Notifies pharmacist of new prescription
end note

note over SendPharmNotif
  Triggered when: Pharmacist creates prescription for patient,
  Notifies patient of new prescription,
  Includes pharmacist and pharmacy details
end note

@enduml

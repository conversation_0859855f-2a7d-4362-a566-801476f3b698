@startuml
!theme plain
skinparam backgroundColor white
skinparam actor {
    BackgroundColor lightblue
    BorderColor black
}
skinparam usecase {
    BackgroundColor lightgreen
    BorderColor black
}
skinparam system {
    BackgroundColor lightyellow
    BorderColor black
}

title Prescription Management and Processing Use Case Diagram

actor Patient
actor Pharmacist

rectangle "Prescription Management System" {
    usecase "View Prescriptions" as UC1
    usecase "View Prescription Details" as UC2
    usecase "Select Patient" as UC3
    usecase "Add Prescription for Patient" as UC4
    usecase "Create Medicine Package" as UC5
    usecase "View Patient History" as UC6
}

rectangle "Patient Prescription System" {
    usecase "Upload Prescription" as UC7
    usecase "View My Prescriptions" as UC8
    usecase "Receive Prescription Added Notification" as UC9
    usecase "View Prescription Status" as UC10
}

rectangle "Notification System" as NotifSystem {
    usecase "Send Prescription Upload Alert" as NOTIF1
    usecase "Send Prescription Added Confirmation" as NOTIF2
    usecase "Process Notification Queue" as NOTIF3
}

rectangle "Authentication System" {
    usecase "Login" as AUTH
}

Patient --> AUTH
Patient --> UC7
Patient --> UC8
Patient --> UC9
Patient --> UC10

Pharmacist --> AU<PERSON>
Pharmacist --> UC1
Pharmacist --> UC2
Pharmacist --> UC3
Pharmacist --> UC4
Pharmacist --> UC5
Pharmacist --> UC6

UC3 --> UC4 : <<include>>
UC4 --> UC5 : <<include>>

UC7 --> NOTIF1 : <<include>>
UC4 --> NOTIF2 : <<include>>

NOTIF1 --> UC1 : triggers
NOTIF2 --> UC9 : triggers

note right of UC4
  Pharmacist creates prescription
  for selected patient after
  reviewing their needs
end note

note right of UC5
  Create appropriate medicine
  package based on prescription
  requirements
end note

note right of NOTIF1
  Alerts pharmacist when
  patient uploads new
  prescription
end note

note right of NOTIF2
  Confirms to patient when
  pharmacist adds prescription
  for them
end note

@enduml

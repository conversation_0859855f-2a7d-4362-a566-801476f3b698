@startuml
!theme plain
skinparam backgroundColor white
skinparam actor {
    BackgroundColor lightblue
    BorderColor black
}
skinparam usecase {
    BackgroundColor lightgreen
    BorderColor black
}
skinparam system {
    BackgroundColor lightyellow
    BorderColor black
}

title Prescription Upload and Management Use Case Diagram

actor Patient
actor Pharmacist

rectangle "Prescription Upload System" {
    usecase "Upload Prescription Photo" as UC1
    usecase "Take Instant Photo" as UC2
    usecase "Select from Gallery" as UC3
    usecase "Upload PDF File" as UC4
    usecase "Add Prescription Notes" as UC5
    usecase "View Upload Progress" as UC6
    usecase "Process with OCR" as UC7
    usecase "Extract Medicine Data" as UC8
    usecase "Store Original Files" as UC9
    usecase "View Prescription History" as UC10
    usecase "Edit Prescription Details" as UC11
    usecase "Delete Prescription" as UC12
}

rectangle "AI OCR System" as OCRSystem {
    usecase "Analyze Prescription Image" as OCR1
    usecase "Extract Text Content" as OCR2
    usecase "Identify Medicine Names" as OCR3
    usecase "Parse Dosage Information" as OCR4
}

rectangle "Authentication System" {
    usecase "Login" as AUTH
}

Patient --> AUTH
Patient --> UC1
Patient --> UC2
Patient --> UC3
Patient --> UC4
Patient --> UC5
Patient --> UC6
Patient --> UC10
Patient --> UC11
Patient --> UC12

UC1 ..> UC2 : <<include>>
UC1 ..> UC3 : <<include>>
UC1 ..> UC4 : <<include>>
UC1 --> UC7 : <<include>>
UC7 --> UC8 : <<include>>
UC7 --> UC9 : <<include>>

UC7 --> OCR1 : <<include>>
OCR1 --> OCR2 : <<include>>
OCR2 --> OCR3 : <<include>>
OCR3 --> OCR4 : <<include>>
OCR4 --> UC8 : <<include>>

Pharmacist --> AUTH
Pharmacist --> UC10

note right of UC7
  AI-powered OCR processing
  extracts medicine information
  from uploaded prescriptions
end note

note right of UC8
  Structured data extraction:
  - Medicine names
  - Dosages
  - Frequencies
  - Duration
end note

@enduml

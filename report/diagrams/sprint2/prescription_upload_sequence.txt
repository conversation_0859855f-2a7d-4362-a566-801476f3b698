@startuml
!theme plain
skinparam backgroundColor white
skinparam participant {
    BackgroundColor lightblue
    BorderColor black
}
skinparam sequence {
    ArrowColor black
    LifeLineBackgroundColor white
    LifeLineBorderColor black
}

title Prescription Upload and OCR Processing Sequence Diagram

actor Patient
participant "Mobile App" as App
participant "File Upload\nService" as FileService
participant "Prescription\nController" as Controller
participant "Prescription\nService" as Service
participant "OCR Service" as OCR
participant "Prescription\nRepository" as Repo
participant "Notification\nService" as NotifService
participant "Patient\nRepository" as PatientRepo
participant Pharmacist

Patient -> App: Select prescription upload
App -> Patient: Show upload options\n(Camera, Gallery, PDF)

Patient -> App: Choose camera/gallery/PDF
App -> App: Capture/Select file(s)
App -> FileService: Upload file(s)
FileService -> FileService: Validate file type & size
FileService -> App: Return file path(s)

App -> Controller: POST /prescriptions/create\n{patientId, pharmacyId, storagePaths, note}
Controller -> Service: createPrescription(command)

Service -> OCR: extractTextFromFile(filePath)
OCR -> OCR: Process image/PDF with AI
OCR -> OCR: Extract medicine information
OCR -> Service: Return medicines[]

Service -> Repo: create(prescription)
Repo -> Repo: Save prescription with\nOCR extracted data
Repo -> Service: Return saved prescription

alt OCR extracted medicines successfully
    Service -> PatientRepo: findOneById(patientId)
    PatientRepo -> Service: Return patient details
    
    Service -> NotifService: sendPrescriptionNotification(data)
    NotifService -> Pharmacist: Send notification\n"New prescription with medicines"
    NotifService -> Service: Notification sent
end

Service -> Controller: Return prescription
Controller -> App: Return success response
App -> Patient: Show upload success\n& processing status

note right of OCR
  AI-powered OCR extracts:
  - Medicine names
  - Dosages
  - Frequencies
  - Duration
  - Timing
end note

note right of NotifService
  Notification includes:
  - Patient name
  - Medicine count
  - Prescription ID
  - Pharmacy ID
end note

@enduml

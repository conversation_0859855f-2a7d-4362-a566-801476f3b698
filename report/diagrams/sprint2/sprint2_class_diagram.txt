@startuml
!theme plain
skinparam backgroundColor white
skinparam class {
    BackgroundColor lightblue
    BorderColor black
}

title Sprint 2 Class Diagram - Prescription Management

package "Core Entities" {
    class Prescription {
        -id: string
        -patientId: string
        -pharmacyId: string
        -storagePath: string[]
        -prescriptionStatus: PrescriptionStatus
        -medicines: JSON[]
        -note: string
        -uploadedAt: string
        +getId(): string
        +getStatus(): PrescriptionStatus
    }

    class Patient {
        -id: string
        -firstName: string
        -lastName: string
        -email: string
        +getFullName(): string
    }

    class Pharmacy {
        -id: string
        -name: string
        -pharmacistEmail: string
        +getName(): string
    }
}

package "Use Cases" {
    class PrescriptionCreate {
        +execute(command): Prescription
    }

    class PrescriptionUpdate {
        +execute(command): Prescription
    }

    class PrescriptionGetAll {
        +execute(command): {prescriptions, total}
    }

    class PrescriptionGetById {
        +execute(command): Prescription
    }

    class PrescriptionDelete {
        +execute(id, patientId): void
    }

    class PrescriptionCreateForPatient {
        +execute(command): Prescription
    }
}

package "Services" {
    class OcrService {
        +extractTextFromFile(path): JSON[]
    }

    class NotificationService {
        +sendToPharmacist(data): void
        +sendToPatient(data): void
    }
}

package "Infrastructure" {
    class PrescriptionRepository {
        +create(prescription): Prescription
        +findById(id): Prescription
        +update(id, data): Prescription
        +delete(id): void
    }

    class PrescriptionController {
        +createPrescription(): Response
        +updatePrescription(): Response
        +getPrescriptions(): Response
        +deletePrescription(): Response
    }
}

' Simple relationships without crossing lines
Prescription }o-- Patient
Prescription }o-- Pharmacy

PrescriptionController ..> PrescriptionCreate
PrescriptionController ..> PrescriptionUpdate
PrescriptionController ..> PrescriptionGetAll
PrescriptionController ..> PrescriptionGetById
PrescriptionController ..> PrescriptionDelete

PrescriptionCreate ..> PrescriptionRepository
PrescriptionCreate ..> OcrService
PrescriptionCreate ..> NotificationService

PrescriptionUpdate ..> PrescriptionRepository
PrescriptionUpdate ..> OcrService

PrescriptionCreateForPatient ..> PrescriptionRepository
PrescriptionCreateForPatient ..> NotificationService

note right of Prescription::medicines
  JSON Array: {medicine, dosage, frequency}
end note

@enduml

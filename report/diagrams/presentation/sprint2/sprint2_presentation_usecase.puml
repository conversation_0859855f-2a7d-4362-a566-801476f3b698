@startuml sprint2_presentation
!theme plain
skinparam backgroundColor white
skinparam actor {
    BackgroundColor lightblue
    BorderColor black
}
skinparam usecase {
    BackgroundColor lightgreen
    BorderColor black
}
skinparam system {
    BackgroundColor lightyellow
    BorderColor black
}

title Sprint 2: Prescription Management & AI OCR

actor "Patient" as P
actor "Pharmacist" as Ph

rectangle "Sprint 2 Features" {
    package "Prescription Upload" {
        usecase "Upload Prescription" as UC1
        usecase "Take Photo" as UC2
        usecase "Select from Gallery" as UC3
        usecase "Upload PDF" as UC4
    }
    
    package "AI Processing" {
        usecase "Process with OCR" as UC5
        usecase "Extract Medicine Data" as UC6
    }
    
    package "Prescription Management" {
        usecase "View Prescriptions" as UC7
        usecase "Add Prescription for Patient" as UC8
        usecase "Create Medicine Package" as UC9
    }
    
    package "Notifications" {
        usecase "Send Upload Alert" as UC10
        usecase "Receive Notifications" as UC11
    }
}

rectangle "AI OCR System" as OCR {
    usecase "Analyze Image" as AI1
    usecase "Extract Text" as AI2
}

' Patient connections
P --> UC1
P --> UC2
P --> UC3
P --> UC4
P --> UC7
P --> UC11

' Pharmacist connections
Ph --> UC7
Ph --> UC8
Ph --> UC9
Ph --> UC10

' System connections
UC1 --> UC5
UC5 --> UC6
UC5 --> AI1
AI1 --> AI2
AI2 --> UC6

@enduml

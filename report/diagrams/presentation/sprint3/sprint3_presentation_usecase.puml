@startuml sprint3_presentation
!theme plain
skinparam backgroundColor white
skinparam actor {
    BackgroundColor lightblue
    BorderColor black
}
skinparam usecase {
    BackgroundColor lightgreen
    BorderColor black
}

title Sprint 3: Order Management & Stock Management

actor "Patient" as P
actor "Pharmacist" as Ph
actor "Delivery Personnel" as D
actor "Admin" as A

rectangle "Sprint 3 Features" {
    package "Order Management" {
        usecase "Browse Products" as UC1
        usecase "Add to Cart" as UC2
        usecase "Submit Order" as UC3
        usecase "Track Order" as UC4
        usecase "Process Order" as UC5
        usecase "Confirm Order" as UC6
    }
    
    package "Stock Management" {
        usecase "Manage Products" as UC7
        usecase "Update Stock" as UC8
        usecase "Manage Categories" as UC9
        usecase "Set Stock Alerts" as UC10
    }
    
    package "Delivery Management" {
        usecase "Update Order Status" as UC11
        usecase "Mark Delivered" as UC12
    }
}

' Patient connections
P --> UC1
P --> UC2
P --> UC3
P --> UC4

' Pharmacist connections
Ph --> UC5
Ph --> UC6
Ph --> UC7
Ph --> UC8
Ph --> UC9
Ph --> UC10

' Delivery connections
D --> UC11
D --> UC12

' Admin connections
A --> UC7
A --> UC8
A --> UC9
A --> UC10

@enduml

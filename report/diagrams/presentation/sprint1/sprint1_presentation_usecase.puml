@startuml sprint1_presentation
!theme plain
skinparam backgroundColor white
skinparam actor {
    BackgroundColor lightblue
    BorderColor black
}
skinparam usecase {
    BackgroundColor lightgreen
    BorderColor black
}

top to bottom direction

title Sprint 1: Authentication & Profile Management

' Actors positioned to avoid intersections
actor "Patient" as P
actor "Pharmacist" as Ph

rectangle "Sprint 1 Features" {
    package "Authentication" {
        usecase "Login" as UC1
        usecase "Register Patient" as UC2
        usecase "Reset Password" as UC3
        usecase "Enable 2FA" as UC4
        usecase "Google OAuth" as UC5
    }

    package "Profile Management" {
        usecase "Update Profile" as UC6
        usecase "Upload Photo" as UC7
        usecase "Manage Address" as UC8
        usecase "View Profile" as UC9
    }
}

' Patient connections - positioned on left side
P --> UC1
P --> UC2
P --> UC3
P --> UC4
P --> UC5
P --> UC6
P --> UC7
P --> UC8
P --> UC9

' Pharmacist connections - positioned on right side
UC1 <-- Ph


@enduml

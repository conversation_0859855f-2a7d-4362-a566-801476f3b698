@startuml sprint4_presentation
!theme plain
skinparam backgroundColor white
skinparam actor {
    BackgroundColor lightblue
    BorderColor black
}
skinparam usecase {
    BackgroundColor lightgreen
    BorderColor black
}
skinparam system {
    BackgroundColor lightyellow
    BorderColor black
}

title Sprint 4: AI-Powered Prescription OCR System

actor "Patient" as P
actor "Pharmacist" as Ph
actor "System Admin" as A

rectangle "Sprint 4 Features" {
    package "AI OCR Processing" {
        usecase "Process Prescription Image" as UC1
        usecase "Extract Medicine Information" as UC2
        usecase "Generate Structured JSON" as UC3
        usecase "Validate OCR Results" as UC4
    }

    package "Integration & Testing" {
        usecase "Integrate with Backend" as UC5
        usecase "Test OCR Accuracy" as UC6
        usecase "Monitor Performance" as UC7
    }
}

rectangle "AI Systems" {
    usecase "Gemini AI Engine" as AI1
    usecase "EasyOCR + LLM" as AI2
    usecase "TrOCR Model" as AI3
}

' Patient connections (indirect through system)
P --> UC1 : "uploads prescription"

' Pharmacist connections
Ph --> UC4


' Admin connections
A --> UC5
A --> UC6
A --> UC7

' AI System connections
UC1 --> AI1
UC2 --> AI1
UC3 --> AI1

note right of AI1
  Final chosen solution:
  Google Gemini AI
  - 95% accuracy
  - 1-2 second processing
  - Structured JSON output
end note

@enduml

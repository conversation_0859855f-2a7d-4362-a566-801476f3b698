@startuml profile_usecase
!theme plain
skinparam usecase {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}

skinparam actor {
    BackgroundColor White
    BorderColor Black
}

' Actors positioned to avoid intersections
actor "Patient" as Patient
actor "Pharmacist" as Pharmacist

' System boundary
rectangle "Profile Management System" {
    
    ' Profile management use cases - positioned vertically
    usecase "Update Personal Details" as UC1
    usecase "Upload Profile Photo" as UC2
    usecase "Manage Delivery Address" as UC3
    usecase "Configure Preferences" as UC4
    usecase "View Profile Summary" as UC5
    
    ' Pharmacist-specific use cases
    usecase "View Patient Profile" as UC6
    usecase "Search Patients" as UC7
    usecase "Access Patient Information" as UC8
    
    ' Profile validation use cases
    usecase "Validate Profile Data" as UC9
    usecase "Sync Profile Changes" as UC10
}

' Patient connections - positioned on left side
Patient --> UC1
Patient --> UC2
Patient --> UC3
Patient --> UC4
Patient --> UC5

' Pharmacist connections - positioned on right side
Pharmacist --> UC6
Pharmacist --> UC7
Pharmacist --> UC8

' Include relationships
UC1 ..> UC9 : <<include>>
UC1 ..> UC10 : <<include>>
UC2 ..> UC9 : <<include>>
UC3 ..> UC9 : <<include>>
UC4 ..> UC10 : <<include>>
UC6 ..> UC8 : <<include>>
UC7 ..> UC6 : <<include>>

@enduml

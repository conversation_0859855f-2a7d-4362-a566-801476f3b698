@startuml
!theme plain
skinparam backgroundColor white
skinparam class {
    BackgroundColor lightblue
    BorderColor black
}
skinparam package {
    BackgroundColor lightyellow
    BorderColor black
}

title Sprint 1 Class Diagram - Authentication and Profile Management

' Core Entities
class User {
    -id: string
    -email: string
    -password: string
    -role: Role
    -phone: string
    -fcm?: string
    +getId(): string
    +getEmail(): string
    +getRole(): Role
}

class Patient {
    -id: string
    -pharmacyId: string
    -firstName: string
    -lastName: string
    -email: string
    -phone: string
    -post?: number
    -officePhone?: string
    -address: JSON[]
    -authorizedPerson?: JSON
    -paymentType?: PaymentType
    -deliveryManNote?: string
    -pharmacyNote?: string
    -householdNote?: string
    -spot?: string
    -signatureType?: SignatureType
    -hospitalizedPatient?: boolean
    -vacationPeriod?: JSON
    -recurrenceOption?: JSON
    -deliveryType: string[]
    -houseHoldId?: string
    -roomNumber?: number
    -createdBy?: string
    -deletedAt?: Date
    -deletedBy?: string
    +getId(): string
    +getFullName(): string
    +updateProfile(data: any): void
}

class UserProperties {
    -id: string
    -profilePhoto?: string
    -resetCode?: string
    -resetCodeExpiresAt?: Date
    -isTwoFactorEnabled: boolean
    -twoFactorCode?: string
    -twoFactorExpiresAt?: Date
    +getId(): string
    +isResetCodeValid(): boolean
}

enum Role {
    PATIENT
    INTERNAL_DELIVERY_MAN
    EXTERNAL_DELIVERY_MAN
    INTERNAL_PHARMACY
    EXTERNAL_PHARMACY
    ADMIN
}

enum PaymentType {
    CASH
    CARD
    INSURANCE
}

enum SignatureType {
    DIGITAL
    PHYSICAL
    NONE
}

' JSON Data Structures (as classes)
class AddressJSON <<JSON>> {
    address: string
    lat: number
    long: number
    apartmentNumber?: string
}

class AuthPersonJSON <<JSON>> {
    name: string
    type: string
    phone: string
}

class VacationJSON <<JSON>> {
    start: Date
    end: Date
}

class RecurrenceJSON <<JSON>> {
    note: string
    frequency: object
    from: Date
    to: Date
}

' Authentication Use Cases
class UserLogin {
    +execute(command: LoginCommand): Promise<LoginResponse>
}

class PatientRegister {
    +execute(command: PatientRegisterCommand): Promise<void>
}

class UserForgotPassword {
    +execute(command: ForgotPasswordCommand): Promise<boolean>
}

class UserResetPassword {
    +execute(command: ResetPasswordCommand): Promise<boolean>
}

' Two-Factor Authentication Use Cases
class EnableTwoFactorUseCase {
    +execute(userId: string): Promise<string>
}

class VerifyTwoFactor {
    +execute(command: VerifyTwoFactorCommand): Promise<string>
}

' Profile Management Use Cases
class UserGetProfile {
    +execute(userId: string): Promise<UserProfileResponse>
}

class PatientUpdateProfile {
    +execute(userId: string, command: UpdateProfileCommand): Promise<Patient>
}

class UploadProfilePhoto {
    +execute(userId: string, file: any): Promise<string>
}

' Entity Relationships
User ||--|| UserProperties : "same id"
User ||--|| Patient : "same id"
User }o--|| Role : "has role"
Patient }o--|| PaymentType : "has payment type"
Patient }o--|| SignatureType : "has signature type"

' JSON Relationships
Patient ||--o{ AddressJSON : "contains"
Patient ||--o| AuthPersonJSON : "contains"
Patient ||--o| VacationJSON : "contains"
Patient ||--o| RecurrenceJSON : "contains"

' Use Case Dependencies (simplified)
UserLogin ..> User : "authenticates"
PatientRegister ..> Patient : "creates"
PatientRegister ..> User : "creates"

UserForgotPassword ..> UserProperties : "updates reset code"
UserResetPassword ..> UserProperties : "clears reset code"

EnableTwoFactorUseCase ..> UserProperties : "enables 2FA"
VerifyTwoFactor ..> UserProperties : "verifies code"

UserGetProfile ..> User : "retrieves"
UserGetProfile ..> Patient : "retrieves"
PatientUpdateProfile ..> Patient : "updates"
UploadProfilePhoto ..> UserProperties : "updates photo"

@enduml

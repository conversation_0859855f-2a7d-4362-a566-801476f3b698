@startuml auth_sequence
!theme plain
skinparam sequence {
    ArrowColor Black
    ActorBorderColor Black
    LifeLineBorderColor Black
    ParticipantBorderColor Black
}

actor "<PERSON><PERSON>" as Patient
participant "Mobile App" as Mobile
participant "Auth Controller" as AuthController
participant "Auth Service" as AuthService
participant "JWT Service" as JWTService
participant "User Repository" as UserRepo
participant "Email Service" as EmailService
database "MongoDB" as DB

Patient -> Mobile: Enter credentials
Mobile -> AuthController: POST /auth/login
AuthController -> AuthService: validateUser(email, password)
AuthService -> UserRepo: findByEmail(email)
UserRepo -> DB: Query user
DB --> UserRepo: User data
UserRepo --> AuthService: User entity
AuthService -> AuthService: validatePassword(password)

alt Valid credentials
    AuthService -> JWTService: generateToken(user)
    JWTService --> AuthService: JWT token
    AuthService --> AuthController: Authentication success
    AuthController --> Mobile: 200 OK + JWT token
    Mobile -> Mobile: Store token securely
    
    alt 2FA enabled
        Mobile -> AuthController: POST /auth/verify-2fa
        AuthController -> AuthService: verify2FA(token, code)
        AuthService -> EmailService: sendVerificationCode(email)
        EmailService --> AuthService: Code sent
        AuthService --> AuthController: 2FA code sent
        AuthController --> Mobile: 2FA required
        Mobile --> Patient: Enter 2FA code
        Patient -> Mobile: 2FA code
        Mobile -> AuthController: POST /auth/confirm-2fa
        AuthController -> AuthService: validateCode(code)
        AuthService --> AuthController: Code validated
        AuthController --> Mobile: Authentication complete
    end
    
    Mobile --> Patient: Login successful
else Invalid credentials
    AuthService --> AuthController: Authentication failed
    AuthController --> Mobile: 401 Unauthorized
    Mobile --> Patient: Login failed
end

@enduml

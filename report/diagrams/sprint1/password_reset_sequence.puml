@startuml password_reset_sequence
!theme plain
skinparam sequence {
    ArrowColor Black
    ActorBorderColor Black
    LifeLineBorderColor Black
    ParticipantBorderColor Black
}

actor "<PERSON><PERSON>" as Patient
participant "Mobile App" as Mobile
participant "Auth <PERSON>" as AuthController
participant "Auth Service" as AuthService
participant "Email Service" as EmailService
participant "User Repository" as UserRepo
database "MongoDB" as DB

Patient -> Mobile: Request password reset
Mobile -> AuthController: POST /auth/forgot-password
AuthController -> AuthService: initiatePasswordReset(email)
AuthService -> UserRepo: findByEmail(email)
UserRepo -> DB: Query user
DB --> UserRepo: User data

alt User exists
    UserRepo --> AuthService: User found
    AuthService -> AuthService: generateResetCode()
    AuthService -> UserRepo: saveResetCode(userId, code)
    UserRepo -> DB: Update user with reset code
    DB --> UserRepo: Code saved
    AuthService -> EmailService: sendResetCode(email, code)
    EmailService --> AuthService: Email sent
    AuthService --> AuthController: Reset initiated
    AuthController --> Mobile: 200 OK - Check email
    Mobile --> Patient: Check your email for reset code
    
    Patient -> Mobile: Enter reset code + new password
    Mobile -> AuthController: POST /auth/reset-password
    AuthController -> AuthService: resetPassword(email, code, newPassword)
    AuthService -> UserRepo: validateResetCode(email, code)
    UserRepo -> DB: Query reset code
    DB --> UserRepo: Code validation result
    
    alt Valid reset code
        UserRepo --> AuthService: Code valid
        AuthService -> AuthService: hashPassword(newPassword)
        AuthService -> UserRepo: updatePassword(userId, hashedPassword)
        UserRepo -> DB: Update password
        DB --> UserRepo: Password updated
        AuthService -> UserRepo: clearResetCode(userId)
        UserRepo -> DB: Clear reset code
        AuthService --> AuthController: Password reset successful
        AuthController --> Mobile: 200 OK - Password updated
        Mobile --> Patient: Password reset successful
    else Invalid or expired code
        UserRepo --> AuthService: Code invalid
        AuthService --> AuthController: Invalid reset code
        AuthController --> Mobile: 400 Bad Request
        Mobile --> Patient: Invalid or expired code
    end
    
else User not found
    UserRepo --> AuthService: User not found
    AuthService --> AuthController: User not found
    AuthController --> Mobile: 404 Not Found
    Mobile --> Patient: Email not registered
end

@enduml

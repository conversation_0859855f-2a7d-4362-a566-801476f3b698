@startuml auth_usecase
!theme plain
skinparam usecase {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}

skinparam actor {
    BackgroundColor White
    BorderColor Black
}

' Use explicit layout with actors positioned around the system
' to create clear separation and avoid intersections

' Top left actor
actor "Patient" as Patient

' Top right actor
actor "Pharmacist" as Pharmacist

rectangle "Authentication Management System" {

    ' Arrange use cases in a grid to minimize crossings
    usecase "Login" as UC1
    usecase "Logout" as UC2

    usecase "Enable 2FA" as UC3
    usecase "Verify 2FA Code" as UC4
    usecase "Google OAuth Login" as UC5

    usecase "Register Patient" as UC6
    usecase "Activate Account" as UC7
    usecase "Send Activation Link" as UC8

    usecase "Reset Password" as UC9
    usecase "Forgot Password" as UC10
    usecase "Verify Reset Code" as UC14
    usecase "Change Password" as UC11

    usecase "Manage Session" as UC12
    usecase "Refresh Token" as UC13
}

' Bottom left actor
actor "Delivery Personnel" as Delivery

' Bottom right actor
actor "Administrator" as Admin

' Patient connections (from top left)
Patient --> UC1
Patient --> UC2
Patient --> UC3
Patient --> UC4
Patient --> UC5
Patient --> UC7
Patient --> UC9
Patient --> UC10
Patient --> UC14
Patient --> UC11

' Pharmacist connections (from top right)
Pharmacist --> UC1
Pharmacist --> UC2
Pharmacist --> UC6
Pharmacist --> UC8
Pharmacist --> UC11

' Delivery connections (from bottom left)
Delivery --> UC1
Delivery --> UC2

' Admin connections (from bottom right)
Admin --> UC1
Admin --> UC2
Admin --> UC12
Admin --> UC13

' Include relationships
UC1 ..> UC12 : <<include>>
UC1 ..> UC13 : <<include>>
UC3 ..> UC4 : <<include>>
UC6 ..> UC8 : <<include>>
UC10 ..> UC9 : <<include>>
UC9 ..> UC14 : <<include>>

@enduml

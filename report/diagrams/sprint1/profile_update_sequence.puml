@startuml profile_update_sequence
!theme plain
skinparam sequence {
    ArrowColor Black
    ActorBorderColor Black
    LifeLineBorderColor Black
    ParticipantBorderColor Black
}

actor "Patient" as Patient
participant "Mobile App" as Mobile
participant "Patient Controller" as PatientController
participant "Patient Service" as PatientService
participant "Patient Repository" as PatientRepo
participant "File Service" as FileService
database "MongoDB" as DB
participant "File System" as FileSystem

Patient -> Mobile: Update profile information
Mobile -> PatientController: PUT /patients/profile
PatientController -> PatientService: updateProfile(patientId, profileData)
PatientService -> PatientRepo: findById(patientId)
PatientRepo -> DB: Query patient
DB --> PatientRepo: Patient data
PatientRepo --> PatientService: Patient entity

PatientService -> PatientService: validateProfileData(profileData)

alt Valid profile data
    PatientService -> PatientRepo: updatePatient(patientId, profileData)
    PatientRepo -> DB: Update patient document
    DB --> PatientRepo: Update result
    PatientRepo --> PatientService: Patient updated
    PatientService --> PatientController: Profile updated
    PatientController --> Mobile: 200 OK - Profile updated
    
    alt Profile photo upload requested
        Patient -> Mobile: Select profile photo
        Mobile -> PatientController: POST /patients/uploadProfilePhoto
        PatientController -> FileService: validateImageFile(file)
        
        alt Valid image file
            FileService -> FileService: generateFileName()
            FileService -> FileSystem: saveFile(uploads/profilePhotos/)
            FileSystem --> FileService: File saved
            FileService -> PatientService: updateProfilePhoto(patientId, fileName)
            PatientService -> PatientRepo: updateProfilePhoto(patientId, photoPath)
            PatientRepo -> DB: Update photo path
            DB --> PatientRepo: Photo updated
            PatientService --> FileService: Photo path updated
            FileService --> PatientController: Photo uploaded
            PatientController --> Mobile: 200 OK - Photo uploaded
            Mobile --> Patient: Profile photo updated
        else Invalid file
            FileService --> PatientController: Invalid file format
            PatientController --> Mobile: 400 Bad Request
            Mobile --> Patient: Invalid file format
        end
    end
    
    Mobile --> Patient: Profile updated successfully
    
else Invalid profile data
    PatientService --> PatientController: Validation failed
    PatientController --> Mobile: 400 Bad Request
    Mobile --> Patient: Invalid profile data
end

@enduml

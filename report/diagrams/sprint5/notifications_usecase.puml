@startuml notifications_usecase
!theme plain
skinparam actorStyle awesome

top to bottom direction

actor Patient as P
actor Pharmacist as Ph
actor "Delivery Personnel" as D
actor "System Admin" as A

rectangle "MED4SOLUTIONS Notification System" {
    package "Patient Notifications" {
        usecase "Receive Prescription\nRenewal Alerts" as UC1
        usecase "Receive Order\nStatus Updates" as UC8
        usecase "Receive Delivery\nNotifications" as UC9
    }

    package "Common Notification Actions" {
        usecase "Read Notifications" as UC2
        usecase "Mark Notifications\nas Read/Unread" as UC3
        usecase "View Notification\nHistory" as UC5
        usecase "Manage Notification\nPreferences" as UC4
    }

    package "Pharmacist Notifications" {
        usecase "Receive Prescription\nAdded Notifications" as UC6
        usecase "Receive Package\nCreation Alerts" as UC7
    }

    package "System Administration" {
        usecase "Send System\nAnnouncements" as UC10
        usecase "Send Maintenance\nAlerts" as UC11
        usecase "Manage Notification\nTemplates" as UC12
    }

    package "Firebase Integration" {
        usecase "Register FCM Token" as UC13
        usecase "Handle Push\nNotifications" as UC14
        usecase "Sync Notification\nStatus" as UC15
    }
}

}

' Patient use cases - positioned on the left
P --> UC1
P --> UC2
P --> UC3
P --> UC4
P --> UC5
P --> UC8
P --> UC9
P --> UC13
P --> UC14

' Pharmacist use cases - positioned on the right
UC2 <-- Ph
UC3 <-- Ph
UC6 <-- Ph
UC7 <-- Ph
UC13 <-- Ph
UC14 <-- Ph

' Delivery Personnel use cases - positioned at bottom
UC2 <-- D
UC9 <-- D
UC13 <-- D
UC14 <-- D

' System Admin use cases - positioned at top right
UC10 <-- A
UC11 <-- A
UC12 <-- A

' Include relationships
UC13 ..> UC15 : <<include>>
UC14 ..> UC15 : <<include>>

' Extend relationships
UC1 ..> UC2 : <<extend>>
UC6 ..> UC7 : <<extend>>
UC8 ..> UC9 : <<extend>>

@enduml

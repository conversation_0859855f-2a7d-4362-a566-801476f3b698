@startuml
!theme plain
skinparam backgroundColor white
skinparam participant {
    BackgroundColor lightblue
    BorderColor black
}
skinparam actor {
    BackgroundColor lightgreen
    BorderColor black
}

title Prescription Addition Notification Sequence Diagram

actor "Patient" as patient
participant "Mobile App" as mobile
participant "Backend API" as backend
participant "Prescription\nService" as prescService
participant "Notification\nService" as notifService
participant "FCM Service" as fcmService
participant "Firebase Cloud\nMessaging" as fcm
participant "Web App" as web
actor "Pharmacist" as pharmacist

== Prescription Addition ==
patient -> mobile : Add prescription with\nOCR processed data
mobile -> backend : POST /prescriptions\n{prescriptionData, packageType}
backend -> prescService : createPrescription(data)
prescService -> prescService : Save prescription\nto database

== Notification Trigger ==
prescService -> notifService : triggerPrescriptionNotification(\nprescriptionId, pharmacistId)
notifService -> notifService : Create notification record\nin database

== FCM Token Retrieval ==
notifService -> notifService : Get pharmacist's\nFCM token from database
alt FCM Token exists
    notifService -> fcmService : sendNotification(token, payload)
    
    == Firebase Message Sending ==
    fcmService -> fcmService : Create FCM message\nwith notification payload
    fcmService -> fcm : Send push notification\nto pharmacist's devices
    fcm -> web : Deliver notification\n(if web app is active)
    fcm -> fcmService : Return message ID
    fcmService -> notifService : Return delivery status
    
    == Notification Display ==
    web -> web : Display notification\nin browser
    web -> pharmacist : Show prescription\nnotification alert
    
    == Notification Interaction ==
    pharmacist -> web : Click notification
    web -> backend : GET /prescriptions/{id}
    backend -> web : Return prescription details
    web -> pharmacist : Display prescription\nfor package creation
    
else No FCM Token
    notifService -> notifService : Log warning:\nNo FCM token found
end

== Response to Mobile ==
notifService -> prescService : Notification sent successfully
prescService -> backend : Prescription created\nwith notification sent
backend -> mobile : 201 Created\n{prescriptionId, status}
mobile -> patient : Show success message\n"Prescription added successfully"

@enduml

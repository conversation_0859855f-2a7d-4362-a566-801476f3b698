@startuml
!theme plain
skinparam backgroundColor white
skinparam class {
    BackgroundColor lightblue
    BorderColor black
}
skinparam interface {
    BackgroundColor lightgreen
    BorderColor black
}
skinparam package {
    BackgroundColor lightyellow
    BorderColor black
}

title Firebase Integration Class Diagram

package "Backend (NestJS)" {
    class FCMService {
        -app: admin.app.App
        -logger: Logger
        --
        +constructor()
        +initializeFirebase(): void
        +sendNotification(fcmToken: string, payload: FCMNotificationPayload): Promise<string>
        +sendBatchNotifications(tokens: string[], payload: FCMNotificationPayload): Promise<BatchResponse>
        +validateToken(token: string): Promise<boolean>
        -createMessage(token: string, payload: FCMNotificationPayload): Message
    }
    
    class FirebaseConfig {
        +type: string
        +project_id: string
        +private_key_id: string
        +private_key: string
        +client_email: string
        +client_id: string
        +auth_uri: string
        +token_uri: string
    }
    
    class FCMNotificationPayload {
        +title: string
        +message: string
        +type: NotificationType
        +data: any
    }
    
    class Message {
        +token: string
        +notification: NotificationPayload
        +data: {[key: string]: string}
        +android: AndroidConfig
        +apns: ApnsConfig
    }
}

package "Web Frontend (Angular)" {
    class FirebaseService {
        -app: FirebaseApp
        -messaging: Messaging
        -fcmTokenSubject: BehaviorSubject<string>
        -messageSubject: BehaviorSubject<MessagePayload>
        --
        +constructor()
        +initializeFirebase(): void
        +requestPermission(): Promise<boolean>
        +getFcmToken(): Promise<string>
        +setupMessageListener(): void
        +showBrowserNotification(payload: MessagePayload): void
    }
    
    class FirebaseConfig {
        +apiKey: string
        +authDomain: string
        +projectId: string
        +storageBucket: string
        +messagingSenderId: string
        +appId: string
        +vapidKey: string
    }
    
    class ServiceWorker {
        +messaging: Messaging
        --
        +onBackgroundMessage(payload: MessagePayload): void
        +showNotification(title: string, options: NotificationOptions): void
        +onNotificationClick(event: NotificationEvent): void
    }
    
    class NotificationComponent {
        -firebaseService: FirebaseService
        -notifications: Notification[]
        --
        +ngOnInit(): void
        +requestNotificationPermission(): void
        +handleIncomingMessage(payload: MessagePayload): void
        +markAsRead(notificationId: string): void
    }
}

package "Mobile Frontend (Flutter)" {
    class FirebaseMessagingService {
        -firebaseMessaging: FirebaseMessaging
        -localNotifications: FlutterLocalNotificationsPlugin
        -fcmToken: string
        -onTokenRefresh: Function
        -onMessageReceived: Function
        --
        +initialize(): Future<void>
        +requestPermission(): Future<void>
        +getToken(): Future<String>
        +subscribeToTopic(topic: String): Future<void>
        +handleForegroundMessage(message: RemoteMessage): void
        +handleBackgroundMessage(message: RemoteMessage): void
        +handleNotificationTap(message: RemoteMessage): void
    }
    
    class NotificationService {
        -repository: NotificationRepository
        -bloc: NotificationBloc
        --
        +initialize(context: BuildContext): Future<void>
        +onTokenRefresh(token: String): void
        +onMessageReceived(data: Map<String, dynamic>): void
        +registerFcmToken(token: String): Future<void>
    }
    
    class LocalNotificationHelper {
        -plugin: FlutterLocalNotificationsPlugin
        --
        +initialize(): Future<void>
        +showNotification(title: String, body: String, payload: String): Future<void>
        +createNotificationChannel(): void
        +onNotificationTap(payload: String): void
    }
    
    class NotificationBloc {
        +state: NotificationState
        --
        +add(event: NotificationEvent): void
        +mapEventToState(event: NotificationEvent): Stream<NotificationState>
        +loadNotifications(): void
        +markAsRead(notificationId: String): void
    }
}

package "Firebase Cloud Messaging" {
    class FCMServer {
        --
        +sendToDevice(token: string, payload: any): Promise<string>
        +sendToTopic(topic: string, payload: any): Promise<string>
        +sendBatch(tokens: string[], payload: any): Promise<BatchResponse>
        +validateToken(token: string): Promise<boolean>
    }
    
    class MessagePayload {
        +notification: NotificationPayload
        +data: {[key: string]: string}
        +token: string
        +topic: string
    }
    
    class NotificationPayload {
        +title: string
        +body: string
        +icon: string
        +badge: string
        +sound: string
    }
}

' Relationships
FCMService --> FirebaseConfig
FCMService --> FCMNotificationPayload
FCMService --> Message
FCMService --> FCMServer

FirebaseService --> FirebaseConfig
FirebaseService --> ServiceWorker
NotificationComponent --> FirebaseService

FirebaseMessagingService --> NotificationService
FirebaseMessagingService --> LocalNotificationHelper
NotificationService --> NotificationBloc

FCMServer --> MessagePayload
MessagePayload --> NotificationPayload

' Cross-platform relationships
FCMService ..> FCMServer : "sends via"
FirebaseService ..> FCMServer : "receives from"
FirebaseMessagingService ..> FCMServer : "receives from"

@enduml

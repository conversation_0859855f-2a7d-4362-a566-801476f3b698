@startuml
!theme plain
skinparam backgroundColor white
skinparam participant {
    BackgroundColor lightblue
    BorderColor black
}
skinparam actor {
    BackgroundColor lightgreen
    BorderColor black
}

title Order Status Notification Sequence Diagram

actor "Delivery Personnel" as delivery
participant "Web App" as web
participant "Backend API" as backend
participant "Order Service" as orderService
participant "Notification\nService" as notifService
participant "FCM Service" as fcmService
participant "Firebase Cloud\nMessaging" as fcm
participant "Mobile App" as mobile
actor "Patient" as patient

== Order Status Update ==
delivery -> web : Update order status\nto "In Transit"
web -> backend : PUT /orders/{orderId}/status\n{status: "IN_TRANSIT"}
backend -> orderService : updateOrderStatus(orderId, status)
orderService -> orderService : Update order status\nin database

== Notification Trigger ==
orderService -> notifService : triggerOrderStatusNotification(\norderId, patientId, newStatus)
notifService -> notifService : Create notification record\nwith order status details

== Patient FCM Token Retrieval ==
notifService -> notifService : Get patient's FCM token\nfrom user database
alt FCM Token exists
    notifService -> fcmService : sendNotification(token, payload)
    
    == Firebase Message Construction ==
    fcmService -> fcmService : Create FCM message:\n{\n  title: "Order Update",\n  body: "Your order is now in transit",\n  data: {orderId, status}\n}
    
    == Push Notification Delivery ==
    fcmService -> fcm : Send push notification\nto patient's mobile device
    fcm -> mobile : Deliver notification\n(background/foreground)
    fcm -> fcmService : Return delivery confirmation
    fcmService -> notifService : Notification sent successfully
    
    == Mobile Notification Handling ==
    alt Mobile app in foreground
        mobile -> mobile : Display in-app notification
        mobile -> patient : Show order status update
    else Mobile app in background
        mobile -> mobile : Show system notification
        patient -> mobile : Tap notification
        mobile -> mobile : Navigate to order\ntracking screen
        mobile -> backend : GET /orders/{orderId}
        backend -> mobile : Return updated order details
        mobile -> patient : Display order tracking\nwith current status
    end
    
else No FCM Token
    notifService -> notifService : Log warning:\nPatient has no FCM token
    notifService -> notifService : Store notification\nfor later retrieval
end

== Response Chain ==
notifService -> orderService : Notification processing complete
orderService -> backend : Order status updated\nwith notification sent
backend -> web : 200 OK\n{orderId, newStatus}
web -> delivery : Show success message\n"Order status updated"

== Additional Status Updates ==
note over orderService, patient : This process repeats for each\nstatus change: "Out for Delivery",\n"Delivered", etc.

@enduml

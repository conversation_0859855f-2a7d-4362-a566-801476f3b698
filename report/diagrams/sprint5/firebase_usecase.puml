@startuml firebase_usecase
!theme plain
skinparam actorStyle awesome

top to bottom direction

actor "Mobile App" as M
actor "Web App" as W
actor "Backend Service" as B
actor "Firebase Cloud Messaging" as F

rectangle "MED4SOLUTIONS Firebase Integration" {
    package "Token Management" {
        usecase "Generate FCM Token" as UC1
        usecase "Register Token\nwith Backend" as UC2
        usecase "Refresh FCM Token" as UC3
        usecase "Store User Tokens" as UC4
    }

    package "Message Operations" {
        usecase "Send Push\nNotification" as UC5
        usecase "Create Notification\nPayload" as UC6
        usecase "Handle Message\nDelivery" as UC7
    }

    package "Client Message Handling" {
        usecase "Receive Foreground\nMessages" as UC8
        usecase "Receive Background\nMessages" as UC9
        usecase "Handle Notification\nTaps" as UC10
    }

    package "Platform Specific Features" {
        usecase "Configure Service\nWorker (Web)" as UC11
        usecase "Handle Local\nNotifications (Mobile)" as UC12
        usecase "Manage Notification\nPermissions" as UC13
    }

    package "Backend Integration" {
        usecase "Initialize Firebase\nAdmin SDK" as UC14
        usecase "Validate FCM\nTokens" as UC15
        usecase "Track Notification\nDelivery Status" as UC16
    }
}

}

' Mobile App use cases - positioned on the left
M --> UC1
M --> UC2
M --> UC3
M --> UC8
M --> UC9
M --> UC10
M --> UC12
M --> UC13

' Web App use cases - positioned on the top
UC1 <-- W
UC2 <-- W
UC3 <-- W
UC8 <-- W
UC9 <-- W
UC10 <-- W
UC11 <-- W
UC13 <-- W

' Backend Service use cases - positioned on the right
UC4 <-- B
UC5 <-- B
UC6 <-- B
UC14 <-- B
UC15 <-- B
UC16 <-- B

' Firebase Cloud Messaging use cases - positioned at bottom
UC7 <-- F
UC8 <-- F
UC9 <-- F

' Include relationships
UC1 ..> UC2 : <<include>>
UC3 ..> UC2 : <<include>>
UC5 ..> UC6 : <<include>>
UC5 ..> UC7 : <<include>>
UC2 ..> UC4 : <<include>>
UC5 ..> UC15 : <<include>>
UC7 ..> UC16 : <<include>>

' Extend relationships
UC8 ..> UC10 : <<extend>>
UC9 ..> UC10 : <<extend>>

@enduml

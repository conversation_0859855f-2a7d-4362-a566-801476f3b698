@startuml
!theme plain
skinparam backgroundColor white
skinparam participant {
    BackgroundColor lightblue
    BorderColor black
}
skinparam actor {
    BackgroundColor lightgreen
    BorderColor black
}
skinparam maxMessageSize 120
skinparam sequenceMessageAlign center
skinparam minClassWidth 80

title Firebase Token Management

actor "User" as user
participant "Client App" as client
participant "Firebase" as firebase
participant "Backend" as backend

== Initialization & Registration ==
user -> client : Launch app
client -> firebase : Initialize & request permissions
alt Permission OK
    firebase -> client : FCM token generated
    client -> backend : Register token
    backend -> client : Token saved
    client -> user : Ready for notifications
else Permission Denied
    firebase -> client : No permissions
    client -> user : Notifications disabled
end

== Token Management ==
firebase -> client : Token refresh
client -> backend : Update token
backend -> client : Updated

backend -> firebase : Validate token (before sending)
alt Valid
    firebase -> backend : Token OK
else Invalid
    firebase -> backend : Token expired
    backend -> backend : Skip notification
end

== Logout ==
user -> client : Logout
client -> backend : Clear token
backend -> client : Cleared
client -> firebase : Remove local token

@enduml

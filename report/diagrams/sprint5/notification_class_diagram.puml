@startuml notification_class_diagram
!theme plain
skinparam backgroundColor white
skinparam class {
    BackgroundColor lightblue
    BorderColor black
}
skinparam interface {
    BackgroundColor lightgreen
    BorderColor black
}
skinparam enum {
    BackgroundColor lightyellow
    BorderColor black
}

title Notification System Class Diagram

package "Entities" {
    class Notification {
        +id: string
        +recipientId: string
        +senderId?: string
        +title: string
        +message: string
        +type: NotificationType
        +status: NotificationStatus
        +data?: any
        +fcmSent: boolean
        +fcmMessageId?: string
        +readAt?: Date
        +createdAt: Date
        +updatedAt: Date
    }

    class User {
        +id: string
        +email: string
        +password: string
        +role: UserRole
        +fcm?: string
        +isActive: boolean
        +createdAt: Date
        +updatedAt: Date
        --
        +updateFcmToken(token: string): void
        +clearFcmToken(): void
    }

    enum NotificationType {
        PRESCRIPTION_ADDED
        PRESCRIPTION_RENEWAL
        ORDER_STATUS_UPDATE
        DELIVERY_UPDATE
        SYSTEM_ANNOUNCEMENT
        MAINTENANCE_ALERT
    }

    enum NotificationStatus {
        UNREAD
        READ
        DELETED
    }
}

package "Commands" {
    class SendNotificationCommand {
        +recipientId: string
        +senderId?: string
        +title: string
        +message: string
        +type: NotificationType
        +data?: any
    }

    class GetUserNotificationsCommand {
        +userId: string
        +status?: NotificationStatus
        +type?: NotificationType
        +page: number
        +limit: number
    }

    class MarkNotificationAsReadCommand {
        +notificationId: string
        +userId: string
    }

    class UpdateFcmTokenCommand {
        +userId: string
        +fcmToken: string
    }
}

package "DTOs" {
    class FCMNotificationPayload {
        +title: string
        +message: string
        +type: string
        +data?: any
    }
}

package "Services" {
    class FCMService {
        -logger: Logger
        --
        +sendNotification(fcmToken: string, payload: FCMNotificationPayload): Promise<string>
        +sendMulticastNotification(fcmTokens: string[], payload: FCMNotificationPayload): Promise<any>
        +validateToken(fcmToken: string): Promise<boolean>
        -initializeFirebase(): void
    }
}

package "Use Cases" {
    class SendNotification {
        -notificationRepository: NotificationRepository
        -fcmService: FCMService
        -userRepository: UserRepository
        -logger: Logger
        --
        +execute(command: SendNotificationCommand): Promise<Notification>
    }

    class GetUserNotifications {
        -notificationRepository: NotificationRepository
        --
        +execute(command: GetUserNotificationsCommand): Promise<{notifications: Notification[], total: number}>
    }

    class MarkNotificationAsRead {
        -notificationRepository: NotificationRepository
        --
        +execute(command: MarkNotificationAsReadCommand): Promise<Notification>
    }

    class UpdateFcmToken {
        -userRepository: UserRepository
        --
        +execute(command: UpdateFcmTokenCommand): Promise<void>
    }

    class SendPrescriptionNotification {
        -sendNotificationUseCase: SendNotification
        -userRepository: UserRepository
        -logger: Logger
        --
        +execute(data: PrescriptionNotificationData): Promise<void>
        -buildNotificationMessage(data: any): string
    }

    class SendPharmacistPrescriptionNotification {
        -sendNotificationUseCase: SendNotification
        -userRepository: UserRepository
        -logger: Logger
        --
        +execute(data: PrescriptionNotificationData): Promise<void>
        -buildNotificationMessage(data: any): string
    }
}

package "Repositories" {
    class NotificationRepository {
        -notificationModel: Model<Notification>
        --
        +create(data: Partial<Notification>): Promise<Notification>
        +findById(id: string): Promise<Notification>
        +findByRecipientId(recipientId: string, status?: NotificationStatus, type?: NotificationType, page: number, limit: number): Promise<{notifications: Notification[], total: number}>
        +markAsRead(notificationId: string, userId: string): Promise<Notification>
        +markAllAsRead(userId: string): Promise<void>
        +getUnreadCount(userId: string): Promise<number>
        +updateFcmStatus(notificationId: string, fcmMessageId: string): Promise<void>
        +delete(notificationId: string, userId: string): Promise<void>
    }
}

package "Controllers" {
    class NotificationController {
        -sendNotificationUseCase: SendNotification
        -getUserNotificationsUseCase: GetUserNotifications
        -markNotificationAsReadUseCase: MarkNotificationAsRead
        -updateFcmTokenUseCase: UpdateFcmToken
        --
        +getUserNotifications(req: any, query: any, res: Response): Promise<Response>
        +markAsRead(req: any, notificationId: string, res: Response): Promise<Response>
        +updateFcmToken(req: any, body: any, res: Response): Promise<Response>
        +sendNotification(req: any, body: any, res: Response): Promise<Response>
    }
}

' Entity Relationships
Notification --> NotificationType
Notification --> NotificationStatus
User ||--o{ Notification : "receives"
User ||--o{ Notification : "sends"

' Use Case Dependencies
SendNotification --> NotificationRepository
SendNotification --> FCMService
GetUserNotifications --> NotificationRepository
MarkNotificationAsRead --> NotificationRepository
UpdateFcmToken --> UserRepository
SendPrescriptionNotification --> SendNotification
SendPharmacistPrescriptionNotification --> SendNotification

' Controller Dependencies
NotificationController --> SendNotification
NotificationController --> GetUserNotifications
NotificationController --> MarkNotificationAsRead
NotificationController --> UpdateFcmToken

' Command Usage
SendNotification ..> SendNotificationCommand : uses
GetUserNotifications ..> GetUserNotificationsCommand : uses
MarkNotificationAsRead ..> MarkNotificationAsReadCommand : uses
UpdateFcmToken ..> UpdateFcmTokenCommand : uses

' Service Dependencies
FCMService ..> FCMNotificationPayload : uses

@enduml

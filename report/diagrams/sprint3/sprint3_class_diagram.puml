@startuml sprint3_class_diagram
!theme plain
skinparam classAttributeIconSize 0
skinparam classFontSize 8
skinparam packageFontSize 9
skinparam maxMessageSize 60
skinparam wrapWidth 120
skinparam minClassWidth 80
skinparam linetype ortho

package "Order Management" {
    class Order {
        +id: string
        +patientId: string
        +status: OrderStatus
        +orderDate: Date
        +totalAmount: number
        +productIds: string[]
        +flag: boolean
        +quantity: number
        +deliveryManNote?: string
    }

    enum OrderStatus {
        PENDING
        APPROVED
        IN_TRANSIT
        DELIVERED
        CANCELLED
    }

    class OrderRepository {
        +create(order): Promise<Order>
        +update(id, update): Promise<Order>
        +delete(id): Promise<void>
        +findById(id): Promise<Order>
        +findBasketByPatientId(patientId): Promise<Order>
        +findAllByPatientId(patientId): Promise<Order[]>
    }

    class OrderAddShop {
        +execute(command): Promise<Order>
    }

    class OrderDeleteShop {
        +execute(patientId, productId): Promise<any>
    }

    class OrderGetBasket {
        +execute(patientId): Promise<Order>
    }

    class OrderConfirm {
        +execute(command): Promise<Order>
    }

    class OrderMarkDelivered {
        +execute(orderId): Promise<Order>
    }

    class OrderGetHistory {
        +execute(patientId): Promise<Order[]>
    }

    class OrderController {
        +addToBasket(req, body): Promise<any>
        +removeFromBasket(req, productId): Promise<any>
        +getBasket(req): Promise<any>
        +confirmOrder(req, body): Promise<any>
        +markOrderAsDelivered(id, req): Promise<any>
        +getOrderHistory(req): Promise<any>
    }
}

package "Product & Stock Management" {
    class Product {
        +id: string
        +pharmacyId: string
        +name: string
        +description: string
        +quantity: number
        +price: number
        +discount: number
        +stockStatus: StockStatus
        +categoryId: string
        +expiryDate: Date
        +supplier: string
        +barcode: string
        +brand: string
        +actif: boolean
    }

    enum StockStatus {
        IN_STOCK
        OUT_OF_STOCK
    }

    class Category {
        +id: string
        +name: string
    }

    class ProductRepository {
        +create(product): Promise<Product>
        +update(id, update): Promise<Product>
        +delete(id): Promise<void>
        +findById(id): Promise<Product>
        +findAll(): Promise<Product[]>
        +findWithFilters(filters): Promise<Product[]>
    }

    class CategoryRepository {
        +create(category): Promise<Category>
        +update(id, update): Promise<Category>
        +delete(id): Promise<void>
        +findById(id): Promise<Category>
        +findAll(): Promise<Category[]>
    }

    class ProductCreate {
        +execute(command, pharmacyId): Promise<Product>
    }

    class ProductUpdate {
        +execute(command): Promise<Product>
    }

    class ProductDelete {
        +execute(id): Promise<void>
    }

    class ProductGetAll {
        +execute(command): Promise<any>
    }

    class CategoryCreate {
        +execute(command): Promise<Category>
    }

    class CategoryUpdate {
        +execute(command): Promise<Category>
    }

    class ProductController {
        +createProduct(req, body): Promise<any>
        +updateProduct(req, body): Promise<any>
        +deleteProduct(id): Promise<any>
        +getAllProducts(query): Promise<any>
        +getProductById(id): Promise<any>
    }

    class CategoryController {
        +createCategory(body): Promise<any>
        +updateCategory(body): Promise<any>
        +deleteCategory(id): Promise<any>
        +getAllCategories(): Promise<any>
    }
}

' Relationships
Order ||--|| OrderStatus
Product ||--|| StockStatus
Product }|--|| Category

OrderRepository ||--o{ Order
ProductRepository ||--o{ Product
CategoryRepository ||--o{ Category

OrderController --> OrderAddShop
OrderController --> OrderDeleteShop
OrderController --> OrderGetBasket
OrderController --> OrderConfirm
OrderController --> OrderMarkDelivered
OrderController --> OrderGetHistory

ProductController --> ProductCreate
ProductController --> ProductUpdate
ProductController --> ProductDelete
ProductController --> ProductGetAll

CategoryController --> CategoryCreate
CategoryController --> CategoryUpdate

OrderAddShop --> OrderRepository
OrderDeleteShop --> OrderRepository
OrderGetBasket --> OrderRepository
OrderConfirm --> OrderRepository
OrderConfirm --> ProductRepository
OrderMarkDelivered --> OrderRepository
OrderGetHistory --> OrderRepository

ProductCreate --> ProductRepository
ProductUpdate --> ProductRepository
ProductDelete --> ProductRepository
ProductGetAll --> ProductRepository

CategoryCreate --> CategoryRepository
CategoryUpdate --> CategoryRepository

Order }|--o{ Product : contains

@enduml

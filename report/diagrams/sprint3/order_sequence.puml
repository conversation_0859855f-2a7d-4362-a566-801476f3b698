@startuml order_sequence
!theme plain
skinparam sequenceMessageAlign center

title Order Processing Sequence Diagram

actor Patient as P
actor Pharmacist as Ph
actor "Delivery Personnel" as DP
participant "Mobile App" as MA
participant "Web App" as WA
participant "Order Controller" as OC
participant "OrderAddShop" as OAS
participant "OrderConfirm" as OCF
participant "OrderPharmacistConfirm" as OPC
participant "OrderMarkDelivered" as OMD
participant "OrderRepository" as OR
participant "ProductRepository" as PR
participant "MongoDB" as DB
participant "Notification Service" as NS

== Add Product to Basket ==
P -> MA: Browse products and select item
MA -> OC: POST /basket/add {productId}
OC -> OAS: execute(command)
OAS -> OR: findBasketByPatientId(patientId)
OR -> DB: findOne({patientId, flag: false})
DB --> OR: existing basket or null
OAS -> PR: findById(productId)
PR -> DB: findOne({id: productId})
DB --> PR: product details
PR --> OAS: product with price

alt No existing basket
    OAS -> OR: create(new basket)
    OR -> DB: create({patientId, productIds: [productId], ...})
    DB --> OR: created basket
else Existing basket
    OAS -> OR: update(basketId, {add productId})
    OR -> DB: updateOne({id: basketId}, {$push: {productIds: productId}})
    DB --> OR: updated basket
end

OR --> OAS: basket result
OAS --> OC: basket
OC --> MA: {success: true, basket}
MA --> P: Product added to basket

== Confirm Order ==
P -> MA: Review basket and confirm order
MA -> OC: POST /basket/confirm {quantity, deliveryManNote}
OC -> OCF: execute(command)
OCF -> OR: findBasketByPatientId(patientId)
OR -> DB: findOne({patientId, flag: false})
DB --> OR: basket
OCF -> PR: findById(productId)
PR -> DB: findOne({id: productId})
DB --> PR: product details
PR --> OCF: product with pricing

OCF -> OCF: Calculate total amount\n(price - discount) * quantity
OCF -> OR: update(basketId, {flag: true, status: PENDING, ...})
OR -> DB: updateOne({id: basketId}, {flag: true, status: "PENDING", ...})
DB --> OR: pending order
OR --> OCF: order
OCF --> OC: pending order
OC -> NS: sendOrderNotificationToPharmacy(order)
NS --> OC: notification sent
OC --> MA: {success: true, order}
MA --> P: Order submitted for pharmacy approval

== Pharmacist Views and Confirms Order ==
Ph -> WA: Access order management dashboard
WA -> OC: GET /orders/filter?date=today&status=PENDING
OC -> OR: findByFilters({status: PENDING, orderDate: today})
OR -> DB: find({status: "PENDING", orderDate: {...}})
DB --> OR: pending orders
OR --> OC: orders list
OC --> WA: {success: true, orders}
WA --> Ph: Display pending orders

Ph -> WA: Select order and review details
WA -> OC: GET /orders/{orderId}
OC -> OR: findById(orderId)
OR -> DB: findOne({id: orderId})
DB --> OR: order details
OR --> OC: order
OC --> WA: {success: true, order}
WA --> Ph: Display order details

Ph -> WA: Confirm order with quantity and delivery note
WA -> OC: POST /orders/pharmacistConfirm {patientId, quantity, deliveryManNote}
OC -> OPC: execute(command)
OPC -> OR: findBasketByPatientId(patientId)
OR -> DB: findOne({patientId, flag: true, status: "PENDING"})
DB --> OR: pending order
OPC -> PR: findById(productId)
PR -> DB: findOne({id: productId})
DB --> PR: product details
PR --> OPC: product with pricing

OPC -> OPC: Calculate final amount\n(price - discount) * quantity
OPC -> OR: update(orderId, {status: APPROVED, quantity, deliveryManNote, totalAmount})
OR -> DB: updateOne({id: orderId}, {status: "APPROVED", ...})
DB --> OR: approved order
OR --> OPC: order
OPC --> OC: approved order
OC -> NS: sendOrderApprovalNotification(order)
NS --> OC: notification sent
OC --> WA: {success: true, order}
WA --> Ph: Order confirmed and approved

== Track Order Status ==
P -> MA: Check order status
MA -> OC: GET /basket/history
OC -> OR: findAllByPatientId(patientId)
OR -> DB: find({patientId}).sort({createdAt: -1})
DB --> OR: order history
OR --> OC: orders
OC --> MA: {success: true, orders}
MA --> P: Display order history with status

== Mark Order as Delivered ==
DP -> WA: Access delivery management interface
WA -> OC: GET /orders/filter?status=APPROVED&assignedTo=deliveryPersonId
OC -> OR: findByFilters({status: APPROVED, assignedTo: deliveryPersonId})
OR -> DB: find({status: "APPROVED", assignedTo: deliveryPersonId})
DB --> OR: assigned orders
OR --> OC: orders list
OC --> WA: {success: true, orders}
WA --> DP: Display assigned orders for delivery

DP -> WA: Select order and mark as delivered
WA -> OC: PUT /basket/markDelivered/{orderId}
OC -> OC: Check user role (delivery/admin)
OC -> OMD: execute(orderId)
OMD -> OR: findById(orderId)
OR -> DB: findOne({id: orderId})
DB --> OR: order details
OR --> OMD: order
OMD -> OR: update(orderId, {status: DELIVERED, deliveredAt: now()})
OR -> DB: updateOne({id: orderId}, {status: "DELIVERED", deliveredAt: now()})
DB --> OR: delivered order
OR --> OMD: order
OMD --> OC: delivered order
OC -> NS: sendDeliveryConfirmationNotification(order)
NS --> OC: notification sent
OC --> WA: {success: true, order}
WA --> DP: Order marked as delivered successfully

@enduml

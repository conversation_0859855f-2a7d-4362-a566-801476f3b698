@startuml stock_usecase
!theme plain
skinparam actorStyle awesome

top to bottom direction

actor Pharmacist as Ph
actor <PERSON><PERSON> as A

rectangle "Stock Management System" {
    package "Product Management" {
        usecase "Create Product" as UC1
        usecase "Update Product" as UC2
        usecase "Delete Product" as UC3
        usecase "View Product Details" as UC4
        usecase "Search Products" as UC5
        usecase "Filter Products" as UC6
    }

    package "Stock Control" {
        usecase "Manage Stock Levels" as UC7
        usecase "Update Stock Status" as UC8
        usecase "Set Low Stock Alerts" as UC9
        usecase "Track Expiry Dates" as UC17
    }

    package "Category Management" {
        usecase "Create Category" as UC10
        usecase "Update Category" as UC11
        usecase "Delete Category" as UC12
        usecase "Assign Product to Category" as UC13
    }

    package "Product Configuration" {
        usecase "Upload Product Images" as UC14
        usecase "Set Product Pricing" as UC15
        usecase "Manage Product Discounts" as UC16
        usecase "Manage Suppliers" as UC20
        usecase "Set Product Barcodes" as UC21
    }

    package "Reporting & Analytics" {
        usecase "Generate Stock Reports" as UC18
        usecase "Export Inventory Data" as UC19
    }
}

' Pharmacist use cases - positioned on the left
Ph --> UC1
Ph --> UC2
Ph --> UC3
Ph --> UC4
Ph --> UC5
Ph --> UC6
Ph --> UC7
Ph --> UC8
Ph --> UC9
Ph --> UC10
Ph --> UC11
Ph --> UC12
Ph --> UC13
Ph --> UC14
Ph --> UC15
Ph --> UC16
Ph --> UC17
Ph --> UC18
Ph --> UC19
Ph --> UC20
Ph --> UC21

' Admin use cases - positioned on the right (all pharmacist capabilities plus system-wide management)
UC1 <-- A
UC2 <-- A
UC3 <-- A
UC4 <-- A
UC5 <-- A
UC6 <-- A
UC7 <-- A
UC8 <-- A
UC9 <-- A
UC10 <-- A
UC11 <-- A
UC12 <-- A
UC13 <-- A
UC14 <-- A
UC15 <-- A
UC16 <-- A
UC17 <-- A
UC18 <-- A
UC19 <-- A
UC20 <-- A
UC21 <-- A

' Include relationships
UC1 ..> UC13 : <<include>>
UC1 ..> UC15 : <<include>>
UC2 ..> UC8 : <<include>>
UC7 ..> UC8 : <<include>>
UC18 ..> UC5 : <<include>>
UC19 ..> UC18 : <<include>>

' Extend relationships
UC5 ..> UC4 : <<extend>>
UC6 ..> UC4 : <<extend>>
UC9 ..> UC7 : <<extend>>
UC14 ..> UC1 : <<extend>>
UC16 ..> UC15 : <<extend>>
UC17 ..> UC7 : <<extend>>
UC20 ..> UC1 : <<extend>>
UC21 ..> UC1 : <<extend>>

@enduml

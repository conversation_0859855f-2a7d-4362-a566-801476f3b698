@startuml stock_sequence
!theme plain
skinparam sequenceMessageAlign center

title Stock Management Sequence Diagram

actor Pharmacist as Ph
participant "Web App" as WA
participant "Product Controller" as PC
participant "ProductCreate" as PCR
participant "ProductUpdate" as PU
participant "ProductRepository" as PR
participant "CategoryRepository" as CR
participant "MongoDB" as DB

== Create New Product ==
Ph -> WA: Access product management
WA -> WA: Display create product form
Ph -> WA: Fill product details and submit
WA -> PC: POST /products/create {product data}
PC -> PC: Validate user role (pharmacist/admin)
PC -> PCR: execute(command, pharmacyId)
PCR -> CR: findById(categoryId)
CR -> DB: findOne({id: categoryId})
DB --> CR: category details
CR --> PCR: category

PCR -> PCR: Prepare product data\n- Generate UUID\n- Set pharmacy ID\n- Calculate stock status
PCR -> PR: create(productData)
PR -> DB: create({id, pharmacyId, name, quantity, price, ...})
DB --> PR: created product
PR --> PCR: product
PCR --> PC: created product
PC --> WA: {success: true, product}
WA --> Ph: Product created successfully

== Update Product and Stock Status ==
Ph -> WA: Edit existing product
WA -> PC: GET /products/{id}
PC -> PR: findById(productId)
PR -> DB: findOne({id: productId})
DB --> PR: product details
PR --> PC: product
PC --> WA: {success: true, product}
WA --> Ph: Display product edit form

Ph -> WA: Update product details (quantity, price, etc.)
WA -> PC: PUT /products/update {updated data}
PC -> PU: execute(command)
PU -> PR: findById(productId)
PR -> DB: findOne({id: productId})
DB --> PR: existing product
PR --> PU: product

PU -> PU: Prepare update data\n- Auto-update stock status\n- Validate changes
PU -> PR: update(productId, updateData)
PR -> DB: updateOne({id: productId}, {quantity, stockStatus, ...})
DB --> PR: updated product
PR --> PU: product
PU --> PC: updated product
PC --> WA: {success: true, product}
WA --> Ph: Product updated successfully

== Search and Filter Products ==
Ph -> WA: Access inventory dashboard
WA -> PC: GET /products/pharmacy?search=&stockStatus=&actif=
PC -> PC: Extract pharmacy ID from user
PC -> PR: findForPharmacyWithFilters(pharmacyId, filters)
PR -> DB: find({pharmacyId, ...filters}).sort({createdAt: -1})
DB --> PR: filtered products
PR --> PC: products list
PC --> WA: {success: true, products, total}
WA --> Ph: Display filtered inventory

== Category Management ==
Ph -> WA: Manage product categories
WA -> PC: GET /categories/getAll
PC -> CR: findAll()
CR -> DB: find({}).sort({name: 1})
DB --> CR: all categories
CR --> PC: categories
PC --> WA: {success: true, categories}
WA --> Ph: Display categories list

Ph -> WA: Create new category
WA -> PC: POST /categories/create {name}
PC -> CR: create({id: UUID, name})
CR -> DB: create({id, name})
DB --> CR: created category
CR --> PC: category
PC --> WA: {success: true, category}
WA --> Ph: Category created

== Stock Status Automation ==
note over PU, PR: Automatic stock status update
PU -> PU: Check quantity value
alt quantity > 0
    PU -> PU: Set stockStatus = IN_STOCK
else quantity = 0
    PU -> PU: Set stockStatus = OUT_OF_STOCK
end

PU -> PR: update with new stockStatus
PR -> DB: updateOne({stockStatus})
DB --> PR: updated product

@enduml

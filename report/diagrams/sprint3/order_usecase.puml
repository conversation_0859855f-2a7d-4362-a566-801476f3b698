@startuml order_usecase
!theme plain
skinparam actorStyle awesome

top to bottom direction

actor <PERSON><PERSON> as P
actor Pharmacist as Ph
actor "Delivery Personnel" as D
actor <PERSON><PERSON> as A

rectangle "Order Management System" {
    package "Product Management" {
        usecase "Browse Products" as UC1
        usecase "Search Products" as UC2
        usecase "Filter by Category" as UC3
    }

    package "Basket Management" {
        usecase "Add to Basket" as UC4
        usecase "Remove from Basket" as UC5
        usecase "View Basket" as UC6
    }

    package "Order Processing" {
        usecase "Confirm Order" as UC7
        usecase "Track Order Status" as UC8
        usecase "View Order History" as UC9
    }

    package "Pharmacy Operations" {
        usecase "Process Order" as UC10
        usecase "Confirm Order for Patient" as UC11
        usecase "View All Orders" as UC12
        usecase "Filter Orders" as UC13
    }

    package "Delivery Operations" {
        usecase "Update Order Status" as UC14
        usecase "Mark Order Delivered" as UC15
    }

    package "Management & Reports" {
        usecase "View Order Details" as UC16
        usecase "Generate Order Reports" as UC17
    }
}

' Patient use cases - positioned on the left
P --> UC1
P --> UC2
P --> UC3
P --> UC4
P --> UC5
P --> UC6
P --> UC7
P --> UC8
P --> UC9

' Pharmacist use cases - positioned on the right
UC10 <-- Ph
UC11 <-- Ph
UC12 <-- Ph
UC13 <-- Ph
UC16 <-- Ph
UC17 <-- Ph

' Delivery Personnel use cases - positioned at bottom
UC14 <-- D
UC15 <-- D
UC16 <-- D

' Admin use cases - positioned at top right
UC12 <-- A
UC13 <-- A
UC16 <-- A
UC17 <-- A

' Include relationships
UC4 ..> UC1 : <<include>>
UC5 ..> UC6 : <<include>>
UC7 ..> UC6 : <<include>>
UC11 ..> UC10 : <<include>>
UC15 ..> UC14 : <<include>>

' Extend relationships
UC2 ..> UC1 : <<extend>>
UC3 ..> UC1 : <<extend>>
UC13 ..> UC12 : <<extend>>

@enduml

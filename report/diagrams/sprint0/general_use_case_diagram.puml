@startuml
!theme plain
top to bottom direction

skinparam backgroundColor white
skinparam actor {
    BackgroundColor lightblue
    BorderColor black
}
skinparam usecase {
    BackgroundColor lightgreen
    BorderColor black
}

title General Use Case Diagram - MED4SOLUTIONS Platform

' Top actors
actor "Patient" as patient
actor "Delivery Personnel" as delivery

' System boundary
rectangle "MED4SOLUTIONS Platform" #lightgray {

    note top : Authentication is mandatory for all users #red

    ' Patient use cases (top section)
    usecase "Manage Profile" as UC1
    usecase "Upload Prescription" as UC2
    usecase "Browse Medications" as UC3
    usecase "Manage Cart" as UC4
    usecase "Submit Order" as UC5
    usecase "View Order History" as UC6
    usecase "Set Delivery Preferences" as UC7
    usecase "Confirm Delivery" as UC8
    usecase "Manage Notifications" as UC9

    ' Pharmacist use cases (middle-left section)
    usecase "Manage Patient Accounts" as UC10
    usecase "View Patient Profiles" as UC11
    usecase "Manage Prescriptions" as UC12
    usecase "Process Orders" as UC13
    usecase "Create Packages" as UC14
    usecase "Manage Stock" as UC15
    usecase "Manage Product Categories" as UC16

    ' Delivery use cases (middle-right section)
    usecase "Manage Order Status" as UC17
    usecase "Mark Delivery Attempts" as UC18

    ' Admin management use cases (bottom section)
    usecase "Manage User Accounts" as UC19
    usecase "Manage System Performance" as UC20
    usecase "Manage Data Backup" as UC21
    usecase "Monitor System Security" as UC22
    usecase "Generate Reports" as UC23

    ' AI System component
    usecase "Process with AI/OCR" as UC24
}

' Bottom actors
actor "Pharmacist" as pharmacist
actor "System Administrator" as admin

' Patient relationships (top left)
patient --> UC1
patient --> UC2
patient --> UC3
patient --> UC4
patient --> UC5
patient --> UC6
patient --> UC7
patient --> UC8
patient --> UC9

' Delivery Personnel relationships (top right)
delivery --> UC17
delivery --> UC18

' Pharmacist relationships (bottom left)
pharmacist --> UC10
pharmacist --> UC11
pharmacist --> UC12
pharmacist --> UC13
pharmacist --> UC14
pharmacist --> UC15
pharmacist --> UC16

' System Administrator relationships (bottom right)
admin --> UC19
admin --> UC20
admin --> UC21
admin --> UC22
admin --> UC23

' System relationships (automated processes)
UC2 ..> UC24 : <<include>>
UC5 ..> UC14 : <<include>>

@enduml

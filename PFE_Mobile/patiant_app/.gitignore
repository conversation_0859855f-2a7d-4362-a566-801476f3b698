# Flutter specific files
build/
.flutter/
.packages
.dart_tool/
.metadata
.pub-cache/
.pub/
.flutter-plugins
.flutter-plugins-dependencies

# iOS related files
ios/Flutter/Flutter.framework
ios/Flutter/App.framework
ios/Flutter/Flutter.podspec
ios/Flutter/Generated.xcconfig
ios/Flutter/*.xcworkspace
ios/Flutter/*.xcuserstate
ios/.symlinks/
ios/Pods/
ios/Runner.xcworkspace/
ios/Runner.xcuserstate
ios/Runner/GoogleService-Info.plist

# Android related files
android/.gradle/
android/.idea/
android/app/debug.keystore
android/key.properties
android/app/google-services.json
android/build/

# macOS related files
macos/Flutter/Flutter.framework
macos/Flutter/App.framework
macos/Flutter/Flutter.podspec
macos/Flutter/*.xcworkspace
macos/Flutter/*.xcuserstate
macos/Runner.xcworkspace/
macos/Runner.xcuserstate
macos/.symlinks/
macos/Pods/

# Web related files
web/.dart_tool/
web/.flutter-plugins
web/.flutter-plugins-dependencies

# VS Code settings
.vscode/

# IntelliJ and Android Studio
.idea/
*.iml
*.ipr
*.iws

# Logs and temp files
*.log
*.tmp
*.swp
*.lock
flutter_*.log
.DS_Store

# Coverage reports
coverage/

# Secrets and configuration files
.env
config/
secrets/
.keys/

# Firebase functions (if applicable)
functions/node_modules/
functions/package-lock.json

# Dart Pub files
pubspec.lock

# Ignore temporary files in the assets folder (if needed)
# Uncomment if you want to exclude only specific files inside assets
# assets/temp/
# assets/*.tmp

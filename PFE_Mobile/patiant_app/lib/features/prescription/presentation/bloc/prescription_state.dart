import 'package:equatable/equatable.dart';

abstract class PrescriptionState extends Equatable {
  @override
  List<Object?> get props => [];
}

class PrescriptionInitial extends PrescriptionState {}

class PrescriptionLoading extends PrescriptionState {}

class PrescriptionListLoaded extends PrescriptionState {
  final List<dynamic> prescriptions;
  final int total;

  PrescriptionListLoaded(this.prescriptions, this.total);

  @override
  List<Object?> get props => [prescriptions, total];
}

class PrescriptionDetailsLoaded extends PrescriptionState {
  final Map<String, dynamic> prescription;
  PrescriptionDetailsLoaded(this.prescription);
}

class PrescriptionSuccess extends PrescriptionState {}

class PrescriptionError extends PrescriptionState {
  final String message;
  PrescriptionError(this.message);
}

class PrescriptionUpdatedSuccess extends PrescriptionState {}

class PrescriptionDeletedSuccess extends PrescriptionState {}

import 'package:equatable/equatable.dart';

abstract class PrescriptionEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class LoadPrescriptions extends PrescriptionEvent {
  final String? date;
  final String? note;
  LoadPrescriptions({this.date, this.note});
}

class LoadPrescriptionDetails extends PrescriptionEvent {
  final String id;
  LoadPrescriptionDetails(this.id);
}

class CreatePrescription extends PrescriptionEvent {
  final List<String> storagePaths;
  final String? note;
  final String? issueDate;

  CreatePrescription(this.storagePaths, {this.note, this.issueDate});
}

class UpdatePrescriptionEvent extends PrescriptionEvent {
  final String prescriptionId;
  final String? note;
  final List<String> storagePaths; // Files to be updated or added
  final List<String> removedFiles; // Files to be removed

  UpdatePrescriptionEvent({
    required this.prescriptionId,
    this.note,
    required this.storagePaths,
    required this.removedFiles,
  });

  @override
  List<Object?> get props => [prescriptionId, note, storagePaths, removedFiles];
}

class DeletePrescription extends PrescriptionEvent {
  final String prescriptionId;

  DeletePrescription(this.prescriptionId);

  @override
  List<Object?> get props => [prescriptionId];
}

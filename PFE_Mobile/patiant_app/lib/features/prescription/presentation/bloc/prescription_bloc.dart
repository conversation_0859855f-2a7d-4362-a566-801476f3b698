import 'package:flutter_bloc/flutter_bloc.dart';
import 'prescription_event.dart';
import 'prescription_state.dart';
import '../../data/repositories/prescription_repository.dart';

class PrescriptionBloc extends Bloc<PrescriptionEvent, PrescriptionState> {
  final PrescriptionRepository repo;

  PrescriptionBloc(this.repo) : super(PrescriptionInitial()) {
    on<LoadPrescriptions>((event, emit) async {
      emit(PrescriptionLoading());
      try {
        final result =
            await repo.getPrescriptions(date: event.date, note: event.note);
        emit(PrescriptionListLoaded(result['prescriptions'], result['total']));
      } catch (e) {
        emit(PrescriptionError(e.toString()));
      }
    });

    on<LoadPrescriptionDetails>((event, emit) async {
      emit(PrescriptionLoading());
      try {
        final data = await repo.getPrescriptionById(event.id);
        emit(PrescriptionDetailsLoaded(data));
      } catch (e) {
        emit(PrescriptionError(e.toString()));
      }
    });

    on<CreatePrescription>((event, emit) async {
      emit(PrescriptionLoading());
      try {
        await repo.createPrescription(event.storagePaths,
            note: event.note, issueDate: event.issueDate);
        emit(PrescriptionSuccess());
      } catch (e) {
        emit(PrescriptionError(e.toString()));
      }
    });
    on<UpdatePrescriptionEvent>((event, emit) async {
      try {
        emit(PrescriptionLoading());

        // Call the repository to update the prescription with files
        await repo.updatePrescription(
          event.prescriptionId,
          event.note,
          event.storagePaths,
        );

        emit(PrescriptionUpdatedSuccess());
      } catch (e) {
        emit(PrescriptionError('Error updating the prescription: $e'));
      }
    });

    on<DeletePrescription>((event, emit) async {
      try {
        emit(PrescriptionLoading());
        await repo.deletePrescription(
            event.prescriptionId); // Delete the prescription
        emit(PrescriptionDeletedSuccess()); // Emit success state after deletion
      } catch (e) {
        emit(PrescriptionError('Error deleting the prescription: $e'));
      }
    });
  }
}

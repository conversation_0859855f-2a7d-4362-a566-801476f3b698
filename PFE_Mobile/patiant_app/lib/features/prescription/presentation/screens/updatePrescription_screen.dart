import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:open_file/open_file.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/prescription/presentation/bloc/prescription_state.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';
import '../../data/repositories/prescription_repository.dart';
import '../bloc/prescription_bloc.dart';
import '../bloc/prescription_event.dart';

class UpdatePrescriptionScreen extends StatefulWidget {
  final String prescriptionId;
  const UpdatePrescriptionScreen({Key? key, required this.prescriptionId})
      : super(key: key);

  @override
  _UpdatePrescriptionScreenState createState() =>
      _UpdatePrescriptionScreenState();
}

class _UpdatePrescriptionScreenState extends State<UpdatePrescriptionScreen> {
  late TextEditingController noteController;
  List<File> files = [];
  List<String> removedFiles = [];
  List<String> serverPaths = [];
  late List<String> initialServerPaths;
  late String initialNote;
  bool isChanged = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    noteController = TextEditingController();
    context
        .read<PrescriptionBloc>()
        .add(LoadPrescriptionDetails(widget.prescriptionId));
  }

  void _trackChanges() {
    final hasNoteChanged = noteController.text.trim() != initialNote.trim();
    final hasNewFiles = files.isNotEmpty;
    final hasRemovedServerFiles =
        removedFiles.any((removed) => initialServerPaths.contains(removed));
    final remainingFileCount = serverPaths.length + files.length;

    setState(() {
      isChanged = (hasNoteChanged || hasNewFiles || hasRemovedServerFiles) &&
          remainingFileCount > 0;
    });
  }

  Future<void> _pickFiles() async {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      allowedExtensions: ['pdf'],
      type: FileType.custom,
    );
    if (result != null) {
      setState(() {
        files.addAll(result.paths.map((path) => File(path!)));
        _trackChanges();
      });
    }
  }

  Future<void> _pickImageFromGallery() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      allowMultiple: true,
    );
    if (result != null) {
      setState(() {
        files.addAll(result.paths.map((path) => File(path!)));
        _trackChanges();
      });
    }
  }

  Future<void> _takePhoto() async {
    final picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      setState(() {
        files.add(File(image.path));
        _trackChanges();
      });
    }
  }

  void _removeFile(int index) {
    setState(() {
      removedFiles.add(files[index].path);
      files.removeAt(index);
      _trackChanges();
    });
  }

  Future<void> _savePrescription() async {
    if (!isChanged || _isLoading) return;

    setState(() => _isLoading = true);

    try {
      List<String> uploadedPaths = [];
      if (files.isNotEmpty) {
        uploadedPaths =
            await context.read<PrescriptionRepository>().uploadFiles(files);
      }

      final updatedStoragePaths = [...serverPaths, ...uploadedPaths];

      context.read<PrescriptionBloc>().add(UpdatePrescriptionEvent(
            prescriptionId: widget.prescriptionId,
            note: noteController.text,
            storagePaths: updatedStoragePaths,
            removedFiles: removedFiles,
          ));
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur lors du téléchargement: $e')),
      );
    }
  }

  Widget _buildFilePreview(File file, int index) {
    final isPdf = file.path.toLowerCase().endsWith('.pdf');
    if (isPdf) {
      return ListTile(
        leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
        title: Text(file.path.split('/').last),
        trailing: IconButton(
          icon: const Icon(Icons.delete, color: Colors.grey),
          onPressed: () => _removeFile(index),
        ),
        onTap: () => OpenFile.open(file.path),
      );
    } else {
      return Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              file,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            top: 2,
            right: 2,
            child: GestureDetector(
              onTap: () => _removeFile(index),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
                padding: const EdgeInsets.all(4),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      );
    }
  }

  Future<void> _showFullscreenImage(String url) async {
    await showDialog(
      context: context,
      builder: (_) => Dialog(
        child: InteractiveViewer(
          child: Image.network(url, fit: BoxFit.contain),
        ),
      ),
    );
  }

  Future<void> _showFullscreenPdf(String url) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;
        final tempDir = await getTemporaryDirectory();
        final file = File('${tempDir.path}/${url.split('/').last}');
        await file.writeAsBytes(bytes);
        await OpenFile.open(file.path);
      } else {
        throw Exception("Échec du téléchargement");
      }
    } catch (_) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Impossible d’ouvrir le PDF.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final imageFiles = files
        .asMap()
        .entries
        .where((e) => !e.value.path.toLowerCase().endsWith('.pdf'))
        .toList();
    final pdfFiles = files
        .asMap()
        .entries
        .where((e) => e.value.path.toLowerCase().endsWith('.pdf'))
        .toList();

    return BlocListener<PrescriptionBloc, PrescriptionState>(
      listener: (context, state) {
        if (state is PrescriptionDetailsLoaded) {
          setState(() {
            initialNote = state.prescription['note'] ?? '';
            noteController.text = initialNote;
            serverPaths = List<String>.from(state.prescription['storagePath']);
            initialServerPaths = List<String>.from(serverPaths);
            _trackChanges();
          });
        }

        if (state is PrescriptionUpdatedSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Prescription mise à jour avec succès!')),
          );
          Navigator.pop(context, true);
        }

        if (state is PrescriptionError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, profileState) {
          final profilePhoto =
              profileState is ProfileLoaded ? profileState.profilePhoto : null;
          final firstName =
              profileState is ProfileLoaded ? profileState.firstName : '';
          final lastName =
              profileState is ProfileLoaded ? profileState.lastName : '';

          return BlocBuilder<NotificationBloc, NotificationState>(
            builder: (context, notificationState) {
              int unreadCount = 0;
              if (notificationState is NotificationLoaded) {
                unreadCount = notificationState.unreadCount;
              } else if (notificationState is UnreadCountLoaded) {
                unreadCount = notificationState.count;
              }

              return Scaffold(
                backgroundColor: Colors.white,
                appBar: CustomAppBar(
                  logoPath: 'assets/logo.png',
                  notificationCount: unreadCount > 0 ? unreadCount : null,
                  onNotificationTap: () {
                    Navigator.pushNamed(context, '/notifications');
                  },
                  onProfileTap: () =>
                      Navigator.pushReplacementNamed(context, '/profile'),
                  profilePhoto: profilePhoto,
                  firstName: firstName,
                  lastName: lastName,
                ),
                bottomNavigationBar: CustomNavBar(
                  selectedIndex: 0, // ✅ Previously 2
                  onTap: (index) {
                    if (index == 0) {
                      Navigator.pushReplacementNamed(context, '/home');
                    } else if (index == 1) {
                      Navigator.pushReplacementNamed(context, '/packages');
                    } else if (index == 2) {
                      Navigator.pushReplacementNamed(context, '/shop');
                    } else if (index == 3) {
                      Navigator.pushReplacementNamed(context, '/profile');
                    }
                  },
                ),
                body: SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.arrow_back,
                                color: Colors.blue),
                            onPressed: () => Navigator.pop(context, false),
                          ),
                          const Expanded(
                            child: Center(
                              child: Text(
                                'Modifier ordonnance',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 48),
                        ],
                      ),
                      const SizedBox(height: 20),
                      TextField(
                        controller: noteController,
                        decoration:
                            const InputDecoration(labelText: 'Description'),
                        onChanged: (_) => _trackChanges(),
                      ),
                      const SizedBox(height: 16),
                      if (serverPaths.isNotEmpty)
                        SizedBox(
                          height: 100,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: serverPaths.length,
                            itemBuilder: (context, index) {
                              final path = serverPaths[index];
                              final fullUrl = 'http://************:3000$path';
                              final isPdf = path.toLowerCase().endsWith('.pdf');

                              return Padding(
                                padding: const EdgeInsets.only(right: 10),
                                child: Stack(
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        isPdf
                                            ? _showFullscreenPdf(fullUrl)
                                            : _showFullscreenImage(fullUrl);
                                      },
                                      child: isPdf
                                          ? Container(
                                              width: 80,
                                              height: 100,
                                              decoration: BoxDecoration(
                                                color: Colors.red.shade100,
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              child: const Icon(
                                                  Icons.picture_as_pdf,
                                                  color: Colors.red),
                                            )
                                          : ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              child: Image.network(
                                                fullUrl,
                                                width: 80,
                                                height: 100,
                                                fit: BoxFit.cover,
                                                errorBuilder: (_, __, ___) =>
                                                    const Icon(
                                                        Icons.broken_image),
                                              ),
                                            ),
                                    ),
                                    Positioned(
                                      top: 2,
                                      right: 2,
                                      child: GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            removedFiles.add(path);
                                            serverPaths.removeAt(index);
                                            _trackChanges();
                                          });
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color:
                                                Colors.black.withOpacity(0.5),
                                            shape: BoxShape.circle,
                                          ),
                                          padding: const EdgeInsets.all(4),
                                          child: const Icon(Icons.close,
                                              color: Colors.white, size: 16),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      const SizedBox(height: 16),
                      Wrap(
                        spacing: 10,
                        runSpacing: 10,
                        children: [
                          ElevatedButton.icon(
                            onPressed: _pickFiles,
                            icon: const Icon(Icons.picture_as_pdf),
                            label: const Text('Ajouter PDF'),
                          ),
                          ElevatedButton.icon(
                            onPressed: _pickImageFromGallery,
                            icon: const Icon(Icons.photo_library),
                            label: const Text('Ajouter image'),
                          ),
                          ElevatedButton.icon(
                            onPressed: _takePhoto,
                            icon: const Icon(Icons.camera_alt),
                            label: const Text('Prendre photo'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      if (imageFiles.isNotEmpty)
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: imageFiles.length,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            mainAxisSpacing: 8,
                            crossAxisSpacing: 8,
                            childAspectRatio: 1,
                          ),
                          itemBuilder: (context, i) => _buildFilePreview(
                              imageFiles[i].value, imageFiles[i].key),
                        ),
                      if (pdfFiles.isNotEmpty)
                        ...pdfFiles.map(
                          (e) => _buildFilePreview(e.value, e.key),
                        ),
                      const SizedBox(height: 20),
                      ElevatedButton.icon(
                        onPressed:
                            isChanged && !_isLoading ? _savePrescription : null,
                        icon: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.save),
                        label:
                            Text(_isLoading ? 'Chargement...' : 'Enregistrer'),
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size(double.infinity, 50),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}

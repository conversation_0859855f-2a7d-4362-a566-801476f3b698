import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';

import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import '../bloc/prescription_bloc.dart';
import '../bloc/prescription_event.dart';
import '../bloc/prescription_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';

class PrescriptionScreen extends StatefulWidget {
  @override
  _PrescriptionScreenState createState() => _PrescriptionScreenState();
}

class _PrescriptionScreenState extends State<PrescriptionScreen> {
  String selectedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
  String? searchNote;
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    initializeDateFormatting('fr_FR', null).then((_) {
      setState(() => _initialized = true);
      _loadPrescriptions();
    });
  }

  void _loadPrescriptions() {
    context
        .read<PrescriptionBloc>()
        .add(LoadPrescriptions(date: selectedDate, note: searchNote));
  }

  void _pickDate() async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.parse(selectedDate),
      firstDate: DateTime(2023),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        selectedDate = DateFormat('yyyy-MM-dd').format(picked);
      });
      _loadPrescriptions();
    }
  }

  String _formatDate(String rawDate) {
    try {
      final date = DateTime.parse(rawDate);
      return DateFormat("d MMMM yyyy 'à' HH:mm", 'fr_FR').format(date);
    } catch (_) {
      return 'Date inconnue';
    }
  }

  Future<void> _showFullscreenImage(String url) async {
    await showDialog(
      context: context,
      builder: (_) => Dialog(
        child: InteractiveViewer(
          child: Image.network(url, fit: BoxFit.contain),
        ),
      ),
    );
  }

  Future<void> _showFullscreenPdf(String url) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;
        final tempDir = await getTemporaryDirectory();
        final file = File('${tempDir.path}/${url.split('/').last}');
        await file.writeAsBytes(bytes);
        await OpenFile.open(file.path);
      } else {
        throw Exception("Download failed");
      }
    } catch (_) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Impossible d’ouvrir le PDF.')),
      );
    }
  }

  void _deletePrescription(String prescriptionId) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Êtes-vous sûr?'),
          content:
              const Text('Voulez-vous vraiment supprimer cette ordonnance?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Annuler'),
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
            ),
            TextButton(
              child: const Text('Supprimer'),
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
                context.read<PrescriptionBloc>().add(DeletePrescription(
                    prescriptionId)); // Dispatch the delete event
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return !_initialized
        ? const Center(child: CircularProgressIndicator())
        : BlocListener<PrescriptionBloc, PrescriptionState>(
            listener: (context, state) {
              if (state is PrescriptionUpdatedSuccess) {
                _loadPrescriptions(); // Reload prescriptions after update
              }
              if (state is PrescriptionDeletedSuccess) {
                _loadPrescriptions(); // Reload prescriptions after deletion
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text('Ordonnance supprimée avec succès!')),
                );
              } else if (state is PrescriptionError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(state.message)),
                );
              }
            },
            child: BlocBuilder<ProfileBloc, ProfileState>(
              builder: (context, profileState) {
                final profilePhoto = profileState is ProfileLoaded
                    ? profileState.profilePhoto
                    : null;
                final firstName =
                    profileState is ProfileLoaded ? profileState.firstName : '';
                final lastName =
                    profileState is ProfileLoaded ? profileState.lastName : '';

                return BlocBuilder<NotificationBloc, NotificationState>(
                  builder: (context, notificationState) {
                    int unreadCount = 0;
                    if (notificationState is NotificationLoaded) {
                      unreadCount = notificationState.unreadCount;
                    } else if (notificationState is UnreadCountLoaded) {
                      unreadCount = notificationState.count;
                    }

                    return Scaffold(
                      backgroundColor: Colors.white,
                      appBar: CustomAppBar(
                        logoPath: 'assets/logo.png',
                        notificationCount: unreadCount > 0 ? unreadCount : null,
                        onNotificationTap: () {
                          Navigator.pushNamed(context, '/notifications');
                        },
                        onProfileTap: () =>
                            Navigator.pushReplacementNamed(context, '/profile'),
                        profilePhoto: profilePhoto,
                        firstName: firstName,
                        lastName: lastName,
                      ),
                      bottomNavigationBar: CustomNavBar(
                        selectedIndex: 0, // ✅ Previously 2
                        onTap: (index) {
                          if (index == 0) {
                            Navigator.pushReplacementNamed(context, '/home');
                          } else if (index == 1) {
                            Navigator.pushReplacementNamed(
                                context, '/packages');
                          } else if (index == 2) {
                            Navigator.pushReplacementNamed(context, '/shop');
                          } else if (index == 3) {
                            Navigator.pushReplacementNamed(context, '/profile');
                          }
                        },
                      ),
                      floatingActionButton: FloatingActionButton(
                        backgroundColor: Colors.blue,
                        child: const Icon(Icons.add, color: Colors.white),
                        onPressed: () async {
                          final result = await Navigator.pushNamed(
                              context, '/createPrescription');
                          if (result == true) {
                            _loadPrescriptions(); // Triggers refresh
                          }
                        },
                      ),
                      body: Column(
                        children: [
                          Container(
                            margin: const EdgeInsets.only(
                                top: 20, right: 16, left: 16, bottom: 12),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  "Mes ordonnances",
                                  style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold),
                                ),
                                GestureDetector(
                                  onTap: _pickDate,
                                  child: Row(
                                    children: [
                                      const Icon(Icons.calendar_today,
                                          color: Colors.blue),
                                      const SizedBox(width: 6),
                                      Text(
                                        selectedDate ==
                                                DateFormat('yyyy-MM-dd')
                                                    .format(DateTime.now())
                                            ? "Aujourd'hui"
                                            : selectedDate,
                                        style: const TextStyle(
                                            color: Colors.blue,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const Icon(Icons.keyboard_arrow_down,
                                          color: Colors.blue),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          BlocBuilder<PrescriptionBloc, PrescriptionState>(
                            builder: (context, state) {
                              int total = 0;
                              if (state is PrescriptionListLoaded) {
                                total = state.total;
                              }
                              return Container(
                                margin: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                        color: Colors.grey.shade300,
                                        blurRadius: 4)
                                  ],
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Row(
                                      children: [
                                        Icon(Icons.description,
                                            color: Colors.blue),
                                        SizedBox(width: 8),
                                        Text(
                                          "Nombre total des ordonnances",
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black),
                                        ),
                                      ],
                                    ),
                                    Text(
                                      total.toString(),
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: TextField(
                              decoration: InputDecoration(
                                hintText: "Rechercher par description",
                                prefixIcon: const Icon(Icons.search),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              onChanged: (value) {
                                setState(() => searchNote = value);
                                _loadPrescriptions();
                              },
                            ),
                          ),
                          const SizedBox(height: 10),
                          Expanded(
                            child: BlocBuilder<PrescriptionBloc,
                                PrescriptionState>(
                              builder: (context, state) {
                                if (state is PrescriptionLoading) {
                                  return const Center(
                                      child: CircularProgressIndicator());
                                } else if (state is PrescriptionListLoaded) {
                                  if (state.prescriptions.isEmpty) {
                                    return const Center(
                                        child:
                                            Text('Aucune ordonnance trouvée.'));
                                  }

                                  return ListView.separated(
                                    padding: const EdgeInsets.all(10),
                                    itemCount: state.prescriptions.length,
                                    separatorBuilder: (_, __) =>
                                        const SizedBox(height: 10),
                                    itemBuilder: (context, index) {
                                      final prescription =
                                          state.prescriptions[index];
                                      final storagePaths =
                                          prescription['storagePath'] as List;
                                      final createdAt = _formatDate(
                                          prescription['createdAt']);

                                      return GestureDetector(
                                        onTap: () {
                                          final id = prescription[
                                              'id']; // ✅ Utilise le champ personnalisé "id"

                                          Navigator.pushNamed(
                                            context,
                                            '/prescriptionDetails',
                                            arguments: id,
                                          );
                                        },
                                        child: Card(
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          elevation: 3,
                                          child: Padding(
                                            padding: const EdgeInsets.all(16),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Stack(
                                                  children: [
                                                    SizedBox(
                                                      height: 80,
                                                      child: ListView.builder(
                                                        scrollDirection:
                                                            Axis.horizontal,
                                                        itemCount:
                                                            storagePaths.length,
                                                        itemBuilder:
                                                            (context, i) {
                                                          final path =
                                                              storagePaths[i];
                                                          final isPdf = path
                                                              .toLowerCase()
                                                              .endsWith('.pdf');
                                                          final fileUrl =
                                                              'http://192.168.0.70:3000$path';

                                                          return Padding(
                                                            padding:
                                                                EdgeInsets.only(
                                                              right: 10,
                                                              left: i == 0
                                                                  ? 0
                                                                  : 0,
                                                            ),
                                                            child:
                                                                GestureDetector(
                                                              onTap: () {
                                                                isPdf
                                                                    ? _showFullscreenPdf(
                                                                        fileUrl)
                                                                    : _showFullscreenImage(
                                                                        fileUrl);
                                                              },
                                                              child: isPdf
                                                                  ? Container(
                                                                      width: 80,
                                                                      height:
                                                                          80,
                                                                      decoration:
                                                                          BoxDecoration(
                                                                        color: Colors
                                                                            .red
                                                                            .shade100,
                                                                        borderRadius:
                                                                            BorderRadius.circular(8),
                                                                      ),
                                                                      child:
                                                                          const Icon(
                                                                        Icons
                                                                            .picture_as_pdf,
                                                                        color: Colors
                                                                            .red,
                                                                      ),
                                                                    )
                                                                  : ClipRRect(
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              8),
                                                                      child: Image
                                                                          .network(
                                                                        fileUrl,
                                                                        width:
                                                                            80,
                                                                        height:
                                                                            80,
                                                                        fit: BoxFit
                                                                            .cover,
                                                                        errorBuilder: (_, __, ___) => const Icon(
                                                                            Icons
                                                                                .broken_image,
                                                                            size:
                                                                                50),
                                                                      ),
                                                                    ),
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                    Positioned(
                                                      top: 0,
                                                      right: 0,
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                horizontal: 6,
                                                                vertical: 2),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Colors.blue,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(12),
                                                        ),
                                                        child: Text(
                                                          '${storagePaths.length}',
                                                          style: const TextStyle(
                                                              color:
                                                                  Colors.white,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(height: 10),
                                                Row(
                                                  children: [
                                                    const Text(
                                                      "Description : ",
                                                      style: TextStyle(
                                                        color: Colors.blue,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: Text(
                                                        prescription['note'] ??
                                                            'Aucune note',
                                                        style: const TextStyle(
                                                            fontWeight:
                                                                FontWeight
                                                                    .w600),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(height: 6),
                                                Text(
                                                  "Ajoutée le $createdAt",
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      color:
                                                          Colors.grey.shade600),
                                                ),
                                                // Edit and Delete Buttons below the documents
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        IconButton(
                                                          icon: const Icon(
                                                              Icons.edit,
                                                              color:
                                                                  Colors.blue),
                                                          onPressed: () async {
                                                            final id =
                                                                prescription[
                                                                    'id'];
                                                            final result =
                                                                await Navigator
                                                                    .pushNamed(
                                                              context,
                                                              '/updatePrescription',
                                                              arguments: id,
                                                            );

                                                            if (result ==
                                                                true) {
                                                              _loadPrescriptions(); // ✅ Reload the list only if updated
                                                            }
                                                          },
                                                        ),
                                                        const SizedBox(
                                                            width: 16),
                                                        IconButton(
                                                          icon: const Icon(
                                                              Icons.delete,
                                                              color:
                                                                  Colors.grey),
                                                          onPressed: () {
                                                            _deletePrescription(
                                                                prescription[
                                                                    'id']); // Show confirmation dialog
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  );
                                }
                                return const Center(
                                    child: Text('Une erreur est survenue'));
                              },
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          );
  }
}

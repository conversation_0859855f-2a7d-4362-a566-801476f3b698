import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_event.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_state.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_event.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_state.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';

class ShopDetailsScreen extends StatefulWidget {
  final String productId;
  final int quantity;

  const ShopDetailsScreen({
    super.key,
    required this.productId,
    required this.quantity,
  });

  @override
  State<ShopDetailsScreen> createState() => _ShopDetailsScreenState();
}

class _ShopDetailsScreenState extends State<ShopDetailsScreen> {
  final TextEditingController _noteController = TextEditingController();
  int _selectedIndex = 2;
  String? profilePhoto;
  String firstName = "";
  String lastName = "";
  int quantity = 1;
  bool isOrderConfirmed = false;

  @override
  void initState() {
    super.initState();
    quantity = widget.quantity;

    final profileState = context.read<ProfileBloc>().state;
    if (profileState is ProfileLoaded) {
      profilePhoto = profileState.profilePhoto;
      firstName = profileState.firstName;
      lastName = profileState.lastName;
    }

    context.read<ProductBloc>().add(LoadProductDetails(widget.productId));
    context.read<OrderBloc>().add(LoadBasket());

    context.read<OrderBloc>().stream.listen((state) {
      if (state is OrderLoaded) {
        final basket = state.basket;
        if (basket['flag'] == true && basket['status'] == 'APPROVED') {
          setState(() {
            isOrderConfirmed = true;
            final basketQty = basket['quantity'];
            if (basketQty != null && basketQty > 0) {
              quantity = basketQty;
            }
            final note = basket['deliveryManNote'];
            if (note != null) {
              _noteController.text = note;
            }
          });
        }
      }
    });
  }

  void _showImageFullScreen(String imageUrl) {
    showDialog(
      context: context,
      builder: (_) => Dialog(
        insetPadding: const EdgeInsets.all(16),
        child: InteractiveViewer(
          child: Image.network(imageUrl),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OrderBloc, OrderState>(
      listener: (context, state) {
        if (state is OrderSuccess) {
          Navigator.pushReplacementNamed(context, '/shop');
        } else if (state is OrderError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.error)),
          );
        }
      },
      child: BlocBuilder<NotificationBloc, NotificationState>(
        builder: (context, notificationState) {
          int unreadCount = 0;
          if (notificationState is NotificationLoaded) {
            unreadCount = notificationState.unreadCount;
          } else if (notificationState is UnreadCountLoaded) {
            unreadCount = notificationState.count;
          }

          return Scaffold(
            appBar: CustomAppBar(
              logoPath: 'assets/logo.png',
              profilePhoto: profilePhoto,
              firstName: firstName,
              lastName: lastName,
              notificationCount: unreadCount > 0 ? unreadCount : null,
              onNotificationTap: () {
                Navigator.pushNamed(context, '/notifications');
              },
              onProfileTap: () => Navigator.pushNamed(context, '/profile'),
            ),
            body: BlocBuilder<ProductBloc, ProductState>(
              builder: (context, state) {
                if (state is ProductLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is ProductDetailsLoaded) {
                  final product = state.product;
                  final discount = product['discount'] ?? 0;
                  final price = product['price'] ?? 0.0;
                  final discountedPrice = price - (price * (discount / 100));
                  final imageUrl = product['storagePath']?.isNotEmpty == true
                      ? 'http://192.168.0.70:3000${product['storagePath'][0]}'
                      : null;

                  return Padding(
                    padding: const EdgeInsets.all(16),
                    child: ListView(
                      children: [
                        Align(
                          alignment: Alignment.centerLeft,
                          child: IconButton(
                            icon: const Icon(Icons.arrow_back,
                                color: Colors.black),
                            onPressed: () => Navigator.pop(context),
                          ),
                        ),
                        if (imageUrl != null)
                          GestureDetector(
                            onTap: () => _showImageFullScreen(imageUrl),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Image.network(
                                imageUrl,
                                height: 200,
                                fit: BoxFit.cover,
                                errorBuilder: (_, __, ___) =>
                                    const Icon(Icons.broken_image),
                              ),
                            ),
                          ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Text(
                              product['name'] ?? '',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            if (discount > 0)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text("-$discount%",
                                    style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold)),
                              )
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            if (discount > 0)
                              Text(
                                "${(price * quantity).toStringAsFixed(2)} \$CAD",
                                style: const TextStyle(
                                  color: Colors.grey,
                                  decoration: TextDecoration.lineThrough,
                                ),
                              ),
                            const SizedBox(width: 6),
                            Text(
                              "${(discountedPrice * quantity).toStringAsFixed(2)} \$CAD",
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                            ),
                          ],
                        ),
                        const Divider(height: 32),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (!isOrderConfirmed)
                              IconButton(
                                onPressed: () {
                                  if (quantity > 1) {
                                    setState(() => quantity--);
                                  }
                                },
                                icon: const Icon(Icons.remove_circle_outline),
                              ),
                            Text(
                              quantity.toString(),
                              style: const TextStyle(fontSize: 18),
                            ),
                            if (!isOrderConfirmed)
                              IconButton(
                                onPressed: () {
                                  final availableQuantity =
                                      product['quantity'] ??
                                          product['stock'] ??
                                          product['availableQuantity'] ??
                                          999;
                                  if (quantity < availableQuantity) {
                                    setState(() => quantity++);
                                  } else {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                            'Stock maximum atteint ($availableQuantity disponible)'),
                                        backgroundColor: Colors.orange,
                                      ),
                                    );
                                  }
                                },
                                icon: const Icon(Icons.add_circle_outline),
                              ),
                          ],
                        ),
                        const Divider(height: 32),
                        const Text("Note au livreur",
                            style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 10),
                        TextField(
                          controller: _noteController,
                          maxLines: 2,
                          enabled: !isOrderConfirmed,
                          decoration: const InputDecoration(
                            hintText: "Ex: appeler à l’arrivée",
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 30),
                        if (!isOrderConfirmed)
                          ElevatedButton.icon(
                            icon: const Icon(Icons.check),
                            label: const Text("Confirmer la commande"),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            onPressed: () {
                              context.read<OrderBloc>().add(
                                    ConfirmOrder(
                                      quantity: quantity,
                                      deliveryManNote:
                                          _noteController.text.trim().isEmpty
                                              ? null
                                              : _noteController.text.trim(),
                                    ),
                                  );
                              // ❌ No navigation here — BlocListener handles it
                            },
                          )
                        else
                          const Padding(
                            padding: EdgeInsets.symmetric(vertical: 20),
                            child: Text(
                              "✅ Cette commande est déjà confirmée et en attente de livraison.",
                              style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 16,
                                  color: Colors.blueGrey),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    ),
                  );
                }

                return const Center(
                    child: Text("Erreur lors du chargement du produit"));
              },
            ),
            bottomNavigationBar: CustomNavBar(
              selectedIndex: _selectedIndex,
              onTap: (index) => setState(() => _selectedIndex = index),
            ),
          );
        },
      ),
    );
  }
}

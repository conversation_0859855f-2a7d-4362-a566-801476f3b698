import 'package:dio/dio.dart';
import 'package:patiant_app/core/network/api_service.dart';

class ProductRepository {
  final ApiService _apiService = ApiService();

  Future<List<Map<String, dynamic>>> getAllProducts({
    String? search,
    String? categoryId,
    double? minPrice,
    double? maxPrice,
  }) async {
    final response = await _apiService.get(
      '/products/getAll',
      requiresAuth: true,
      queryParams: {
        if (search != null) 'search': search,
        if (categoryId != null) 'categoryId': categoryId,
        if (minPrice != null) 'minPrice': minPrice,
        if (maxPrice != null) 'maxPrice': maxPrice,
      },
    );

    final List<dynamic> data = response.data['products'];
    return data.cast<Map<String, dynamic>>();
  }

  Future<Map<String, dynamic>> getProductById(String id) async {
    final response =
        await _apiService.get('/products/get/$id', requiresAuth: true);
    if (response.statusCode == 200 && response.data['success']) {
      return response.data['product'];
    } else {
      throw Exception('Failed to load product details');
    }
  }

  // ✅ New: Fetch all categories
  Future<List<Map<String, dynamic>>> getAllCategories() async {
    final response =
        await _apiService.get('/categories/getAll', requiresAuth: true);
    return response.data['categories'].cast<Map<String, dynamic>>();
  }

  // ✅ New: Fetch a category by ID
  Future<Map<String, dynamic>?> getCategoryById(String id) async {
    final response =
        await _apiService.get('/categories/get/$id', requiresAuth: true);
    return response.data['category'];
  }
}

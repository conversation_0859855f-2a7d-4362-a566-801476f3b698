// ✅ 1. order_repository.dart
import 'package:dio/dio.dart';
import 'package:patiant_app/core/network/api_service.dart';

class OrderRepository {
  final ApiService _apiService = ApiService();

  Future<Map<String, dynamic>?> getBasket() async {
    final response = await _apiService.get("/basket/get", requiresAuth: true);
    return response.data['basket'];
  }

  Future<void> addToBasket(String productId) async {
    await _apiService.post(
        "/basket/add",
        {
          "productId": productId,
        },
        requiresAuth: true);
  }

  Future<void> removeFromBasket(String productId) async {
    await _apiService.delete("/basket/remove?productId=$productId",
        requiresAuth: true);
  }

  Future<void> confirmOrder(int quantity, String? note) async {
    await _apiService.post(
      '/basket/confirm',
      {
        'quantity': quantity,
        if (note != null) 'deliveryManNote': note,
      },
      requiresAuth: true,
    );
  }

  Future<List<Map<String, dynamic>>> getOrderHistory() async {
    final response =
        await _apiService.get("/basket/history", requiresAuth: true);
    return List<Map<String, dynamic>>.from(response.data['orders']);
  }
}

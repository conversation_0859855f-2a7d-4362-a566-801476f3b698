import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:patiant_app/features/package/presentation/bloc/package_bloc.dart';
import 'package:patiant_app/features/package/presentation/bloc/package_event.dart';
import 'package:patiant_app/features/package/presentation/bloc/package_state.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/package/presentation/screens/packageDetails_screen.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_event.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';

class PackageScreen extends StatefulWidget {
  const PackageScreen({super.key});

  @override
  State<PackageScreen> createState() => _PackageScreenState();
}

class _PackageScreenState extends State<PackageScreen> {
  String _selectedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
  String? _selectedStatusFilter;
  String? _selectedPeriodFilter;
  String? _searchQuery;

  @override
  void initState() {
    super.initState();
    context.read<PackageBloc>().add(LoadPackages(date: _selectedDate));
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => ProfileBloc(context.read())..add(LoadProfile()),
        ),
      ],
      child: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, profileState) {
          String? profilePhoto;
          String firstName = '';
          String lastName = '';
          String? loggedInPatientId;

          if (profileState is ProfileLoaded) {
            profilePhoto = profileState.profilePhoto;
            firstName = profileState.firstName;
            lastName = profileState.lastName;
            loggedInPatientId = profileState.id;
          }

          return BlocBuilder<NotificationBloc, NotificationState>(
            builder: (context, notificationState) {
              int unreadCount = 0;
              if (notificationState is NotificationLoaded) {
                unreadCount = notificationState.unreadCount;
              } else if (notificationState is UnreadCountLoaded) {
                unreadCount = notificationState.count;
              }

              return Scaffold(
                appBar: CustomAppBar(
                  logoPath: 'assets/logo.png',
                  notificationCount: unreadCount > 0 ? unreadCount : null,
                  onNotificationTap: () {
                    Navigator.pushNamed(context, '/notifications');
                  },
                  onProfileTap: () {
                    Navigator.pushReplacementNamed(context, '/profile');
                  },
                  profilePhoto: profilePhoto,
                  firstName: firstName,
                  lastName: lastName,
                ),
                backgroundColor: Colors.white,
                bottomNavigationBar: CustomNavBar(
                  selectedIndex: 1,
                  onTap: (index) {
                    if (index == 0) {
                      Navigator.pushReplacementNamed(context, '/home');
                    } else if (index == 2) {
                      Navigator.pushReplacementNamed(context, '/messages');
                    } else if (index == 3) {
                      Navigator.pushReplacementNamed(context, '/profile');
                    }
                  },
                ),
                body: BlocBuilder<PackageBloc, PackageState>(
                  builder: (context, state) {
                    if (state is PackageLoading) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (state is PackageSummaryLoaded) {
                      return Column(
                        children: [
                          _buildDatePicker(),
                          _buildPackageSummary(
                              state.packageCount, state.totalPrice),
                          _buildSearchAndFilter(context),
                          Expanded(
                            child: _buildPackageList(
                              context,
                              state.packages,
                              loggedInPatientId,
                            ),
                          ),
                        ],
                      );
                    } else if (state is PackageError) {
                      return Center(child: Text(state.message));
                    } else {
                      return const Center(child: Text("Aucun colis trouvé"));
                    }
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildDatePicker() {
    return Container(
      margin: const EdgeInsets.only(top: 20, right: 16, left: 16, bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            "Liste des colis",
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          GestureDetector(
            onTap: () async {
              DateTime? pickedDate = await showDatePicker(
                context: context,
                initialDate: DateTime.parse(_selectedDate),
                firstDate: DateTime(2023),
                lastDate: DateTime.now(),
              );
              if (pickedDate != null) {
                setState(() {
                  _selectedDate = DateFormat('yyyy-MM-dd').format(pickedDate);
                  _selectedStatusFilter = null;
                  _searchQuery = null;
                });
                _fetchWithPeriod();
              }
            },
            child: Row(
              children: [
                const Icon(Icons.calendar_today, color: Colors.blue),
                const SizedBox(width: 6),
                Text(
                  _selectedDate ==
                          DateFormat('yyyy-MM-dd').format(DateTime.now())
                      ? "Aujourd'hui"
                      : _selectedDate,
                  style: const TextStyle(
                      color: Colors.blue, fontWeight: FontWeight.bold),
                ),
                const Icon(Icons.keyboard_arrow_down, color: Colors.blue),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageSummary(int packageCount, double totalPrice) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [BoxShadow(color: Colors.grey.shade300, blurRadius: 4)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: const [
                  Icon(Icons.local_shipping, color: Colors.blue),
                  SizedBox(width: 8),
                  Text(
                    "Nombre total des colis",
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.black),
                  ),
                ],
              ),
              Text(
                "$packageCount",
                style: const TextStyle(
                    fontWeight: FontWeight.bold, color: Colors.black),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "Prix total",
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Text(
                  "${totalPrice.toStringAsFixed(3)}\$",
                  style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter(BuildContext context) {
    final List<String> packageStatuses = [
      "AM",
      "PM",
      "Planifié",
      "En préparation",
      "Validé",
      "En cours",
      "Complété",
      "En retour",
      "Annulé",
      "En pause",
      "Retourné"
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
            decoration: InputDecoration(
              hintText: "Rechercher",
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
          const SizedBox(height: 10),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: packageStatuses
                  .map((status) => Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: _buildFilterButton(context, status),
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton(BuildContext context, String label) {
    final bool isPeriod = (label == 'AM' || label == 'PM');
    final bool isSelected = isPeriod
        ? _selectedPeriodFilter == label
        : _selectedStatusFilter == label;

    return GestureDetector(
      onTap: () async {
        setState(() {
          final bool isPeriod = (label == 'AM' || label == 'PM');
          if (isPeriod) {
            _selectedPeriodFilter =
                (_selectedPeriodFilter == label) ? null : label;
          } else {
            if (_selectedStatusFilter == label) {
              _selectedStatusFilter = null;
            } else {
              _selectedStatusFilter = label;
            }
          }
        });

        if (label == 'AM' || label == 'PM') {
          _fetchWithPeriod();
        } else {
          if (_selectedStatusFilter != null) {
            context.read<PackageBloc>().add(
                  FilterPackages(
                    searchQuery: _searchQuery ?? '',
                    status: _selectedStatusFilter,
                  ),
                );
          } else {
            context.read<PackageBloc>().add(
                  LoadPackages(
                    date: _selectedDate,
                    deliveryPeriod: _selectedPeriodFilter,
                  ),
                );
            Future.delayed(const Duration(milliseconds: 300), () {
              if (_searchQuery != null && _searchQuery!.isNotEmpty) {
                context.read<PackageBloc>().add(
                      FilterPackages(
                        searchQuery: _searchQuery ?? '',
                      ),
                    );
              }
            });
          }
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isSelected ? Colors.blue : Colors.transparent,
          border: Border.all(color: Colors.blue),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.blue,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _fetchWithPeriod() {
    context.read<PackageBloc>().add(
          LoadPackages(
            date: _selectedDate,
            deliveryPeriod: _selectedPeriodFilter,
          ),
        );
    Future.delayed(const Duration(milliseconds: 300), () {
      if (_selectedStatusFilter != null ||
          (_searchQuery?.isNotEmpty ?? false)) {
        context.read<PackageBloc>().add(FilterPackages(
              searchQuery: _searchQuery ?? '',
              status: _selectedStatusFilter,
            ));
      }
    });
  }

  Widget _buildPackageList(BuildContext context,
      List<Map<String, dynamic>> packages, String? patientId) {
    final filteredPackages = packages
        .where((p) {
          final belongsToPatient =
              p['patientReceiver']?['patient'] == patientId;
          final belongsToHousehold = p['householdReceiver']?['patients']
                  ?.any((patient) => patient['id'] == patientId) ??
              false;
          return belongsToPatient || belongsToHousehold;
        })
        .where((p) =>
            _searchQuery == null ||
            _searchQuery!.isEmpty ||
            p['originAddress']['address']
                .toString()
                .toLowerCase()
                .contains(_searchQuery!.toLowerCase()))
        .toList();

    return filteredPackages.isEmpty
        ? const Center(child: Text("Aucun colis trouvé"))
        : ListView.separated(
            padding: const EdgeInsets.all(10),
            itemCount: filteredPackages.length,
            separatorBuilder: (context, index) => const SizedBox(height: 10),
            itemBuilder: (context, index) {
              final package = filteredPackages[index];
              return _buildPackageCard(context, package, patientId);
            },
          );
  }

  Widget _buildPackageCard(
      BuildContext context, Map<String, dynamic> package, String? patientId) {
    final isHouseholdPackage = package['householdReceiver'] != null;
    final price = package['patientReceiver']?['packagePrice'] ??
        package['householdReceiver']?['patients']?[0]?['packagePrice'] ??
        0;

    final householdId = package['householdReceiver']?['household'] ?? '';
    final householdName =
        context.read<PackageBloc>().householdNameCache[householdId] ??
            householdId;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isHouseholdPackage)
              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text(
                  'Foyer: $householdName',
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, color: Colors.deepPurple),
                ),
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildStatusBadge(package['status']),
                Row(
                  children: [
                    const Icon(Icons.access_time, size: 18, color: Colors.blue),
                    const SizedBox(width: 5),
                    Text(
                      package['deliveryTime'],
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, color: Colors.black),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                Column(
                  children: [
                    _buildCircle(Colors.blue),
                    _buildDashedLine(),
                    _buildCircle(Colors.green),
                  ],
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text("Pharmacie",
                          style: TextStyle(
                              fontWeight: FontWeight.bold, color: Colors.blue)),
                      Text(
                        context
                                .read<PackageBloc>()
                                .pharmacyNameCache[package['pharmacyId']] ??
                            package['pharmacyId'],
                        style: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 10),
                      const Text("Adresse de livraison",
                          style: TextStyle(
                              fontWeight: FontWeight.bold, color: Colors.blue)),
                      Text(package['originAddress']['address'],
                          style: const TextStyle(fontSize: 16)),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text("Prix total",
                    style:
                        TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                Text(
                  "\$${price.toStringAsFixed(3)}",
                  style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Align(
              alignment: Alignment.centerRight,
              child: IconButton(
                icon: const Icon(Icons.arrow_forward_ios, color: Colors.blue),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          PackageDetailsScreen(packageId: package['id']),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    final statusColor = _getStatusColor(status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        status,
        style: TextStyle(color: statusColor, fontWeight: FontWeight.bold),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case "Validé":
        return Colors.green;
      case "Annulé":
        return Colors.red;
      case "En cours":
      case "En préparation":
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Widget _buildCircle(Color color) {
    return Container(
      width: 10,
      height: 10,
      decoration: BoxDecoration(color: color, shape: BoxShape.circle),
    );
  }

  Widget _buildDashedLine() {
    return Column(
      children: List.generate(
        3,
        (index) => Container(
          width: 2,
          height: 6,
          margin: const EdgeInsets.symmetric(vertical: 2),
          color: Colors.grey.shade400,
        ),
      ),
    );
  }
}

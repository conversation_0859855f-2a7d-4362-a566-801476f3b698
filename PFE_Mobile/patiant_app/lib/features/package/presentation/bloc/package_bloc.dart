import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/package/data/repositories/package_repository.dart';
import 'package:patiant_app/features/package/presentation/bloc/package_event.dart';
import 'package:patiant_app/features/package/presentation/bloc/package_state.dart';

class PackageBloc extends Bloc<PackageEvent, PackageState> {
  final PackageRepository packageRepository;
  final Map<String, String> householdNameCache = {};
  final Map<String, String> pharmacyNameCache = {};

  List<Map<String, dynamic>> _originalPackages = [];
  List<Map<String, dynamic>> _displayedPackages = [];

  String? _currentSearchQuery;
  String? _currentStatus;

  int _totalCount = 0;
  double _totalPrice = 0;

  PackageBloc(this.packageRepository) : super(PackageInitial()) {
    on<LoadPackages>(_onLoadPackages);
    on<LoadPackageDetails>(_onLoadPackageDetails);
    on<FilterPackages>(_onFilterPackages);
    on<LoadPackageCount>(_onLoadPackageCount);
    on<LoadTotalPackagePrice>(_onLoadTotalPackagePrice);
  }

  Future<void> _onLoadPackages(
      LoadPackages event, Emitter<PackageState> emit) async {
    emit(PackageLoading());
    try {
      final packages = await packageRepository.fetchFilteredPackages(
        date: event.date,
        period: event.deliveryPeriod,
      );
      _originalPackages = packages;
      _displayedPackages = packages;

      _totalCount = packages.length;
      _totalPrice = packages.fold(
        0,
        (sum, item) =>
            sum +
            (item['patientReceiver']?['packagePrice'] ?? 0) +
            (item['householdReceiver']?['patients']?[0]?['packagePrice'] ?? 0),
      );
      _currentSearchQuery = null;
      _currentStatus = null;

      //fetch houseHoldinfo
      for (var pkg in packages) {
        final householdId = pkg['householdReceiver']?['household'];
        if (householdId != null &&
            !householdNameCache.containsKey(householdId)) {
          final householdName =
              await packageRepository.fetchHouseholdName(householdId);
          if (householdName != null) {
            householdNameCache[householdId] = householdName;
          }
        }
        //fetch houseHoldinfo
        for (var pkg in packages) {
          final pharmacyId = pkg['pharmacyId'];
          if (pharmacyId != null &&
              !pharmacyNameCache.containsKey(pharmacyId)) {
            final pharmacyName =
                await packageRepository.fetchPharmacyName(pharmacyId);
            if (pharmacyName != null) {
              pharmacyNameCache[pharmacyId] = pharmacyName;
            }
          }
        }
      }

      emit(PackageSummaryLoaded(
        packages: _displayedPackages,
        packageCount: _totalCount,
        totalPrice: _totalPrice,
      ));
    } catch (e) {
      emit(PackageError("Failed to load package data"));
    }
  }

  Future<void> _onLoadPackageDetails(
      LoadPackageDetails event, Emitter<PackageState> emit) async {
    emit(PackageLoading());
    try {
      Map<String, dynamic>? packageDetails =
          await packageRepository.fetchPackageDetails(event.packageId);
      if (packageDetails != null) {
        String? householdName;
        final householdId = packageDetails['householdReceiver']?['household'];
        if (householdId != null) {
          householdName =
              await packageRepository.fetchHouseholdName(householdId);
        }

        final pharmacyId = packageDetails['pharmacyId'];
        String? pharmacyName;
        if (pharmacyId != null) {
          pharmacyName = await packageRepository.fetchPharmacyName(pharmacyId);
          if (pharmacyName != null) {
            pharmacyNameCache[pharmacyId] = pharmacyName;
          }
        }

        emit(PackageDetailsLoaded(packageDetails,
            householdName: householdName, pharmacyName: pharmacyName));
      } else {
        emit(PackageError("Failed to load package details"));
      }
    } catch (e) {
      emit(PackageError("Error fetching package details"));
    }
  }

  Future<void> _onFilterPackages(
      FilterPackages event, Emitter<PackageState> emit) async {
    emit(PackageLoading());
    try {
      // Update current search and status filters if they come from the event
      if (event.searchQuery != null) {
        _currentSearchQuery = event.searchQuery;
      }
      if (event.status != null) {
        _currentStatus = event.status;
      }

      // Always filter from _originalPackages based on current values
      List<Map<String, dynamic>> filtered = _originalPackages;

      if (_currentSearchQuery != null && _currentSearchQuery!.isNotEmpty) {
        filtered = filtered
            .where((package) =>
                (package['pharmacyId'] ?? '')
                    .toLowerCase()
                    .contains(_currentSearchQuery!.toLowerCase()) ||
                (package['address']?['address'] ?? '')
                    .toLowerCase()
                    .contains(_currentSearchQuery!.toLowerCase()))
            .toList();
      }

      if (_currentStatus != null && _currentStatus!.isNotEmpty) {
        filtered = filtered
            .where((package) => package['status'] == _currentStatus)
            .toList();
      }

      _displayedPackages = filtered;

      emit(PackageSummaryLoaded(
        packages: _displayedPackages,
        packageCount: filtered.length,
        totalPrice: filtered.fold(
            0,
            (sum, item) =>
                sum + (item['patientReceiver']?['packagePrice'] ?? 0)),
      ));
    } catch (e) {
      emit(PackageError("Error filtering packages"));
    }
  }

  Future<void> _onLoadPackageCount(
      LoadPackageCount event, Emitter<PackageState> emit) async {
    try {
      int count = await packageRepository.fetchPackageCount();
      emit(PackageCountLoaded(count));
    } catch (e) {
      emit(PackageError("Error fetching package count"));
    }
  }

  Future<void> _onLoadTotalPackagePrice(
      LoadTotalPackagePrice event, Emitter<PackageState> emit) async {
    try {
      double totalPrice = await packageRepository.fetchTotalPackagePrice();
      emit(PackageTotalPriceLoaded(totalPrice));
    } catch (e) {
      emit(PackageError("Error fetching total price"));
    }
  }
}

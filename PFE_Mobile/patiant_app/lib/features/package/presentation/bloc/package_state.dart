abstract class PackageState {}

class PackageInitial extends PackageState {}

class PackageLoading extends PackageState {}

class PackageLoaded extends PackageState {
  final List<Map<String, dynamic>> packages;
  PackageLoaded(this.packages);
}

class PackageDetailsLoaded extends PackageState {
  final Map<String, dynamic> packageDetails;
  final String? householdName;
  final String? pharmacyName;

  PackageDetailsLoaded(this.packageDetails,
      {this.householdName, this.pharmacyName});
}

class PackageCountLoaded extends PackageState {
  final int count;
  PackageCountLoaded(this.count);
}

class PackageError extends PackageState {
  final String message;
  PackageError(this.message);
}

class PackageTotalPriceLoaded extends PackageState {
  final double totalPrice;
  PackageTotalPriceLoaded(this.totalPrice);
}

class PackageSummaryLoaded extends PackageState {
  final List<Map<String, dynamic>> packages;
  final int packageCount;
  final double totalPrice;
  final String? date;

  PackageSummaryLoaded({
    required this.packages,
    required this.packageCount,
    required this.totalPrice,
    this.date,
  });
}

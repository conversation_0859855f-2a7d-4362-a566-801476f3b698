import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../data/repositories/notification_repository.dart';
import '../presentation/bloc/notification_bloc.dart';
import '../presentation/bloc/notification_event.dart';
import 'firebase_messaging_service.dart';

class NotificationService {
  static NotificationRepository? _repository;
  static NotificationBloc? _bloc;
  static bool _isInitialized = false;

  // Initialize the notification service
  static Future<void> initialize(BuildContext context) async {
    if (_isInitialized) return;

    _repository = context.read<NotificationRepository>();
    _bloc = context.read<NotificationBloc>();

    // Initialize Firebase Messaging
    try {
      await FirebaseMessagingService.initialize(
        onTokenRefresh: _onTokenRefresh,
        onMessageReceived: _onMessageReceived,
      );
      print('Firebase Messaging initialized successfully');
    } catch (e) {
      print('Firebase Messaging initialization failed: $e');
      // Continue without Firebase messaging for now
    }

    _isInitialized = true;
  }

  // Handle FCM token refresh
  static void _onTokenRefresh(String token) {
    print('FCM Token refreshed: $token');
    if (_bloc != null) {
      _bloc!.add(UpdateFcmToken(token));
    }
  }

  // Handle incoming messages
  static void _onMessageReceived(Map<String, dynamic> data) {
    print('Notification received: $data');

    // Refresh notifications to get the latest data
    if (_bloc != null) {
      _bloc!.add(const LoadUnreadCount());
    }

    // You can add navigation logic here if needed
    // For example, navigate to specific screens based on notification type
    _handleNotificationNavigation(data);
  }

  // Handle notification navigation
  static void _handleNotificationNavigation(Map<String, dynamic> data) {
    // Get the current context from the navigator
    final context = navigatorKey.currentContext;
    if (context == null) return;

    final type = data['type']?.toString().toLowerCase();
    final notificationId = data['notificationId']?.toString();

    switch (type) {
      case 'prescription':
        final prescriptionId = data['prescriptionId']?.toString();
        if (prescriptionId != null) {
          Navigator.pushNamed(
            context,
            '/prescriptionDetails',
            arguments: prescriptionId,
          );
        }
        break;
      case 'package':
        final packageId = data['packageId']?.toString();
        if (packageId != null) {
          Navigator.pushNamed(
            context,
            '/packageDetails',
            arguments: packageId,
          );
        }
        break;
      case 'order':
        Navigator.pushNamed(context, '/history');
        break;
      default:
        // Navigate to notifications screen
        Navigator.pushNamed(context, '/notifications');
    }

    // Mark notification as read if we have the ID
    if (notificationId != null && _bloc != null) {
      _bloc!.add(MarkNotificationAsRead(notificationId));
    }
  }

  // Get unread notification count
  static Future<int> getUnreadCount() async {
    if (_repository == null) return 0;
    try {
      return await _repository!.getUnreadCount();
    } catch (e) {
      print('Error getting unread count: $e');
      return 0;
    }
  }

  // Subscribe to user-specific topic
  static Future<void> subscribeToUserTopic(String userId) async {
    await FirebaseMessagingService.subscribeToTopic('user_$userId');
  }

  // Unsubscribe from user-specific topic
  static Future<void> unsubscribeFromUserTopic(String userId) async {
    await FirebaseMessagingService.unsubscribeFromTopic('user_$userId');
  }

  // Clear all notifications
  static Future<void> clearAllNotifications() async {
    await FirebaseMessagingService.clearAllNotifications();
  }

  // Get current FCM token
  static String? getCurrentToken() {
    return FirebaseMessagingService.getCurrentToken();
  }
}

// Global navigator key for navigation from static methods
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

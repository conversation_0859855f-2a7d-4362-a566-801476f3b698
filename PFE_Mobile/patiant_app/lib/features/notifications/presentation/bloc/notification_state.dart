import 'package:equatable/equatable.dart';
import '../../data/models/notification_model.dart';

abstract class NotificationState extends Equatable {
  const NotificationState();

  @override
  List<Object?> get props => [];
}

class NotificationInitial extends NotificationState {}

class NotificationLoading extends NotificationState {}

class NotificationLoaded extends NotificationState {
  final List<NotificationModel> notifications;
  final int total;
  final int unreadCount;
  final int currentPage;
  final int totalPages;
  final bool hasMore;

  const NotificationLoaded({
    required this.notifications,
    required this.total,
    required this.unreadCount,
    required this.currentPage,
    required this.totalPages,
    required this.hasMore,
  });

  @override
  List<Object> get props => [
        notifications,
        total,
        unreadCount,
        currentPage,
        totalPages,
        hasMore,
      ];

  NotificationLoaded copyWith({
    List<NotificationModel>? notifications,
    int? total,
    int? unreadCount,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
  }) {
    return NotificationLoaded(
      notifications: notifications ?? this.notifications,
      total: total ?? this.total,
      unreadCount: unreadCount ?? this.unreadCount,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

class NotificationLoadingMore extends NotificationState {
  final List<NotificationModel> currentNotifications;
  final int unreadCount;

  const NotificationLoadingMore({
    required this.currentNotifications,
    required this.unreadCount,
  });

  @override
  List<Object> get props => [currentNotifications, unreadCount];
}

class NotificationError extends NotificationState {
  final String message;

  const NotificationError(this.message);

  @override
  List<Object> get props => [message];
}

class NotificationMarkAsReadSuccess extends NotificationState {
  final NotificationModel notification;
  final int newUnreadCount;

  const NotificationMarkAsReadSuccess({
    required this.notification,
    required this.newUnreadCount,
  });

  @override
  List<Object> get props => [notification, newUnreadCount];
}

class FcmTokenUpdateSuccess extends NotificationState {}

class FcmTokenUpdateError extends NotificationState {
  final String message;

  const FcmTokenUpdateError(this.message);

  @override
  List<Object> get props => [message];
}

class UnreadCountLoaded extends NotificationState {
  final int count;

  const UnreadCountLoaded(this.count);

  @override
  List<Object> get props => [count];
}

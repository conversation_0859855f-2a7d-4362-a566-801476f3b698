import 'package:dio/dio.dart';
import 'package:patiant_app/core/network/api_service.dart';
import '../models/notification_model.dart';

class NotificationRepository {
  final ApiService _apiService = ApiService();

  // Get user notifications with pagination and filters
  Future<Map<String, dynamic>> getUserNotifications({
    String? status,
    String? type,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      print(
          '📡 Calling API: /notifications with params: status=$status, type=$type, page=$page, limit=$limit');

      final response = await _apiService.get(
        '/notifications',
        requiresAuth: true,
        queryParams: {
          if (status != null) 'status': status,
          if (type != null) 'type': type,
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];
        final notifications = (data['notifications'] as List)
            .map((json) => NotificationModel.fromJson(json))
            .toList();

        return {
          'notifications': notifications,
          'total': data['total'] ?? 0,
          'unreadCount': data['unreadCount'] ?? 0,
          'currentPage': data['currentPage'] ?? 1,
          'totalPages': data['totalPages'] ?? 1,
        };
      }
      throw Exception('Failed to load notifications');
    } catch (e) {
      print('Error fetching notifications: $e');
      throw Exception('Failed to load notifications: $e');
    }
  }

  // Mark notification as read
  Future<NotificationModel> markAsRead(String notificationId) async {
    try {
      final response = await _apiService.put(
        '/notifications/$notificationId/read',
        {},
        requiresAuth: true,
      );

      if (response.statusCode == 200) {
        return NotificationModel.fromJson(response.data['data']);
      }
      throw Exception('Failed to mark notification as read');
    } catch (e) {
      print('Error marking notification as read: $e');
      throw Exception('Failed to mark notification as read: $e');
    }
  }

  // Update FCM token
  Future<void> updateFcmToken(String fcmToken) async {
    try {
      final response = await _apiService.post(
        '/notifications/fcm-token',
        {'fcmToken': fcmToken},
        requiresAuth: true,
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to update FCM token');
      }
    } catch (e) {
      print('Error updating FCM token: $e');
      throw Exception('Failed to update FCM token: $e');
    }
  }

  // Get unread notifications count
  Future<int> getUnreadCount() async {
    try {
      final response = await _apiService.get(
        '/notifications',
        requiresAuth: true,
        queryParams: {
          'status': 'UNREAD',
          'limit': 1, // We only need the count
        },
      );

      if (response.statusCode == 200) {
        return response.data['data']['unreadCount'] ?? 0;
      }
      return 0;
    } catch (e) {
      print('Error fetching unread count: $e');
      return 0;
    }
  }
}

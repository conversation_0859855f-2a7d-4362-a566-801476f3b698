import 'package:dio/dio.dart';
import 'package:flutter_web_auth/flutter_web_auth.dart';
import 'package:patiant_app/core/network/api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthRepository {
  final ApiService _apiService = ApiService();

  final String googleAuthUrl =
      'https://accounts.google.com/o/oauth2/auth?client_id=************-est5offjd7dd66rlri6ljcvhtogji8jl.apps.googleusercontent.com&redirect_uri=https://6cf4-196-179-48-12.ngrok-free.app/user/oauth/callback&response_type=code&scope=email%20profile&access_type=offline&prompt=consent';
  // ✅ Login (Updated: Fetches & Stores User Profile)
  Future<bool> login(String email, String password) async {
    try {
      print("📡 Calling API: /user/login with email: $email");

      Response response = await _apiService.post('/user/login', {
        "email": email,
        "password": password,
      });

      print("📥 API Response: ${response.data}");

      if (response.statusCode == 200 && response.data["token"] != null) {
        final Map<String, dynamic> tokenData = response.data["token"];
        final String jwtToken = tokenData["token"];
        final bool requiresPasswordReset = tokenData["requiresPasswordReset"];

        if (jwtToken.isNotEmpty) {
          SharedPreferences prefs = await SharedPreferences.getInstance();
          prefs.setString("token", jwtToken);

          // ✅ Fetch & Store User Profile after login
          await _fetchAndStoreUserProfile();

          // ✅ Redirect based on requiresPasswordReset flag
          return !requiresPasswordReset; // false → Redirect to reset password screen
        }
      }

      print("⚠️ Login failed with status: ${response.statusCode}");
      return false;
    } catch (e) {
      print("❌ Login error: $e");
      throw Exception("Login failed: ${e.toString()}");
    }
  }

  // ✅ Fetch & Store User Profile
  Future<void> _fetchAndStoreUserProfile() async {
    try {
      Response profileResponse =
          await _apiService.get('/user/getProfile', requiresAuth: true);

      if (profileResponse.statusCode == 200) {
        final profileData = profileResponse.data['data'];

        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.setString("firstName", profileData["firstName"]);
        prefs.setString("lastName", profileData["lastName"]);
        prefs.setString("phoneNumber", profileData["phoneNumber"]);

        print("✅ User profile stored in SharedPreferences");
      } else {
        print("⚠ Failed to fetch user profile");
      }
    } catch (e) {
      print("⚠ Error fetching user profile: $e");
    }
  }

  // ✅ Logout (Updated: Now Clears Profile Data)
  Future<void> logout() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove("token");
    await prefs.remove("firstName");
    await prefs.remove("lastName");
    await prefs.remove("phoneNumber");

    print("✅ User logged out and profile data cleared");
  }

  // ✅ Register (Unchanged)
  Future<bool> register({
    required String email,
    required String password,
    required String confirmPassword,
    required String firstName,
    required String lastName,
  }) async {
    try {
      print("Calling API: /user/register");
      final response = await _apiService.post('/user/register', {
        "email": email,
        "password": password,
        "confirmPassword": confirmPassword,
        "firstName": firstName,
        "lastName": lastName,
      });

      print("API Response: ${response.data}");
      return response.statusCode == 201;
    } catch (e) {
      print("Registration error: $e");
      throw Exception("Registration failed");
    }
  }

  // ✅ Forgot Password (Unchanged)
  Future<void> forgotPassword(String email) async {
    try {
      await _apiService.post('/user/forgotPassword', {"email": email});
    } catch (e) {
      throw Exception("Failed to send reset email");
    }
  }

  // ✅ Verify Reset Code (Unchanged)
  Future<void> verifyResetCode(String email, String code) async {
    try {
      await _apiService.post('/user/verifyResetCode', {
        "email": email,
        "code": code,
      });
    } catch (e) {
      throw Exception("Invalid or expired reset code");
    }
  }

  // ✅ Reset Password (Unchanged)
  Future<void> resetPassword(String email, String code, String newPassword,
      String confirmNewPassword) async {
    try {
      await _apiService.post('/user/resetPassword', {
        "email": email,
        "newPassword": newPassword,
        "confirmNewPassword": confirmNewPassword,
      });
    } catch (e) {
      throw Exception("Failed to reset password");
    }
  }

  // 🌟 Google OAuth Login (No Bloc Change)
  Future<String?> initiateGoogleLogin() async {
    try {
      final result = await FlutterWebAuth.authenticate(
        url: googleAuthUrl,
        callbackUrlScheme: "patiantapp",
      );

      final uri = Uri.parse(result);
      final code = uri.queryParameters['code'];
      final email = uri.queryParameters['email'];

      if (code != null && email != null) {
        // Emit the existing Bloc event with the email and code
        return "$email:$code";
      } else {
        print("⚠️ No code or email received.");
        return null;
      }
    } catch (e) {
      print("❌ Google OAuth error: $e");
      return null;
    }
  }

  // 📧 Verify 2FA Code (auth_repository.dart)
  Future<String?> verifyTwoFactor(String email, String code) async {
    try {
      final response = await _apiService.post('/user/verify-2fa', {
        "email": email,
        "code": code,
      });

      if (response.statusCode == 200 && response.data['token'] != null) {
        final token = response.data['token'];

        // 🟢 Store the token securely
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString("token", token);

        print("✅ 2FA verified, token received: $token");
        return token;
      }

      print("⚠️ 2FA failed, no token returned.");
      return null;
    } catch (e) {
      print("❌ 2FA verification error: $e");
      return null;
    }
  }

  Future<bool> resetLoginPassword(
      String newPassword, String confirmPassword) async {
    try {
      Response response = await _apiService.post(
        '/user/resetLoginPassword', // ✅ Make sure this matches your backend
        {
          "newPassword": newPassword,
          "confirmPassword": confirmPassword,
        },
        requiresAuth: true, // ✅ Ensure this request includes authentication
      );

      return response.statusCode == 200;
    } catch (e) {
      print("Reset password error: $e");
      return false;
    }
  }

  // Send FCM token to backend
  Future<bool> sendFCMToken(String fcmToken) async {
    try {
      print("📡 Sending FCM token to backend: $fcmToken");

      Response response = await _apiService.post(
        '/notifications/fcm-token',
        {
          "fcmToken": fcmToken,
        },
        requiresAuth: true,
      );

      if (response.statusCode == 200) {
        print("✅ FCM token sent successfully");
        return true;
      } else {
        print("⚠️ Failed to send FCM token: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      print("❌ Error sending FCM token: $e");
      return false;
    }
  }
}

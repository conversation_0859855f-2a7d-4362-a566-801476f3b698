import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pinput/pinput.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';
import 'login_screen.dart';

class Verify2FAScreen extends StatefulWidget {
  final String email;
  final String oauthCode;

  const Verify2FAScreen(
      {super.key, required this.email, required this.oauthCode});

  @override
  _Verify2FAScreenState createState() => _Verify2FAScreenState();
}

class _Verify2FAScreenState extends State<Verify2FAScreen> {
  final TextEditingController codeController = TextEditingController();
  bool isValidCode = false;
  bool isWrongCode = false;
  bool isProcessing = false;

  void _verifyCode(String code) {
    if (isProcessing) return;
    setState(() => isProcessing = true);

    context.read<AuthBloc>().add(
          VerifyTwoFactorEvent(widget.email, code),
        );
  }

  Future<bool> _onWillPop() async {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => const LoginScreen()),
      (route) => false,
    );
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.black),
        ),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: BlocListener<AuthBloc, AuthState>(
            listener: (context, state) {
              if (state is AuthAuthenticated) {
                setState(() {
                  isValidCode = true;
                  isWrongCode = false;
                  isProcessing = false;
                });

                Future.delayed(const Duration(seconds: 1), () {
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(builder: (context) => LoginScreen()),
                    (route) => false,
                  );
                });
              } else if (state is AuthError) {
                setState(() {
                  isValidCode = false;
                  isWrongCode = true;
                  isProcessing = false;
                });
              }
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 20),
                const Text(
                  "Enter verification code",
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Text(
                  "A verification code was sent to ${widget.email}.",
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),

                // PIN Input Field
                Pinput(
                  length: 6,
                  controller: codeController,
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    setState(() {
                      isValidCode = false;
                      isWrongCode = false;
                    });
                  },
                  onCompleted: (value) => _verifyCode(value),
                  defaultPinTheme: PinTheme(
                    width: 50,
                    height: 50,
                    textStyle: const TextStyle(
                        fontSize: 24, fontWeight: FontWeight.bold),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: isValidCode
                            ? Colors.green
                            : isWrongCode
                                ? Colors.red
                                : Colors.grey.shade400,
                      ),
                    ),
                  ),
                  focusedPinTheme: PinTheme(
                    width: 50,
                    height: 50,
                    textStyle: const TextStyle(
                        fontSize: 24, fontWeight: FontWeight.bold),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.blue),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                if (isProcessing) const CircularProgressIndicator(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

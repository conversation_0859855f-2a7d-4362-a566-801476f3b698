import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/authentication/presentation/screens/home_screen.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_event.dart';
import 'package:patiant_app/features/profile/presentation/screens/profile_screen.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_event.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';
import 'package:patiant_app/features/notifications/services/notification_service.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/custom_nav_bar.dart';

class MainLayout extends StatefulWidget {
  const MainLayout({super.key});

  @override
  _MainLayoutState createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  int _selectedIndex = 0;
  String? profilePhoto;
  String firstName = "";
  String lastName = "";
  int unreadNotificationCount = 0;

  @override
  void initState() {
    super.initState();
    context.read<ProfileBloc>().add(LoadProfile());

    // Initialize notification service
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    try {
      await NotificationService.initialize(context);
      // Load initial unread count
      if (mounted) {
        context.read<NotificationBloc>().add(const LoadUnreadCount());
      }
    } catch (e) {
      print('Notification service initialization failed: $e');
      // Continue without notifications for now
    }
  }

  final List<Widget> _screens = [
    const HomeScreen(),
    const ProfileScreen(),
  ];

  void _onNavItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ProfileBloc, ProfileState>(
          listener: (context, state) {
            if (state is ProfileLoaded) {
              setState(() {
                profilePhoto = state.profilePhoto;
                firstName = state.firstName;
                lastName = state.lastName;
              });
            }
          },
        ),
        BlocListener<NotificationBloc, NotificationState>(
          listener: (context, state) {
            print('🔔 Notification state changed: ${state.runtimeType}');
            if (state is UnreadCountLoaded) {
              setState(() {
                unreadNotificationCount = state.count;
              });
              print('🔔 Unread count updated: ${state.count}');
            } else if (state is NotificationLoaded) {
              setState(() {
                unreadNotificationCount = state.unreadCount;
              });
              print('🔔 Unread count from notifications: ${state.unreadCount}');
            }
          },
        ),
      ],
      child: Scaffold(
        appBar: CustomAppBar(
          logoPath: 'assets/logo.png',
          profilePhoto: profilePhoto,
          firstName: firstName,
          lastName: lastName,
          notificationCount:
              unreadNotificationCount > 0 ? unreadNotificationCount : null,
          onNotificationTap: () {
            print(
                '🔔 Notification icon clicked - navigating to /notifications');
            Navigator.pushNamed(context, '/notifications');
          },
          onProfileTap: () {
            setState(() {
              _selectedIndex = 1;
            });
          },
        ),
        body: IndexedStack(
          index: _selectedIndex,
          children: _screens,
        ),
        bottomNavigationBar: CustomNavBar(
          selectedIndex: _selectedIndex,
          onTap: _onNavItemTapped,
        ),
      ),
    );
  }
}

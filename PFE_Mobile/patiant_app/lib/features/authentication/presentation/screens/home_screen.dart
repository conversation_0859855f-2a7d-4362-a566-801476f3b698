import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_event.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/notifications/services/notification_service.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_event.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/custom_nav_bar.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  String? profilePhoto;
  String firstName = "";
  String lastName = "";

  @override
  void initState() {
    super.initState();
    context.read<ProfileBloc>().add(LoadProfile());

    context.read<ProfileBloc>().stream.listen((state) {
      if (state is ProfileLoaded) {
        setState(() {
          profilePhoto = state.profilePhoto;
          firstName = state.firstName;
          lastName = state.lastName;
        });
      }
    });

    // Initialize notification service
    _initializeNotificationService();
  }

  // Initialize notification service
  Future<void> _initializeNotificationService() async {
    try {
      await NotificationService.initialize(context);
      // Load initial unread count after initialization
      if (mounted) {
        context.read<NotificationBloc>().add(const LoadUnreadCount());
      }
      print('Notification service initialized successfully');
    } catch (e) {
      print('Failed to initialize notification service: $e');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)?.settings.arguments as Map?;
    if (args != null && args['selectedIndex'] != null) {
      setState(() {
        _selectedIndex = args['selectedIndex'];
      });
    }
  }

  void _onNavItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  void _navigateToProducts() {
    Navigator.pushNamed(context, '/products');
  }

  void _navigateToPrescriptions() {
    Navigator.pushNamed(context, '/prescriptions');
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, notificationState) {
        int unreadCount = 0;
        if (notificationState is NotificationLoaded) {
          unreadCount = notificationState.unreadCount;
        } else if (notificationState is UnreadCountLoaded) {
          unreadCount = notificationState.count;
        }

        return Scaffold(
          appBar: CustomAppBar(
            logoPath: 'assets/logo.png',
            profilePhoto: profilePhoto,
            firstName: firstName,
            lastName: lastName,
            notificationCount: unreadCount > 0 ? unreadCount : null,
            onNotificationTap: () {
              Navigator.pushNamed(context, '/notifications');
            },
            onProfileTap: () {
              Navigator.pushReplacementNamed(
                context,
                '/profile',
                arguments: {'selectedIndex': 3},
              );
            },
          ),
          body: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                const SizedBox(height: 20),
                _buildFeatureCard(
                  title: "Explorer les produits",
                  subtitle:
                      "Parcourez et commandez des articles disponibles en pharmacie",
                  icon: Icons.shopping_bag,
                  onTap: _navigateToProducts,
                  color: Colors.teal.shade400,
                ),
                const SizedBox(height: 20),
                _buildFeatureCard(
                  title: "Mes ordonnances",
                  subtitle:
                      "Consultez, téléversez ou mettez à jour vos prescriptions",
                  icon: Icons.description,
                  onTap: _navigateToPrescriptions,
                  color: Colors.indigo.shade400,
                ),
              ],
            ),
          ),
          bottomNavigationBar: CustomNavBar(
            selectedIndex: _selectedIndex,
            onTap: _onNavItemTapped,
          ),
        );
      },
    );
  }

  Widget _buildFeatureCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color color = Colors.blue,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        color: color,
        elevation: 5,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
          child: Row(
            children: [
              Icon(icon, size: 40, color: Colors.white),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, color: Colors.white),
            ],
          ),
        ),
      ),
    );
  }
}

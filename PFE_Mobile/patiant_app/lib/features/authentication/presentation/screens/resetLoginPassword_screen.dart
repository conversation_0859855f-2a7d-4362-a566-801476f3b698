import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/authentication/presentation/bloc/auth_bloc.dart';
import 'package:patiant_app/features/authentication/presentation/bloc/auth_event.dart';
import 'package:patiant_app/features/authentication/presentation/bloc/auth_state.dart';
import 'package:patiant_app/features/authentication/presentation/screens/home_screen.dart';

class ResetLoginPasswordScreen extends StatefulWidget {
  @override
  _ResetLoginPasswordScreenState createState() =>
      _ResetLoginPasswordScreenState();
}

class _ResetLoginPasswordScreenState extends State<ResetLoginPasswordScreen> {
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  final _formKey = GlobalKey<FormState>(); // ✅ Form validation key

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Reset Password")),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey, // ✅ Attach form key
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text("New Password", style: TextStyle(fontSize: 16)),
              TextFormField(
                controller: newPasswordController,
                obscureText: true,
                decoration: InputDecoration(
                  hintText: "Enter new password",
                  errorStyle: const TextStyle(
                      color: Colors.red), // 🔴 Ensure errors show in red
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "Password cannot be empty";
                  }
                  if (value.length < 6) {
                    return "Password must be at least 6 characters long";
                  }
                  if (!RegExp(r'[A-Z]').hasMatch(value)) {
                    return "Password must contain at least one uppercase letter";
                  }
                  if (!RegExp(r'\d').hasMatch(value)) {
                    return "Password must contain at least one number";
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              const Text("Confirm Password", style: TextStyle(fontSize: 16)),
              TextFormField(
                controller: confirmPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  hintText: "Confirm new password",
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "Confirm password cannot be empty";
                  }
                  if (value != newPasswordController.text) {
                    return "Passwords do not match";
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              BlocConsumer<AuthBloc, AuthState>(
                listener: (context, state) {
                  if (state is AuthAuthenticated) {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const HomeScreen()),
                    );
                  } else if (state is AuthError) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(state.message)),
                    );
                  }
                },
                builder: (context, state) {
                  return Column(
                    children: [
                      if (state is AuthLoading)
                        const CircularProgressIndicator(),
                      ElevatedButton(
                        onPressed: () {
                          if (_formKey.currentState!.validate()) {
                            context.read<AuthBloc>().add(
                                  ResetLoginPasswordEvent(
                                    newPasswordController.text.trim(),
                                    confirmPasswordController.text.trim(),
                                  ),
                                );
                          }
                        },
                        child: const Text("Reset Password"),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

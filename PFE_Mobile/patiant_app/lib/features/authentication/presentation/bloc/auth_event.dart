abstract class AuthEvent {}

class LoginEvent extends AuthEvent {
  final String email;
  final String password;

  LoginEvent(this.email, this.password);
}

class LogoutEvent extends AuthEvent {} // Added this

class RegisterEvent extends AuthEvent {
  final String email;
  final String password;
  final String confirmPassword;
  final String firstName;
  final String lastName;

  RegisterEvent(
    this.email,
    this.password,
    this.confirmPassword,
    this.firstName,
    this.lastName,
  );
}

class ForgotPasswordEvent extends AuthEvent {
  final String email;
  ForgotPasswordEvent(this.email);
}

class VerifyResetCodeEvent extends AuthEvent {
  final String email;
  final String code;
  VerifyResetCodeEvent(this.email, this.code);
}

class ResetPasswordEvent extends AuthEvent {
  final String email;
  final String code;
  final String newPassword;
  final String confirmNewPassword;
  ResetPasswordEvent(
      this.email, this.code, this.newPassword, this.confirmNewPassword);
}

class GoogleLoginEvent extends AuthEvent {}

class VerifyTwoFactorEvent extends AuthEvent {
  final String email;
  final String code;

  VerifyTwoFactorEvent(this.email, this.code);
}

class ResetLoginPasswordEvent extends AuthEvent {
  final String newPassword;
  final String confirmPassword;

  ResetLoginPasswordEvent(this.newPassword, this.confirmPassword);
}

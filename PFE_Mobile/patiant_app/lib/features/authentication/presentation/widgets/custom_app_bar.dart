import 'package:flutter/material.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String logoPath;
  final VoidCallback onNotificationTap;
  final VoidCallback onProfileTap;
  final String? profilePhoto;
  final String firstName;
  final String lastName;
  final int? notificationCount;

  const CustomAppBar({
    super.key,
    required this.logoPath,
    required this.onNotificationTap,
    required this.onProfileTap,
    this.profilePhoto,
    required this.firstName,
    required this.lastName,
    this.notificationCount,
  });

  String getInitials() {
    String firstInitial =
        firstName.isNotEmpty ? firstName[0].toUpperCase() : "";
    String lastInitial = lastName.isNotEmpty ? lastName[0].toUpperCase() : "";
    return "$firstInitial$lastInitial";
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      automaticallyImplyLeading: false,
      title: Image.asset(logoPath, height: 40),
      actions: [
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications, color: Colors.blue),
              onPressed: onNotificationTap,
            ),
            if (notificationCount != null && notificationCount! > 0)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    notificationCount! > 99
                        ? '99+'
                        : notificationCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
        GestureDetector(
          onTap: onProfileTap,
          child: CircleAvatar(
            radius: 20,
            backgroundColor: Colors.grey,
            backgroundImage: (profilePhoto != null && profilePhoto!.isNotEmpty)
                ? NetworkImage("http://192.168.0.70:3000$profilePhoto")
                : null,
            child: (profilePhoto == null || profilePhoto!.isEmpty)
                ? Text(
                    getInitials(),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
        ),
        const SizedBox(width: 10),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);
}

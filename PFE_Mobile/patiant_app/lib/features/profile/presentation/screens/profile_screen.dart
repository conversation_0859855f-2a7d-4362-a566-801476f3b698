import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_event.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/profile/presentation/screens/updateProfile_screen.dart';
import 'package:patiant_app/features/authentication/presentation/screens/login_screen.dart';
import 'package:patiant_app/features/authentication/presentation/bloc/auth_bloc.dart';
import 'package:patiant_app/features/authentication/presentation/bloc/auth_event.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  int _selectedIndex = 3;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)?.settings.arguments as Map?;
    if (args != null && args['selectedIndex'] != null) {
      setState(() {
        _selectedIndex = args['selectedIndex'];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ProfileBloc(context.read())..add(LoadProfile()),
      child: BlocListener<ProfileBloc, ProfileState>(
        listener: (context, state) {
          if (state is ProfileUpdated) {
            context.read<ProfileBloc>().add(ProfileRefreshed());
          }
        },
        child: BlocBuilder<ProfileBloc, ProfileState>(
          builder: (context, state) {
            String? profilePhoto;
            String firstName = '';
            String lastName = '';

            if (state is ProfileLoaded) {
              profilePhoto = state.profilePhoto;
              firstName = state.firstName;
              lastName = state.lastName;
            }

            return BlocBuilder<NotificationBloc, NotificationState>(
              builder: (context, notificationState) {
                int unreadCount = 0;
                if (notificationState is NotificationLoaded) {
                  unreadCount = notificationState.unreadCount;
                } else if (notificationState is UnreadCountLoaded) {
                  unreadCount = notificationState.count;
                }

                return Scaffold(
                  appBar: CustomAppBar(
                    logoPath: 'assets/logo.png',
                    notificationCount: unreadCount > 0 ? unreadCount : null,
                    onNotificationTap: () {
                      print('🔔 Profile screen - notification icon clicked');
                      Navigator.pushNamed(context, '/notifications');
                    },
                    onProfileTap: () {},
                    profilePhoto: profilePhoto,
                    firstName: firstName,
                    lastName: lastName,
                  ),
                  backgroundColor: Colors.white,
                  bottomNavigationBar: CustomNavBar(
                    selectedIndex: _selectedIndex,
                    onTap: (index) {
                      setState(() {
                        _selectedIndex = index;
                      });
                    },
                  ),
                  body: state is ProfileLoading
                      ? const Center(child: CircularProgressIndicator())
                      : state is ProfileLoaded
                          ? _buildProfileContent(
                              state.firstName,
                              state.lastName,
                              state.phoneNumber,
                              state.profilePhoto,
                              context,
                            )
                          : state is ProfileError
                              ? Center(child: Text(state.message))
                              : const Center(
                                  child: Text(
                                      "Erreur lors du chargement du profil")),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileContent(
    String firstName,
    String lastName,
    String phoneNumber,
    String? profilePhoto,
    BuildContext context,
  ) {
    String? imageUrl = (profilePhoto != null && profilePhoto.isNotEmpty)
        ? (profilePhoto.startsWith('http')
            ? profilePhoto
            : "http://192.168.0.70:3000$profilePhoto")
        : null;

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: Colors.grey,
            backgroundImage: imageUrl != null ? NetworkImage(imageUrl) : null,
            child: imageUrl == null
                ? Text(
                    "${firstName.isNotEmpty ? firstName[0].toUpperCase() : "?"}${lastName.isNotEmpty ? lastName[0].toUpperCase() : "?"}",
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          const SizedBox(height: 10),
          Text(
            "${firstName.isNotEmpty ? firstName[0].toUpperCase() + firstName.substring(1) : "?"} "
            "${lastName.isNotEmpty ? lastName[0].toUpperCase() + lastName.substring(1) : "?"}",
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            overflow: TextOverflow.ellipsis,
          ),
          Text(phoneNumber, style: const TextStyle(color: Colors.grey)),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildStatCard("100", "Commandes livrées"),
              const SizedBox(width: 20),
              _buildStatCard("5", "Commandes annulées"),
            ],
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFDC859),
              minimumSize: const Size(double.infinity, 50),
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => BlocProvider.value(
                    value: context.read<ProfileBloc>(),
                    child: const UpdateProfileScreen(),
                  ),
                ),
              ).then((result) {
                if (result == true) {
                  context.read<ProfileBloc>().add(LoadProfile());
                }
              });
            },
            child: const Text(
              "Modifier le profil",
              style: TextStyle(fontSize: 16, color: Colors.black),
            ),
          ),
          const SizedBox(height: 20),
          _buildOption(context, "Mot de passe et sécurité", Icons.lock),
          _buildOption(context, "Notifications", Icons.notifications,
              route: '/notifications'),
          _buildOption(context, "Mes commandes", Icons.history,
              route: '/history'),
          _buildOption(context, "Se déconnecter", Icons.exit_to_app,
              isLogout: true),
        ],
      ),
    );
  }

  Widget _buildStatCard(String value, String label) {
    return Container(
      width: 140,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: const [
          BoxShadow(color: Colors.grey, blurRadius: 3, spreadRadius: 1),
        ],
      ),
      child: Column(
        children: [
          Text(
            value,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Text(label, style: const TextStyle(color: Colors.grey, fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildOption(BuildContext context, String title, IconData icon,
      {bool isLogout = false, String? route}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Card(
        elevation: 0,
        color: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: ListTile(
          leading: Icon(icon, color: isLogout ? Colors.red : Colors.blue),
          title: Text(title),
          onTap: () async {
            if (isLogout) {
              SharedPreferences prefs = await SharedPreferences.getInstance();
              await prefs.remove("token");
              context.read<AuthBloc>().add(LogoutEvent());

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text("Déconnexion réussie.")),
              );

              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const LoginScreen()),
              );
            } else if (route != null) {
              Navigator.pushNamed(context, route);
            }
          },
        ),
      ),
    );
  }
}

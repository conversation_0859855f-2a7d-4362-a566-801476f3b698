import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_event.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/phone_number.dart';

class UpdateProfileScreen extends StatefulWidget {
  const UpdateProfileScreen({super.key});

  @override
  _UpdateProfileScreenState createState() => _UpdateProfileScreenState();
}

class _UpdateProfileScreenState extends State<UpdateProfileScreen> {
  bool is2FAEnabled = false;
  bool isChanged = false;

  // Controllers for form fields
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  String countryCode = "TN";
  String? initialProfilePhoto;
  String? currentProfilePhoto;
  File? selectedImage;
  bool photoRemoved = false;

  // Store initial values for comparison
  String? initialFirstName;
  String? initialLastName;
  String? initialEmail;
  String? initialPhoneNumber;

  @override
  void initState() {
    super.initState();
    context.read<ProfileBloc>().add(LoadProfile());
  }

  // 🟢 Track Changes to Enable Save Button
  void _trackChanges() {
    setState(() {
      isChanged = firstNameController.text != (initialFirstName ?? "") ||
          lastNameController.text != (initialLastName ?? "") ||
          emailController.text != (initialEmail ?? "") ||
          phoneController.text != (initialPhoneNumber ?? "") ||
          (selectedImage != null) ||
          (photoRemoved &&
              initialProfilePhoto != null &&
              initialProfilePhoto!.isNotEmpty);
    });
  }

  // 🟢 Pick Image from Gallery
  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        selectedImage = File(image.path);
        currentProfilePhoto = null;
        photoRemoved = false;
        _trackChanges();
      });
    }
  }

  // 🔴 Remove Selected Image
  void _removeImage() {
    setState(() {
      selectedImage = null;
      currentProfilePhoto = null;
      photoRemoved = true;
      _trackChanges();
    });
  }

  // 🟢 Save Profile Changes
  void _saveProfile() {
    if (!isChanged) return;

    context.read<ProfileBloc>().add(
          UpdateProfileEvent(
            firstName: firstNameController.text,
            lastName: lastNameController.text,
            email: emailController.text,
            phoneNumber: phoneController.text,
            profilePhoto: selectedImage,
          ),
        );
  }

  // 🟢 Generate Initials for Avatar
  String _getInitials(String? firstName, String? lastName) {
    String firstInitial =
        (firstName?.isNotEmpty ?? false) ? firstName![0].toUpperCase() : "?";
    String lastInitial =
        (lastName?.isNotEmpty ?? false) ? lastName![0].toUpperCase() : "?";
    return "$firstInitial$lastInitial";
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, notificationState) {
        int unreadCount = 0;
        if (notificationState is NotificationLoaded) {
          unreadCount = notificationState.unreadCount;
        } else if (notificationState is UnreadCountLoaded) {
          unreadCount = notificationState.count;
        }

        return Scaffold(
          backgroundColor: const Color(0xFFF8F9FC),
          appBar: CustomAppBar(
            logoPath: 'assets/logo.png',
            notificationCount: unreadCount > 0 ? unreadCount : null,
            profilePhoto: currentProfilePhoto,
            firstName: firstNameController.text,
            lastName: lastNameController.text,
            onNotificationTap: () {
              Navigator.pushNamed(context, '/notifications');
            },
            onProfileTap: () {},
          ),
          body: BlocListener<ProfileBloc, ProfileState>(
            listener: (context, state) {
              if (state is ProfileLoaded) {
                // Initialize form with loaded data
                firstNameController.text = initialFirstName = state.firstName;
                lastNameController.text = initialLastName = state.lastName;
                emailController.text = initialEmail = state.email;
                phoneController.text = initialPhoneNumber = state.phoneNumber;

                initialProfilePhoto = state.profilePhoto ?? "";
                currentProfilePhoto = state.profilePhoto ?? "";
                is2FAEnabled = state.isTwoFactorEnabled;
                photoRemoved = false;
                _trackChanges();
              } else if (state is ProfileUpdated) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text("Profile updated successfully!")),
                );
                Navigator.pop(context, true);
              } else if (state is ProfileError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(state.message)),
                );
              }
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.black),
                    onPressed: () => Navigator.pop(context),
                  ),
                  const Center(
                    child: Text(
                      "Edit Profile",
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(height: 20),
                  _buildProfileImageSection(),
                  const SizedBox(height: 20),
                  _buildTextField("First Name", firstNameController),
                  _buildTextField("Last Name", lastNameController),
                  _buildTextField("Email", emailController, enabled: false),
                  _buildPhoneNumberField(),
                  _buildToggleSwitch(),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          isChanged ? const Color(0xFF54BAB9) : Colors.grey,
                      minimumSize: const Size(double.infinity, 50),
                    ),
                    onPressed: isChanged ? _saveProfile : null,
                    child: const Text(
                      "Save",
                      style: TextStyle(fontSize: 16, color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // 🟢 Profile Image Section
  Widget _buildProfileImageSection() {
    bool hasPhoto =
        currentProfilePhoto != null && currentProfilePhoto!.isNotEmpty;
    bool hasSelectedImage = selectedImage != null;

    return Center(
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: Colors.grey,
            backgroundImage: hasSelectedImage
                ? FileImage(selectedImage!)
                : hasPhoto
                    ? NetworkImage(
                        "http://192.168.0.70:3000$currentProfilePhoto")
                    : null,
            child: (!hasPhoto && !hasSelectedImage)
                ? Text(
                    _getInitials(initialFirstName, initialLastName),
                    style: const TextStyle(
                        fontSize: 24, fontWeight: FontWeight.bold),
                  )
                : null,
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: _pickImage,
            child: const Text(
              "Change Image",
              style: TextStyle(color: Colors.blue),
            ),
          ),

          // Reserve space for Remove Image button using Visibility
          Visibility(
            visible: hasPhoto || hasSelectedImage,
            child: TextButton(
              onPressed: _removeImage,
              child: const Text(
                "Remove Image",
                style: TextStyle(color: Colors.red),
              ),
            ),
            replacement:
                const SizedBox(height: 20), // Space even if button is hidden
          ),
        ],
      ),
    );
  }

  // 🟢 Build TextField Widget
  Widget _buildTextField(String label, TextEditingController controller,
      {bool enabled = true}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: TextField(
        controller: controller,
        enabled: enabled,
        onChanged: (_) => _trackChanges(),
        decoration: InputDecoration(
          labelText: label,
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }

  // 📞 Phone Number Field
  Widget _buildPhoneNumberField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: Focus(
        onFocusChange: (hasFocus) {
          if (!hasFocus) {
            _trackChanges();
          }
        },
        child: IntlPhoneField(
          decoration: InputDecoration(
            labelText: "Phone Number",
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          initialCountryCode: countryCode, // ✅ Retain country code
          controller: phoneController,
          keyboardType: TextInputType.phone,
          textInputAction: TextInputAction.done,
          onChanged: (phone) {
            setState(() {
              phoneController.text = phone.number;
              _trackChanges();
            });
          },

          onCountryChanged: (country) {
            setState(() => countryCode = country.code);
            _trackChanges();
          },
          onSubmitted: (_) {
            FocusScope.of(context).unfocus();
            _trackChanges();
          },
        ),
      ),
    );
  }

  // 🔒 Two-Factor Authentication Toggle
  Widget _buildToggleSwitch() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          "Enable Two-Factor Authentication (2FA)",
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        Switch(
          value: is2FAEnabled,
          onChanged: (bool value) {
            setState(() => is2FAEnabled = value);
            context.read<ProfileBloc>().add(
                  value ? EnableTwoFactorEvent() : DisableTwoFactorEvent(),
                );
          },
          activeColor: Colors.green,
          inactiveTrackColor: Colors.red.shade300,
          inactiveThumbColor: Colors.white,
        ),
      ],
    );
  }
}

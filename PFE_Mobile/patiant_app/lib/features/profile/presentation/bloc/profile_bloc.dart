import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/profile/data/repositories/profile_repository.dart';
import 'profile_event.dart';
import 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final ProfileRepository profileRepository;
  String? uploadedPhotoPath;

  ProfileBloc(this.profileRepository) : super(ProfileInitial()) {
    on<LoadProfile>(_onLoadProfile);
    on<UpdateProfileEvent>(_onUpdateProfile);
    on<UploadProfilePhotoEvent>(_onUploadProfilePhoto);
    on<RemoveProfilePhotoEvent>(_onRemoveProfilePhoto);
    on<EnableTwoFactorEvent>(_onEnableTwoFactor);
    on<DisableTwoFactorEvent>(_onDisableTwoFactor);
    on<ProfileRefreshed>(_onProfileRefreshed);
  }

  Future<void> _onLoadProfile(
      LoadProfile event, Emitter<ProfileState> emit) async {
    emit(ProfileLoading());
    try {
      var userData = await profileRepository.fetchUserProfile();
      if (userData != null) {
        uploadedPhotoPath = userData["profilePhoto"];
        emit(ProfileLoaded(
          id: userData["id"],
          firstName: userData["firstName"],
          lastName: userData["lastName"],
          email: userData["email"],
          phoneNumber: userData["phoneNumber"],
          address: userData["address"]?["address"] ?? "", // 🟢 New field
          isTwoFactorEnabled: userData["isTwoFactorEnabled"],
          profilePhoto: uploadedPhotoPath ?? "",
        ));
      } else {
        emit(ProfileError("Failed to load profile data"));
      }
    } catch (e) {
      emit(ProfileError("Error fetching profile: $e"));
    }
  }

  Future<void> _onUploadProfilePhoto(
      UploadProfilePhotoEvent event, Emitter<ProfileState> emit) async {
    try {
      String? photoPath =
          await profileRepository.uploadProfilePhoto(event.photo);
      if (photoPath != null) {
        uploadedPhotoPath = photoPath;
        emit(ProfilePhotoUploaded(photoPath));
      } else {
        emit(ProfileError("Failed to upload profile photo"));
      }
    } catch (e) {
      emit(ProfileError("Error uploading photo: $e"));
    }
  }

  Future<void> _onRemoveProfilePhoto(
      RemoveProfilePhotoEvent event, Emitter<ProfileState> emit) async {
    uploadedPhotoPath = "";
    emit(ProfilePhotoUploaded("")); // ✅ Set empty path for removal
  }

  Future<void> _onUpdateProfile(
    UpdateProfileEvent event,
    Emitter<ProfileState> emit,
  ) async {
    emit(ProfileLoading());
    try {
      // 🟢 If the user picked a new image, upload it and update the bloc field
      if (event.profilePhoto != null) {
        uploadedPhotoPath =
            await profileRepository.uploadProfilePhoto(event.profilePhoto!);
      }
      // 🟢 Build the value to send: either the new upload, or the existing (or empty) path
      final String? photoToSend = uploadedPhotoPath;

      // 🟢 Call your API with the correct photo path
      bool success = await profileRepository.updateUserProfile(
        firstName: event.firstName,
        lastName: event.lastName,
        email: event.email,
        phoneNumber: event.phoneNumber,
        profilePhoto: photoToSend,
      );

      if (success) {
        // Reload to reflect changes
        add(LoadProfile());
        emit(ProfileUpdated());
      } else {
        emit(ProfileError("Profile update failed"));
      }
    } catch (e) {
      emit(ProfileError("Error updating profile: $e"));
    }
  }

  Future<void> _onEnableTwoFactor(
      EnableTwoFactorEvent event, Emitter<ProfileState> emit) async {
    try {
      bool success = await profileRepository.enableTwoFactor();
      if (success) {
        add(ProfileRefreshed());
        emit(TwoFactorEnabled());
      } else {
        emit(ProfileError("Failed to enable 2FA"));
      }
    } catch (e) {
      emit(ProfileError("Error enabling 2FA: $e"));
    }
  }

  Future<void> _onDisableTwoFactor(
      DisableTwoFactorEvent event, Emitter<ProfileState> emit) async {
    try {
      bool success = await profileRepository.disableTwoFactor();
      if (success) {
        add(ProfileRefreshed());
        emit(TwoFactorDisabled());
      } else {
        emit(ProfileError("Failed to disable 2FA"));
      }
    } catch (e) {
      emit(ProfileError("Error disabling 2FA: $e"));
    }
  }

  Future<void> _onProfileRefreshed(
      ProfileRefreshed event, Emitter<ProfileState> emit) async {
    await _onLoadProfile(LoadProfile(), emit);
  }
}

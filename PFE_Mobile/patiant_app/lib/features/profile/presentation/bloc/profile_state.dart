abstract class ProfileState {}

class Profile<PERSON>nitial extends ProfileState {}

class ProfileLoading extends ProfileState {}

class ProfileLoaded extends ProfileState {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String phoneNumber;
  final String address; // 🟢 New field
  final bool isTwoFactorEnabled;
  final String? profilePhoto;

  ProfileLoaded({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phoneNumber,
    required this.address, // 🟢 New field
    required this.isTwoFactorEnabled,
    this.profilePhoto,
  });
}

class ProfileUpdated extends ProfileState {}

class ProfilePhotoUploaded extends ProfileState {
  final String photoPath;
  ProfilePhotoUploaded(this.photoPath);
}

class ProfileError extends ProfileState {
  final String message;
  ProfileError(this.message);
}

class TwoFactorEnabled extends ProfileState {}

class TwoFactorDisabled extends ProfileState {}

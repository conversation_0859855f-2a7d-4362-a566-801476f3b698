import 'dart:io';

abstract class ProfileEvent {}

class LoadProfile extends ProfileEvent {}

class UpdateProfileEvent extends ProfileEvent {
  final String firstName;
  final String lastName;
  final String email;
  final String phoneNumber;
  final File? profilePhoto;

  UpdateProfileEvent({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phoneNumber,
    this.profilePhoto,
  });
}

class EnableTwoFactorEvent extends ProfileEvent {}

class DisableTwoFactorEvent extends ProfileEvent {}

class ProfileRefreshed extends ProfileEvent {}

// ✅ Upload Photo Event
class UploadProfilePhotoEvent extends ProfileEvent {
  final File photo;
  UploadProfilePhotoEvent(this.photo);
}

// ✅ Remove Photo Event
class RemoveProfilePhotoEvent extends ProfileEvent {}

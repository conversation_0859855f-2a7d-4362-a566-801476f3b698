import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:patiant_app/features/ecommerce/data/repositories/order_repository.dart';
import 'package:patiant_app/features/ecommerce/data/repositories/product_repository.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/screens/order/history_screen.dart';
import 'package:patiant_app/features/ecommerce/presentation/screens/order/shopDetails_screen.dart';
import 'package:patiant_app/features/ecommerce/presentation/screens/order/shop_screen.dart';
import 'package:patiant_app/features/ecommerce/presentation/screens/product/productDetails_screen.dart';
import 'package:patiant_app/features/ecommerce/presentation/screens/product/product_screen.dart';
import 'package:patiant_app/features/package/data/repositories/package_repository.dart';
import 'package:patiant_app/features/package/presentation/bloc/package_bloc.dart';
import 'package:patiant_app/features/package/presentation/screens/packageDetails_screen.dart';
import 'package:patiant_app/features/package/presentation/screens/package_screen.dart';
import 'package:patiant_app/features/prescription/data/repositories/prescription_repository.dart';
import 'package:patiant_app/features/prescription/presentation/bloc/prescription_bloc.dart';
import 'package:patiant_app/features/prescription/presentation/bloc/prescription_event.dart';
import 'package:patiant_app/features/prescription/presentation/screens/createPrescription_screen.dart';
import 'package:patiant_app/features/prescription/presentation/screens/prescriptionDetails_screen.dart';
import 'package:patiant_app/features/prescription/presentation/screens/prescription_screen.dart';
import 'package:patiant_app/features/notifications/data/repositories/notification_repository.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/screens/notification_screen.dart';
import 'package:patiant_app/features/notifications/services/notification_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:patiant_app/features/profile/data/repositories/profile_repository.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/authentication/data/repositories/auth_repository.dart';
import 'package:patiant_app/features/authentication/presentation/bloc/auth_bloc.dart';
import 'features/authentication/presentation/screens/login_screen.dart';
import 'features/authentication/presentation/screens/home_screen.dart';
import 'features/prescription/presentation/screens/updatePrescription_screen.dart';
import 'features/profile/presentation/screens/profile_screen.dart';
import 'features/profile/presentation/screens/updateProfile_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('Firebase initialized successfully');
  } catch (e) {
    print('Firebase initialization failed: $e');
    // Continue without Firebase for now
  }

  SharedPreferences prefs = await SharedPreferences.getInstance();
  bool isLoggedIn = prefs.getString("token")?.isNotEmpty ??
      false; // ✅ Fix: Empty string check

  runApp(MyApp(isLoggedIn: isLoggedIn));
}

class MyApp extends StatelessWidget {
  final bool isLoggedIn;
  const MyApp({super.key, required this.isLoggedIn});

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider(create: (context) => AuthRepository()),
        RepositoryProvider(create: (context) => ProfileRepository()),
        RepositoryProvider(create: (context) => PackageRepository()),
        RepositoryProvider(create: (context) => PrescriptionRepository()),
        RepositoryProvider(create: (context) => ProductRepository()),
        RepositoryProvider(create: (context) => OrderRepository()),
        RepositoryProvider(create: (context) => NotificationRepository()),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
              create: (context) => AuthBloc(context.read<AuthRepository>())),
          BlocProvider(
              create: (context) =>
                  ProfileBloc(context.read<ProfileRepository>())),
          BlocProvider(
              create: (context) =>
                  PackageBloc(context.read<PackageRepository>())),
          BlocProvider(
              create: (context) =>
                  PrescriptionBloc(context.read<PrescriptionRepository>())),
          BlocProvider(
              create: (context) =>
                  ProductBloc(context.read<ProductRepository>())),
          BlocProvider(
            create: (context) => OrderBloc(context.read<OrderRepository>()),
          ),
          BlocProvider(
            create: (context) =>
                NotificationBloc(context.read<NotificationRepository>()),
          ),
        ],
        child: MaterialApp(
          navigatorKey: navigatorKey,
          debugShowCheckedModeBanner: false,
          title: 'Patient App',
          theme: ThemeData(primarySwatch: Colors.blue),
          initialRoute: isLoggedIn
              ? '/home'
              : '/login', // ✅ Fix: Ensure the correct initial route
          routes: {
            '/login': (context) => const LoginScreen(),
            '/home': (context) =>
                const HomeScreen(), // ✅ Fix: Explicitly define the home route
            '/profile': (context) => const ProfileScreen(),
            '/updateProfile': (context) => const UpdateProfileScreen(),
            '/packages': (context) => PackageScreen(), // ✅ Added Package Screen
            '/packageDetails': (context) => PackageDetailsScreen(
                packageId:
                    ''), // ✅ Added Package Details Screen (Needs packageId)
            '/prescriptions': (context) => PrescriptionScreen(),
            '/createPrescription': (context) => CreatePrescriptionScreen(),
            '/prescriptionDetails': (context) {
              final prescriptionId =
                  ModalRoute.of(context)!.settings.arguments as String;

              return BlocProvider(
                create: (_) =>
                    PrescriptionBloc(context.read<PrescriptionRepository>())
                      ..add(LoadPrescriptionDetails(prescriptionId)),
                child:
                    PrescriptionDetailsScreen(prescriptionId: prescriptionId),
              );
            },
            '/updatePrescription': (context) {
              final prescriptionId =
                  ModalRoute.of(context)!.settings.arguments as String;
              return BlocProvider(
                create: (context) =>
                    PrescriptionBloc(context.read<PrescriptionRepository>())
                      ..add(LoadPrescriptionDetails(prescriptionId)),
                child: UpdatePrescriptionScreen(prescriptionId: prescriptionId),
              );
            },

            '/products': (context) => const ProductScreen(), // ✅ New screen
            '/productDetails': (context) {
              final productId =
                  ModalRoute.of(context)!.settings.arguments as String;
              return ProductDetailsScreen(
                  productId: productId); // ✅ New details screen
            },
            '/shop': (context) => const ShopScreen(),
            '/shopDetails': (context) {
              final args = ModalRoute.of(context)!.settings.arguments as Map;
              return ShopDetailsScreen(
                productId: args['productId'],
                quantity: args['quantity'],
              );
            },
            '/history': (context) => const HistoryScreen(),
            '/notifications': (context) => const NotificationScreen(),
          },
        ),
      ),
    );
  }
}

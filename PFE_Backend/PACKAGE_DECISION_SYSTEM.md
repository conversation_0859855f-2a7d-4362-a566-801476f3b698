# Package Decision System

## Overview

The Package Decision System automatically determines the appropriate packaging and handling method for prescriptions based on the medicines they contain. This system helps pharmacists understand whether they need to create a package, perform picking, or both.

## Decision Types

### 1. Package (Colis)
- **Description**: Standard packaging for regular medicines
- **French**: "Colis"
- **Priority**: 1 (lowest)
- **Use Case**: Safe, common medicines that can be packaged normally

### 2. Package+Picking (Colis+Cueillette)
- **Description**: Requires both packaging and special picking procedures
- **French**: "Colis+Cueillette"
- **Priority**: 2 (medium)
- **Use Case**: Medicines that need special handling but can still be packaged

### 3. Picking (Cueillette)
- **Description**: Requires special picking procedures only
- **French**: "Cueillette"
- **Priority**: 3 (highest)
- **Use Case**: Dangerous or controlled substances requiring careful handling

## Priority System

The system follows a priority hierarchy where **Picking > Package+Picking > Package**:

- If a prescription contains any medicine requiring "Picking", the entire prescription becomes "Picking"
- If a prescription contains medicines requiring "Package+Picking" but no "Picking", it becomes "Package+Picking"
- If all medicines only require "Package", the prescription becomes "Package"

## Examples

### Example 1: Package Decision
```json
{
  "medicines": [
    { "medicine": "Dolipranne" },
    { "medicine": "Inflamil" }
  ],
  "result": {
    "decision": "Package",
    "decisionFrench": "Colis"
  }
}
```

### Example 2: Package+Picking Decision
```json
{
  "medicines": [
    { "medicine": "Dolipranne" },
    { "medicine": "Augmentin" }
  ],
  "result": {
    "decision": "Package+Picking",
    "decisionFrench": "Colis+Cueillette"
  }
}
```

### Example 3: Picking Decision
```json
{
  "medicines": [
    { "medicine": "Morphine" },
    { "medicine": "Inflamil" }
  ],
  "result": {
    "decision": "Picking",
    "decisionFrench": "Cueillette"
  }
}
```

## Medicine Categories

### Package Medicines
- Dolipranne, Inflamil, Paracétamol, Ibuprofène, Aspirine
- Prednisone, Hydrocortisone, Oméprazole
- Atorvastatine, Amlodipine, Metformine
- And many other common, safe medicines

### Package+Picking Medicines
- Augmentin, Amoxicilline (antibiotics requiring special handling)
- Warfarine, Héparine (anticoagulants)
- Insuline, Digoxine, Lithium (critical medications)
- Méthotrexate, Cyclosporine (immunosuppressants)

### Picking Medicines
- Morphine, Codéine, Tramadol, Oxycodone (opioids)
- Fentanyl, Méthadone (strong opioids)
- Diazépam, Lorazépam, Alprazolam (benzodiazepines)
- Zolpidem, Zopiclone (sleep medications)

## Integration with Notifications

When a prescription is created, the system:

1. **Analyzes all medicines** in the prescription
2. **Determines the package decision** based on priority rules
3. **Includes the decision in notifications** sent to pharmacists/patients

### Notification Messages

#### For Pharmacists (when patients add prescriptions):
```
"[Patient Name] a ajouté une nouvelle ordonnance avec [X] médicament(s): [complete medicine list]. Action recommandée: créer un [Package Decision in French]."
```

#### For Patients (when pharmacists create prescriptions):
```
"[Pharmacist Name] vient de créer une ordonnance pour vous avec [X] médicament(s): [complete medicine list]. Le pharmacien va préparer un [Package Decision in French]. Veuillez vérifier les détails et confirmer la réception."
```

**Note**: All medicines are shown in the notification (no truncation with "et X autre(s)") so pharmacists can see the complete list to make informed decisions.

## API Endpoints

### Get All Package Decisions
```http
GET /package-decisions
```

### Determine Package Decision
```http
POST /package-decisions/determine
Content-Type: application/json

{
  "medicines": [
    { "medicine": "Morphine" },
    { "medicine": "Dolipranne" }
  ]
}
```

### Update Medicine Decision (Admin)
```http
POST /package-decisions/update/MedicineName
Content-Type: application/json

{
  "decision": "Picking"
}
```

### Test Medicine Decision
```http
GET /package-decisions/test/MedicineName
```

## Configuration

The system uses a JSON configuration file located at:
```
src/core/data/packageDecisions.json
```

### Configuration Structure
```json
{
  "medicines": {
    "MedicineName": "DecisionType"
  },
  "decisionPriority": {
    "Picking": 3,
    "Package+Picking": 2,
    "Package": 1
  },
  "translations": {
    "Package": "Colis",
    "Picking": "Cueillette",
    "Package+Picking": "Colis+Cueillette"
  }
}
```

## Features

### Smart Medicine Matching
- **Exact matching**: Direct medicine name lookup
- **Case-insensitive matching**: Handles different capitalizations
- **Partial matching**: Finds medicines containing search terms
- **Normalization**: Removes dosage information and special characters

### Fallback Handling
- Unknown medicines default to "Package" decision
- Empty medicine lists default to "Package" decision
- System continues to work even if configuration file is missing

### Extensibility
- Easy to add new medicines through API or configuration
- Support for custom decision types
- Configurable priority system
- Multilingual support through translations

## Testing

Run the package decision tests:
```bash
npm test -- packageDecision.service.spec.ts
```

The test suite covers:
- All decision type scenarios
- Priority system validation
- Edge cases (empty lists, unknown medicines)
- Case-insensitive matching
- API functionality

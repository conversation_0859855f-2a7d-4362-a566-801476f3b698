# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Node.js dependencies
node_modules/
jspm_packages/

# Build output
dist/
out/
.next/
.nuxt/
.cache/
.parcel-cache/

# TypeScript cache
*.tsbuildinfo

# dotenv environment variable files (DO NOT COMMIT .env)
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# IDE and editor-specific files
.idea/
.vscode/
.vscode/*
*.sublime-workspace

# OS and misc
.DS_Store

# Yarn v2 cache
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Remove old build files
/build/

# Uploaded files
/uploads/

# Firebase service account key (SECURITY: Never commit this!)
firebase-service-account.json
*firebase-adminsdk*.json


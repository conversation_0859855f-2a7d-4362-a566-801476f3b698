# Firebase Cloud Messaging (FCM) Notification System Setup

## Overview
This notification system integrates Firebase Cloud Messaging (FCM) with your NestJS pharmacy management backend. When patients add prescriptions after OCR scanning, pharmacists receive push notifications about new prescriptions requiring package creation.

## Prerequisites
1. Firebase project with FCM enabled
2. Firebase Admin SDK service account key
3. Mobile/web app configured with FCM

## Environment Setup

### 1. Firebase Service Account
Create a Firebase service account and download the JSON key file:

1. Go to Firebase Console → Project Settings → Service Accounts
2. Click "Generate new private key"
3. Save the JSON file securely
4. Set environment variable:

```bash
export FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account","project_id":"your-project-id",...}'
```

Or place the JSON file in your project and set:
```bash
export FIREBASE_SERVICE_ACCOUNT_KEY_PATH='/path/to/serviceAccountKey.json'
```

### 2. Required Environment Variables
```bash
# Firebase Configuration
FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account",...}'
# OR
FIREBASE_SERVICE_ACCOUNT_KEY_PATH='/path/to/serviceAccountKey.json'
```

## API Endpoints

### 1. Update FCM Token
**POST** `/notifications/fcm-token`
```json
{
  "fcmToken": "user_device_fcm_token"
}
```

### 2. Get User Notifications
**GET** `/notifications?page=1&limit=20&status=UNREAD&type=PRESCRIPTION_ADDED`

### 3. Mark Notification as Read
**PUT** `/notifications/{notificationId}/read`

### 4. Send Notification (Admin/Pharmacy only)
**POST** `/notifications/send`
```json
{
  "recipientId": "user_id",
  "title": "Notification Title",
  "message": "Notification message",
  "type": "PRESCRIPTION_ADDED",
  "data": {
    "prescriptionId": "prescription_id",
    "additionalData": "value"
  }
}
```

## Notification Types
- `PRESCRIPTION_ADDED`: When patient adds new prescription
- `PACKAGE_READY`: When package is prepared
- `DELIVERY_ASSIGNED`: When delivery is assigned
- `DELIVERY_COMPLETED`: When delivery is completed
- `GENERAL`: General notifications

## Notification Status
- `UNREAD`: New notification
- `READ`: User has read the notification
- `ARCHIVED`: Archived notification

## Automatic Prescription Notifications

When a patient uploads a prescription with OCR:
1. OCR extracts medicines from the prescription image
2. Prescription is created in the database
3. System automatically sends notification to all pharmacists
4. Notification includes:
   - Patient name
   - Number of medicines
   - List of medicines (first 3 shown)
   - Prescription ID for reference

## Mobile App Integration

### 1. FCM Token Registration
When user logs in, register their FCM token:
```javascript
// Get FCM token
const token = await messaging().getToken();

// Send to backend
await fetch('/notifications/fcm-token', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + userToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ fcmToken: token })
});
```

### 2. Handle Incoming Notifications
```javascript
// Foreground notifications
messaging().onMessage(async remoteMessage => {
  console.log('Notification received:', remoteMessage);
  // Show in-app notification
});

// Background/quit state notifications
messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Background notification:', remoteMessage);
});
```

## Database Schema

### Notification Entity
```typescript
{
  id: string;
  recipientId: string;
  senderId?: string;
  title: string;
  message: string;
  type: NotificationType;
  status: NotificationStatus;
  data?: any;
  fcmSent: boolean;
  fcmMessageId?: string;
  readAt?: Date;
  archivedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### User Entity (FCM Token)
```typescript
{
  // ... existing fields
  fcm?: string; // FCM token for push notifications
}
```

## Testing

### 1. Test FCM Token Update
```bash
curl -X POST http://localhost:3000/notifications/fcm-token \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"fcmToken":"test_fcm_token"}'
```

### 2. Test Prescription Upload
Upload a prescription image through the existing prescription endpoint. If OCR extracts medicines, notifications should be sent automatically.

### 3. Test Manual Notification
```bash
curl -X POST http://localhost:3000/notifications/send \
  -H "Authorization: Bearer PHARMACY_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "recipientId": "user_id",
    "title": "Test Notification",
    "message": "This is a test notification",
    "type": "GENERAL"
  }'
```

## Troubleshooting

### 1. FCM Service Not Initialized
- Check FIREBASE_SERVICE_ACCOUNT_KEY environment variable
- Verify JSON format is valid
- Ensure Firebase project has FCM enabled

### 2. Notifications Not Received
- Verify FCM token is valid and updated
- Check notification logs in application
- Ensure device has proper FCM configuration

### 3. Permission Errors
- Verify user roles for sending notifications
- Check authentication middleware

## Security Notes
- FCM tokens should be updated on each app launch
- Implement token refresh mechanism
- Store service account key securely
- Validate notification permissions based on user roles

## Future Enhancements
1. Implement pharmacy-user relationship for targeted notifications
2. Add notification preferences and settings
3. Implement notification batching for multiple prescriptions
4. Add notification analytics and delivery tracking
5. Support for rich notifications with images and actions

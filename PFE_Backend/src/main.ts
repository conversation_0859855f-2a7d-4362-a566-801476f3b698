import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from "@nestjs/common";
import { CustomValidationPipe } from "./core/pipes/CustomI18nValidationPipe";
import { I18nService } from "nestjs-i18n";
import * as dotenv from 'dotenv';
import { join } from 'path';
import { NestExpressApplication } from '@nestjs/platform-express';

dotenv.config();
//console.log("GOOGLE CLIENT ID:", process.env.GOOGLE_CLIENT_ID); // Debugging

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const i18n = app.get(I18nService);
  //@ts-ignore
  app.useGlobalPipes(new CustomValidationPipe(i18n));
// ✅ Serve static assets
  app.useStaticAssets(join(__dirname, '..', 'uploads'), {
    prefix: '/uploads/',
  });


  // Enable CORS for Angular
  app.enableCors({
    origin: ['http://localhost:4200', 'http://127.0.0.1:4200'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true,
  });

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();

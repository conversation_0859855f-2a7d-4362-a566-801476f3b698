{"medicines": {"Inflamil": "Package", "Augmentin": "Package+Picking", "Dolipranne": "Package", "Morphine": "Picking", "Paracétamol": "Package", "Ibuprofène": "Package", "Aspirine": "Package", "Amoxicilline": "Package+Picking", "Codéine": "Picking", "Tramadol": "Picking", "Oxycodone": "Picking", "Fentanyl": "Picking", "Méthadone": "Picking", "Diazépam": "Picking", "Lorazépam": "Picking", "Alprazolam": "Picking", "Clonazépam": "Picking", "Zolpidem": "Picking", "Zopiclone": "Picking", "Warfarine": "Package+Picking", "Héparine": "Package+Picking", "Insuline": "Package+Picking", "Digoxine": "Package+Picking", "Lithium": "Package+Picking", "Phénytoïne": "Package+Picking", "Carbamazépine": "Package+Picking", "Valproate": "Package+Picking", "Méthotrexate": "Package+Picking", "Cyclosporine": "Package+Picking", "Tacrolimus": "Package+Picking", "Prednisone": "Package", "Hydrocortisone": "Package", "Dexaméthasone": "Package", "Oméprazole": "Package", "Lansoprazole": "Package", "Pantoprazole": "Package", "Atorvastatine": "Package", "Simvastatine": "Package", "Rosuvastatine": "Package", "Amlodipine": "Package", "Lisinopril": "Package", "Losartan": "Package", "Métoprolol": "Package", "Atenolol": "Package", "Furosémide": "Package", "Hydrochlorothiazide": "Package", "Spironolactone": "Package", "Metformine": "Package", "Gliclazide": "Package", "Glibenclamide": "Package", "Lévothyroxine": "Package", "Propylthiouracile": "Package+Picking", "Méthimazole": "Package+Picking", "Salbutamol": "Package", "Fluticasone": "Package", "Budesonide": "Package", "Théophylline": "Package+Picking", "Ciprofloxacine": "Package", "Levofloxacine": "Package", "Azithromycine": "Package", "Clarithromycine": "Package", "Doxycycline": "Package", "Céphalexine": "Package", "Clindamycine": "Package", "Vancomycine": "Package+Picking", "Gentamicine": "Package+Picking", "Fluconazole": "Package", "Itraconazole": "Package+Picking", "Aciclovir": "Package", "Valaciclovir": "Package", "Oseltamivir": "Package", "Ribavirine": "Package+Picking", "Chloroquine": "Package+Picking", "Hydroxychloroquine": "Package+Picking", "Quinine": "Package+Picking", "Artéméther": "Package+Picking", "Lumefantrine": "Package+Picking"}, "decisionPriority": {"Picking": 3, "Package+Picking": 2, "Package": 1}, "translations": {"Package": "<PERSON><PERSON>", "Picking": "Cueillette", "Package+Picking": "Colis+Cueillette"}}
"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserErrors = void 0;
var DomainError_1 = require("./DomainError");
// eslint-disable-next-line @typescript-eslint/no-namespace
var UserErrors;
(function (UserErrors) {
    var PasswordInvalid = /** @class */ (function (_super) {
        __extends(PasswordInvalid, _super);
        function PasswordInvalid() {
            return _super.call(this, "PASSWORD_INVALID") || this;
        }
        return PasswordInvalid;
    }(DomainError_1.DomainError));
    UserErrors.PasswordInvalid = PasswordInvalid;
    var PhoneNumberAlreadyUsed = /** @class */ (function (_super) {
        __extends(PhoneNumberAlreadyUsed, _super);
        function PhoneNumberAlreadyUsed() {
            return _super.call(this, "PHONE_NUMBER_ALREADY_USED") || this;
        }
        return PhoneNumberAlreadyUsed;
    }(DomainError_1.DomainError));
    UserErrors.PhoneNumberAlreadyUsed = PhoneNumberAlreadyUsed;
    var EmailAlreadyUsed = /** @class */ (function (_super) {
        __extends(EmailAlreadyUsed, _super);
        function EmailAlreadyUsed() {
            return _super.call(this, "EMAIL_ALREADY_USED") || this;
        }
        return EmailAlreadyUsed;
    }(DomainError_1.DomainError));
    UserErrors.EmailAlreadyUsed = EmailAlreadyUsed;
    var ProfileAlreadyUsed = /** @class */ (function (_super) {
        __extends(ProfileAlreadyUsed, _super);
        function ProfileAlreadyUsed() {
            return _super.call(this, "PROFILE_ALREADY_USED") || this;
        }
        return ProfileAlreadyUsed;
    }(DomainError_1.DomainError));
    UserErrors.ProfileAlreadyUsed = ProfileAlreadyUsed;
    var PhoneNumberNotVerified = /** @class */ (function (_super) {
        __extends(PhoneNumberNotVerified, _super);
        function PhoneNumberNotVerified() {
            return _super.call(this, "PHONE_NUMBER_NOT_VERIFIED") || this;
        }
        return PhoneNumberNotVerified;
    }(DomainError_1.DomainError));
    UserErrors.PhoneNumberNotVerified = PhoneNumberNotVerified;
    var PhoneNumberDoesNotExist = /** @class */ (function (_super) {
        __extends(PhoneNumberDoesNotExist, _super);
        function PhoneNumberDoesNotExist() {
            return _super.call(this, "PHONE_NUMBER_DOES_NOT_EXIST") || this;
        }
        return PhoneNumberDoesNotExist;
    }(DomainError_1.DomainError));
    UserErrors.PhoneNumberDoesNotExist = PhoneNumberDoesNotExist;
    var UserNotFound = /** @class */ (function (_super) {
        __extends(UserNotFound, _super);
        function UserNotFound() {
            return _super.call(this, "USER_NOT_FOUND") || this;
        }
        return UserNotFound;
    }(DomainError_1.DomainError));
    UserErrors.UserNotFound = UserNotFound;
    var UserNotActive = /** @class */ (function (_super) {
        __extends(UserNotActive, _super);
        function UserNotActive() {
            return _super.call(this, "USER_NOT_ACTIVE") || this;
        }
        return UserNotActive;
    }(DomainError_1.DomainError));
    UserErrors.UserNotActive = UserNotActive;
    var InvalidEmailFormat = /** @class */ (function (_super) {
        __extends(InvalidEmailFormat, _super);
        function InvalidEmailFormat() {
            return _super.call(this, "INVALID_EMAIL_FORMAT") || this;
        }
        return InvalidEmailFormat;
    }(DomainError_1.DomainError));
    UserErrors.InvalidEmailFormat = InvalidEmailFormat;
    var AuthenticationFailed = /** @class */ (function (_super) {
        __extends(AuthenticationFailed, _super);
        function AuthenticationFailed() {
            return _super.call(this, "AUTHENTICATION_FAILED") || this;
        }
        return AuthenticationFailed;
    }(DomainError_1.DomainError));
    UserErrors.AuthenticationFailed = AuthenticationFailed;
    var AccountNotValidated = /** @class */ (function (_super) {
        __extends(AccountNotValidated, _super);
        function AccountNotValidated() {
            return _super.call(this, "ACCOUNT_NOT_VALIDATED") || this;
        }
        return AccountNotValidated;
    }(DomainError_1.DomainError));
    UserErrors.AccountNotValidated = AccountNotValidated;
    var RoleInvalid = /** @class */ (function (_super) {
        __extends(RoleInvalid, _super);
        function RoleInvalid() {
            return _super.call(this, "ROLE_INVALID") || this;
        }
        return RoleInvalid;
    }(DomainError_1.DomainError));
    UserErrors.RoleInvalid = RoleInvalid;
    var WrongCredentials = /** @class */ (function (_super) {
        __extends(WrongCredentials, _super);
        function WrongCredentials() {
            return _super.call(this, "WRONG_CREDENTIALS") || this;
        }
        return WrongCredentials;
    }(DomainError_1.DomainError));
    UserErrors.WrongCredentials = WrongCredentials;
    var InvalidCostumer = /** @class */ (function (_super) {
        __extends(InvalidCostumer, _super);
        function InvalidCostumer() {
            return _super.call(this, "COSTUMER_INVALID") || this;
        }
        return InvalidCostumer;
    }(DomainError_1.DomainError));
    UserErrors.InvalidCostumer = InvalidCostumer;
    var InvalidResetCode = /** @class */ (function () {
        function InvalidResetCode() {
        }
        return InvalidResetCode;
    }());
    UserErrors.InvalidResetCode = InvalidResetCode;
    var ResetCodeExpired = /** @class */ (function () {
        function ResetCodeExpired() {
        }
        return ResetCodeExpired;
    }());
    UserErrors.ResetCodeExpired = ResetCodeExpired;
    var PasswordsDoNotMatch = /** @class */ (function (_super) {
        __extends(PasswordsDoNotMatch, _super);
        function PasswordsDoNotMatch() {
            return _super !== null && _super.apply(this, arguments) || this;
        }
        return PasswordsDoNotMatch;
    }(Error));
    UserErrors.PasswordsDoNotMatch = PasswordsDoNotMatch;
})(UserErrors || (exports.UserErrors = UserErrors = {}));

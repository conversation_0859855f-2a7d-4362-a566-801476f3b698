"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Email = void 0;
var UserErrors_1 = require("../errors/UserErrors");
var Email = /** @class */ (function () {
    function Email(email) {
        var emailValidation = this.validEmail(email);
        if (!emailValidation) {
            throw new UserErrors_1.UserErrors.InvalidEmailFormat();
        }
        this.value = email;
    }
    Email.prototype.validEmail = function (email) {
        var regexp = new RegExp(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/);
        return regexp.test(email);
    };
    return Email;
}());
exports.Email = Email;

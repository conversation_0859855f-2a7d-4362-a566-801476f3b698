import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
    private transporter;

    constructor() {
        this.transporter = nodemailer.createTransport({
            host: process.env.EMAIL_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.EMAIL_PORT) || 587,
            secure: false,
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASS,
            },
        });
    }


    async sendPasswordResetEmail(email: string, resetCode: string, expiryMinutes: number): Promise<void> {
        // ✅ Generate deep link
        const deepLink = `myapp://reset-password?email=${email}&code=${resetCode}`;
        const webFallback = `${process.env.FRONTEND_URL}/reset-password?email=${email}&code=${resetCode}`;

        const mailOptions = {
            from: process.env.EMAIL_FROM || '"Support Team" <<EMAIL>>',
            to: email,
            subject: 'Password Reset Request',
            html: `
        <h3>Password Reset Request</h3>
        <p>We received a request to reset your password.</p>
        <p>Your verification code: <strong>${resetCode}</strong></p>
        <p>This code will expire in <strong>${expiryMinutes} minutes</strong>.</p>
        <p>Click the link below to verify your code and reset your password:</p>
        
        <!-- 🔗 Instead of a button, use a simple hyperlink -->
        <a href="${deepLink}" style="color: #007BFF; font-size: 16px;">Click Here to Reset Password</a>

        <p>If the above link does not work, copy and paste the following link into your browser:</p>
        <p><a href="${webFallback}">${webFallback}</a></p>

        <p>If you did not request this, please ignore this email.</p>
      `,
        };

        await this.transporter.sendMail(mailOptions);
    }





// ✅ **New Method to Send 2FA Code**
    async sendTwoFactorCode(email: string, twoFactorCode: string): Promise<void> {
        const mailOptions = {
            from: process.env.EMAIL_FROM || '"Support Team" <<EMAIL>>',
            to: email,
            subject: 'Your Two-Factor Authentication Code',
            html: `
        <h3>Two-Factor Authentication Code</h3>
        <p>To complete your login, please enter the following code:</p>
        <h2>${twoFactorCode}</h2>
        <p>This code is valid for <strong>10 minutes</strong>.</p>
        <p>If you did not request this, please ignore this email.</p>
      `,
        };

        console.log(`📧 Sending 2FA Code to: ${email}`);
        await this.transporter.sendMail(mailOptions);
    }



    // ✅ New method to send patient account creation email
    async sendPatientAccountEmail(email: string, firstName: string, defaultPassword: string): Promise<void> {
        const mailOptions = {
            from: process.env.EMAIL_FROM || '"Support Team" <<EMAIL>>',
            to: email,
            subject: 'Your Patient Account is Ready',
            html: `
        <h3>Welcome, ${firstName}!</h3>
        <p>Your patient account has been created successfully. Here are your login details:</p>
        <ul>
            <li><strong>Email:</strong> ${email}</li>
            <li><strong>Temporary Password:</strong> ${defaultPassword}</li>
        </ul>
        <p>💡 Please log in and change your password as soon as possible.</p>
        <p><a href="${process.env.FRONTEND_URL}/login" style="color: #007BFF;">Login Now</a></p>
        <p>If you did not request this account, please contact support immediately.</p>
        `,
        };

        console.log(`📧 Sending patient account email to: ${email}`);
        await this.transporter.sendMail(mailOptions);
    }

}

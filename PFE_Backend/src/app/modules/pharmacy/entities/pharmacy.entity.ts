import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { PharmacyRole } from '../../../../core/enums/pharmacy/PharmacyRole';

@Schema({ timestamps: true, collection: 'pharmacy_profiles' })
export class Pharmacy extends Document {
    @Prop({ required: true, unique: true })
    id: string;

    @Prop({ required: true })
    pharmacyName: string;

    @Prop({ required: true })
    firstName: string;

    @Prop({ required: true })
    lastName: string;

    @Prop({ required: true })
    pharmacistPhone: string;

    @Prop({ required: true })
    pharmacistEmail: string;

    @Prop({
        type: {
            address: { type: String },
            street: { type: String },
            city: { type: String },
            province: { type: String },
            postalCode: { type: String },
            country: { type: String },
            long: { type: Number },
            lat: { type: Number },
        },
        required: true,
    })
    address: {
        address: string;
        street?: string;
        city?: string;
        province?: string;
        postalCode?: string;
        country?: string;
        long?: number;
        lat?: number;
    };

    @Prop({ type: String, enum: PharmacyRole })
    title?: PharmacyRole;

    @Prop()
    banner: string;

    @Prop()
    plan: string;

    @Prop()
    selectedCost: string;

    @Prop({
        type: {
            paymentMethodId: { type: String },
            cardName: { type: String },
            token: { type: String },
            billingAddress: {
                address: { type: String },
                street: { type: String },
                city: { type: String },
                province: { type: String },
                postalCode: { type: String },
                country: { type: String },
                long: { type: Number },
                lat: { type: Number },
            },
            couponCode: { type: String },
        },
    })
    cardInfo?: {
        paymentMethodId?: string;
        cardName?: string | null;
        token?: string | null;
        billingAddress?: any;
        couponCode?: string | null;
    };

    @Prop({ type: Object })
    paymentToken?: any;


    @Prop({ type: [{ type: Object }] })
    paymentMethods?: any[];

    @Prop()
    website?: string;

    @Prop({
        type: [
            {
                type: { type: String },
                date: { type: String },
            },
        ],
    })
    exceptionalClosure?: {
        type: string;
        date: string;
    }[];

    @Prop()
    description?: string;

    @Prop()
    notification?: boolean;

    @Prop()
    startTime?: string;

    @Prop()
    endTime?: string;

    @Prop({ required: true })
    firstLogin: boolean;

    @Prop()
    startDate?: string;
}

export const PharmacySchema = SchemaFactory.createForClass(Pharmacy);

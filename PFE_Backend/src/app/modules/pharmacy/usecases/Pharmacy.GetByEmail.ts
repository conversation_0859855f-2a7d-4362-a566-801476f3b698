// usecases/pharmacy.getByEmail.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { PharmacyRepository } from '../repositories/pharmacy.repository';

@Injectable()
export class PharmacyGetByEmail {
    constructor(private readonly pharmacyRepo: PharmacyRepository) {}

    async execute(email: string) {
        const pharmacy = await this.pharmacyRepo.findByEmail(email);
        if (!pharmacy) {
            throw new NotFoundException('Pharmacie introuvable avec cet email');
        }
        return pharmacy;
    }
}

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Pharmacy } from '../entities/pharmacy.entity';

@Injectable()
export class PharmacyRepository {
    constructor(
        @InjectModel('Pharmacy') private readonly pharmacyModel: Model<Pharmacy>,
    ) {}

    async findById(pharmacyId: string): Promise<any> {
        return this.pharmacyModel.findOne({ id: pharmacyId }).lean();
    }

    async findByEmail(email: string): Promise<Pharmacy | null> {
        return this.pharmacyModel.findOne({ pharmacistEmail: email }).exec();
    }


}

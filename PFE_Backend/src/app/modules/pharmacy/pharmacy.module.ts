import {forwardRef, Module} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Pharmacy, PharmacySchema } from './entities/pharmacy.entity';
import {JwtModule} from "@nestjs/jwt";
import {UserModule} from "../user/user.module";
import {PharmacyController} from "./controllers/pharmacy.controller";
import {PharmacyRepository} from "./repositories/pharmacy.repository";
import {GetPharmacyById} from "./usecases/getPharmacyById";
import {PharmacyGetByEmail} from "./usecases/Pharmacy.GetByEmail";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: 'Pharmacy', schema: PharmacySchema },
        ]),
        JwtModule.register({
            secret: 'reallysecurekey',
            signOptions: { expiresIn: '1y' },
        }),
        forwardRef(() => UserModule),
    ],
    controllers: [PharmacyController],
    providers: [PharmacyRepository, GetPharmacyById,PharmacyGetByEmail],
    exports: [MongooseModule, PharmacyRepository, PharmacyGetByEmail], // export for use in other modules if needed
})
export class PharmacyModule {}

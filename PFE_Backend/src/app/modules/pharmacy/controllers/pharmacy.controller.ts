import {
    <PERSON>,
    Get,
    Param,
    Req,
    Res,
    UseGuards,
} from '@nestjs/common';
import { GetPharmacyById } from '../usecases/getPharmacyById';
import { AuthenticationMiddleware } from '../../../middlewares/authenticationMiddleware';
import { AuthenticatedRequest } from '../../../config/authenticatedRequest';
import { Response } from 'express';
import { I18n, I18nContext } from 'nestjs-i18n';
import {PharmacyGetByEmail} from "../usecases/Pharmacy.GetByEmail";

@Controller('/pharmacy')
export class PharmacyController {
    constructor(private readonly getPharmacyById: GetPharmacyById,
                private readonly getPharmacyByEmail:PharmacyGetByEmail) {}

    @Get('/getPharmacyInfo/:pharmacyId')
    @UseGuards(AuthenticationMiddleware)
    async getPharmacyInfo(
        @Req() req: AuthenticatedRequest,
        @Res() res: Response,
        @I18n() i18n: I18nContext,
        @Param('pharmacyId') pharmacyId: string,
    ) {
        try {
            const pharmacy = await this.getPharmacyById.execute(pharmacyId);

            if (!pharmacy) {
                return res.status(404).json({
                    success: false,
                    message: i18n.translate('errors.PHARMACY_NOT_FOUND'),
                });
            }

            return res.status(200).json({
                success: true,
                message: i18n.translate('success.PHARMACY_FETCHED_SUCCESSFULLY'),
                data: pharmacy,
            });
        } catch (error) {
            console.error(error);
            return res.status(500).json({
                success: false,
                message: i18n.translate('errors.INTERNAL_SERVER_ERROR'),
            });
        }
    }



    @Get('/getPharmacyInfoByEmail/:pharmacistEmail')
    @UseGuards(AuthenticationMiddleware)
    async getPharmacyInfoByEmail(
        @Req() req: AuthenticatedRequest,
        @Res() res: Response,
        @I18n() i18n: I18nContext,
        @Param('pharmacistEmail') pharmacistEmail: string,
    ) {
        try {
            const pharmacy = await this.getPharmacyByEmail.execute(pharmacistEmail);

            if (!pharmacy) {
                return res.status(404).json({
                    success: false,
                    message: i18n.translate('errors.PHARMACY_NOT_FOUND'),
                });
            }

            return res.status(200).json({
                success: true,
                message: i18n.translate('success.PHARMACY_FETCHED_SUCCESSFULLY'),
                data: pharmacy,
            });
        } catch (error) {
            console.error(error);
            return res.status(500).json({
                success: false,
                message: i18n.translate('errors.INTERNAL_SERVER_ERROR'),
            });
        }
    }
}

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Prescription } from '../entities/prescription.entity';
import { v4 as uuidv4 } from 'uuid';


@Injectable()
export class PrescriptionRepository {
    constructor(
        @InjectModel(Prescription.name) private readonly model: Model<Prescription>,
    ) {}


    async create(prescription: Partial<Prescription>): Promise<Prescription> {
        prescription.id = uuidv4(); // 🔄 Génère un id unique
        return await this.model.create(prescription);
    }


    async findById(id: string): Promise<Prescription | null> {
        return await this.model.findOne({ id });
    }

    async findAll(): Promise<Prescription[]> {
        return await this.model.find().exec();
    }

    async update(id: string, updates: Partial<Prescription>): Promise<Prescription | null> {
        return await this.model.findOneAndUpdate({ id }, updates, { new: true });
    }

    async delete(id: string): Promise<void> {
        await this.model.deleteOne({ id });
    }
    async findByPatientId(patientId: string): Promise<Prescription[]> {
        return await this.model.find({ patientId }).exec();
    }

    async findByPatientIdAndDateRange(patientId: string, start: Date, end: Date): Promise<Prescription[]> {
        return this.model.find({
            patientId,
            date: { $gte: start, $lte: end },
        }).exec();
    }
    async findByFilters(filters: any): Promise<Prescription[]> {
        return this.model.find(filters).exec();
    }



}

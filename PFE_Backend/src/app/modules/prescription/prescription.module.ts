import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from '../user/user.module';
import { Prescription, PrescriptionSchema } from './entities/prescription.entity';
import { PrescriptionRepository } from './repositories/prescription.repository';
import { PrescriptionController } from './controllers/prescription.controller';
import { PrescriptionUploadFile } from './usecases/prescription.uploadFile';
import { PatientModule } from '../patient/patient.module';
import {PrescriptionCreate} from "./usecases/prescription.create";
import {PrescriptionUpdate} from "./usecases/prescription.update";
import {PrescriptionGetAll} from "./usecases/prescription.getAll";
import {PrescriptionGetById} from "./usecases/prescription.getById";
import {PrescriptionDelete} from "./usecases/prescription.delete";
import {OcrModule} from "../ocr/ocr.module";
import {PrescriptionFilter} from "./usecases/web/prescription.filter";
import {PrescriptionCreateForPatient} from "./usecases/web/PrescriptionCreateForPatient";
import { PharmacyModule } from "../pharmacy/pharmacy.module";
import { PackageDecisionService } from "./services/packageDecision.service";
import { PackageDecisionController } from "./controllers/packageDecision.controller";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: 'Prescription', schema: PrescriptionSchema },
        ]),
        JwtModule.register({
            secret: 'reallysecurekey',
            signOptions: { expiresIn: '1y' },
        }),
        forwardRef(() => UserModule),
        forwardRef(() => PatientModule),
        forwardRef(() => PharmacyModule),
        OcrModule,
    ],
    controllers: [PrescriptionController, PackageDecisionController],
    providers: [
        PrescriptionRepository,
        PrescriptionUploadFile,
        PrescriptionCreate,
        PrescriptionUpdate,
        PrescriptionGetAll,
        PrescriptionGetById,
        PrescriptionDelete,
        PrescriptionFilter,
        PrescriptionCreateForPatient,
        PackageDecisionService
    ],
    exports: [MongooseModule, PackageDecisionService],
})
export class PrescriptionModule {}
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { PrescriptionStatus } from '../../../../core/enums/prescription/PrescriptionStatus';
import {v4 as uuidv4} from "uuid";

@Schema({ timestamps: true })
export class Prescription extends Document {
    @Prop({ required: true, unique: true, default: uuidv4 })
    id: string;

    @Prop({ required: true })
    patientId: string;

    @Prop({ required: true })
    pharmacyId: string;

    @Prop({ required: true, type: [String] }) // ✅ Array of file paths
    storagePath: string[];

    @Prop({ required: true })
    issueDate: Date;

    @Prop({ required: true })
    date: Date;

    @Prop({ required: true })
    prescriptionStatus: PrescriptionStatus;

    @Prop({ default: false })
    approved: boolean;

    @Prop()
    note?: string;

    @Prop({ required: true })
    uploadedAt: string;

    @Prop()
    reviewedAt?: string;

    @Prop()
    reviewedBy?: string;

    @Prop()
    rejectionReason?: string;

    @Prop()
    expiresAt?: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
// ✅ New property: structured medicines list
    @Prop({ type: [{
            medicine: String,
            dosage: String,
            frequency: String,
            duration: String,
            timing: String
        }], _id: false })  // 👈 Disable _id in subdocuments
    medicines?: {
        medicine: string;
        dosage?: string;
        frequency?: string;
        duration?: string;
        timing?: string;
    }[];
}

export const PrescriptionSchema = SchemaFactory.createForClass(Prescription);
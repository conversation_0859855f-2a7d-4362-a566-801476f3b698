// controllers/prescription.controller.ts
import {
    Controller,
    Post,
    UploadedFile,
    UseGuards,
    UseInterceptors,
    Req,
    Res,
    Body, Patch, Param, Get, Put, Delete,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { Response } from 'express';
import * as path from 'path';
import {PrescriptionUploadFile} from "../usecases/prescription.uploadFile";
import {AuthenticationMiddleware} from "../../../middlewares/authenticationMiddleware";
import {PrescriptionUploadFileCommand} from "../commands/prescription.uploadFileCommand";
import {PatientRepository} from "../../patient/repositories/patient.repository";
import {PrescriptionCreateCommand} from "../commands/prescription.createCommand";
import {PrescriptionCreate} from "../usecases/prescription.create";
import {PrescriptionUpdateCommand} from "../commands/prescription.updateCommand";
import {PrescriptionUpdate} from "../usecases/prescription.update";
import {PrescriptionGetAllCommand} from "../commands/prescription.getAllCommand";
import {PrescriptionGetAll} from "../usecases/prescription.getAll";
import {PrescriptionGetByIdCommand} from "../commands/prescription.getByIdCommand";
import {PrescriptionGetById} from "../usecases/prescription.getById";
import { Query as NestQuery } from '@nestjs/common';
import {PrescriptionDelete} from "../usecases/prescription.delete";
import {PrescriptionFilterCommand} from "../commands/web/prescription.filterCommand";
import {PrescriptionFilter} from "../usecases/web/prescription.filter";
import {PrescriptionCreateForPatientCommand} from "../commands/web/PrescriptionCreateForPatientCommand";
import {PrescriptionCreateForPatient} from "../usecases/web/PrescriptionCreateForPatient";



@Controller('prescriptions')
export class PrescriptionController {
    constructor(private readonly prescriptionUploadFile: PrescriptionUploadFile,
                private readonly patientRepository: PatientRepository,
                private readonly prescriptionCreate:PrescriptionCreate,
                private readonly prescriptionUpdate:PrescriptionUpdate,
                private readonly prescriptionGetAll:PrescriptionGetAll,
                private readonly prescriptionGetById:PrescriptionGetById,
                private readonly deletePrescriptionUseCase:PrescriptionDelete,
                private readonly prescriptionFilter:PrescriptionFilter,
                private readonly prescriptionCreateForPatient:PrescriptionCreateForPatient


    ) {}

    @Post('/uploadFile')
    @UseGuards(AuthenticationMiddleware)
    @UseInterceptors(
        FileInterceptor('file', {
            storage: diskStorage({
                destination: './uploads/prescriptions',
                filename: (req, file, cb) => {
                    const uniqueName = `${Date.now()}-${file.originalname}`;
                    cb(null, uniqueName);
                },
            }),
        }),
    )
    async uploadFileOnly(
        @UploadedFile() file: Express.Multer.File,
        @Res() res: Response,
    ) {
        if (!file) {
            return res.status(400).json({ message: 'No file uploaded' });
        }

        const command = new PrescriptionUploadFileCommand();
        command.filePath = `/uploads/prescriptions/${file.filename}`;
        command.fileSize = file.size;

        const result = await this.prescriptionUploadFile.execute(command);

        return res.status(200).json({
            success: true,
            message: 'File uploaded successfully.',
            ...result,
        });
    }
    @Post('/create')
    @UseGuards(AuthenticationMiddleware)
    async createPrescription(
        @Req() req: any,
        @Body() body: Omit<PrescriptionCreateCommand, 'patientId' | 'pharmacyId'>,
        @Res() res: Response,
    ) {
        const userId = req.identity.id;
        const patient = await this.patientRepository.findOneById(userId);

        if (!patient || !patient.pharmacyId) {
            return res.status(400).json({ message: 'Pharmacy ID not found for patient' });
        }

        const command = new PrescriptionCreateCommand();
        command.patientId = userId;
        command.pharmacyId = patient.pharmacyId;
        command.issueDate = body.issueDate;
        command.storagePaths = body.storagePaths;
        command.note = body.note;
        command.medicines = body.medicines;


        const prescription = await this.prescriptionCreate.execute(command);

        return res.status(201).json({
            success: true,
            message: `Prescription created successfully.`,
            prescription,
        });
    }



    @Put('/update/:id')
    @UseGuards(AuthenticationMiddleware)
    async updatePrescription(
        @Param('id') id: string,
        @Body() body: Omit<PrescriptionUpdateCommand, 'id'>,
        @Res() res: Response,
    ) {
        const command = new PrescriptionUpdateCommand();
        command.id = id;
        command.note = body.note;
        command.storagePaths = body.storagePaths;
        command.medicines = body.medicines;



        try {
            const updated = await this.prescriptionUpdate.execute(command);
            return res.status(200).json({
                success: true,
                message: 'Prescription updated successfully.',
                updated,
            });
        } catch (error) {
            return res.status(500).json({ success: false, message: error.message });
        }
    }
    @Get('/getAll')
    @UseGuards(AuthenticationMiddleware)
    async getFilteredPrescriptions(
        @Req() req: any,
        @Res() res: Response,
        @NestQuery('date') date?: string,
        @NestQuery('note') note?: string,
    ) {
        const command = new PrescriptionGetAllCommand();
        command.patientId = req.identity.id;
        command.date = date;
        command.note = note;

        const result = await this.prescriptionGetAll.execute(command);

        return res.status(200).json({
            success: true,
            total: result.total,
            prescriptions: result.prescriptions,
        });
    }


    @Get('/get/:id')
    @UseGuards(AuthenticationMiddleware)
    async getMyPrescriptionDetails(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
        const command = new PrescriptionGetByIdCommand();
        command.prescriptionId = id;
        command.patientId = req.identity.id;

        const prescription = await this.prescriptionGetById.execute(command);

        return res.status(200).json({
            success: true,
            prescription,
        });
    }


    @Delete('/delete/:id')
    @UseGuards(AuthenticationMiddleware)  // Use AuthenticationMiddleware to authenticate the request
    async deletePrescription(
        @Param('id') id: string,  // Prescription ID from URL
        @Req() req: any,  // Request to access authenticated user info
        @Res() res: Response,  // Response object to send the response
    ) {
        try {
            const patientId = req.identity.id;  // Extract the user ID from the authenticated request
            await this.deletePrescriptionUseCase.execute(id, patientId);  // Call the use case to delete the prescription

            return res.status(200).json({
                success: true,
                message: 'Prescription deleted successfully',
            });
        } catch (error) {
            return res.status(error.status || 500).json({
                success: false,
                message: error.response || 'Internal Server Error',
            });
        }
    }
    ///////////////////////////////////////////ADMIN///////////////////////////////////////////////////////


    @Get('/filter')
    @UseGuards(AuthenticationMiddleware)
    async filterPrescriptions(
        @Req() req: any,
        @Res() res: Response,
        @NestQuery() query: PrescriptionFilterCommand
    ) {
        try {
            const result = await this.prescriptionFilter.execute(query, req.identity);
            return res.status(200).json({
                success: true,
                total: result.total,
                prescriptions: result.prescriptions,
            });
        } catch (error) {
            console.error(error);
            return res.status(error.status || 500).json({
                success: false,
                message: error.message || 'Internal Server Error',
            });
        }
    }

    @Post('/createByAdmin')
    @UseGuards(AuthenticationMiddleware) // Optionnel, si nécessaire
    async createPrescriptionByAdmin(
        @Body() body: PrescriptionCreateForPatientCommand,
        @Res() res: Response
    ) {
        const { patientId, pharmacyId } = body;

        if (!patientId || !pharmacyId) {
            return res.status(400).json({
                message: 'Patient ID and Pharmacy ID are required',
            });
        }

        const command = new PrescriptionCreateForPatientCommand();
        command.patientId = patientId;
        command.pharmacyId = pharmacyId;
        command.issueDate = body.issueDate;
        command.storagePaths = body.storagePaths;
        command.note = body.note;
        command.medicines = body.medicines;

        const prescription = await this.prescriptionCreateForPatient.execute(command);

        return res.status(201).json({
            success: true,
            message: 'Prescription created successfully for selected patient.',
            prescription,
        });
    }





}
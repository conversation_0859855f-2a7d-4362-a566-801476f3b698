import { <PERSON>, Get, Post, Body, Param, HttpStatus, Res, UseGuards, Logger } from '@nestjs/common';
import { Response } from 'express';
import { PackageDecisionService } from '../services/packageDecision.service';
import { AuthenticationMiddleware } from '../../../middlewares/authenticationMiddleware';

@Controller('package-decisions')
@UseGuards(AuthenticationMiddleware)
export class PackageDecisionController {
    private readonly logger = new Logger(PackageDecisionController.name);

    constructor(
        private readonly packageDecisionService: PackageDecisionService
    ) {}

    /**
     * Get all medicine package decisions
     */
    @Get()
    async getAllDecisions(@Res() res: Response) {
        try {
            const decisions = this.packageDecisionService.getAllMedicineDecisions();
            const translations = this.packageDecisionService.getDecisionTranslations();

            return res.status(HttpStatus.OK).json({
                success: true,
                message: 'Package decisions retrieved successfully',
                data: {
                    medicines: decisions,
                    translations
                }
            });
        } catch (error) {
            this.logger.error(`❌ Failed to get package decisions: ${error.message}`);
            return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                success: false,
                message: 'Failed to retrieve package decisions',
                error: error.message
            });
        }
    }

    /**
     * Get package decision for specific medicines
     */
    @Post('determine')
    async determinePackageDecision(
        @Body() body: { medicines: Array<{ medicine: string; [key: string]: any }> },
        @Res() res: Response
    ) {
        try {
            if (!body.medicines || !Array.isArray(body.medicines)) {
                return res.status(HttpStatus.BAD_REQUEST).json({
                    success: false,
                    message: 'Medicines array is required'
                });
            }

            const decision = this.packageDecisionService.determinePackageDecision(body.medicines);

            return res.status(HttpStatus.OK).json({
                success: true,
                message: 'Package decision determined successfully',
                data: decision
            });
        } catch (error) {
            this.logger.error(`❌ Failed to determine package decision: ${error.message}`);
            return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                success: false,
                message: 'Failed to determine package decision',
                error: error.message
            });
        }
    }

    /**
     * Update medicine decision (Admin only)
     */
    @Post('update/:medicineName')
    async updateMedicineDecision(
        @Param('medicineName') medicineName: string,
        @Body() body: { decision: string },
        @Res() res: Response
    ) {
        try {
            if (!body.decision) {
                return res.status(HttpStatus.BAD_REQUEST).json({
                    success: false,
                    message: 'Decision is required'
                });
            }

            if (!['Package', 'Picking', 'Package+Picking'].includes(body.decision)) {
                return res.status(HttpStatus.BAD_REQUEST).json({
                    success: false,
                    message: 'Invalid decision. Must be one of: Package, Picking, Package+Picking'
                });
            }

            this.packageDecisionService.updateMedicineDecision(medicineName, body.decision);

            return res.status(HttpStatus.OK).json({
                success: true,
                message: `Medicine decision updated successfully: ${medicineName} -> ${body.decision}`
            });
        } catch (error) {
            this.logger.error(`❌ Failed to update medicine decision: ${error.message}`);
            return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                success: false,
                message: 'Failed to update medicine decision',
                error: error.message
            });
        }
    }

    /**
     * Test endpoint to see how specific medicines would be processed
     */
    @Get('test/:medicineName')
    async testMedicineDecision(
        @Param('medicineName') medicineName: string,
        @Res() res: Response
    ) {
        try {
            const testMedicines = [{ medicine: medicineName }];
            const decision = this.packageDecisionService.determinePackageDecision(testMedicines);

            return res.status(HttpStatus.OK).json({
                success: true,
                message: `Test result for medicine: ${medicineName}`,
                data: decision
            });
        } catch (error) {
            this.logger.error(`❌ Failed to test medicine decision: ${error.message}`);
            return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                success: false,
                message: 'Failed to test medicine decision',
                error: error.message
            });
        }
    }
}

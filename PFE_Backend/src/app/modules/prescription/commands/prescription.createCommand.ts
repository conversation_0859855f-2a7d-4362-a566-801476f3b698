import { Is<PERSON>rray, IsDateString, IsNot<PERSON>mpty, <PERSON><PERSON><PERSON>ber, IsOptional } from 'class-validator';

export class PrescriptionCreateCommand {
    @IsNotEmpty()
    patientId!: string;

    @IsNotEmpty()
    pharmacyId!: string;

    @IsOptional()
    @IsDateString({}, { message: 'validation.ISSUE_DATE_INVALID' })
    issueDate?: string; // ✅ Optional

    @IsArray()
    storagePaths!: string[];

    @IsOptional()
    note?: string;

    @IsOptional()
    @IsArray()
    medicines?: {
        medicine: string;
        dosage?: string;
        frequency?: string;
        duration?: string;
        timing?: string;
    }[];
}
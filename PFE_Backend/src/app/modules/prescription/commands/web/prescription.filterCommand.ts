import { IsOptional, IsEnum, IsBooleanString } from 'class-validator';
import {PrescriptionStatus} from "../../../../../core/enums/prescription/PrescriptionStatus";

export class PrescriptionFilterCommand {
    @IsOptional()
    date?: string; // Format: YYYY-MM-DD

    @IsOptional()
    @IsEnum(PrescriptionStatus)
    status?: PrescriptionStatus;

    @IsOptional()
    @IsBooleanString()
    hasHousehold?: string; // 'true', 'false', or undefined
}

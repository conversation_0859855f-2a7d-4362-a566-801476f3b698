import { IsArray, IsDateString, <PERSON>NotEmpty, IsOptional } from 'class-validator';

export class PrescriptionCreateForPatientCommand {
    @IsNotEmpty()
    patientId!: string;

    @IsNotEmpty()
    pharmacyId!: string;

    @IsOptional()
    @IsDateString({}, { message: 'validation.ISSUE_DATE_INVALID' })
    issueDate?: string;

    @IsArray()
    storagePaths!: string[];

    @IsOptional()
    note?: string;

    @IsOptional()
    @IsArray()
    medicines?: {
        medicine: string;
        dosage?: string;
        frequency?: string;
        duration?: string;
        timing?: string;
    }[];
}

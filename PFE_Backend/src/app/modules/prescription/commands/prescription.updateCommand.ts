import { IsArray, IsOptional, IsString } from 'class-validator';

export class PrescriptionUpdateCommand {
    @IsString()
    id!: string; // The prescription ID to update

    @IsOptional()
    @IsArray()
    storagePaths?: string[]; // The full list of files (new + existing)

    @IsOptional()
    note?: string;

    @IsOptional()
    @IsArray()
    medicines?: {
        medicine: string;
        dosage?: string;
        frequency?: string;
        duration?: string;
        timing?: string;
    }[];
}
import { IsNotEmpty, IsOptional } from 'class-validator';

export class PrescriptionUploadFileCommand {
    @IsNotEmpty({ message: 'validation.FILE_PATH_REQUIRED' })
    filePath!: string;

    @IsNotEmpty({ message: 'validation.FILE_SIZE_REQUIRED' })
    fileSize!: number;

    @IsOptional()
    patientId?: string;

    @IsOptional()
    pharmacyId?: string;

    @IsOptional()
    issueDate?: string;
}
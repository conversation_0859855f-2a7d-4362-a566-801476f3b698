import { Test, TestingModule } from '@nestjs/testing';
import { PackageDecisionService } from './packageDecision.service';

describe('PackageDecisionService', () => {
    let service: PackageDecisionService;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [PackageDecisionService],
        }).compile();

        service = module.get<PackageDecisionService>(PackageDecisionService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('determinePackageDecision', () => {
        it('should return Package for Dolipranne and Inflamil', () => {
            const medicines = [
                { medicine: 'Dolipranne' },
                { medicine: 'Inflamil' }
            ];

            const result = service.determinePackageDecision(medicines);

            expect(result.decision).toBe('Package');
            expect(result.decisionFrench).toBe('Colis');
            expect(result.priority).toBe(1);
        });

        it('should return Package+Picking for Dolipranne and Augmentin', () => {
            const medicines = [
                { medicine: 'Dolipranne' },
                { medicine: 'Augmentin' }
            ];

            const result = service.determinePackageDecision(medicines);

            expect(result.decision).toBe('Package+Picking');
            expect(result.decisionFrench).toBe('Colis+Cueillette');
            expect(result.priority).toBe(2);
        });

        it('should return Picking for Augmentin and Morphine', () => {
            const medicines = [
                { medicine: 'Augmentin' },
                { medicine: 'Morphine' }
            ];

            const result = service.determinePackageDecision(medicines);

            expect(result.decision).toBe('Picking');
            expect(result.decisionFrench).toBe('Cueillette');
            expect(result.priority).toBe(3);
        });

        it('should return Picking for Morphine and Inflamil (Picking > Package)', () => {
            const medicines = [
                { medicine: 'Morphine' },
                { medicine: 'Inflamil' }
            ];

            const result = service.determinePackageDecision(medicines);

            expect(result.decision).toBe('Picking');
            expect(result.decisionFrench).toBe('Cueillette');
            expect(result.priority).toBe(3);
        });

        it('should return Package for unknown medicines', () => {
            const medicines = [
                { medicine: 'UnknownMedicine1' },
                { medicine: 'UnknownMedicine2' }
            ];

            const result = service.determinePackageDecision(medicines);

            expect(result.decision).toBe('Package');
            expect(result.decisionFrench).toBe('Colis');
            expect(result.priority).toBe(1);
        });

        it('should return Package for empty medicines array', () => {
            const medicines: any[] = [];

            const result = service.determinePackageDecision(medicines);

            expect(result.decision).toBe('Package');
            expect(result.decisionFrench).toBe('Colis');
            expect(result.priority).toBe(1);
        });

        it('should handle case-insensitive medicine names', () => {
            const medicines = [
                { medicine: 'morphine' }, // lowercase
                { medicine: 'DOLIPRANNE' } // uppercase
            ];

            const result = service.determinePackageDecision(medicines);

            expect(result.decision).toBe('Picking');
            expect(result.decisionFrench).toBe('Cueillette');
            expect(result.priority).toBe(3);
        });

        it('should include matched medicines in result', () => {
            const medicines = [
                { medicine: 'Dolipranne' },
                { medicine: 'Morphine' }
            ];

            const result = service.determinePackageDecision(medicines);

            expect(result.matchedMedicines).toHaveLength(2);
            expect(result.matchedMedicines[0].medicine).toBe('Dolipranne');
            expect(result.matchedMedicines[0].decision).toBe('Package');
            expect(result.matchedMedicines[1].medicine).toBe('Morphine');
            expect(result.matchedMedicines[1].decision).toBe('Picking');
        });
    });

    describe('updateMedicineDecision', () => {
        it('should update medicine decision', () => {
            service.updateMedicineDecision('TestMedicine', 'Picking');
            const decisions = service.getAllMedicineDecisions();
            expect(decisions['TestMedicine']).toBe('Picking');
        });

        it('should throw error for invalid decision', () => {
            expect(() => {
                service.updateMedicineDecision('TestMedicine', 'InvalidDecision');
            }).toThrow('Invalid decision: InvalidDecision');
        });
    });

    describe('getAllMedicineDecisions', () => {
        it('should return all medicine decisions', () => {
            const decisions = service.getAllMedicineDecisions();
            expect(decisions).toBeDefined();
            expect(typeof decisions).toBe('object');
        });
    });

    describe('getDecisionTranslations', () => {
        it('should return decision translations', () => {
            const translations = service.getDecisionTranslations();
            expect(translations).toBeDefined();
            expect(translations['Package']).toBe('Colis');
            expect(translations['Picking']).toBe('Cueillette');
            expect(translations['Package+Picking']).toBe('Colis+Cueillette');
        });
    });
});

import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';

export interface PackageDecisionData {
    medicines: Record<string, string>;
    decisionPriority: Record<string, number>;
    translations: Record<string, string>;
}

export interface PackageDecisionResult {
    decision: string;
    decisionFrench: string;
    priority: number;
    matchedMedicines: Array<{
        medicine: string;
        decision: string;
        decisionFrench: string;
    }>;
}

@Injectable()
export class PackageDecisionService {
    private readonly logger = new Logger(PackageDecisionService.name);
    private packageDecisionData: PackageDecisionData;

    constructor() {
        this.loadPackageDecisionData();
    }

    private loadPackageDecisionData(): void {
        try {
            const dataPath = path.join(process.cwd(), 'src', 'core', 'data', 'packageDecisions.json');
            const rawData = fs.readFileSync(dataPath, 'utf8');
            this.packageDecisionData = JSON.parse(rawData);
            this.logger.log('📦 Package decision data loaded successfully');
        } catch (error) {
            this.logger.error(`❌ Failed to load package decision data: ${error.message}`);
            // Fallback to default data
            this.packageDecisionData = {
                medicines: {},
                decisionPriority: {
                    "Picking": 3,
                    "Package+Picking": 2,
                    "Package": 1
                },
                translations: {
                    "Package": "Colis",
                    "Picking": "Cueillette",
                    "Package+Picking": "Colis+Cueillette"
                }
            };
        }
    }

    /**
     * Determine package decision for a list of medicines
     * Priority: Picking > Package+Picking > Package
     */
    public determinePackageDecision(medicines: Array<{ medicine: string; [key: string]: any }>): PackageDecisionResult {
        if (!medicines || medicines.length === 0) {
            return {
                decision: 'Package',
                decisionFrench: 'Colis',
                priority: 1,
                matchedMedicines: []
            };
        }

        let highestPriority = 0;
        let finalDecision = 'Package';
        const matchedMedicines: Array<{ medicine: string; decision: string; decisionFrench: string }> = [];

        // Check each medicine against our decision data
        for (const med of medicines) {
            const medicineName = this.normalizeMedicineName(med.medicine);
            const decision = this.findMedicineDecision(medicineName);
            
            if (decision) {
                const priority = this.packageDecisionData.decisionPriority[decision] || 1;
                const decisionFrench = this.packageDecisionData.translations[decision] || decision;
                
                matchedMedicines.push({
                    medicine: med.medicine,
                    decision,
                    decisionFrench
                });

                // Update final decision if this medicine has higher priority
                if (priority > highestPriority) {
                    highestPriority = priority;
                    finalDecision = decision;
                }

                this.logger.log(`📋 Medicine: ${med.medicine} -> Decision: ${decision} (Priority: ${priority})`);
            } else {
                // Default to Package for unknown medicines
                matchedMedicines.push({
                    medicine: med.medicine,
                    decision: 'Package',
                    decisionFrench: 'Colis'
                });
                this.logger.log(`📋 Medicine: ${med.medicine} -> Default Decision: Package (Unknown medicine)`);
            }
        }

        // If no medicines matched, default to Package
        if (highestPriority === 0) {
            highestPriority = 1;
            finalDecision = 'Package';
        }

        const finalDecisionFrench = this.packageDecisionData.translations[finalDecision] || finalDecision;

        this.logger.log(`📦 Final package decision: ${finalDecision} (${finalDecisionFrench}) with priority ${highestPriority}`);

        return {
            decision: finalDecision,
            decisionFrench: finalDecisionFrench,
            priority: highestPriority,
            matchedMedicines
        };
    }

    /**
     * Find decision for a specific medicine name
     */
    private findMedicineDecision(medicineName: string): string | null {
        // Direct match
        if (this.packageDecisionData.medicines[medicineName]) {
            return this.packageDecisionData.medicines[medicineName];
        }

        // Case-insensitive search
        const lowerMedicineName = medicineName.toLowerCase();
        for (const [medicine, decision] of Object.entries(this.packageDecisionData.medicines)) {
            if (medicine.toLowerCase() === lowerMedicineName) {
                return decision;
            }
        }

        // Partial match (contains)
        for (const [medicine, decision] of Object.entries(this.packageDecisionData.medicines)) {
            if (medicine.toLowerCase().includes(lowerMedicineName) || 
                lowerMedicineName.includes(medicine.toLowerCase())) {
                return decision;
            }
        }

        return null;
    }

    /**
     * Normalize medicine name for better matching
     */
    private normalizeMedicineName(medicineName: string): string {
        if (!medicineName) return '';
        
        return medicineName
            .trim()
            .replace(/\s+/g, ' ') // Replace multiple spaces with single space
            .replace(/[®™©]/g, '') // Remove trademark symbols
            .replace(/\d+mg|\d+g|\d+ml/gi, '') // Remove dosage information
            .trim();
    }

    /**
     * Get all available medicines and their decisions
     */
    public getAllMedicineDecisions(): Record<string, string> {
        return { ...this.packageDecisionData.medicines };
    }

    /**
     * Add or update a medicine decision
     */
    public updateMedicineDecision(medicineName: string, decision: string): void {
        if (!['Package', 'Picking', 'Package+Picking'].includes(decision)) {
            throw new Error(`Invalid decision: ${decision}. Must be one of: Package, Picking, Package+Picking`);
        }

        this.packageDecisionData.medicines[medicineName] = decision;
        this.logger.log(`📝 Updated medicine decision: ${medicineName} -> ${decision}`);
    }

    /**
     * Get decision translations
     */
    public getDecisionTranslations(): Record<string, string> {
        return { ...this.packageDecisionData.translations };
    }
}

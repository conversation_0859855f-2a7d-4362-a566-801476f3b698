import {ForbiddenException, Injectable, NotFoundException} from '@nestjs/common';
import { PrescriptionRepository } from '../repositories/prescription.repository';

@Injectable()
export class PrescriptionDelete {
    constructor(
        private readonly prescriptionRepository: PrescriptionRepository,
    ) {}

    async execute(prescriptionId: string, patientId: string): Promise<void> {
        // Find the prescription by ID
        const prescription = await this.prescriptionRepository.findById(prescriptionId);
        if (!prescription) {
            throw new NotFoundException('Prescription not found');
        }

        // Check if the prescription belongs to the user
        if (prescription.patientId !== patientId) {
            throw new ForbiddenException('You do not have permission to delete this prescription');
        }

        // Delete the prescription
        await this.prescriptionRepository.delete(prescriptionId);
    }
}
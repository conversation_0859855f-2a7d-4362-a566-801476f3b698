import { Injectable } from '@nestjs/common';
import { PrescriptionRepository } from '../repositories/prescription.repository';
import { PrescriptionUpdateCommand } from '../commands/prescription.updateCommand';
import { OcrService } from '../../ocr/services/ocr.service';
import * as path from 'path';
import * as fs from 'fs';

@Injectable()
export class PrescriptionUpdate {
    constructor(
        private readonly prescriptionRepo: PrescriptionRepository,
        private readonly ocrService: OcrService,
    ) {}

    async execute(command: PrescriptionUpdateCommand) {
        const existing = await this.prescriptionRepo.findById(command.id);
        const updatedStoragePaths = command.storagePaths ?? existing.storagePath;

        let updatedMedicines = existing.medicines;

        // Try OCR if new file is provided
        if (command.storagePaths && command.storagePaths.length > 0) {
            const relativePath = command.storagePaths[0].startsWith('/')
                ? command.storagePaths[0].substring(1)
                : command.storagePaths[0];

            const absolutePath = path.join(process.cwd(), relativePath);

            if (fs.existsSync(absolutePath)) {
                try {
                    updatedMedicines = await this.ocrService.extractTextFromFile(absolutePath);
                } catch (error) {
                    console.warn(`⚠️ OCR failed during update: ${error.message}`);
                }
            } else {
                console.warn(`⚠️ File does not exist at: ${absolutePath}`);
            }
        }

        return await this.prescriptionRepo.update(command.id, {
            storagePath: updatedStoragePaths,
            note: command.note ?? existing.note,
            uploadedAt: new Date().toISOString(),
            medicines: updatedMedicines, // ✅ Always update medicines (either new or fallback)
        });
    }
}

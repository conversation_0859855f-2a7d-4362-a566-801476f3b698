import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrescriptionRepository } from '../repositories/prescription.repository';
import {PrescriptionGetByIdCommand} from "../commands/prescription.getByIdCommand";

@Injectable()
export class PrescriptionGetById {
    constructor(private readonly prescriptionRepo: PrescriptionRepository) {}

    async execute(command: PrescriptionGetByIdCommand) {
        const prescription = await this.prescriptionRepo.findById(command.prescriptionId);
        if (!prescription) {
            throw new NotFoundException('Prescription not found');
        }

        // if (prescription.patientId !== command.patientId) {
        //     throw new ForbiddenException('Access denied to this prescription');
        // }

        return prescription;
    }
}
import { Injectable, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import * as fs from 'fs';
import {PrescriptionRepository} from "../../repositories/prescription.repository";
import {OcrService} from "../../../ocr/services/ocr.service";
import {PrescriptionCreateForPatientCommand} from "../../commands/web/PrescriptionCreateForPatientCommand";
import {PrescriptionStatus} from "../../../../../core/enums/prescription/PrescriptionStatus";
import { SendPharmacistPrescriptionNotification } from "../../../user/usecases/notifications/sendPharmacistPrescriptionNotification";
import { UserRepository } from "../../../user/repositories/user.repository";
import { PharmacyRepository } from "../../../pharmacy/repositories/pharmacy.repository";


@Injectable()
export class PrescriptionCreateForPatient {
    private readonly logger = new Logger(PrescriptionCreateForPatient.name);

    constructor(
        private readonly prescriptionRepo: PrescriptionRepository,
        private readonly ocrService: OcrService,
        private readonly sendPharmacistPrescriptionNotificationUseCase: SendPharmacistPrescriptionNotification,
        private readonly userRepository: UserRepository,
        private readonly pharmacyRepository: PharmacyRepository,
    ) {}

    async execute(command: PrescriptionCreateForPatientCommand) {
        let medicines: any[] = [];

        if (command.storagePaths?.length > 0) {
            const relativePath = command.storagePaths[0].startsWith('/')
                ? command.storagePaths[0].substring(1)
                : command.storagePaths[0];

            const absolutePath = path.join(process.cwd(), relativePath);
            this.logger.log(`🧪 OCR path: ${absolutePath}`);

            if (fs.existsSync(absolutePath)) {
                try {
                    medicines = await this.ocrService.extractTextFromFile(absolutePath);
                    this.logger.log(`🧪 OCR extracted ${medicines.length} medicine(s)`);
                } catch (err) {
                    this.logger.warn(`⚠️ OCR failed: ${err.message}`);
                }
            } else {
                this.logger.warn(`⚠️ File not found for OCR: ${absolutePath}`);
            }
        }

        const prescription = await this.prescriptionRepo.create({
            id: uuidv4(),
            patientId: command.patientId,
            pharmacyId: command.pharmacyId,
            issueDate: command.issueDate ? new Date(command.issueDate) : new Date(),
            date: new Date(),
            storagePath: command.storagePaths,
            prescriptionStatus: PrescriptionStatus.UPLOADED,
            approved: false,
            uploadedAt: new Date().toISOString(),
            note: command.note,
            medicines,
        });

        // 🔔 Send notification to patient about pharmacist-created prescription
        try {
            // Get pharmacist information from the pharmacy
            const pharmacy = await this.pharmacyRepository.findById(command.pharmacyId);
            if (pharmacy && pharmacy.pharmacistEmail) {
                const pharmacist = await this.userRepository.findOneByEmail(pharmacy.pharmacistEmail);

                if (pharmacist) {
                    const pharmacistName = pharmacist.email.split('@')[0]; // Use email prefix as name

                    // Prepare notification data
                    const notificationData = {
                        prescriptionId: prescription.id,
                        patientId: command.patientId,
                        pharmacistId: pharmacist.id,
                        pharmacistName,
                        pharmacyId: command.pharmacyId,
                        medicineCount: medicines.length,
                        medicines: medicines.map(med => ({
                            medicine: med.medicine || med.name || 'Médicament',
                            dosage: med.dosage,
                            quantity: med.quantity
                        }))
                    };

                    // Send notification to the patient
                    await this.sendPharmacistPrescriptionNotificationUseCase.execute(notificationData);

                    this.logger.log(`🔔 Patient notification sent for pharmacist-created prescription: ${prescription.id}`);
                } else {
                    this.logger.warn(`⚠️ Pharmacist not found for email: ${pharmacy.pharmacistEmail}`);
                }
            } else {
                this.logger.warn(`⚠️ Pharmacy not found or missing pharmacist email: ${command.pharmacyId}`);
            }
        } catch (error) {
            this.logger.error(`❌ Failed to send patient notification: ${error.message}`);
            // Don't throw error - prescription creation should succeed even if notification fails
        }

        return prescription;
    }
}

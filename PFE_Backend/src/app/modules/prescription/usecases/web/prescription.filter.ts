import { Injectable, ForbiddenException } from '@nestjs/common';
import { PrescriptionRepository } from '../../repositories/prescription.repository';
import { PatientRepository } from '../../../patient/repositories/patient.repository';
import { PrescriptionFilterCommand } from '../../commands/web/prescription.filterCommand';

@Injectable()
export class PrescriptionFilter {
    constructor(
        private readonly prescriptionRepo: PrescriptionRepository,
        private readonly patientRepo: PatientRepository
    ) {}

    async execute(command: PrescriptionFilterCommand, user: any) {
        const allowedRoles = [4, 5, 6]; // INTERNAL_PHARMACY, EXTERNAL_PHARMACY, ADMIN

        const role = Number(user?.role);
        if (!allowedRoles.includes(role)) {
            throw new ForbiddenException('Access denied');
        }

        const filters: any = {};

        // ✅ Always default to today if no date is provided
        const dateToUse = command.date ? new Date(command.date) : new Date();
        const startOfDay = new Date(dateToUse.setHours(0, 0, 0, 0));
        const endOfDay = new Date(dateToUse.setHours(24, 0, 0, 0));

        filters.date = { $gte: startOfDay, $lt: endOfDay };

        if (command.status) {
            filters.prescriptionStatus = command.status;
        }

        if (command.hasHousehold !== undefined) {
            const patients = await this.patientRepo.findAll();
            const patientIdsWithHouseholds = patients
                .filter(p => !!p.houseHoldId)
                .map(p => p.id);

            filters.patientId =
                command.hasHousehold === 'true'
                    ? { $in: patientIdsWithHouseholds }
                    : { $nin: patientIdsWithHouseholds };
        }

        const prescriptions = await this.prescriptionRepo.findByFilters(filters);

        return {
            prescriptions,
            total: prescriptions.length,
        };
    }
}

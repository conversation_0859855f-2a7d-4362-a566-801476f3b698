import { Injectable } from '@nestjs/common';
import { PrescriptionUploadFileCommand } from '../commands/prescription.uploadFileCommand';

@Injectable()
export class PrescriptionUploadFile {
    async execute(command: PrescriptionUploadFileCommand): Promise<{
        filePath: string;
        fileSize: number;
    }> {
        return {
            filePath: command.filePath,
            fileSize: command.fileSize,
        };
    }
}
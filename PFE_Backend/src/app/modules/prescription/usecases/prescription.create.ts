import { Injectable, Logger } from '@nestjs/common';
import { PrescriptionRepository } from '../repositories/prescription.repository';
import { PrescriptionStatus } from '../../../../core/enums/prescription/PrescriptionStatus';
import { PrescriptionCreateCommand } from "../commands/prescription.createCommand";
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import * as fs from 'fs';
import { OcrService } from '../../ocr/services/ocr.service';
import { SendPrescriptionNotification } from '../../user/usecases/notifications/sendPrescriptionNotification';
import { PatientRepository } from '../../patient/repositories/patient.repository';

@Injectable()
export class PrescriptionCreate {
    private readonly logger = new Logger(PrescriptionCreate.name);

    constructor(
        private readonly prescriptionRepo: PrescriptionRepository,
        private readonly ocrService: OcrService,
        private readonly sendPrescriptionNotificationUseCase: SendPrescriptionNotification,
        private readonly patientRepository: PatientRepository,
    ) {}

    async execute(command: PrescriptionCreateCommand) {
        let medicines: any[] = [];

        if (command.storagePaths?.length > 0) {
            // Get the file path
            const relativePath = command.storagePaths[0].startsWith('/')
                ? command.storagePaths[0].substring(1)
                : command.storagePaths[0];

            const absolutePath = path.join(process.cwd(), relativePath);

            // Debugging: Log file path
            this.logger.log(`🧪 Resolving full path for OCR: ${absolutePath}`);

            if (fs.existsSync(absolutePath)) {
                try {
                    medicines = await this.ocrService.extractTextFromFile(absolutePath);
                    this.logger.log(`🧪 OCR extracted ${medicines.length} medicine(s)`);
                } catch (err) {
                    this.logger.warn(`⚠️ OCR failed during create: ${err.message}`);
                    medicines = []; // Ensure it's set even if OCR fails
                }
            } else {
                this.logger.warn(`⚠️ OCR skipped during create: File does not exist at ${absolutePath}`);
                medicines = []; // Ensure it's set even if file doesn't exist
            }
        }

        const prescription = await this.prescriptionRepo.create({
            id: uuidv4(),
            patientId: command.patientId,
            pharmacyId: command.pharmacyId,
            issueDate: command.issueDate ? new Date(command.issueDate) : new Date(),
            date: new Date(),
            storagePath: command.storagePaths,
            prescriptionStatus: PrescriptionStatus.UPLOADED,
            approved: false,
            uploadedAt: new Date().toISOString(),
            note: command.note,
            medicines, // Always defined even if empty
        });

        // 🔔 Send notification to pharmacists if medicines were extracted
        if (medicines.length > 0) {
            try {
                // Get patient information for the notification
                const patient = await this.patientRepository.findOneById(command.patientId);

                if (patient) {
                    const patientName = `${patient.firstName} ${patient.lastName}`;

                    // Prepare notification data
                    const notificationData = {
                        prescriptionId: prescription.id,
                        patientId: command.patientId,
                        patientName,
                        pharmacyId: command.pharmacyId,
                        medicineCount: medicines.length,
                        medicines: medicines.map(med => ({
                            medicine: med.medicine || med.name || 'Unknown',
                            dosage: med.dosage,
                            quantity: med.quantity
                        }))
                    };

                    // Send notification to pharmacists
                    await this.sendPrescriptionNotificationUseCase.execute(notificationData);

                    this.logger.log(`🔔 Prescription notification sent for prescription: ${prescription.id}`);
                } else {
                    this.logger.warn(`⚠️ Patient not found for notification: ${command.patientId}`);
                }
            } catch (notificationError) {
                // Don't fail the prescription creation if notification fails
                this.logger.error(`❌ Failed to send prescription notification: ${notificationError.message}`);
            }
        }

        return prescription;
    }
}

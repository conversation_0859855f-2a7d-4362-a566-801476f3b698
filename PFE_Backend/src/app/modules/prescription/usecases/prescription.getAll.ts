import { Injectable } from '@nestjs/common';
import { PrescriptionRepository } from '../repositories/prescription.repository';
import { PrescriptionGetAllCommand } from '../commands/prescription.getAllCommand';

@Injectable()
export class PrescriptionGetAll {
    constructor(private readonly prescriptionRepo: PrescriptionRepository) {}

    async execute(command: PrescriptionGetAllCommand) {
        const dateToFilter = command.date ?? new Date().toISOString().split('T')[0];

        const start = new Date(`${dateToFilter}T00:00:00.000Z`);
        const end = new Date(`${dateToFilter}T23:59:59.999Z`);

        const prescriptions = await this.prescriptionRepo.findByPatientIdAndDateRange(
            command.patientId,
            start,
            end,
        );

        let filtered = command.note
            ? prescriptions.filter((p) =>
                p.note?.toLowerCase().includes(command.note!.toLowerCase()),
            )
            : prescriptions;

        // ✅ Sort by createdAt: most recent first
        filtered = filtered.sort((a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );

        return {
            prescriptions: filtered,
            total: filtered.length,
        };
    }

}
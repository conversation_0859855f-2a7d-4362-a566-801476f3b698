import {<PERSON>, Get, Param, Query, Req, Res, UseGuards} from '@nestjs/common';
import { Response } from 'express';
import { I18n, I18nContext } from 'nestjs-i18n';
import { AuthenticatedRequest } from '../../../config/authenticatedRequest';
import { AuthenticationMiddleware } from '../../../middlewares/authenticationMiddleware';
import { GetHouseHoldById } from '../usecases/getHouseHoldById';
import {SearchHouseHoldByName} from "../usecases/searchHouseHoldByName";

@Controller('/household')
export class HouseHoldController {
    constructor(private readonly getHouseHoldById: GetHouseHoldById,
                private readonly searchByName: SearchHouseHoldByName) {}

    @Get('/getHouseHoldDetails/:householdId')
    @UseGuards(AuthenticationMiddleware)
    async getHouseholdDetails(
        @Req() req: AuthenticatedRequest,
        @Res() res: Response,
        @I18n() i18n: I18nContext,
        @Param('householdId') householdId: string,
    ) {
        try {
            const household = await this.getHouseHoldById.execute(householdId);

            if (!household) {
                return res.status(404).json({
                    success: false,
                    message: i18n.translate('errors.HOUSEHOLD_NOT_FOUND'),
                });
            }

            return res.status(200).json({
                success: true,
                message: i18n.translate('success.HOUSEHOLD_FETCHED_SUCCESSFULLY'),
                data: household,
            });
        } catch (error) {
            console.error(error);
            return res.status(500).json({
                success: false,
                message: i18n.translate('errors.INTERNAL_SERVER_ERROR'),
            });
        }
    }
    @Get('/search')
    async searchByNameQuery(@Query('query') query: string) {
        const households = await this.searchByName.execute(query);
        return {
            success: true,
            total: households.length,
            households,
        };
    }
}

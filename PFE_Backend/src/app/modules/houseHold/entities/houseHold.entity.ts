import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class HouseHold extends Document {
    @Prop({ required: true, unique: true })
    id: string;

    @Prop({ required: true })
    name: string;

    @Prop({ required: true })
    pharmacyId: string;

    @Prop()
    email?: string;

    @Prop()
    responsibleName?: string;

    @Prop()
    phoneNumber?: string;

    @Prop()
    post?: number;

    @Prop({
        type: {
            address: { type: String, required: true },
            lat: { type: Number, required: true },
            long: { type: Number, required: true },
        },
        required: true,
    })
    address: {
        address: string;
        lat: number;
        long: number;
    };

    @Prop()
    note?: string;

    @Prop({
        type: {
            note: { type: String },
            frequency: { type: String },
            from: { type: Date },
            to: { type: Date },
        },
    })
    recurrenceOption?: {
        note: string;
        frequency: string;
        from: Date;
        to: Date;
    };

    @Prop()
    deliveryManNote?: string;

    @Prop()
    pharmacyNote?: string;

    @Prop()
    createdBy?: string;

    @Prop()
    deletedAt?: Date;

    @Prop()
    deletedBy?: string;
}

export const HouseHoldSchema = SchemaFactory.createForClass(HouseHold);

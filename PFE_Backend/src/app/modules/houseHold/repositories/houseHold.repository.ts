import {Injectable} from "@nestjs/common";
import {InjectModel} from "@nestjs/mongoose";
import {Model} from "mongoose";
import {HouseHold} from "../entities/houseHold.entity";


@Injectable()
export class HouseHoldRepository {
    constructor(@InjectModel('HouseHold') private houseHoldModel: Model<HouseHold>) {}


    async findById(householdId: string): Promise<any> {
        return this.houseHoldModel.findOne({ id: householdId }).lean();
    }

    async findByNameLike(query: string): Promise<HouseHold[]> {
        const regex = new RegExp(query.trim(), 'i'); // case-insensitive partial match
        return this.houseHoldModel.find({ name: regex }).exec();
    }


}

import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HouseHold, HouseHoldSchema } from './entities/household.entity';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from '../user/user.module';
import {HouseHoldController} from "./controllers/houseHold.controller";
import {GetHouseHoldById} from "./usecases/getHouseHoldById";
import {HouseHoldRepository} from "./repositories/houseHold.repository";
import {SearchHouseHoldByName} from "./usecases/searchHouseHoldByName";


@Module({
    imports: [
        MongooseModule.forFeature([
            { name: 'HouseHold', schema: HouseHoldSchema },
        ]),
        JwtModule.register({
            secret: 'reallysecurekey',
            signOptions: { expiresIn: '1y' },
        }),
        forwardRef(() => UserModule),
    ],
    controllers: [HouseHoldController],
    providers: [
        HouseHoldRepository,GetHouseHoldById,SearchHouseHoldByName],
    exports: [],
})
export class HouseHoldModule {}

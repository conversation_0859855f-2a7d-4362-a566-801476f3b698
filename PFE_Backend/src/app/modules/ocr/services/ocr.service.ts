import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import * as fs from 'fs';
import * as path from 'path';
import FormData from 'form-data';

@Injectable()
export class OcrService {
    constructor(private readonly httpService: HttpService) {}

    // In OcrService
    async extractTextFromFile(filePath: string): Promise<any[]> {
        // Use the direct filePath as it should already be absolute
        console.log(`🧪 Resolved full path for OCR: ${filePath}`);

        if (!fs.existsSync(filePath)) {
            throw new Error(`File does not exist at: ${filePath}`);
        }

        const form = new FormData();
        const fileStream = fs.createReadStream(filePath);
        form.append('file', fileStream, path.basename(filePath));

        try {
            const response = await lastValueFrom(
                this.httpService.post('http://localhost:8001/extract', form, {
                    headers: form.getHeaders(),
                }),
            );

            // Ensure the stream is closed
            fileStream.close();
            return response.data;
        } catch (error) {
            fileStream.close();
            throw new Error(`Error while processing OCR: ${error.message}`);
        }
    }
}

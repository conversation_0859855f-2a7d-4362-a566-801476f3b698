import { Controller, Post, Body, Res, UseGuards } from '@nestjs/common';
import { OcrExtractTextCommand } from '../commands/ocr.extractTextCommand';
import { OcrExtractTextFromFile } from '../usecases/ocr.extractTextFromFile';
import { AuthenticationMiddleware } from '../../../middlewares/authenticationMiddleware';
import { Response } from 'express';

@Controller('ocr')
export class OcrController {
    constructor(private readonly extractTextFromFile: OcrExtractTextFromFile) {}

    @Post('extract')
    @UseGuards(AuthenticationMiddleware)
    async extractText(@Body() body: OcrExtractTextCommand, @Res() res: Response) {
        try {
            const result = await this.extractTextFromFile.execute(body);
            return res.status(200).json({ success: true, data: result });
        } catch (err) {
            return res.status(500).json({ success: false, message: err.message });
        }
    }
}

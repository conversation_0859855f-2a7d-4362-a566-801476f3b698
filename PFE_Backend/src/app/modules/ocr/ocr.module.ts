import { forwardRef, Modu<PERSON> } from '@nestjs/common';
import { OcrController } from './controllers/ocr.controller';
import { OcrService } from './services/ocr.service';
import { OcrExtractTextFromFile } from './usecases/ocr.extractTextFromFile';
import { JwtModule } from '@nestjs/jwt';
import { HttpModule } from '@nestjs/axios';
import {UserModule} from "../user/user.module";

@Module({
    imports: [
        HttpModule,
        JwtModule.register({
            secret: 'reallysecurekey',
            signOptions: { expiresIn: '1y' },
        }),
        forwardRef(() => UserModule),
    ],
    controllers: [OcrController],
    providers: [OcrService, OcrExtractTextFromFile],
    exports: [OcrService], // 👈 export service for use in other modules
})
export class OcrModule {}

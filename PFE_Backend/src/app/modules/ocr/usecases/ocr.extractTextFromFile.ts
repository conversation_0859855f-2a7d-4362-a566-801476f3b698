import { Injectable } from '@nestjs/common';
import { OcrService } from '../services/ocr.service';
import { OcrExtractTextCommand } from '../commands/ocr.extractTextCommand';

@Injectable()
export class OcrExtractTextFromFile {
    constructor(private readonly ocrService: OcrService) {}

    async execute(command: OcrExtractTextCommand) {
        return await this.ocrService.extractTextFromFile(command.filePath);
    }
}

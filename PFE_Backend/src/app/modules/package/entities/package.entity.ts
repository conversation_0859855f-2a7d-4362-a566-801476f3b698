import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import {PackageReceiverType} from "../../../../core/enums/package/PackageReceiverType";
import {PatientPaymentType} from "../../../../core/enums/package/PatientPaymentType";
import {StandardFrequencyType} from "../../../../core/enums/patient/StandardFrequencyType";
import {PeriodType} from "../../../../core/enums/patient/PeriodType";
import {RecurrenceOptionType} from "../../../../core/enums/package/RecurrenceOptionType";
import {PackagesStatus} from "../../../../core/enums/package/PackagesStatus";
import {PreferenceType} from "../../../../core/enums/package/PreferenceType";


@Schema({ timestamps: true })
export class Package extends Document {
    @Prop({ required: true, unique: true })
    id: string;

    @Prop({ required: true })
    pharmacyId: string;

    @Prop({ required: true })
    priority: boolean;

    @Prop({ required: true })
    isPicking: boolean;

    @Prop({ type: String, enum: PackageReceiverType, required: true })
    receiverType: PackageReceiverType;

    @Prop({
        type: {
            patient: { type: String, required: true },
            groups: { type: [String], default: [] },
            paymentType: { type: String, enum: PatientPaymentType, required: true },
            price: { type: Number, required: true },
            packagePrice: { type: Number },
            additionalPrice: { type: Number },
            signatureType: { type: String, required: true },
            fullName: { type: String },
            deliveryType: { type: [String], default: [] },
            pharmacyNote: { type: String },
            deliveryManNote: { type: String },
            authorizedPerson: {
                name: { type: String, required: true },
                type: { type: String, required: true },
                phone: { type: String },
            },
            spot: { type: String },
        },
        required: false,
    })
    patientReceiver?: {
        patient: string;
        groups: string[];
        paymentType: PatientPaymentType;
        price: number;
        packagePrice?: number;
        additionalPrice?: number;
        signatureType: string;
        fullName?: string;
        deliveryType: string[];
        pharmacyNote: string;
        deliveryManNote: string;
        authorizedPerson: {
            name: string;
            type: string;
            phone?: string;
        };
        spot: string;
    };

    @Prop({
        type: {
            household: { type: String, required: true },
            groups: { type: [String], default: [] },
            patients: [
                {
                    id: { type: String, required: true },
                    fullName: { type: String, required: true },
                    roomNumber: { type: Number },
                    paymentType: { type: String, required: true },
                    signatureType: { type: String, required: true },
                    deliveryType: { type: [String], default: [] },
                    pharmacyNote: { type: String },
                    deliveryManNote: { type: String },
                    householdNote: { type: String },
                    authorizedPerson: {
                        name: { type: String, required: true },
                        type: { type: String, required: true },
                        phone: { type: String },
                    },
                    spot: { type: String },
                    recipient: { type: String },
                    price: { type: Number },
                    packagePrice: { type: Number },
                    additionalPrice: { type: Number },
                    hospitalizedPatient: { type: Boolean },
                    completed: { type: Boolean },
                    delivered: { type: Boolean },
                    isPrepared: { type: Boolean },
                },
            ],
        },
        required: false,
    })
    householdReceiver?: {
        household: string;
        groups: string[];
        patients: {
            id: string;
            fullName: string;
            roomNumber?: number;
            paymentType: string;
            signatureType: string;
            deliveryType: string[];
            pharmacyNote: string;
            deliveryManNote: string;
            householdNote: string;
            authorizedPerson: {
                name: string;
                type: string;
                phone?: string;
            };
            spot: string;
            recipient?: string;
            price?: number;
            packagePrice?: number;
            additionalPrice?: number;
            hospitalizedPatient?: boolean;
            completed?: boolean;
            delivered?: boolean;
            isPrepared?: boolean;
        }[];
    };

    @Prop({ required: true })
    deliveryDate: string;

    @Prop({ required: true })
    deliveryTime: string;

    @Prop({
        type: {
            note: { type: String },
            frequency: {
                standardFrequency: { type: String, enum: StandardFrequencyType },
                each: { type: Number },
                period: { type: String, enum: PeriodType },
            },
            from: { type: Date },
            to: { type: Date },
            type: { type: String, enum: RecurrenceOptionType },
        },
    })
    recurrenceOption?: {
        note?: string;
        frequency?: {
            standardFrequency?: StandardFrequencyType;
            each?: number;
            period?: PeriodType;
        };
        from?: Date;
        to?: Date;
        type?: RecurrenceOptionType;
    };

    @Prop({ type: String })
    recurrenceId?: string;

    @Prop({ type: String })
    deliveryId?: string;

    @Prop({ type: String, enum: PackagesStatus, required: true })
    status: PackagesStatus;

    @Prop({
        type: {
            address: { type: String, required: true },
            lat: { type: Number, required: true },
            long: { type: Number, required: true },
            apartmentNumber: { type: String },
        },
        required: true,
    })
    address: {
        address: string;
        lat: number;
        long: number;
        apartmentNumber?: string;
    };

    @Prop({
        type: {
            address: { type: String, required: true },
            lat: { type: Number, required: true },
            long: { type: Number, required: true },
            apartmentNumber: { type: String },
        },
        required: false,
    })
    originAddress?: {
        address: string;
        lat: number;
        long: number;
        apartmentNumber?: string;
    };

    @Prop({
        type: {
            type: { type: String, enum: PreferenceType, required: true },
            time: { type: String },
            from: { type: String },
            to: { type: String },
        },
    })
    preference: {
        type: PreferenceType;
        time?: string;
        from?: string;
        to?: string;
    };

    @Prop({ default: false })
    inRoute?: boolean;

    @Prop({ required: true })
    createdBy: string;

    @Prop()
    nbArticle?: number;

    @Prop()
    payedForDeliveryMan?: number;
}

export const PackageSchema = SchemaFactory.createForClass(Package);

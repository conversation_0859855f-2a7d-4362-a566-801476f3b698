import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {Package} from "../entities/package.entity";

@Injectable()
export class PackageRepository {
    constructor(@InjectModel('Package') private packageModel: Model<Package>) {}

    // ✅ Get all packages for a specific patient
    async findByPatientId(patientId: string): Promise<Package[]> {
        return this.packageModel.find({ 'patientReceiver.patient': patientId }).exec();
    }

    // ✅ Get package details by package ID
    async findById(packageId: string): Promise<Package | null> {
        return this.packageModel.findOne({ id: packageId }).exec();
    }



   /* async findByFilters(patientId: string, searchQuery?: string, status?: string) {
        const filter: any = { "patientReceiver.patient": patientId };

        if (searchQuery) {
            filter.$or = [
               // { "pharmacyId": { $regex: searchQuery, $options: "i" } },
                { "address.address": { $regex: searchQuery, $options: "i" } }
            ];
        }

        if (status) {
            filter.status = status;
        }

        return this.packageModel.find(filter).lean();
    }*/
    async findByFilters(
        patientId: string,
        searchQuery?: string,
        status?: string,
        deliveryDate?: string,
        deliveryPeriod?: 'AM' | 'PM'
    ): Promise<any[]> {
        const dateFilter = deliveryDate ? deliveryDate : new Date().toISOString().split('T')[0];
        const timeFilter = deliveryPeriod
            ? deliveryPeriod === 'AM'
                ? 'Matin (AM)'
                : "L'après-midi (PM)"
            : undefined;

        const baseConditions: any = [];

        // Patient-specific packages
        const patientCondition: any = {
            'patientReceiver.patient': patientId,
            deliveryDate: dateFilter,
        };
        if (timeFilter) patientCondition.deliveryTime = timeFilter;
        if (searchQuery) {
            patientCondition.$or = [
                { pharmacyId: { $regex: searchQuery, $options: 'i' } },
                { 'originAddress.address': { $regex: searchQuery, $options: 'i' } },
            ];
        }
        if (status) patientCondition.status = status;

        baseConditions.push(patientCondition);

        // Household packages for that patient
        const householdCondition: any = {
            'householdReceiver.patients.id': patientId,
            deliveryDate: dateFilter,
        };
        if (timeFilter) householdCondition.deliveryTime = timeFilter;
        if (searchQuery) {
            householdCondition.$or = [
                { pharmacyId: { $regex: searchQuery, $options: 'i' } },
                { 'originAddress.address': { $regex: searchQuery, $options: 'i' } },
            ];
        }
        if (status) householdCondition.status = status;

        baseConditions.push(householdCondition);

        const packages = await this.packageModel.find({ $or: baseConditions }).lean();

        // Add source tag
        return packages.map((pkg) => ({
            ...pkg,
            source: pkg.patientReceiver?.patient === patientId ? 'patient' : 'household',
        }));
    }


    async countByPatientId(patientId: string) {
        return this.packageModel.countDocuments({
            $or: [
                { "patientReceiver.patient": patientId },
                { "householdReceiver.patients.id": patientId }
            ]
        });
    }

    async sumPackagePricesByPatientId(patientId: string): Promise<number> {
        const patientPackagesTotal = await this.packageModel.aggregate([
            { $match: { "patientReceiver.patient": patientId } },
            {
                $group: {
                    _id: null,
                    total: { $sum: { $ifNull: ["$patientReceiver.packagePrice", 0] } }
                }
            }
        ]);

        const householdPackagesTotal = await this.packageModel.aggregate([
            { $match: { "householdReceiver.patients.id": patientId } },
            { $unwind: "$householdReceiver.patients" },
            { $match: { "householdReceiver.patients.id": patientId } },
            {
                $group: {
                    _id: null,
                    total: { $sum: { $ifNull: ["$householdReceiver.patients.packagePrice", 0] } }
                }
            }
        ]);

        const patientTotal = patientPackagesTotal[0]?.total ?? 0;
        const householdTotal = householdPackagesTotal[0]?.total ?? 0;

        return patientTotal + householdTotal;
    }

    async findByPatientIdDateAndTime(
        patientId: string,
        deliveryDate?: string,
        deliveryPeriod?: 'AM' | 'PM'
    ): Promise<Package[]> {
        const filter: any = {
            'patientReceiver.patient': patientId,
        };

        filter.deliveryDate = deliveryDate ?? new Date().toISOString().split('T')[0];

        if (deliveryPeriod === 'AM') {
            filter.deliveryTime = 'Matin (AM)';
        } else if (deliveryPeriod === 'PM') {
            filter.deliveryTime = "L'après-midi (PM)";
        }

        return this.packageModel.find(filter).exec();
    }





}

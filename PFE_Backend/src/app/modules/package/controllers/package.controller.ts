import {<PERSON>, <PERSON>, Req, <PERSON><PERSON>, Param, UseGuards, Post, Body} from '@nestjs/common';
import { Response } from 'express';
import { I18n, I18nContext } from 'nestjs-i18n';
import { GetAllPackagesByPatient } from "../usecases/getAllPackagesByPatient";
import { GetPackageDetails } from "../usecases/getPackageDetails";
import { AuthenticationMiddleware } from "../../../middlewares/authenticationMiddleware";
import { AuthenticatedRequest } from "../../../config/authenticatedRequest";
import {GetFilteredPackages} from "../usecases/GetFilteredPackages";
import {GetPatientPackageCount} from "../usecases/GetPatientPackageCount";
import {GetFiltredPackagesCommand} from "../commands/GetFiltredPackagesCommand";
import {GetPatientPackageCountCommand} from "../commands/GetPatientPackageCountCommand";
import { Query as NestQuery } from '@nestjs/common';
import {GetPatientTotalPackagePrice} from "../usecases/GetPatientTotalPackagePrice";

@Controller('/package') // ✅ Changed general route name
export class PackageController {
    constructor(
        private readonly getAllPackagesByPatient: GetAllPackagesByPatient,
        private readonly getPackageDetails: GetPackageDetails,
        private readonly getFilteredPackages: GetFilteredPackages,
        private readonly getPatientPackageCount: GetPatientPackageCount,
        private readonly getPatientTotalPackagePrice:GetPatientTotalPackagePrice

    ) {}

    @Get('/getAllPackages')
    @UseGuards(AuthenticationMiddleware)
    async getAllPackages(
        @Req() req: AuthenticatedRequest,
        @I18n() i18n: I18nContext,
        @Res() res: Response,
        @NestQuery('date') date?: string,
        @NestQuery('period') period?: 'AM' | 'PM'   // 👉 Nouveau paramètre ici
    ) {
        try {
            const patientId = req.identity.id;
            if (!patientId) {
                return res.status(401).json({ message: i18n.translate('errors.UNAUTHORIZED') });
            }

            const packages = await this.getAllPackagesByPatient.execute(patientId, date, period);
            return res.status(200).json({
                success: true,
                message: i18n.translate('success.SUCCESSFULLY_FETCHED_PACKAGES'),
                data: packages,
            });
        } catch (e) {
            console.error(e);
            return res.status(500).json({ message: i18n.translate('errors.INTERNAL_SERVER_ERROR') });
        }
    }

    // ✅ Get package details
    @Get('/getPackageDetails/:packageId')
    @UseGuards(AuthenticationMiddleware)
    async getPackageDetail(
        @Req() req: AuthenticatedRequest,
        @Res() res: Response,
        @Param('packageId') packageId: string,
        @I18n() i18n: I18nContext
    ) {
        try {
            const patientId = req.identity.id;

            const packageData = await this.getPackageDetails.execute(packageId);

            if (!packageData) {
                return res.status(404).json({ message: i18n.translate('errors.PACKAGE_NOT_FOUND') });
            }

            // ✅ Check if patient is either the direct recipient OR in the household
            const isPatientReceiver = packageData.patientReceiver?.patient === patientId;
            const isHouseholdReceiver = packageData.householdReceiver?.patients?.some(
                (p: any) => p.id === patientId
            );

            if (!isPatientReceiver && !isHouseholdReceiver) {
                return res.status(403).json({ message: i18n.translate('errors.UNAUTHORIZED') });
            }

            return res.status(200).json({
                success: true,
                message: i18n.translate('success.SUCCESSFULLY_FETCHED_PACKAGE_DETAILS'),
                data: packageData,
            });
        } catch (e) {
            console.error(e);
            return res.status(500).json({ message: i18n.translate('errors.INTERNAL_SERVER_ERROR') });
        }
    }

    @Get('/filter')
    @UseGuards(AuthenticationMiddleware)
    async getFilteredPackagesList(
        @Req() req: AuthenticatedRequest,
        @I18n() i18n: I18nContext,
        @Res() res: Response,
        @NestQuery('searchQuery') searchQuery?: string,
        @NestQuery('status') status?: string,
        @NestQuery('date') date?: string,      // ✅ new
        @NestQuery('period') period?: 'AM' | 'PM'  // ✅ new
    ) {
        try {
            const patientId = req.identity.id;
            const packages = await this.getFilteredPackages.execute({
                patientId,
                searchQuery,
                status,
                date,
                period,
            });

            return res.status(200).json({
                success: true,
                message: i18n.translate('success.SUCCESSFULLY_FETCHED_PACKAGES'),
                data: packages,
            });
        } catch (e) {
            console.error(e);
            return res.status(500).json({ message: i18n.translate('errors.INTERNAL_SERVER_ERROR') });
        }
    }


    // ✅ Get total package count
    @Get('/count')
    @UseGuards(AuthenticationMiddleware)
    async getPackageCount(
        @Req() req: AuthenticatedRequest,
        @I18n() i18n: I18nContext,  // ✅ Move required parameter before optional ones
        @Res() res: Response
    ) {
        try {
            const patientId = req.identity.id;
            const count = await this.getPatientPackageCount.execute({ patientId });

            return res.status(200).json({
                success: true,
                message: i18n.translate('success.SUCCESSFULLY_FETCHED_PACKAGE_COUNT'),
                count: count,
            });
        } catch (e) {
            console.error(e);
            return res.status(500).json({ message: i18n.translate('errors.INTERNAL_SERVER_ERROR') });
        }
    }

    @Get('/totalPrice')
    @UseGuards(AuthenticationMiddleware)
    async getTotalPackagePrice(
        @Req() req: AuthenticatedRequest,
        @I18n() i18n: I18nContext,
        @Res() res: Response
    ) {
        try {
            const patientId = req.identity.id;
            const totalPrice = await this.getPatientTotalPackagePrice.execute(patientId);

            return res.status(200).json({
                success: true,
                message: i18n.translate('success.SUCCESSFULLY_FETCHED_TOTAL_PRICE'),
                totalPrice: totalPrice,
            });
        } catch (e) {
            console.error(e);
            return res.status(500).json({ message: i18n.translate('errors.INTERNAL_SERVER_ERROR') });
        }
    }

}

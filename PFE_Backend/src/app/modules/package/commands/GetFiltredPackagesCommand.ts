import { IsOptional, IsString } from 'class-validator';

export class GetFiltredPackagesCommand {
    @IsString()
    patientId: string;

    @IsOptional()
    @IsString()
    searchQuery?: string;  // ✅ Search by pharmacy ID, address

    @IsOptional()
    @IsString()
    status?: string;  // ✅ Filter by package status

    @IsOptional()
    @IsString()
    date?: string;   // ✅ New field for date filter

    @IsOptional()
    @IsString()
    period?: 'AM' | 'PM';  // ✅ New field for period filter
}

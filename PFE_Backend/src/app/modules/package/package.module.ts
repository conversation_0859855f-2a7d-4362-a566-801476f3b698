import {forwardRef, Module} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Package, PackageSchema } from './entities/package.entity';
import {PackageController} from "./controllers/package.controller";
import {PackageRepository} from "./repositories/package.repository";
import {GetAllPackagesByPatient} from "./usecases/getAllPackagesByPatient";
import {GetPackageDetails} from "./usecases/getPackageDetails";
import {JwtModule} from "@nestjs/jwt";
import {UserModule} from "../user/user.module";
import {GetFilteredPackages} from "./usecases/GetFilteredPackages";
import {GetPatientPackageCount} from "./usecases/GetPatientPackageCount";
import {GetPatientTotalPackagePrice} from "./usecases/GetPatientTotalPackagePrice";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Package", schema: PackageSchema },
        ]),
        JwtModule.register({
            secret: "reallysecurekey",
            signOptions: { expiresIn: "1y" }
        }),
        forwardRef(() => UserModule),
    ],
    controllers: [PackageController],
    providers: [PackageRepository, GetAllPackagesByPatient, GetPackageDetails,GetFilteredPackages,GetPatientPackageCount,GetPatientTotalPackagePrice],
    exports: [],
})
export class PackageModule {}

import { Injectable } from "@nestjs/common";
import { PackageRepository } from "../repositories/package.repository";
import { GetFiltredPackagesCommand } from "../commands/GetFiltredPackagesCommand";

@Injectable()
export class GetFilteredPackages {
    constructor(private readonly packageRepository: PackageRepository) {}

    async execute(command: GetFiltredPackagesCommand) {
        return this.packageRepository.findByFilters(
            command.patientId,
            command.searchQuery,
            command.status,
            command.date,
            command.period  // ✅ Add period
        );
    }
}

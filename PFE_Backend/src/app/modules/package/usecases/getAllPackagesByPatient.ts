import { Injectable } from '@nestjs/common';
import { PackageRepository } from '../repositories/package.repository';

@Injectable()
export class GetAllPackagesByPatient {
    constructor(private packageRepository: PackageRepository) {}

    async execute(patientId: string, deliveryDate: string, deliveryPeriod?: 'AM' | 'PM') {
        return this.packageRepository.findByPatientIdDateAndTime(patientId, deliveryDate, deliveryPeriod);
    }
}

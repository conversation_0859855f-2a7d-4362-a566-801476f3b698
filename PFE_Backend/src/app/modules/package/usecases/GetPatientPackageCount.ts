import { Injectable } from "@nestjs/common";
import { PackageRepository } from "../repositories/package.repository";
import {GetPatientPackageCountCommand} from "../commands/GetPatientPackageCountCommand";

@Injectable()
export class GetPatientPackageCount {
    constructor(private readonly packageRepository: PackageRepository) {}

    async execute(command: GetPatientPackageCountCommand) {
        return this.packageRepository.countByPatientId(command.patientId);
    }
}

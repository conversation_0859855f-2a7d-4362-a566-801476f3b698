import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Category } from '../entities/category.entity';

@Injectable()
export class CategoryRepository {
    constructor(@InjectModel(Category.name) private readonly categoryModel: Model<Category>) {}

    // Create a new category
    async create(category: Partial<Category>): Promise<Category> {
        return this.categoryModel.create(category);
    }

    // Update an existing category by ID
    async update(id: string, update: Partial<Category>): Promise<Category | null> {
        return this.categoryModel.findOneAndUpdate({ id }, update, { new: true });
    }

    // Delete a category by ID
    async delete(id: string): Promise<void> {
        await this.categoryModel.deleteOne({ id });
    }

    // Find a category by its ID
    async findById(id: string): Promise<Category | null> {
        return this.categoryModel.findOne({ id });
    }

    // Find all categories
    async findAll(): Promise<Category[]> {
        return this.categoryModel.find().exec();
    }

    async findByName(name: string): Promise<Category | null> {
        return this.categoryModel.findOne({ name }).exec();
    }
}

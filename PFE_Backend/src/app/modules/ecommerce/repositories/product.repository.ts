import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product } from '../entities/product.entity';
import {CategoryRepository} from "./category.repository";
import { StockStatus } from '../../../../core/enums/ecommerce/product/stockStatus.enum';

@Injectable()
export class ProductRepository {
    constructor(@InjectModel(Product.name) private readonly productModel: Model<Product>,
                private readonly categoryRepo: CategoryRepository, // Inject CategoryRepository
    ) {}

    async create(product: Partial<Product>): Promise<Product> {
        return this.productModel.create(product);
    }

    async update(id: string, update: Partial<Product>): Promise<Product | null> {
        return this.productModel.findOneAndUpdate({ id }, update, { new: true });
    }

    async delete(id: string): Promise<void> {
        await this.productModel.deleteOne({ id });
    }

    async findById(id: string): Promise<Product | null> {
        return this.productModel.findOne({ id });
    }

    async findAll(): Promise<Product[]> {
        return this.productModel.find().exec();
    }

    async findWithFilters(
        search?: string,
        categoryId?: string,  // Filtering by category ID now
        minPrice?: number,
        maxPrice?: number
    ): Promise<Product[]> {
        const filters: any = {};

        if (search) {
            filters.name = { $regex: new RegExp(search, 'i') };
        }

        if (categoryId) {
            filters.categoryId = categoryId;  // Filter by categoryId
        }

        if (minPrice !== undefined || maxPrice !== undefined) {
            filters.price = {};
            if (minPrice !== undefined) {
                filters.price.$gte = minPrice;
            }
            if (maxPrice !== undefined) {
                filters.price.$lte = maxPrice;
            }
        }

        return this.productModel.find(filters).sort({ createdAt: -1 }).exec();
    }



    async findForPharmacyWithFilters(
        pharmacyId: string,
        search?: string,
        stockStatus?: string,
        actif?: boolean
    ): Promise<Product[]> {
        const filters: any = { pharmacyId };

        if (search) {
            filters.name = { $regex: new RegExp(search, 'i') };
        }

        if (stockStatus !== undefined) {
            filters.stockStatus = stockStatus;
        }

        if (actif !== undefined) {
            filters.actif = actif;
        }

        return this.productModel.find(filters).sort({ createdAt: -1 }).exec();
    }

    async decrementQuantity(productId: string, quantityToDecrement: number): Promise<Product | null> {
        const product = await this.findById(productId);
        if (!product) {
            throw new Error('Product not found');
        }

        if (product.quantity < quantityToDecrement) {
            throw new Error('Insufficient product quantity');
        }

        const newQuantity = product.quantity - quantityToDecrement;

        return this.productModel.findOneAndUpdate(
            { id: productId },
            {
                quantity: newQuantity,
                stockStatus: newQuantity === 0 ? StockStatus.OUT_OF_STOCK : StockStatus.IN_STOCK
            },
            { new: true }
        );
    }


}

// src/ecommerce/repositories/order.repository.ts
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Order } from '../entities/order.entity';

@Injectable()
export class OrderRepository {
    constructor(
        @InjectModel(Order.name) private readonly orderModel: Model<Order>,
    ) {}

    async create(order: Partial<Order>): Promise<Order> {
        return this.orderModel.create(order);
    }

    async update(id: string, update: Partial<Order>): Promise<Order | null> {
        return this.orderModel.findOneAndUpdate({ id }, update, { new: true });
    }

    async delete(id: string): Promise<void> {
        await this.orderModel.deleteOne({ id });
    }

    async findById(id: string): Promise<Order | null> {
        return this.orderModel.findOne({ id });
    }

    async findBasketByPatientId(patientId: string): Promise<Order | null> {
        return this.orderModel.findOne({
            patientId,
            status: { $ne: 'DELIVERED' },
            $or: [
                { flag: false },
                { flag: true, status: { $in: ['APPROVED', 'PENDING'] } },
            ],
        }).sort({ createdAt: -1 });

    }

    async findAllByPatientId(patientId: string): Promise<Order[]> {
        return this.orderModel.find({ patientId }).sort({ createdAt: -1 });
    }

    async findActiveOrderByProductId(productId: string): Promise<Order | null> {
        return this.orderModel.findOne({
            productIds: productId,
            flag: true,
            status: { $ne: 'DELIVERED' },
        });
    }
    async isProductInActiveBasketOrOrder(productId: string): Promise<boolean> {
        const order = await this.orderModel.findOne({
            productIds: productId,
            status: { $ne: 'DELIVERED' },
        });
        return !!order;
    }
    async findByStatus(patientId: string, status: string): Promise<Order[]> {
        return this.orderModel.find({
            patientId,
            status: status,
        }).sort({ createdAt: -1 });
    }

    async findOneByFilter(filter: Record<string, any>): Promise<Order | null> {
        return this.orderModel.findOne(filter).sort({ createdAt: -1 });
    }

    async findByFilters(filters: any): Promise<Order[]> {
        return this.orderModel.find(filters).sort({ createdAt: -1 });
    }

}

import {forwardRef, Module} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Product, ProductSchema } from './entities/product.entity';
import { Order, OrderSchema } from './entities/order.entity';
import {ProductRepository} from "./repositories/product.repository";
import {ProductCreate} from "./usecases/product/product.create";
import {ProductUpdate} from "./usecases/product/product.update";
import {ProductDelete} from "./usecases/product/product.delete";
import {ProductGetAll} from "./usecases/product/product.getAll";
import {ProductGetById} from "./usecases/product/product.getById";
import {ProductController} from "./controllers/product.controller";
import {JwtModule} from "@nestjs/jwt";
import {UserModule} from "../user/user.module";
import {PharmacyModule} from "../pharmacy/pharmacy.module";
import {ProductUploadFile} from "./usecases/product/product.uploadFile";
import {Category, CategorySchema} from "./entities/category.entity";
import {CategoryRepository} from "./repositories/category.repository";
import {CategoryCreate} from "./usecases/category/category.create";
import {CategoryController} from "./controllers/category.controller";
import {CategoryUpdate} from "./usecases/category/category.update";
import {CategoryDelete} from "./usecases/category/category.delete";
import {CategoryGetAll} from "./usecases/category/category.getAll";
import {CategoryGetById} from "./usecases/category/category.getById";
import {OrderRepository} from "./repositories/order.repository";
import {OrderAddShop} from "./usecases/order/order.addShop";
import {OrderDeleteShop} from "./usecases/order/order.deleteShop";
import {OrderController} from "./controllers/order.controller";
import {OrderGetBasket} from "./usecases/order/order.getBasket";
import {OrderConfirm} from "./usecases/order/order.confirm";
import {OrderMarkDelivered} from "./usecases/order/order.markDelivered";
import {OrderGetHistory} from "./usecases/order/order.getHistory";
import {ProductGetAllForPharmacy} from "./usecases/product/web/product.getAllForPharmacy";
import {OrderFilter} from "./usecases/order/web/OrderFilter";
import {GetOrderById} from "./usecases/order/web/getOrderById";
import {PatientModule} from "../patient/patient.module";
import {OrderPharmacistConfirm} from "./usecases/order/web/OrderPharmacistConfirm";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: Product.name, schema: ProductSchema },
            { name: Order.name, schema: OrderSchema },
            { name: Category.name, schema: CategorySchema },

        ]),
        JwtModule.register({
            secret: 'reallysecurekey',
            signOptions: { expiresIn: '1y' },
        }),
        forwardRef(() => UserModule),
        forwardRef(() => PharmacyModule),
        forwardRef(() => PatientModule),


    ],
    controllers: [ProductController,CategoryController,OrderController],
    providers: [ProductRepository,ProductGetAllForPharmacy,ProductCreate,ProductUpdate,ProductDelete,ProductGetAll,ProductGetById,ProductUploadFile,CategoryRepository,CategoryCreate,CategoryUpdate,CategoryDelete,CategoryGetAll,CategoryGetById,OrderRepository,OrderAddShop,OrderDeleteShop,OrderGetBasket,OrderConfirm,OrderMarkDelivered,OrderGetHistory,OrderFilter,GetOrderById,OrderPharmacistConfirm],
    exports: [],
})
export class EcommerceModule {}

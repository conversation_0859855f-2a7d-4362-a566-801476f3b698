// src/ecommerce/usecases/order/order.deleteShop.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { OrderRepository } from '../../repositories/order.repository';

@Injectable()
export class OrderDeleteShop {
    constructor(private readonly orderRepo: OrderRepository) {}

    async execute(patientId: string, productId: string) {
        const basket = await this.orderRepo.findBasketByPatientId(patientId);

        if (!basket) {
            throw new NotFoundException('Panier introuvable');
        }

        const updatedProducts = basket.productIds.filter(id => id !== productId);

        await this.orderRepo.update(basket.id, {
            productIds: updatedProducts,
        });

        return {
            message: 'Produit supprimé du panier',
        };
    }
}

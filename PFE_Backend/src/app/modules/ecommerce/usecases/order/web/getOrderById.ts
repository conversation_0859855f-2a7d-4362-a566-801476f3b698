import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import {OrderRepository} from "../../../repositories/order.repository";

@Injectable()
export class GetOrderById {
    constructor(private readonly orderRepo: OrderRepository) {}

    async execute(orderId: string, user: any) {
        const allowedRoles = [4, 6]; // INTERNAL_PHARMACY, ADMIN
        const role = Number(user?.role);
        if (!allowedRoles.includes(role)) {
            throw new ForbiddenException('Access denied');
        }

        const order = await this.orderRepo.findById(orderId);
        if (!order) throw new NotFoundException('Order not found');
        return order;
    }
}

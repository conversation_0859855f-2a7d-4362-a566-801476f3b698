import { Injectable, ForbiddenException } from '@nestjs/common';
import {OrderRepository} from "../../../repositories/order.repository";
import {PatientRepository} from "../../../../patient/repositories/patient.repository";
import {OrderFilterCommand} from "../../../commands/order/web/OrderFilterCommand";

@Injectable()
export class OrderFilter {
    constructor(
        private readonly orderRepo: OrderRepository,
        private readonly patientRepo: PatientRepository,
    ) {}

    async execute(command: OrderFilterCommand, user: any) {
        const allowedRoles = [4, 6]; // INTERNAL_PHARMACY, ADMIN
        const role = Number(user?.role);
        if (!allowedRoles.includes(role)) {
            throw new ForbiddenException('Access denied');
        }

        const filters: any = {};

        const dateToUse = command.date ? new Date(command.date) : new Date();
        const startOfDay = new Date(dateToUse.setHours(0, 0, 0, 0));
        const endOfDay = new Date(dateToUse.setHours(24, 0, 0, 0));
        filters.orderDate = { $gte: startOfDay, $lt: endOfDay };

        if (command.status) {
            filters.status = command.status;
        }

        if (command.hasHousehold !== undefined) {
            const patients = await this.patientRepo.findAll();
            const patientIdsWithHouseholds = patients
                .filter(p => !!p.houseHoldId)
                .map(p => p.id);

            filters.patientId =
                command.hasHousehold === 'true'
                    ? { $in: patientIdsWithHouseholds }
                    : { $nin: patientIdsWithHouseholds };
        }
        // ✅ Only include orders with non-empty productIds
        filters.productIds = { $ne: [] };
        const orders = await this.orderRepo.findByFilters(filters);
        return { orders, total: orders.length };
    }
}

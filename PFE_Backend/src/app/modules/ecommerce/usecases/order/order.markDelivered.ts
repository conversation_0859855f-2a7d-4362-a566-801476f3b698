// src/ecommerce/usecases/order/order.markDelivered.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { OrderRepository } from '../../repositories/order.repository';
import {OrderStatus} from "../../../../../core/enums/ecommerce/order/orderStatus.enum";

@Injectable()
export class OrderMarkDelivered {
    constructor(private readonly orderRepo: OrderRepository) {}

    async execute(orderId: string) {
        const order = await this.orderRepo.findById(orderId);

        if (!order) throw new NotFoundException('Commande introuvable');

        return await this.orderRepo.update(orderId, {
            status: OrderStatus.DELIVERED,
        });
    }
}

// src/ecommerce/usecases/order/order.getHistory.ts
import { Injectable } from '@nestjs/common';
import { OrderRepository } from '../../repositories/order.repository';
import {OrderStatus} from "../../../../../core/enums/ecommerce/order/orderStatus.enum";

@Injectable()
export class OrderGetHistory {
    constructor(private readonly orderRepo: OrderRepository) {}

    async execute(patientId: string) {
        return this.orderRepo.findByStatus(patientId, OrderStatus.DELIVERED);
    }
}

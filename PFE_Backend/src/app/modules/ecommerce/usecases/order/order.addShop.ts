import {
    BadRequestException,
    Injectable,
    NotFoundException,
} from '@nestjs/common';
import { OrderRepository } from '../../repositories/order.repository';
import { ProductRepository } from '../../repositories/product.repository';
import { OrderAddShopCommand } from '../../commands/order/order.addShopCommand';
import { v4 as uuidv4 } from 'uuid';
import { OrderStatus } from '../../../../../core/enums/ecommerce/order/orderStatus.enum';

@Injectable()
export class OrderAddShop {
    constructor(
        private readonly orderRepo: OrderRepository,
        private readonly productRepo: ProductRepository,
    ) {}

    async execute(command: OrderAddShopCommand) {
        const { patientId, productId } = command;

        // ✅ 1. Ensure the product exists
        const product = await this.productRepo.findById(productId);
        if (!product) {
            throw new NotFoundException('Produit introuvable');
        }

        const price = product.price;

        // ✅ 2. Check if the product is already in this user's basket
        const basket = await this.orderRepo.findBasketByPatientId(patientId);
        if (basket && basket.productIds.includes(productId)) {
            throw new BadRequestException('Ce produit est déjà dans votre panier.');
        }

        // ✅ 3. Check if user has an ongoing approved order (not yet delivered)
        const existingApprovedOrder = await this.orderRepo.findOneByFilter({
            patientId,
            flag: true, // already confirmed
            status: OrderStatus.APPROVED, // approved but not delivered yet
        });

        if (existingApprovedOrder) {
            throw new BadRequestException(
                'Une commande est déjà en attente de livraison. Veuillez attendre sa livraison avant d’ajouter un nouveau produit.',
            );
        }

        // ✅ 4. No basket → Create new basket
        if (!basket) {
            return await this.orderRepo.create({
                id: uuidv4(),
                patientId,
                status: OrderStatus.PENDING,
                flag: false,
                orderDate: new Date(),
                totalAmount: price,
                productIds: [productId],
                quantity: 1,
            });
        }

        // ✅ 5. Basket exists → Replace existing product
        return await this.orderRepo.update(basket.id, {
            productIds: [productId],
            totalAmount: price,
        });
    }
}

import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { OrderRepository } from '../../repositories/order.repository';
import { ProductRepository } from '../../repositories/product.repository';
import { OrderConfirmCommand } from '../../commands/order/order.confirmCommand';
import { OrderStatus } from '../../../../../core/enums/ecommerce/order/orderStatus.enum';

@Injectable()
export class OrderConfirm {
    constructor(
        private readonly orderRepo: OrderRepository,
        private readonly productRepo: ProductRepository,
    ) {}

    async execute(command: OrderConfirmCommand) {
        const { patientId, quantity, deliveryManNote } = command;

        const basket = await this.orderRepo.findBasketByPatientId(patientId);
        if (!basket) throw new NotFoundException('Panier introuvable');
        if (basket.productIds.length === 0) throw new BadRequestException('Aucun produit dans le panier');

        const product = await this.productRepo.findById(basket.productIds[0]);
        if (!product) throw new NotFoundException('Produit introuvable');

        const price = product.price;
        const discount = product.discount ?? 0;

        const discountedPrice = price - (price * (discount / 100));
        const totalAmount = discountedPrice * quantity;

        // Decrement product quantity
        await this.productRepo.decrementQuantity(product.id, quantity);

        return await this.orderRepo.update(basket.id, {
            flag: true,
            status: OrderStatus.APPROVED,
            quantity,
            deliveryManNote,
            totalAmount,
            orderDate: new Date(),
        });
    }
}

// src/ecommerce/usecases/category/category.getAll.ts
import { Injectable } from '@nestjs/common';
import { CategoryRepository } from '../../repositories/category.repository';
import { Category } from '../../entities/category.entity';

@Injectable()
export class CategoryGetAll {
    constructor(private readonly categoryRepo: CategoryRepository) {}

    async execute(): Promise<Category[]> {
        return this.categoryRepo.findAll();
    }
}

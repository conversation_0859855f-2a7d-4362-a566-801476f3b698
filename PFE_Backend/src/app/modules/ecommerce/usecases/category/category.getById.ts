// src/ecommerce/usecases/category/category.getById.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { CategoryRepository } from '../../repositories/category.repository';
import { Category } from '../../entities/category.entity';

@Injectable()
export class CategoryGetById {
    constructor(private readonly categoryRepo: CategoryRepository) {}

    async execute(id: string): Promise<Category> {
        const category = await this.categoryRepo.findById(id);
        if (!category) {
            throw new NotFoundException('Category not found');
        }
        return category;
    }
}

// src/ecommerce/usecases/category/category.update.ts
import { Injectable } from '@nestjs/common';
import { CategoryRepository } from '../../repositories/category.repository';
import { Category } from '../../entities/category.entity';
import {CategoryUpdateCommand} from "../../commands/category/category.updateCommand";

@Injectable()
export class CategoryUpdate {
    constructor(private readonly categoryRepo: CategoryRepository) {}

    async execute(command: CategoryUpdateCommand): Promise<Category> {
        const { id, name } = command;

        // Update the category using the repository method
        const updatedCategory = await this.categoryRepo.update(id, { name });

        if (!updatedCategory) {
            throw new Error('Category not found');
        }

        return updatedCategory;
    }
}

import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { CategoryRepository } from '../../repositories/category.repository';
import { Category } from '../../entities/category.entity';
import {CategoryCreateCommand} from "../../commands/category/category.createCommand";

@Injectable()
export class CategoryCreate {
    constructor(private readonly categoryRepo: CategoryRepository) {}

    async execute(command: CategoryCreateCommand): Promise<Category> {
        const { name } = command;
        const categoryToCreate = {
            id: uuidv4(),
            name,
        };

        return await this.categoryRepo.create(categoryToCreate);
    }
}

import { Injectable, NotFoundException } from '@nestjs/common';
import { CategoryRepository } from '../../repositories/category.repository';

@Injectable()
export class CategoryDelete {
    constructor(private readonly categoryRepo: CategoryRepository) {}

    async execute(categoryId: string): Promise<void> {
        const existingCategory = await this.categoryRepo.findById(categoryId);

        if (!existingCategory) {
            throw new NotFoundException('Category not found');
        }

        await this.categoryRepo.delete(categoryId);
    }
}

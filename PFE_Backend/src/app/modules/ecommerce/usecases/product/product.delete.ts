import { Injectable, NotFoundException } from '@nestjs/common';
import { ProductRepository } from '../../repositories/product.repository';

@Injectable()
export class ProductDelete {
    constructor(private readonly productRepo: ProductRepository) {}

    async execute(productId: string): Promise<void> {
        const existing = await this.productRepo.findById(productId);
        if (!existing) {
            throw new NotFoundException('Produit non trouvé');
        }

        await this.productRepo.delete(productId);
    }
}

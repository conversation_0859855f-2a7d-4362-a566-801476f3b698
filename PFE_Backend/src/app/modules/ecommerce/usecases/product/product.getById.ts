import { Injectable, NotFoundException } from '@nestjs/common';
import {ProductRepository} from "../../repositories/product.repository";

@Injectable()
export class ProductGetById {
    constructor(private readonly productRepo: ProductRepository) {}

    async execute(productId: string) {
        const product = await this.productRepo.findById(productId);
        if (!product) {
            throw new NotFoundException('Produit non trouvé');
        }
        return product;
    }
}

// src/ecommerce/usecases/product/product.getAllForPharmacy.ts
import { Injectable } from '@nestjs/common';
import {ProductRepository} from "../../../repositories/product.repository";
import {ProductGetAllForPharmacyCommand} from "../../../commands/product/web/ProductGetAllForPharmacyCommand";

@Injectable()
export class ProductGetAllForPharmacy {
    constructor(private readonly productRepo: ProductRepository) {}

    async execute(command: ProductGetAllForPharmacyCommand, pharmacyId: string) {
        const filters: any = { pharmacyId };

        if (command.search) {
            filters.name = { $regex: new RegExp(command.search, 'i') };
        }

        if (command.stockStatus !== undefined) {
            filters.stockStatus = command.stockStatus;
        }

        if (command.actif !== undefined) {
            filters.actif = command.actif;
        }

        const products = await this.productRepo.findForPharmacyWithFilters(
            pharmacyId,
            command.search,
            command.stockStatus,
            command.actif
        );

        return {
            products,
            total: products.length,
        };
    }
}

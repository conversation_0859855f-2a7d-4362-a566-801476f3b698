// src/ecommerce/usecases/product/product.create.ts
import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { ProductRepository } from '../../repositories/product.repository';
import { ProductCreateCommand } from '../../commands/product/product.createCommand';
import {StockStatus} from "../../../../../core/enums/ecommerce/product/stockStatus.enum";

@Injectable()
export class ProductCreate {
    constructor(private readonly productRepo: ProductRepository) {}

    async execute(command: ProductCreateCommand, pharmacyId: string) {
        const productToCreate = {
            id: uuidv4(),
            pharmacyId,
            name: command.name,
            description: command.description,
            quantity: command.quantity,
            price: command.price,
            discount: command.discount ?? 0,
            storagePath: command.storagePath ?? [],
            stockStatus: command.quantity > 0 ? StockStatus.IN_STOCK : StockStatus.OUT_OF_STOCK,
            categoryId: command.categoryId,
            expiryDate: command.expiryDate,
            supplier: command.supplier,
            barcode: command.barcode,
            isTaxable: command.isTaxable ?? false,
            brand: command.brand,
            actif: command.actif ?? false,
        };


        return await this.productRepo.create(productToCreate);
    }
}

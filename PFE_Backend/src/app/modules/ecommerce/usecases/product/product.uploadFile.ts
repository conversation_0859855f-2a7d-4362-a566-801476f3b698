import { Injectable } from '@nestjs/common';
import { ProductUploadFileCommand } from '../../commands/product/product.uploadFileCommand';

@Injectable()
export class ProductUploadFile {
    async execute(command: ProductUploadFileCommand): Promise<{
        filePath: string;
        fileSize: number;
    }> {
        return {
            filePath: command.filePath,
            fileSize: command.fileSize,
        };
    }
}

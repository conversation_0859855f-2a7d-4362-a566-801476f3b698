// src/ecommerce/usecases/product/product.update.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { ProductRepository } from '../../repositories/product.repository';
import { ProductUpdateCommand } from '../../commands/product/product.updateCommand';
import {StockStatus} from "../../../../../core/enums/ecommerce/product/stockStatus.enum";

@Injectable()
export class ProductUpdate {
    constructor(private readonly productRepo: ProductRepository) {}

    async execute(command: ProductUpdateCommand): Promise<any> {
        const existingProduct = await this.productRepo.findById(command.id);

        if (!existingProduct) {
            throw new NotFoundException('Produit non trouvé');
        }

        // Prepare the product to be updated
        const productToUpdate: Partial<typeof existingProduct> = {
            name: command.name,
            description: command.description,
            price: command.price,
            discount: command.discount,
            quantity: command.quantity,
            storagePath: command.storagePath,
            stockStatus: command.quantity !== undefined ? (command.quantity > 0 ? StockStatus.IN_STOCK : StockStatus.OUT_OF_STOCK) : command.stockStatus,
            categoryId: command.categoryId,
            expiryDate: command.expiryDate,
            supplier: command.supplier,
            barcode: command.barcode,
            isTaxable: command.isTaxable,
            brand: command.brand,
            actif: command.actif,
        };


        return await this.productRepo.update(command.id, productToUpdate);
    }
}

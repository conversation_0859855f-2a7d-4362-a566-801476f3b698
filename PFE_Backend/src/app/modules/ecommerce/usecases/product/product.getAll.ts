// src/ecommerce/usecases/product/product.getAll.ts
import { Injectable } from '@nestjs/common';
import { ProductRepository } from '../../repositories/product.repository';
import { ProductGetAllCommand } from '../../commands/product/product.getAllCommand';

@Injectable()
export class ProductGetAll {
    constructor(private readonly productRepo: ProductRepository) {}

    async execute(command: ProductGetAllCommand) {
        const filtered = await this.productRepo.findWithFilters(
            command.search,
            command.categoryId,  // Now we pass categoryId instead of categoryName
            command.minPrice,
            command.maxPrice,
        );

        return {
            products: filtered,
            total: filtered.length,
        };
    }
}

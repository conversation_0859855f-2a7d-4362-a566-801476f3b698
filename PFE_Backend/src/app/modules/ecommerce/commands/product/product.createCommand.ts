// src/ecommerce/commands/product/product.createCommand.ts
import {IsNotEmpty, IsNumber, IsOptional, IsString, IsBoolean, IsEnum, IsArray} from 'class-validator';
import { StockStatus } from '../../../../../core/enums/ecommerce/product/stockStatus.enum';

export class ProductCreateCommand {
    @IsNotEmpty() name!: string;
    @IsOptional() description?: string;
    @IsNotEmpty() @IsNumber() quantity!: number;
    @IsNotEmpty() @IsNumber() price!: number;
    @IsOptional() @IsNumber() discount?: number;
    @IsOptional() @IsArray() storagePath?: string[];
    @IsOptional() @IsEnum(StockStatus) stockStatus?: StockStatus;
    @IsNotEmpty() @IsString() categoryId!: string;  // Single category ID
    @IsOptional() expiryDate?: Date;
    @IsOptional() supplier?: string;
    @IsOptional() barcode?: string;
    @IsOptional() @IsBoolean() isTaxable?: boolean;
    @IsOptional() brand?: string;
    @IsOptional() @IsBoolean() actif?: boolean;

}

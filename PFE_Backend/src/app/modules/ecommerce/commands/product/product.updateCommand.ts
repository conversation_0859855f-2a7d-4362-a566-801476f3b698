// src/ecommerce/commands/product/product.updateCommand.ts
import {IsOptional, IsString, IsNumber, IsBoolean, IsArray, IsEnum, IsNotEmpty} from 'class-validator';
import { StockStatus } from '../../../../../core/enums/ecommerce/product/stockStatus.enum';

export class ProductUpdateCommand {
    @IsNotEmpty() id!: string;

    @IsOptional() name?: string;
    @IsOptional() description?: string;
    @IsOptional() @IsNumber() price?: number;
    @IsOptional() @IsNumber() quantity?: number;
    @IsOptional() @IsNumber() discount?: number;
    @IsOptional() @IsArray() storagePath?: string[];
    @IsOptional() @IsEnum(StockStatus) stockStatus?: StockStatus;
    @IsOptional() @IsString() categoryId?: string;  // Single category ID
    @IsOptional() expiryDate?: Date;
    @IsOptional() supplier?: string;
    @IsOptional() barcode?: string;
    @IsOptional() @IsBoolean() isTaxable?: boolean;
    @IsOptional() brand?: string;
    @IsOptional() @IsBoolean() actif?: boolean;

}

// src/ecommerce/commands/product/product.getAllForPharmacyCommand.ts
import { IsOptional, IsEnum, IsBoolean } from 'class-validator';
import {StockStatus} from "../../../../../../core/enums/ecommerce/product/stockStatus.enum";

export class ProductGetAllForPharmacyCommand {
    @IsOptional()
    search?: string;

    @IsOptional()
    @IsEnum(StockStatus)
    stockStatus?: StockStatus;

    @IsOptional()
    @IsBoolean()
    actif?: boolean;
}

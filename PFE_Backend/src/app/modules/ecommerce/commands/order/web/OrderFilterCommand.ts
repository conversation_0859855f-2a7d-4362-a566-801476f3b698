import { IsOptional, IsEnum, IsBooleanString } from 'class-validator';
import {OrderStatus} from "../../../../../../core/enums/ecommerce/order/orderStatus.enum";

export class OrderFilterCommand {
    @IsOptional()
    date?: string; // Format: YYYY-MM-DD

    @IsOptional()
    @IsEnum(OrderStatus)
    status?: OrderStatus;

    @IsOptional()
    @IsBooleanString()
    hasHousehold?: string; // 'true', 'false', or undefined
}

// src/ecommerce/entities/product.entity.ts
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { StockStatus } from "../../../../core/enums/ecommerce/product/stockStatus.enum";
import { v4 as uuidv4 } from "uuid";

@Schema({ timestamps: true })
export class Product extends Document {
    @Prop({ required: true, unique: true, default: uuidv4 })
    id: string;

    @Prop({ required: true })
    pharmacyId: string;

    @Prop({ required: true })
    name: string;

    @Prop()
    description: string;

    @Prop({ required: true })
    quantity: number;

    @Prop({ required: true })
    price: number;

    @Prop({ default: 0 })
    discount: number;

    @Prop({ type: [String] })
    storagePath: string[];

    @Prop({ enum: StockStatus })
    stockStatus: StockStatus;

    @Prop({ required: true }) // Single categoryId instead of an array
    categoryId: string;  // This will hold the category ID

    @Prop()
    expiryDate: Date;

    @Prop()
    supplier: string;

    @Prop()
    barcode: string;

    @Prop({ default: false })
    isTaxable: boolean;

    @Prop()
    brand: string;

    @Prop({ default: false })
    actif: boolean;

}

export const ProductSchema = SchemaFactory.createForClass(Product);

// order.entity.ts
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { OrderStatus } from '../../../../core/enums/ecommerce/order/orderStatus.enum';

@Schema({ timestamps: true })
export class Order extends Document {
    @Prop({ required: true, unique: true })
    id: string;

    @Prop({ required: true })
    patientId: string;

    @Prop({ enum: OrderStatus, required: true })
    status: OrderStatus; // e.g. PENDING, CONFIRMED, CANCELLED, etc.

    @Prop({ required: true })
    orderDate: Date;

    @Prop({ required: true })
    totalAmount: number;

    @Prop({ type: [String], required: true })
    productIds: string[]; // IDs of the products in the order/basket

    @Prop({ default: false })
    flag: boolean; // false = in basket, true = ordered

    @Prop({ required: true })
    quantity: number;

    @Prop()
    deliveryManNote?: string;

}

export const OrderSchema = SchemaFactory.createForClass(Order);

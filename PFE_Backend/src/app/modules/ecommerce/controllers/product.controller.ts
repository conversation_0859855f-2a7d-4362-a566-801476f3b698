import {
    Body,
    Controller,
    Post,
    Put,
    Delete,
    Get,
    Req,
    Res,
    Param,
    Query,
    UseGuards, UseInterceptors, UploadedFile,
} from '@nestjs/common';
import { Response } from 'express';
import { AuthenticationMiddleware } from '../../../middlewares/authenticationMiddleware';
import {ProductCreate} from "../usecases/product/product.create";
import {ProductUpdate} from "../usecases/product/product.update";
import {ProductDelete} from "../usecases/product/product.delete";
import {ProductGetAll} from "../usecases/product/product.getAll";
import {ProductGetById} from "../usecases/product/product.getById";
import {ProductCreateCommand} from "../commands/product/product.createCommand";
import {ProductUpdateCommand} from "../commands/product/product.updateCommand";
import {ProductGetAllCommand} from "../commands/product/product.getAllCommand";
import {PharmacyGetByEmail} from "../../pharmacy/usecases/Pharmacy.GetByEmail";
import {diskStorage} from "multer";
import {FileInterceptor} from "@nestjs/platform-express";
import {ProductUploadFileCommand} from "../commands/product/product.uploadFileCommand";
import {ProductUploadFile} from "../usecases/product/product.uploadFile";
import {ProductGetAllForPharmacy} from "../usecases/product/web/product.getAllForPharmacy";
import {ProductGetAllForPharmacyCommand} from "../commands/product/web/ProductGetAllForPharmacyCommand";
import {StockStatus} from "../../../../core/enums/ecommerce/product/stockStatus.enum";


@Controller('products')
export class ProductController {
    constructor(
        private readonly productCreate: ProductCreate,
        private readonly productUpdate: ProductUpdate,
        private readonly productDelete: ProductDelete,
        private readonly productGetAll: ProductGetAll,
        private readonly productGetById: ProductGetById,
        private readonly pharmacyGetByEmail: PharmacyGetByEmail,
        private readonly productUploadFile: ProductUploadFile,
        private readonly productGetAllForPharmacy: ProductGetAllForPharmacy,



    ) {}

    @Post('/uploadFile')
    @UseGuards(AuthenticationMiddleware)
    @UseInterceptors(
        FileInterceptor('file', {
            storage: diskStorage({
                destination: './uploads/products',
                filename: (req, file, cb) => {
                    const uniqueName = `${Date.now()}-${file.originalname}`;
                    cb(null, uniqueName);
                },
            }),
            fileFilter: (req: Request, file, cb) => {
                const userRole = Number((req as any).identity?.role);
                if (![4, 6].includes(userRole)) {
                    // ❌ Reject without throwing an error
                    return cb(null, false);
                }
                cb(null, true); // ✅ Accept the file
            },
        }),
    )
    async uploadProductImage(
        @UploadedFile() file: Express.Multer.File,
        @Req() req: any,
        @Res() res: Response,
    ) {
        if (!file) {
            return res.status(403).json({
                success: false,
                message: 'Vous n’avez pas la permission de téléverser une image.',
            });
        }

        const command = new ProductUploadFileCommand();
        command.filePath = `/uploads/products/${file.filename}`;
        command.fileSize = file.size;

        const result = await this.productUploadFile.execute(command);

        return res.status(200).json({
            success: true,
            message: 'Fichier téléchargé avec succès.',
            ...result,
        });
    }

    @Post('/create')
    @UseGuards(AuthenticationMiddleware)
    async createProduct(
        @Req() req: any,
        @Body() body: ProductCreateCommand,
        @Res() res: Response,
    ) {
        const userRole = req.identity.role;
        const userEmail = req.identity.email;



        if (![4, 6].includes(Number(userRole))) {
            return res.status(403).json({
                success: false,
                message: 'Vous n’avez pas la permission de créer un produit.',
            });
        }

        const pharmacy = await this.pharmacyGetByEmail.execute(userEmail);
        console.log('Pharmacy',pharmacy.id)


        const result = await this.productCreate.execute(body, pharmacy.id);

        return res.status(201).json({
            success: true,
            message: 'Product created successfully',
            product: result,
        });
    }



    @Put('/update/:id')
    @UseGuards(AuthenticationMiddleware)
    async updateProduct(
        @Param('id') id: string,
        @Req() req: any,
        @Body() body: Omit<ProductUpdateCommand, 'id'>,
        @Res() res: Response,
    ) {
        const userRole = req.identity.role;

        if (![4, 6].includes(Number(userRole))) {
            return res.status(403).json({
                success: false,
                message: 'Vous n’avez pas la permission de modifier un produit.',
            });
        }

        const command = new ProductUpdateCommand();
        command.id = id;
        Object.assign(command, body);

        const updated = await this.productUpdate.execute(command);

        return res.status(200).json({
            success: true,
            message: 'Product updated successfully',
            product: updated,
        });
    }


    @Delete('/delete/:id')
    @UseGuards(AuthenticationMiddleware)
    async deleteProduct(
        @Param('id') id: string,
        @Req() req: any,
        @Res() res: Response,
    ) {
        const userRole = Number(req.identity.role);

        if (![4, 6].includes(userRole)) {
            return res.status(403).json({
                success: false,
                message: 'Vous n’avez pas la permission de supprimer un produit.',
            });
        }

        await this.productDelete.execute(id);

        return res.status(200).json({
            success: true,
            message: 'Produit supprimé avec succès',
        });
    }

    @Get('/getAll')
    @UseGuards(AuthenticationMiddleware)
    async getAllProducts(
        @Req() req: any,
        @Res() res: Response,
        @Query('search') search?: string,
        @Query('categoryId') categoryId?: string,  // Accept categoryId as a query parameter
        @Query('minPrice') minPrice?: string,
        @Query('maxPrice') maxPrice?: string,
    ) {
        const command = new ProductGetAllCommand();
        command.search = search;
        command.categoryId = categoryId;  // Passing categoryId for filtering
        command.minPrice = minPrice ? parseFloat(minPrice) : undefined;
        command.maxPrice = maxPrice ? parseFloat(maxPrice) : undefined;

        const result = await this.productGetAll.execute(command);

        return res.status(200).json({
            success: true,
            total: result.total,
            products: result.products,
        });
    }

    @Get('/get/:id')
    @UseGuards(AuthenticationMiddleware)
    async getProductById(
        @Param('id') id: string,
        @Res() res: Response,
    ) {
        const product = await this.productGetById.execute(id);

        return res.status(200).json({
            success: true,
            product,
        });
    }

    /////////////////////////////////////////////web////////////////////////////////////////////////////////
    @Get('/getAllForPharmacy')
    @UseGuards(AuthenticationMiddleware)
    async getAllForPharmacy(
        @Req() req: any,
        @Res() res: Response,
        @Query('search') search?: string,
        @Query('stockStatus') stockStatus?: StockStatus,
        @Query('actif') actif?: string, // string to allow parsing
    ) {
        const userRole = Number(req.identity.role);
        const userEmail = req.identity.email;

        if (![4, 6].includes(userRole)) {
            return res.status(403).json({
                success: false,
                message: 'You do not have permission to access this resource.',
            });
        }

        const pharmacy = await this.pharmacyGetByEmail.execute(userEmail);

        const command = new ProductGetAllForPharmacyCommand();
        command.search = search;
        command.stockStatus = stockStatus as StockStatus;
        command.actif = actif !== undefined ? actif === 'true' : undefined;

        const result = await this.productGetAllForPharmacy.execute(command, pharmacy.id);

        return res.status(200).json({
            success: true,
            total: result.total,
            products: result.products,
        });
    }

}

import {
    Body,
    Controller, Delete, Get, Param,
    Post, Put,
    Req,
    Res,
    UseGuards,
} from '@nestjs/common';
import { Response } from 'express';
import { AuthenticationMiddleware } from '../../../middlewares/authenticationMiddleware';
import { CategoryCreate } from "../usecases/category/category.create";
import {CategoryCreateCommand} from "../commands/category/category.createCommand";
import {CategoryUpdate} from "../usecases/category/category.update";
import {CategoryUpdateCommand} from "../commands/category/category.updateCommand";
import {CategoryDelete} from "../usecases/category/category.delete";
import {CategoryGetAll} from "../usecases/category/category.getAll";
import {CategoryGetById} from "../usecases/category/category.getById";

@Controller('categories')
export class CategoryController {
    constructor(
        private readonly categoryCreate: CategoryCreate,
        private readonly categoryUpdate: CategoryUpdate,
        private readonly categoryDelete: CategoryDelete,
        private readonly categoryGetAll: CategoryGetAll,
        private readonly categoryGetById: CategoryGetById,


    ) {}

    // Create a new category
    @Post('/create')
    @UseGuards(AuthenticationMiddleware)
    async createCategory(
        @Req() req: any,
        @Body() body: CategoryCreateCommand,
        @Res() res: Response,
    ) {
        const userRole = req.identity.role;

        // Allow only pharmacists (4) or admins (6) to create a category
        if (![4, 6].includes(Number(userRole))) {
            return res.status(403).json({
                success: false,
                message: 'Vous n’avez pas la permission de créer une catégorie.',
            });
        }

        // Create the category using the CategoryCreate use case
        const result = await this.categoryCreate.execute(body);

        return res.status(201).json({
            success: true,
            message: 'Category created successfully',
            category: result,
        });
    }

    @Put('/update/:id')
    @UseGuards(AuthenticationMiddleware)
    async updateCategory(
        @Param('id') id: string,
        @Req() req: any,
        @Body() body: Omit<CategoryUpdateCommand, 'id'>,
        @Res() res: Response,
    ) {
        const userRole = req.identity.role;

        // Check if the user has the proper role to update a category (pharmacists or admins)
        if (![4, 6].includes(Number(userRole))) {
            return res.status(403).json({
                success: false,
                message: 'Vous n’avez pas la permission de modifier une catégorie.',
            });
        }

        // Initialize the command and assign the 'id' and other properties
        const command = new CategoryUpdateCommand();
        command.id = id;
        Object.assign(command, body);  // This copies other properties from the request body into the command

        try {
            const updatedCategory = await this.categoryUpdate.execute(command);

            return res.status(200).json({
                success: true,
                message: 'Category updated successfully',
                category: updatedCategory,
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: error.message || 'An error occurred while updating the category.',
            });
        }
    }

    @Delete('/delete/:id')
    @UseGuards(AuthenticationMiddleware)
    async deleteCategory(
        @Param('id') id: string,
        @Req() req: any,
        @Res() res: Response,
    ) {
        const userRole = Number(req.identity.role);

        // Check if the user has the proper role to delete a category (pharmacists or admins)
        if (![4, 6].includes(userRole)) {
            return res.status(403).json({
                success: false,
                message: 'Vous n’avez pas la permission de supprimer une catégorie.',
            });
        }

        try {
            // Call the delete use case
            await this.categoryDelete.execute(id);

            return res.status(200).json({
                success: true,
                message: 'Category deleted successfully',
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: error.message || 'An error occurred while deleting the category.',
            });
        }
    }

    @Get('/getAll')
    async getAllCategories(@Req() req: any, @Res() res: Response) {
        try {
            const categories = await this.categoryGetAll.execute();
            return res.status(200).json({
                success: true,
                categories,
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: error.message || 'An error occurred while fetching categories.',
            });
        }
    }

    // Get a category by ID
    @Get('/get/:id')
    async getCategoryById(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
        try {
            const category = await this.categoryGetById.execute(id);
            return res.status(200).json({
                success: true,
                category,
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: error.message || 'An error occurred while fetching the category.',
            });
        }
    }
}

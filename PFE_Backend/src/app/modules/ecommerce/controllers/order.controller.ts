// src/ecommerce/controllers/order.controller.ts
import {
    Body,
    Controller,
    Delete,
    Post,
    Req,
    UseGuards,
    Query, Get, Put, Param, Res, ForbiddenException,
} from '@nestjs/common';
import { OrderAddShopCommand } from '../commands/order/order.addShopCommand';
import { OrderAddShop } from '../usecases/order/order.addShop';
import { OrderDeleteShop } from '../usecases/order/order.deleteShop';
import { AuthenticationMiddleware } from '../../../middlewares/authenticationMiddleware';
import {OrderGetBasket} from "../usecases/order/order.getBasket";
import {OrderConfirm} from "../usecases/order/order.confirm";
import {OrderMarkDelivered} from "../usecases/order/order.markDelivered";
import {OrderGetHistory} from "../usecases/order/order.getHistory";
import {OrderConfirmCommand} from "../commands/order/order.confirmCommand";
import {OrderFilter} from "../usecases/order/web/OrderFilter";
import {OrderFilterCommand} from "../commands/order/web/OrderFilterCommand";
import {GetOrderById} from "../usecases/order/web/getOrderById";
import {OrderPharmacistConfirmCommand} from "../commands/order/web/OrderPharmacistConfirmCommand";
import {OrderPharmacistConfirm} from "../usecases/order/web/OrderPharmacistConfirm";

@Controller('basket')
export class OrderController {
    constructor(
        private readonly orderAddShop: OrderAddShop,
        private readonly orderDeleteShop: OrderDeleteShop,
        private readonly orderGetBasket: OrderGetBasket,
        private readonly orderConfirm: OrderConfirm,
        private readonly orderMarkDelivered: OrderMarkDelivered,
        private readonly orderGetHistory: OrderGetHistory,
        private readonly orderFilter: OrderFilter,
        private readonly getOrderById: GetOrderById,
        private readonly orderPharmacistConfirm:OrderPharmacistConfirm




    ) {}

    @Post('/add')
    @UseGuards(AuthenticationMiddleware)
    async addToBasket(
        @Req() req: any,
        @Body() body: OrderAddShopCommand,
    ) {
        const command = new OrderAddShopCommand();
        command.patientId = req.identity.id;
        command.productId = body.productId;

        const result = await this.orderAddShop.execute(command);

        return {
            success: true,
            message: 'Produit ajouté au panier.',
            basket: result,
        };
    }
    @Delete('/remove')
    @UseGuards(AuthenticationMiddleware)
    async removeFromBasket(
        @Req() req: any,
        @Query('productId') productId: string,
    ) {
        await this.orderDeleteShop.execute(req.identity.id, productId);

        return {
            success: true,
            message: 'Produit retiré du panier.',
        };
    }

    // ✅ Get current basket
    @Get('/get')
    @UseGuards(AuthenticationMiddleware)
    async getBasket(@Req() req: any) {
        const result = await this.orderGetBasket.execute(req.identity.id);
        return {
            success: true,
            basket: result,
        };
    }

    // ✅ Confirm order
    @Post('/confirm')
    @UseGuards(AuthenticationMiddleware)
    async confirmOrder(
        @Req() req: any,
        @Body() body: OrderConfirmCommand,
    ) {
        const command = new OrderConfirmCommand();
        command.patientId = req.identity.id;
        command.quantity = body.quantity;
        command.deliveryManNote = body.deliveryManNote;

        const result = await this.orderConfirm.execute(command);

        return {
            success: true,
            message: 'Commande confirmée avec succès.',
            order: result,
        };
    }

    @Put('/markDelivered/:id')
    @UseGuards(AuthenticationMiddleware)
    async markOrderAsDelivered(@Param('id') id: string, @Req() req: any) {
        const userRole = Number(req.identity.role);

        // ✅ Allow both admin (6) and pharmacist (4)
        if (![4, 6].includes(Number(userRole))) {
            return {
                success: false,
                message: 'Accès refusé',
            };
        }

        const updated = await this.orderMarkDelivered.execute(id);
        return {
            success: true,
            message: 'Commande marquée comme livrée',
            order: updated,
        };
    }

    @Get('/history')
    @UseGuards(AuthenticationMiddleware)
    async getOrderHistory(@Req() req: any) {
        const result = await this.orderGetHistory.execute(req.identity.id);
        return {
            success: true,
            total: result.length,
            orders: result,
        };
    }



    /////////////////////////////////////////////web/////////////////////////////////////////////////////


    @Get('/filter')
    @UseGuards(AuthenticationMiddleware)
    async filterOrders(
        @Req() req: any,
        @Query() query: OrderFilterCommand
    ) {
        const result = await this.orderFilter.execute(query, req.identity);
        return {
            success: true,
            total: result.total,
            orders: result.orders,
        };
    }



    @Get('getOrderById/:id')
    @UseGuards(AuthenticationMiddleware)
    async getOrder(@Param('id') id: string, @Req() req: any) {
        const result = await this.getOrderById.execute(id, req.identity);
        return {
            success: true,
            order: result,
        };
    }


    @Post('/PharmacistConfirm')
    @UseGuards(AuthenticationMiddleware)
    async pharmacistConfirmOrder(
        @Req() req: any,
        @Body() body: OrderPharmacistConfirmCommand,
    ) {
        // Pharmacist roles: validate role here if needed
        const allowedRoles = [4, 6]; // e.g., INTERNAL_PHARMACY, ADMIN
        const role = Number(req.identity.role);
        if (!allowedRoles.includes(role)) {
            throw new ForbiddenException('Accès non autorisé');
        }

        const command = new OrderPharmacistConfirmCommand();
        command.patientId = body.patientId;
        command.quantity = body.quantity;
        command.deliveryManNote = body.deliveryManNote;

        const result = await this.orderPharmacistConfirm.execute(command);

        return {
            success: true,
            message: 'Commande confirmée par la pharmacie.',
            order: result,
        };
    }



}

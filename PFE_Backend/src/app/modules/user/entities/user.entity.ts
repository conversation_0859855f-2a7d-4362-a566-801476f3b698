import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { Role } from "../../../../core/enums/user/Role";
import { v4 as uuidv4 } from "uuid";

@Schema({ timestamps: true })
export class User extends Document {
  @Prop({ required: true, unique: true, default: uuidv4 })
  id: string;

  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: true })
  password: string;

  @Prop({ type: String, enum: Role, default: Role.PATIENT })
  role: Role;

  @Prop({ required: true, unique: true })
  phone: string;

  @Prop({ required: false }) // Firebase Cloud Messaging Token
  fcm?: string;
}

export const UserSchema = SchemaFactory.createForClass(User);

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { NotificationType, NotificationStatus } from '../../../../core/notifications/notification.enums';

@Schema({ timestamps: true })
export class Notification extends Document {
    @Prop({ required: true, unique: true, default: uuidv4 })
    id: string;

    @Prop({ required: true })
    recipientId: string; // User ID who will receive the notification

    @Prop({ required: true })
    senderId?: string; // User ID who triggered the notification (optional)

    @Prop({ required: true })
    title: string;

    @Prop({ required: true })
    message: string;

    @Prop({ type: String, enum: NotificationType, required: true })
    type: NotificationType;

    @Prop({ type: String, enum: NotificationStatus, default: NotificationStatus.UNREAD })
    status: NotificationStatus;

    @Prop({ type: Object })
    data?: any; // Additional data related to the notification (prescription ID, package ID, etc.)

    @Prop({ default: false })
    fcmSent: boolean; // Whether FCM notification was sent successfully

    @Prop()
    fcmMessageId?: string; // FCM message ID for tracking

    @Prop()
    readAt?: Date; // When the notification was read

    @Prop()
    archivedAt?: Date; // When the notification was archived
}

export const NotificationSchema = SchemaFactory.createForClass(Notification);

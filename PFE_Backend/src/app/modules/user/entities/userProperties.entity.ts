import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class UserProperties extends Document {
    @Prop({ required: true, unique: true, index: true }) // ✅ Indexed for faster lookups
    id: string;

    @Prop({ type: String, required: false })
    profilePhoto?: string;

    @Prop({ type: String, required: false })
    resetCode?: string;

    @Prop({ type: Date, required: false })
    resetCodeExpiresAt?: Date;

    @Prop({ type: Boolean, default: false }) // Default: 2FA disabled
    isTwoFactorEnabled?: boolean;

    @Prop()
    twoFactorCode?: string;

    @Prop()
    twoFactorExpiresAt?: Date;
}

export const UserPropertiesSchema = SchemaFactory.createForClass(UserProperties);

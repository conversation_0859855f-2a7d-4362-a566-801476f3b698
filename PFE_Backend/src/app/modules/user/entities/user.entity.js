"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSchema = exports.User = void 0;
var mongoose_1 = require("@nestjs/mongoose");
var mongoose_2 = require("mongoose");
var Role_1 = require("../../../../core/enums/user/Role");
var uuid_1 = require("uuid");
var User = function () {
    var _classDecorators = [(0, mongoose_1.Schema)({ timestamps: true })];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _classSuper = mongoose_2.Document;
    var _id_decorators;
    var _id_initializers = [];
    var _id_extraInitializers = [];
    var _firstName_decorators;
    var _firstName_initializers = [];
    var _firstName_extraInitializers = [];
    var _lastName_decorators;
    var _lastName_initializers = [];
    var _lastName_extraInitializers = [];
    var _email_decorators;
    var _email_initializers = [];
    var _email_extraInitializers = [];
    var _password_decorators;
    var _password_initializers = [];
    var _password_extraInitializers = [];
    var _role_decorators;
    var _role_initializers = [];
    var _role_extraInitializers = [];
    var _profilePhoto_decorators;
    var _profilePhoto_initializers = [];
    var _profilePhoto_extraInitializers = [];
    var _resetCode_decorators;
    var _resetCode_initializers = [];
    var _resetCode_extraInitializers = [];
    var _resetCodeExpiresAt_decorators;
    var _resetCodeExpiresAt_initializers = [];
    var _resetCodeExpiresAt_extraInitializers = [];
    var _isTwoFactorEnabled_decorators;
    var _isTwoFactorEnabled_initializers = [];
    var _isTwoFactorEnabled_extraInitializers = [];
    var _twoFactorCode_decorators;
    var _twoFactorCode_initializers = [];
    var _twoFactorCode_extraInitializers = [];
    var _twoFactorExpiresAt_decorators;
    var _twoFactorExpiresAt_initializers = [];
    var _twoFactorExpiresAt_extraInitializers = [];
    var _phoneNumber_decorators;
    var _phoneNumber_initializers = [];
    var _phoneNumber_extraInitializers = [];
    var User = _classThis = /** @class */ (function (_super) {
        __extends(User_1, _super);
        function User_1() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.id = __runInitializers(_this, _id_initializers, void 0);
            _this.firstName = (__runInitializers(_this, _id_extraInitializers), __runInitializers(_this, _firstName_initializers, void 0));
            _this.lastName = (__runInitializers(_this, _firstName_extraInitializers), __runInitializers(_this, _lastName_initializers, void 0));
            _this.email = (__runInitializers(_this, _lastName_extraInitializers), __runInitializers(_this, _email_initializers, void 0));
            _this.password = (__runInitializers(_this, _email_extraInitializers), __runInitializers(_this, _password_initializers, void 0));
            _this.role = (__runInitializers(_this, _password_extraInitializers), __runInitializers(_this, _role_initializers, void 0));
            _this.profilePhoto = (__runInitializers(_this, _role_extraInitializers), __runInitializers(_this, _profilePhoto_initializers, void 0));
            _this.resetCode = (__runInitializers(_this, _profilePhoto_extraInitializers), __runInitializers(_this, _resetCode_initializers, void 0));
            _this.resetCodeExpiresAt = (__runInitializers(_this, _resetCode_extraInitializers), __runInitializers(_this, _resetCodeExpiresAt_initializers, void 0));
            // ✅ Added Two-Factor Auth Properties
            _this.isTwoFactorEnabled = (__runInitializers(_this, _resetCodeExpiresAt_extraInitializers), __runInitializers(_this, _isTwoFactorEnabled_initializers, void 0));
            _this.twoFactorCode = (__runInitializers(_this, _isTwoFactorEnabled_extraInitializers), __runInitializers(_this, _twoFactorCode_initializers, void 0));
            _this.twoFactorExpiresAt = (__runInitializers(_this, _twoFactorCode_extraInitializers), __runInitializers(_this, _twoFactorExpiresAt_initializers, void 0));
            _this.phoneNumber = (__runInitializers(_this, _twoFactorExpiresAt_extraInitializers), __runInitializers(_this, _phoneNumber_initializers, void 0));
            __runInitializers(_this, _phoneNumber_extraInitializers);
            return _this;
        }
        return User_1;
    }(_classSuper));
    __setFunctionName(_classThis, "User");
    (function () {
        var _a;
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create((_a = _classSuper[Symbol.metadata]) !== null && _a !== void 0 ? _a : null) : void 0;
        _id_decorators = [(0, mongoose_1.Prop)({ required: true, unique: true, default: uuid_1.v4 })];
        _firstName_decorators = [(0, mongoose_1.Prop)({ required: true })];
        _lastName_decorators = [(0, mongoose_1.Prop)({ required: true })];
        _email_decorators = [(0, mongoose_1.Prop)({ required: true, unique: true })];
        _password_decorators = [(0, mongoose_1.Prop)({ required: true })];
        _role_decorators = [(0, mongoose_1.Prop)({ type: String, enum: Role_1.Role, default: Role_1.Role.USER })];
        _profilePhoto_decorators = [(0, mongoose_1.Prop)({ type: String, required: false })];
        _resetCode_decorators = [(0, mongoose_1.Prop)({ type: String, required: false })];
        _resetCodeExpiresAt_decorators = [(0, mongoose_1.Prop)({ type: Date, required: false })];
        _isTwoFactorEnabled_decorators = [(0, mongoose_1.Prop)({ type: Boolean, default: false })];
        _twoFactorCode_decorators = [(0, mongoose_1.Prop)()];
        _twoFactorExpiresAt_decorators = [(0, mongoose_1.Prop)()];
        _phoneNumber_decorators = [(0, mongoose_1.Prop)({ required: true, unique: true })];
        __esDecorate(null, null, _id_decorators, { kind: "field", name: "id", static: false, private: false, access: { has: function (obj) { return "id" in obj; }, get: function (obj) { return obj.id; }, set: function (obj, value) { obj.id = value; } }, metadata: _metadata }, _id_initializers, _id_extraInitializers);
        __esDecorate(null, null, _firstName_decorators, { kind: "field", name: "firstName", static: false, private: false, access: { has: function (obj) { return "firstName" in obj; }, get: function (obj) { return obj.firstName; }, set: function (obj, value) { obj.firstName = value; } }, metadata: _metadata }, _firstName_initializers, _firstName_extraInitializers);
        __esDecorate(null, null, _lastName_decorators, { kind: "field", name: "lastName", static: false, private: false, access: { has: function (obj) { return "lastName" in obj; }, get: function (obj) { return obj.lastName; }, set: function (obj, value) { obj.lastName = value; } }, metadata: _metadata }, _lastName_initializers, _lastName_extraInitializers);
        __esDecorate(null, null, _email_decorators, { kind: "field", name: "email", static: false, private: false, access: { has: function (obj) { return "email" in obj; }, get: function (obj) { return obj.email; }, set: function (obj, value) { obj.email = value; } }, metadata: _metadata }, _email_initializers, _email_extraInitializers);
        __esDecorate(null, null, _password_decorators, { kind: "field", name: "password", static: false, private: false, access: { has: function (obj) { return "password" in obj; }, get: function (obj) { return obj.password; }, set: function (obj, value) { obj.password = value; } }, metadata: _metadata }, _password_initializers, _password_extraInitializers);
        __esDecorate(null, null, _role_decorators, { kind: "field", name: "role", static: false, private: false, access: { has: function (obj) { return "role" in obj; }, get: function (obj) { return obj.role; }, set: function (obj, value) { obj.role = value; } }, metadata: _metadata }, _role_initializers, _role_extraInitializers);
        __esDecorate(null, null, _profilePhoto_decorators, { kind: "field", name: "profilePhoto", static: false, private: false, access: { has: function (obj) { return "profilePhoto" in obj; }, get: function (obj) { return obj.profilePhoto; }, set: function (obj, value) { obj.profilePhoto = value; } }, metadata: _metadata }, _profilePhoto_initializers, _profilePhoto_extraInitializers);
        __esDecorate(null, null, _resetCode_decorators, { kind: "field", name: "resetCode", static: false, private: false, access: { has: function (obj) { return "resetCode" in obj; }, get: function (obj) { return obj.resetCode; }, set: function (obj, value) { obj.resetCode = value; } }, metadata: _metadata }, _resetCode_initializers, _resetCode_extraInitializers);
        __esDecorate(null, null, _resetCodeExpiresAt_decorators, { kind: "field", name: "resetCodeExpiresAt", static: false, private: false, access: { has: function (obj) { return "resetCodeExpiresAt" in obj; }, get: function (obj) { return obj.resetCodeExpiresAt; }, set: function (obj, value) { obj.resetCodeExpiresAt = value; } }, metadata: _metadata }, _resetCodeExpiresAt_initializers, _resetCodeExpiresAt_extraInitializers);
        __esDecorate(null, null, _isTwoFactorEnabled_decorators, { kind: "field", name: "isTwoFactorEnabled", static: false, private: false, access: { has: function (obj) { return "isTwoFactorEnabled" in obj; }, get: function (obj) { return obj.isTwoFactorEnabled; }, set: function (obj, value) { obj.isTwoFactorEnabled = value; } }, metadata: _metadata }, _isTwoFactorEnabled_initializers, _isTwoFactorEnabled_extraInitializers);
        __esDecorate(null, null, _twoFactorCode_decorators, { kind: "field", name: "twoFactorCode", static: false, private: false, access: { has: function (obj) { return "twoFactorCode" in obj; }, get: function (obj) { return obj.twoFactorCode; }, set: function (obj, value) { obj.twoFactorCode = value; } }, metadata: _metadata }, _twoFactorCode_initializers, _twoFactorCode_extraInitializers);
        __esDecorate(null, null, _twoFactorExpiresAt_decorators, { kind: "field", name: "twoFactorExpiresAt", static: false, private: false, access: { has: function (obj) { return "twoFactorExpiresAt" in obj; }, get: function (obj) { return obj.twoFactorExpiresAt; }, set: function (obj, value) { obj.twoFactorExpiresAt = value; } }, metadata: _metadata }, _twoFactorExpiresAt_initializers, _twoFactorExpiresAt_extraInitializers);
        __esDecorate(null, null, _phoneNumber_decorators, { kind: "field", name: "phoneNumber", static: false, private: false, access: { has: function (obj) { return "phoneNumber" in obj; }, get: function (obj) { return obj.phoneNumber; }, set: function (obj, value) { obj.phoneNumber = value; } }, metadata: _metadata }, _phoneNumber_initializers, _phoneNumber_extraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        User = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return User = _classThis;
}();
exports.User = User;
exports.UserSchema = mongoose_1.SchemaFactory.createForClass(User);

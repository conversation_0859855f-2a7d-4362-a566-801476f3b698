import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Notification } from '../entities/notification.entity';
import { NotificationStatus, NotificationType } from '../../../../core/notifications/notification.enums';

@Injectable()
export class NotificationRepository {
    constructor(
        @InjectModel('Notification') private readonly notificationModel: Model<Notification>
    ) {}

    async create(notificationData: Partial<Notification>): Promise<Notification> {
        return await this.notificationModel.create(notificationData);
    }

    async findById(id: string): Promise<Notification | null> {
        return this.notificationModel.findOne({ id }).exec();
    }

    async findByRecipientId(
        recipientId: string,
        status?: NotificationStatus,
        type?: NotificationType,
        page: number = 1,
        limit: number = 20
    ): Promise<{ notifications: Notification[]; total: number }> {
        const filter: any = { recipientId };
        
        if (status) {
            filter.status = status;
        }
        
        if (type) {
            filter.type = type;
        }

        const skip = (page - 1) * limit;

        const [notifications, total] = await Promise.all([
            this.notificationModel
                .find(filter)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .exec(),
            this.notificationModel.countDocuments(filter).exec()
        ]);

        return { notifications, total };
    }

    async markAsRead(notificationId: string, userId: string): Promise<Notification | null> {
        return this.notificationModel.findOneAndUpdate(
            { id: notificationId, recipientId: userId },
            { 
                status: NotificationStatus.READ,
                readAt: new Date()
            },
            { new: true }
        ).exec();
    }

    async markAllAsRead(userId: string): Promise<void> {
        await this.notificationModel.updateMany(
            { recipientId: userId, status: NotificationStatus.UNREAD },
            { 
                status: NotificationStatus.READ,
                readAt: new Date()
            }
        ).exec();
    }

    async getUnreadCount(userId: string): Promise<number> {
        return this.notificationModel.countDocuments({
            recipientId: userId,
            status: NotificationStatus.UNREAD
        }).exec();
    }

    async deleteNotification(notificationId: string, userId: string): Promise<boolean> {
        const result = await this.notificationModel.deleteOne({
            id: notificationId,
            recipientId: userId
        }).exec();
        
        return result.deletedCount > 0;
    }

    async archiveNotification(notificationId: string, userId: string): Promise<Notification | null> {
        return this.notificationModel.findOneAndUpdate(
            { id: notificationId, recipientId: userId },
            { 
                status: NotificationStatus.ARCHIVED,
                archivedAt: new Date()
            },
            { new: true }
        ).exec();
    }

    async updateFcmStatus(notificationId: string, fcmMessageId?: string): Promise<void> {
        const updateData: any = { fcmSent: true };
        if (fcmMessageId) {
            updateData.fcmMessageId = fcmMessageId;
        }

        await this.notificationModel.updateOne(
            { id: notificationId },
            updateData
        ).exec();
    }
}

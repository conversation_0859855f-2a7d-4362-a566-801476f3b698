import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UserProperties } from '../entities/userProperties.entity';

@Injectable()
export class UserPropertiesRepository {
    constructor(@InjectModel('UserProperties') private readonly userPropertiesModel: Model<UserProperties>) {}

    async findOneById(id: string): Promise<UserProperties | null> {
        return this.userPropertiesModel.findOne({ id }).exec();
    }

    async save(userPropsData: Partial<UserProperties>): Promise<UserProperties> {
        console.log(`📦 Creating UserProperties for User ID: ${userPropsData.id}`);
        return await this.userPropertiesModel.create(userPropsData);
    }

    async update(id: string, updateData: Partial<UserProperties>): Promise<UserProperties | null> {
        console.log(`🔄 Updating UserProperties for User ID: ${id}`);
        return this.userPropertiesModel.findOneAndUpdate({ id }, updateData, { new: true }).exec();
    }

    async delete(id: string): Promise<boolean> {
        const result = await this.userPropertiesModel.deleteOne({ id }).exec();
        return result.deletedCount > 0;
    }
}

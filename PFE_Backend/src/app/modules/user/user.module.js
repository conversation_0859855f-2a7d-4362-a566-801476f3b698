"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModule = void 0;
var common_1 = require("@nestjs/common");
var user_login_1 = require("./usecases/auth/user.login");
var mongoose_1 = require("@nestjs/mongoose");
var user_entity_1 = require("./user.entity");
var user_controller_1 = require("./user.controller");
var jwt_1 = require("@nestjs/jwt");
var user_repository_1 = require("./user.repository");
var user_register_1 = require("./usecases/auth/user.register");
var bcrypt_gateway_1 = require("../../gateways/bcrypt.gateway");
var jwt_gateway_1 = require("../../gateways/jwt.gateway");
var authenticationMiddleware_1 = require("../../middlewares/authenticationMiddleware");
var user_updateProfile_1 = require("./usecases/profile/user.updateProfile");
var user_forgotPassword_1 = require("./usecases/auth/user.forgotPassword");
var user_verifyResetCode_1 = require("./usecases/auth/user.verifyResetCode");
var user_resetPassword_1 = require("./usecases/auth/user.resetPassword");
var EmailService_1 = require("../../Shared/Services/EmailService");
var user_oauthLogin_1 = require("./usecases/2FA/user.oauthLogin");
var user_verifyTwoFactorCode_1 = require("./usecases/2FA/user.verifyTwoFactorCode");
var user_enableTwoFactor_1 = require("./usecases/2FA/user.enableTwoFactor");
var user_disableTwoFactor_1 = require("./usecases/2FA/user.disableTwoFactor");
var user_getUserProfile_1 = require("./usecases/profile/user.getUserProfile");
var UserModule = function () {
    var _classDecorators = [(0, common_1.Module)({
            imports: [
                mongoose_1.MongooseModule.forFeature([{ name: "User", schema: user_entity_1.UserSchema }]),
                jwt_1.JwtModule.register({
                    secret: "reallysecurekey",
                    signOptions: { expiresIn: "1y" }
                })
            ],
            controllers: [user_controller_1.UserController],
            providers: [
                user_login_1.UserLogin,
                user_register_1.UserRegister,
                user_repository_1.UserRepository,
                bcrypt_gateway_1.BcryptGateway,
                jwt_gateway_1.JwtGateway,
                authenticationMiddleware_1.AuthenticationMiddleware,
                user_updateProfile_1.UserUpdateProfile,
                user_forgotPassword_1.UserForgotPassword,
                user_verifyResetCode_1.UserVerifyResetCode,
                user_resetPassword_1.UserResetPassword,
                EmailService_1.EmailService,
                user_oauthLogin_1.UserOAuthLogin,
                user_verifyTwoFactorCode_1.VerifyTwoFactor,
                user_enableTwoFactor_1.EnableTwoFactorUseCase,
                user_disableTwoFactor_1.DisableTwoFactorUseCase,
                user_getUserProfile_1.UserGetProfile
            ]
        })];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var UserModule = _classThis = /** @class */ (function () {
        function UserModule_1() {
        }
        return UserModule_1;
    }());
    __setFunctionName(_classThis, "UserModule");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        UserModule = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return UserModule = _classThis;
}();
exports.UserModule = UserModule;

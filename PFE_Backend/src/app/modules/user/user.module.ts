import {forwardRef, Modu<PERSON>} from "@nestjs/common";
import { UserLogin } from "./usecases/auth/user.login";
import { MongooseModule } from "@nestjs/mongoose";
import { User, UserSchema } from "./entities/user.entity";
import { UserController } from "./controllers/user.controller";
import { JwtModule } from "@nestjs/jwt";
import { UserRepository } from "./repositories/user.repository";
import { UserRegister } from "./usecases/auth/user.register";
import { BcryptGateway } from "../../gateways/bcrypt.gateway";
import { JwtGateway } from "../../gateways/jwt.gateway";
import { AuthenticationMiddleware } from "../../middlewares/authenticationMiddleware";
import { UserUpdateProfile } from "./usecases/profile/user.updateProfile";
import { UserForgotPassword } from "./usecases/auth/user.forgotPassword";
import { UserVerifyResetCode } from "./usecases/auth/user.verifyResetCode";
import { UserResetPassword } from "./usecases/auth/user.resetPassword";
import { EmailService } from "../../Shared/Services/EmailService";
import { UserOAuthLogin } from "./usecases/2FA/user.oauthLogin";
import { VerifyTwoFactor } from "./usecases/2FA/user.verifyTwoFactorCode";
import { EnableTwoFactorUseCase } from "./usecases/2FA/user.enableTwoFactor";
import { DisableTwoFactorUseCase } from "./usecases/2FA/user.disableTwoFactor";
import { UserGetProfile } from "./usecases/profile/user.getUserProfile";
import {PatientModule} from "../patient/patient.module";
import {UserProperties, UserPropertiesSchema} from "./entities/userProperties.entity";
import {UserPropertiesRepository} from "./repositories/userProperties.repository";
import {UserResetLoginPassword} from "./usecases/auth/user.resetLoginPassword";
// Notification imports
import { Notification, NotificationSchema } from "./entities/notification.entity";
import { NotificationRepository } from "./repositories/notification.repository";
import { FCMService } from "./services/fcm.service";
import { SendNotification } from "./usecases/notifications/sendNotification";
import { GetUserNotifications } from "./usecases/notifications/getUserNotifications";
import { MarkNotificationAsRead } from "./usecases/notifications/markNotificationAsRead";
import { UpdateFcmToken } from "./usecases/notifications/updateFcmToken";
import { SendPrescriptionNotification } from "./usecases/notifications/sendPrescriptionNotification";
import { SendPharmacistPrescriptionNotification } from "./usecases/notifications/sendPharmacistPrescriptionNotification";
import { NotificationController } from "./controllers/notification.controller";
import { PharmacyModule } from "../pharmacy/pharmacy.module";
import { PrescriptionModule } from "../prescription/prescription.module";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: UserProperties.name, schema: UserPropertiesSchema }, // ✅ Register UserProperties Schema
      { name: 'Notification', schema: NotificationSchema }, // ✅ Register Notification Schema
    ]),
    JwtModule.register({
      secret: "reallysecurekey",
      signOptions: { expiresIn: "1y" },
    }),
    forwardRef(() => PatientModule),
    forwardRef(() => PharmacyModule),
    forwardRef(() => PrescriptionModule),


  ],
  controllers: [UserController, NotificationController],
  providers: [
    UserLogin,
    UserRegister,
    UserRepository,
    UserPropertiesRepository,
    BcryptGateway,
    JwtGateway,
    AuthenticationMiddleware,
    UserUpdateProfile,
    UserForgotPassword,
    UserVerifyResetCode,
    UserResetPassword,
    EmailService,
    UserOAuthLogin,
    VerifyTwoFactor,
    EnableTwoFactorUseCase,
    DisableTwoFactorUseCase,
    UserGetProfile,
    UserResetLoginPassword,
    // Notification providers
    NotificationRepository,
    FCMService,
    SendNotification,
    GetUserNotifications,
    MarkNotificationAsRead,
    UpdateFcmToken,
    SendPrescriptionNotification,
    SendPharmacistPrescriptionNotification
  ],
  exports: [
    UserRepository,
    BcryptGateway,
    EmailService,
    JwtGateway,
    // Export notification services for use in other modules
    SendPrescriptionNotification,
    SendPharmacistPrescriptionNotification,
    NotificationRepository,
    FCMService
  ],
})
export class UserModule {}

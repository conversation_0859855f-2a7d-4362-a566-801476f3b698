import { Injectable, Logger } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { NotificationType } from '../../../../core/notifications/notification.enums';

export interface FCMNotificationPayload {
    title: string;
    message: string;
    type: NotificationType;
    data?: any;
}

@Injectable()
export class FCMService {
    private readonly logger = new Logger(FCMService.name);
    private app: admin.app.App;

    constructor() {
        this.initializeFirebase();
    }

    private initializeFirebase() {
        try {
            // Check if Firebase app is already initialized
            if (admin.apps.length === 0) {
                let serviceAccount: any;

                // Option 1: Load from JSON string in environment variable
                if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
                    serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
                }
                // Option 2: Load from JSON file path
                else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PATH) {
                    const fs = require('fs');
                    const path = require('path');
                    const keyPath = path.resolve(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PATH);
                    serviceAccount = JSON.parse(fs.readFileSync(keyPath, 'utf8'));
                }
                // Option 3: Individual environment variables (legacy)
                else if (process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PRIVATE_KEY) {
                    serviceAccount = {
                        projectId: process.env.FIREBASE_PROJECT_ID,
                        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
                        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
                    };
                }
                else {
                    throw new Error('No Firebase service account configuration found. Please set FIREBASE_SERVICE_ACCOUNT_KEY_PATH or FIREBASE_SERVICE_ACCOUNT_KEY in your .env file');
                }

                this.app = admin.initializeApp({
                    credential: admin.credential.cert(serviceAccount),
                });

                this.logger.log('🔥 Firebase Admin SDK initialized successfully');
            } else {
                this.app = admin.apps[0] as admin.app.App;
                this.logger.log('🔥 Using existing Firebase Admin SDK instance');
            }
        } catch (error) {
            this.logger.error('❌ Failed to initialize Firebase Admin SDK:', error.message);
            throw error;
        }
    }

    async sendNotification(
        fcmToken: string,
        payload: FCMNotificationPayload
    ): Promise<string | null> {
        try {
            if (!fcmToken) {
                this.logger.warn('⚠️ FCM token is empty, skipping notification');
                return null;
            }

            const message: admin.messaging.Message = {
                token: fcmToken,
                notification: {
                    title: payload.title,
                    body: payload.message,
                },
                data: {
                    type: payload.type,
                    ...(payload.data && { data: JSON.stringify(payload.data) }),
                },
                android: {
                    notification: {
                        sound: 'default',
                        priority: 'high' as any,
                    },
                },
                apns: {
                    payload: {
                        aps: {
                            sound: 'default',
                            badge: 1,
                        },
                    },
                },
            };

            const response = await admin.messaging().send(message);
            this.logger.log(`📱 FCM notification sent successfully: ${response}`);
            return response;
        } catch (error) {
            this.logger.error('❌ Failed to send FCM notification:', error.message);
            
            // Handle invalid token errors
            if (error.code === 'messaging/invalid-registration-token' || 
                error.code === 'messaging/registration-token-not-registered') {
                this.logger.warn('⚠️ Invalid FCM token detected, should be removed from user');
            }
            
            return null;
        }
    }

    async sendMulticastNotification(
        fcmTokens: string[],
        payload: FCMNotificationPayload
    ): Promise<admin.messaging.BatchResponse | null> {
        try {
            if (!fcmTokens || fcmTokens.length === 0) {
                this.logger.warn('⚠️ No FCM tokens provided, skipping multicast notification');
                return null;
            }

            // Filter out empty tokens
            const validTokens = fcmTokens.filter(token => token && token.trim() !== '');
            
            if (validTokens.length === 0) {
                this.logger.warn('⚠️ No valid FCM tokens found, skipping multicast notification');
                return null;
            }

            const message: admin.messaging.MulticastMessage = {
                tokens: validTokens,
                notification: {
                    title: payload.title,
                    body: payload.message,
                },
                data: {
                    type: payload.type,
                    ...(payload.data && { data: JSON.stringify(payload.data) }),
                },
                android: {
                    notification: {
                        sound: 'default',
                        priority: 'high' as any,
                    },
                },
                apns: {
                    payload: {
                        aps: {
                            sound: 'default',
                            badge: 1,
                        },
                    },
                },
            };

            const response = await admin.messaging().sendEachForMulticast(message);
            this.logger.log(`📱 FCM multicast notification sent: ${response.successCount}/${validTokens.length} successful`);
            
            // Log failed tokens for cleanup
            if (response.failureCount > 0) {
                response.responses.forEach((resp, idx) => {
                    if (!resp.success) {
                        this.logger.warn(`❌ Failed to send to token ${idx}: ${resp.error?.message}`);
                    }
                });
            }
            
            return response;
        } catch (error) {
            this.logger.error('❌ Failed to send FCM multicast notification:', error.message);
            return null;
        }
    }

    async validateToken(fcmToken: string): Promise<boolean> {
        try {
            if (!fcmToken) return false;
            
            // Try to send a dry run message to validate the token
            const message: admin.messaging.Message = {
                token: fcmToken,
                notification: {
                    title: 'Test',
                    body: 'Test',
                },
            };

            await admin.messaging().send(message, true); // Second parameter is dryRun
            return true;
        } catch (error) {
            this.logger.warn(`⚠️ Invalid FCM token: ${error.message}`);
            return false;
        }
    }
}

import { IsString, <PERSON><PERSON>ength, <PERSON> } from "class-validator";

export class UserResetLoginPasswordCommand {
    @IsString()
    @MinLength(6, { message: "Password must be at least 6 characters long" })
    @Matches(/[A-Z]/, { message: "Password must contain at least one uppercase letter" })
    @Matches(/\d/, { message: "Password must contain at least one number" })
    newPassword: string;

    @IsString()
    confirmPassword: string;
}

import { IsE<PERSON>, <PERSON>NotEmpty } from 'class-validator';

export class ResetPasswordCommand {
    @IsEmail({}, { message: 'validation.EMAIL_INVALID' })
    @IsNotEmpty({ message: 'validation.EMAIL_REQUIRED' })
    email!: string;

    @IsNotEmpty({ message: 'validation.PASSWORD_REQUIRED' })
    newPassword!: string;

    @IsNotEmpty({ message: 'validation.CONFIRM_PASSWORD_REQUIRED' })
    confirmNewPassword!: string;
}

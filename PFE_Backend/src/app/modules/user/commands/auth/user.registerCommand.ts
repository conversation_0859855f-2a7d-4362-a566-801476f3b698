import {Is<PERSON><PERSON>, IsMobileP<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>y, <PERSON>} from 'class-validator';


export class UserReg<PERSON>Command {

  @IsEmail({}, { message: 'validation.EMAIL_INVALID' })
  @IsNotEmpty({ message: 'validation.EMAIL_REQUIRED' })
  email!: string;

  /**
   * Password for user
   *
   * @example abc@123
   * @type {string}
   */
  @IsNotEmpty({ message: 'validation.PASSWORD_REQUIRED' })
  password!: string;

  /**
   * Confirm password for user
   *
   * @example abc@123
   * @type {string}
   */
  @IsNotEmpty({ message: 'validation.CONFIRM_PASSWORD_REQUIRED' })
  confirmPassword!: string;

  /**
   * First name of user
   *
   * @example John
   * @type {string}
   */


  /**
   * Last name of user
   *
   * @example Doe
   * @type {string}
   */


  @IsNotEmpty({ message: 'validation.PHONE_REQUIRED' })
  @Matches(/^\d{8}$/, { message: 'validation.PHONE_INVALID' }) // Ensures exactly 8 digits
  phoneNumber!: string;
}

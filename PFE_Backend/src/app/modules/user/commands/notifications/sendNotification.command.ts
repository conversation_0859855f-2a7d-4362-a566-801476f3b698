import { IsNotEmpty, <PERSON>Optional, <PERSON>Enum, IsString } from 'class-validator';
import { NotificationType } from '../../../../../core/notifications/notification.enums';

export class SendNotificationCommand {
    @IsNotEmpty()
    @IsString()
    recipientId!: string;

    @IsOptional()
    @IsString()
    senderId?: string;

    @IsNotEmpty()
    @IsString()
    title!: string;

    @IsNotEmpty()
    @IsString()
    message!: string;

    @IsEnum(NotificationType)
    type!: NotificationType;

    @IsOptional()
    data?: any;
}

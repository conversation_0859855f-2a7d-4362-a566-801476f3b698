import { <PERSON>NotEmpty, IsO<PERSON>al, IsEnum, IsString, IsN<PERSON>ber, Min } from 'class-validator';
import { NotificationStatus, NotificationType } from '../../../../../core/notifications/notification.enums';
import { Transform } from 'class-transformer';

export class GetUserNotificationsCommand {
    @IsNotEmpty()
    @IsString()
    userId!: string;

    @IsOptional()
    @IsEnum(NotificationStatus)
    status?: NotificationStatus;

    @IsOptional()
    @IsEnum(NotificationType)
    type?: NotificationType;

    @IsOptional()
    @Transform(({ value }) => parseInt(value))
    @IsNumber()
    @Min(1)
    page?: number = 1;

    @IsOptional()
    @Transform(({ value }) => parseInt(value))
    @IsNumber()
    @Min(1)
    limit?: number = 20;
}

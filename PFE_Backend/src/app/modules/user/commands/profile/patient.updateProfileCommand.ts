import { Is<PERSON><PERSON>al, IsString, IsArray, IsNumber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class PatientUpdateProfileCommand {
    @IsOptional()
    @IsString()
    firstName?: string;

    @IsOptional()
    @IsString()
    lastName?: string;

    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true }) // Ensures validation of each item in the array
    @Type(() => AddressInline)
    addresses?: AddressInline[];
}

// ✅ Embedded Address structure inside Patient
export class AddressInline {
    @IsString()
    address: string;

    @IsNumber()
    lat: number;

    @IsNumber()
    long: number;

    @IsOptional()
    @IsString()
    apartmentNumber?: string;
}

import { Is<PERSON><PERSON>, <PERSON>NotEmpty, IsOptional, Matches, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import {PatientUpdateProfileCommand} from "./patient.updateProfileCommand";

export class UserUpdateProfileCommand {
    @IsOptional()
    @IsEmail({}, { message: 'validation.EMAIL_INVALID' })
    email?: string;

    @IsOptional()
    profilePhoto?: string;

    @IsNotEmpty({ message: 'validation.PHONE_REQUIRED' })
    @Matches(/^\d{8}$/, { message: 'validation.PHONE_INVALID' })
    phoneNumber!: string;

    @IsOptional()
    @ValidateNested()
    @Type(() => PatientUpdateProfileCommand)
    patientProfile?: PatientUpdateProfileCommand;
}

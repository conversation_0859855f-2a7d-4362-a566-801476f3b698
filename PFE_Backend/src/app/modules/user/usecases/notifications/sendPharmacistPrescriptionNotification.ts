import { Injectable, Logger } from '@nestjs/common';
import { SendNotification } from './sendNotification';
import { UserRepository } from '../../repositories/user.repository';
import { PharmacyRepository } from '../../../pharmacy/repositories/pharmacy.repository';
import { NotificationType } from '../../../../../core/notifications/notification.enums';
import { PackageDecisionService } from '../../../prescription/services/packageDecision.service';

export interface PharmacistPrescriptionNotificationData {
    prescriptionId: string;
    patientId: string;
    pharmacistId: string;
    pharmacistName: string;
    pharmacyId: string;
    medicineCount: number;
    medicines: Array<{
        medicine: string;
        dosage?: string;
        quantity?: string;
    }>;
}

@Injectable()
export class SendPharmacistPrescriptionNotification {
    private readonly logger = new Logger(SendPharmacistPrescriptionNotification.name);

    constructor(
        private readonly sendNotificationUseCase: SendNotification,
        private readonly userRepository: UserRepository,
        private readonly pharmacyRepository: PharmacyRepository,
        private readonly packageDecisionService: PackageDecisionService
    ) {}

    async execute(data: PharmacistPrescriptionNotificationData): Promise<void> {
        try {
            // Find the patient to notify
            const patient = await this.userRepository.findOneById(data.patientId);

            if (!patient) {
                this.logger.warn(`⚠️ Patient not found: ${data.patientId}`);
                return;
            }

            // Determine package decision based on medicines
            const packageDecision = this.packageDecisionService.determinePackageDecision(data.medicines);

            // Prepare notification content in French
            const title = 'Nouvelle ordonnance créée';
            const message = this.buildNotificationMessage(data, packageDecision);

            // Send notification to the patient
            await this.sendNotificationUseCase.execute({
                recipientId: patient.id,
                senderId: data.pharmacistId,
                title,
                message,
                type: NotificationType.PRESCRIPTION_CREATED_BY_PHARMACIST,
                data: {
                    prescriptionId: data.prescriptionId,
                    patientId: data.patientId,
                    pharmacistId: data.pharmacistId,
                    pharmacistName: data.pharmacistName,
                    pharmacyId: data.pharmacyId,
                    medicineCount: data.medicineCount,
                    medicines: data.medicines,
                    packageDecision: packageDecision
                }
            });

            this.logger.log(`📱 Pharmacist prescription notification sent to patient: ${patient.email} for prescription: ${data.prescriptionId}`);
        } catch (error) {
            this.logger.error(`❌ Failed to send pharmacist prescription notification: ${error.message}`);
            throw error;
        }
    }

    private buildNotificationMessage(data: PharmacistPrescriptionNotificationData, packageDecision: any): string {
        const medicineList = data.medicines.map(med => med.medicine).join(', ');

        let message = `${data.pharmacistName} vient de créer une ordonnance pour vous avec ${data.medicineCount} médicament(s)`;

        if (data.medicineCount > 0) {
            message += `: ${medicineList}`;
        }

        message += `. Le pharmacien va préparer un ${packageDecision.decisionFrench}. Veuillez vérifier les détails et confirmer la réception.`;

        return message;
    }
}

import { Injectable, Logger } from '@nestjs/common';
import { NotificationRepository } from '../../repositories/notification.repository';
import { UserRepository } from '../../repositories/user.repository';
import { FCMService } from '../../services/fcm.service';
import { SendNotificationCommand } from '../../commands/notifications/sendNotification.command';
import { Notification } from '../../entities/notification.entity';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class SendNotification {
    private readonly logger = new Logger(SendNotification.name);

    constructor(
        private readonly notificationRepository: NotificationRepository,
        private readonly userRepository: UserRepository,
        private readonly fcmService: FCMService
    ) {}

    async execute(command: SendNotificationCommand): Promise<Notification> {
        try {
            // 1. Create notification record in database
            const notification = await this.notificationRepository.create({
                id: uuidv4(),
                recipientId: command.recipientId,
                senderId: command.senderId,
                title: command.title,
                message: command.message,
                type: command.type,
                data: command.data,
                fcmSent: false
            });

            this.logger.log(`📝 Notification created: ${notification.id} for user: ${command.recipientId}`);

            // 2. Get recipient's FCM token
            const recipient = await this.userRepository.findOneById(command.recipientId);
            
            if (!recipient) {
                this.logger.warn(`⚠️ Recipient not found: ${command.recipientId}`);
                return notification;
            }

            // 3. Send FCM notification if token exists
            if (recipient.fcm) {
                const fcmMessageId = await this.fcmService.sendNotification(recipient.fcm, {
                    title: command.title,
                    message: command.message,
                    type: command.type,
                    data: command.data
                });

                // 4. Update notification with FCM status
                if (fcmMessageId) {
                    await this.notificationRepository.updateFcmStatus(notification.id, fcmMessageId);
                    this.logger.log(`📱 FCM notification sent successfully: ${fcmMessageId}`);
                } else {
                    this.logger.warn(`⚠️ Failed to send FCM notification for: ${command.recipientId}`);
                }
            } else {
                this.logger.warn(`⚠️ No FCM token found for user: ${command.recipientId}`);
            }

            return notification;
        } catch (error) {
            this.logger.error(`❌ Failed to send notification: ${error.message}`);
            throw error;
        }
    }
}

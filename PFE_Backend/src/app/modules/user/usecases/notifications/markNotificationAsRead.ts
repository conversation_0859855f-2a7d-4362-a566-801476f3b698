import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { NotificationRepository } from '../../repositories/notification.repository';
import { MarkNotificationAsReadCommand } from '../../commands/notifications/markNotificationAsRead.command';
import { Notification } from '../../entities/notification.entity';

@Injectable()
export class MarkNotificationAsRead {
    private readonly logger = new Logger(MarkNotificationAsRead.name);

    constructor(
        private readonly notificationRepository: NotificationRepository
    ) {}

    async execute(command: MarkNotificationAsReadCommand): Promise<Notification> {
        try {
            const { notificationId, userId } = command;

            const notification = await this.notificationRepository.markAsRead(notificationId, userId);

            if (!notification) {
                throw new NotFoundException(`Notification not found or not accessible: ${notificationId}`);
            }

            this.logger.log(`✅ Notification marked as read: ${notificationId} by user: ${userId}`);

            return notification;
        } catch (error) {
            this.logger.error(`❌ Failed to mark notification as read: ${error.message}`);
            throw error;
        }
    }
}

import { Injectable, Logger } from '@nestjs/common';
import { NotificationRepository } from '../../repositories/notification.repository';
import { GetUserNotificationsCommand } from '../../commands/notifications/getUserNotifications.command';
import { Notification } from '../../entities/notification.entity';

@Injectable()
export class GetUserNotifications {
    private readonly logger = new Logger(GetUserNotifications.name);

    constructor(
        private readonly notificationRepository: NotificationRepository
    ) {}

    async execute(command: GetUserNotificationsCommand): Promise<{
        notifications: Notification[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        unreadCount: number;
    }> {
        try {
            const { userId, status, type, page = 1, limit = 20 } = command;

            // Get notifications with pagination
            const { notifications, total } = await this.notificationRepository.findByRecipientId(
                userId,
                status,
                type,
                page,
                limit
            );

            // Get unread count
            const unreadCount = await this.notificationRepository.getUnreadCount(userId);

            const totalPages = Math.ceil(total / limit);

            this.logger.log(`📋 Retrieved ${notifications.length} notifications for user: ${userId}`);

            return {
                notifications,
                total,
                page,
                limit,
                totalPages,
                unreadCount
            };
        } catch (error) {
            this.logger.error(`❌ Failed to get notifications: ${error.message}`);
            throw error;
        }
    }
}

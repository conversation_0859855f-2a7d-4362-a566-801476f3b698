import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { UserRepository } from '../../repositories/user.repository';
import { FCMService } from '../../services/fcm.service';
import { UpdateFcmTokenCommand } from '../../commands/notifications/updateFcmToken.command';
import { User } from '../../entities/user.entity';

@Injectable()
export class UpdateFcmToken {
    private readonly logger = new Logger(UpdateFcmToken.name);

    constructor(
        private readonly userRepository: UserRepository,
        private readonly fcmService: FCMService
    ) {}

    async execute(command: UpdateFcmTokenCommand): Promise<User> {
        try {
            const { userId, fcmToken } = command;

            // Skip FCM token validation for testing tokens or in development
            const isDevelopment = process.env.NODE_ENV !== 'production';
            const isTestToken = fcmToken.includes('test') || fcmToken.includes('dummy');

            if (!isDevelopment && !isTestToken) {
                // Only validate in production with real tokens
                const isValidToken = await this.fcmService.validateToken(fcmToken);
                if (!isValidToken) {
                    throw new Error('Invalid FCM token provided');
                }
            } else {
                this.logger.log(`🧪 Accepting test/development FCM token: ${fcmToken.substring(0, 20)}...`);
            }

            // Update user's FCM token
            const user = await this.userRepository.updateFcmToken(userId, fcmToken);

            if (!user) {
                throw new NotFoundException(`User not found: ${userId}`);
            }

            this.logger.log(`🔄 FCM token updated for user: ${userId}`);

            return user;
        } catch (error) {
            this.logger.error(`❌ Failed to update FCM token: ${error.message}`);
            throw error;
        }
    }
}

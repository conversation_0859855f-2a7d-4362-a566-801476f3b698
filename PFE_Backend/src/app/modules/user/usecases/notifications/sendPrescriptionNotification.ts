import { Injectable, Logger } from '@nestjs/common';
import { SendNotification } from './sendNotification';
import { UserRepository } from '../../repositories/user.repository';
import { NotificationType } from '../../../../../core/notifications/notification.enums';

import { PharmacyRepository } from '../../../pharmacy/repositories/pharmacy.repository';
import { PackageDecisionService } from '../../../prescription/services/packageDecision.service';

export interface PrescriptionNotificationData {
    prescriptionId: string;
    patientId: string;
    patientName: string;
    pharmacyId: string;
    medicineCount: number;
    medicines: Array<{
        medicine: string;
        dosage?: string;
        quantity?: number;
    }>;
}

@Injectable()
export class SendPrescriptionNotification {
    private readonly logger = new Logger(SendPrescriptionNotification.name);

    constructor(
        private readonly sendNotificationUseCase: SendNotification,
        private readonly userRepository: UserRepository,
        private readonly pharmacyRepository: PharmacyRepository,
        private readonly packageDecisionService: PackageDecisionService
    ) {}

    async execute(data: PrescriptionNotificationData): Promise<void> {
        try {
            // Find the specific pharmacist for this pharmacy
            const pharmacist = await this.findPharmacistByPharmacyId(data.pharmacyId);

            if (!pharmacist) {
                this.logger.warn(`⚠️ No pharmacist found for pharmacy: ${data.pharmacyId}`);
                return;
            }

            // Determine package decision based on medicines
            const packageDecision = this.packageDecisionService.determinePackageDecision(data.medicines);

            // Prepare notification content in French
            const title = 'Nouvelle ordonnance ajoutée';
            const message = this.buildNotificationMessage(data, packageDecision);

            // Send notification to the pharmacist
            await this.sendNotificationUseCase.execute({
                recipientId: pharmacist.id,
                senderId: data.patientId,
                title,
                message,
                type: NotificationType.PRESCRIPTION_ADDED,
                data: {
                    prescriptionId: data.prescriptionId,
                    patientId: data.patientId,
                    patientName: data.patientName,
                    pharmacyId: data.pharmacyId,
                    medicineCount: data.medicineCount,
                    medicines: data.medicines,
                    packageDecision: packageDecision
                }
            });

            this.logger.log(`📱 Prescription notification sent to pharmacist: ${pharmacist.email} for prescription: ${data.prescriptionId}`);
        } catch (error) {
            this.logger.error(`❌ Failed to send prescription notification: ${error.message}`);
            throw error;
        }
    }

    private async findPharmacistByPharmacyId(pharmacyId: string): Promise<any | null> {
        try {
            // 1. Find the pharmacy by ID
            const pharmacy = await this.pharmacyRepository.findById(pharmacyId);

            if (!pharmacy) {
                this.logger.warn(`⚠️ Pharmacy not found: ${pharmacyId}`);
                return null;
            }

            // 2. Find the user by pharmacist email
            const pharmacist = await this.userRepository.findOneByEmail(pharmacy.pharmacistEmail);

            if (!pharmacist) {
                this.logger.warn(`⚠️ Pharmacist user not found for email: ${pharmacy.pharmacistEmail}`);
                return null;
            }

            this.logger.log(`📋 Found pharmacist: ${pharmacist.email} for pharmacy: ${pharmacyId}`);

            return pharmacist;
        } catch (error) {
            this.logger.error(`❌ Error finding pharmacist for pharmacy ${pharmacyId}: ${error.message}`);
            return null;
        }
    }

    private buildNotificationMessage(data: PrescriptionNotificationData, packageDecision: any): string {
        const medicineList = data.medicines
            .map(med => med.medicine)
            .join(', ');

        return `${data.patientName} a ajouté une nouvelle ordonnance avec ${data.medicineCount} médicament(s): ${medicineList}. Action recommandée: créer un ${packageDecision.decisionFrench}.`;
    }
}

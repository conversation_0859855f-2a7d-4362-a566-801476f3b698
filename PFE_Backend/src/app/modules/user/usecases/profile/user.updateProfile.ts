import { Injectable } from '@nestjs/common';
import { UserRepository } from "../../repositories/user.repository";
import { UserPropertiesRepository } from "../../repositories/userProperties.repository";
import { PatientRepository } from "../../../patient/repositories/patient.repository";
import { UserUpdateProfileCommand } from "../../commands/profile/user.updateProfileCommand";
import * as path from "path";
import * as fs from "fs";
import { PatientUpdateProfileCommand } from "../../commands/profile/patient.updateProfileCommand";

interface UpdateCommand {
    userUpdateProfileCommand: UserUpdateProfileCommand;
    patientUpdateProfileCommand?: PatientUpdateProfileCommand;
    userId: string;
}

@Injectable()
export class UserUpdateProfile {
    constructor(
        private userRepository: UserRepository,
        private userPropertiesRepository: UserPropertiesRepository,
        private patientRepository: PatientRepository
    ) {}

    async execute(payload: UpdateCommand): Promise<any> {
        const user = await this.userRepository.findOneById(payload.userId);
        if (!user) {
            throw new Error('User not found');
        }

        let userProperties = await this.userPropertiesRepository.findOneById(payload.userId);
        if (!userProperties) {
            userProperties = await this.userPropertiesRepository.save({
                id: user.id,
                profilePhoto: "",
                resetCode: null,
                resetCodeExpiresAt: null,
                isTwoFactorEnabled: false,
                twoFactorCode: null,
                twoFactorExpiresAt: null
            });
        }

        let profilePhoto = userProperties.profilePhoto;

        if (payload.userUpdateProfileCommand.profilePhoto === "") {
            if (userProperties.profilePhoto) {
                const oldPhotoPath = path.join(__dirname, '../../../../../../uploads/profilePhotos', path.basename(userProperties.profilePhoto));
                if (fs.existsSync(oldPhotoPath)) {
                    fs.unlinkSync(oldPhotoPath);
                }
            }
            profilePhoto = null;
        } else if (payload.userUpdateProfileCommand.profilePhoto !== userProperties.profilePhoto) {
            if (userProperties.profilePhoto) {
                const oldPhotoPath = path.join(__dirname, '../../../../../../uploads/profilePhotos', path.basename(userProperties.profilePhoto));
                if (fs.existsSync(oldPhotoPath)) {
                    fs.unlinkSync(oldPhotoPath);
                }
            }
            profilePhoto = payload.userUpdateProfileCommand.profilePhoto;
        }

        // ✅ Update User (Basic info)
        await this.userRepository.update(user.id, {
            email: payload.userUpdateProfileCommand.email,
            phone: payload.userUpdateProfileCommand.phoneNumber
        });

        // ✅ Update UserProperties (Profile photo)
        await this.userPropertiesRepository.update(user.id, {
            profilePhoto
        });

        // ✅ Update Patient with embedded addresses
        if (payload.patientUpdateProfileCommand) {
            const { firstName, lastName, addresses } = payload.patientUpdateProfileCommand;

            const patient = await this.patientRepository.findOneById(user.id);
            if (patient) {
                await this.patientRepository.update(user.id, {
                    firstName,
                    lastName,
                   // address: addresses ?? [],
                });
            }
        }

        return true;
    }
}

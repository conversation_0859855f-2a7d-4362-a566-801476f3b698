import { Injectable } from '@nestjs/common';
import { PatientRepository } from "../../../patient/repositories/patient.repository";
import {UserRepository} from "../../repositories/user.repository";
import {UserPropertiesRepository} from "../../repositories/userProperties.repository";

interface GetProfileCommand {
    userId: string;
}

@Injectable()
export class UserGetProfile {
    constructor(
        private userRepository: UserRepository,
        private userPropertiesRepository: UserPropertiesRepository,
        private patientRepository: PatientRepository
    ) {}

    async execute(payload: GetProfileCommand): Promise<any> {
        const user = await this.userRepository.findOneById(payload.userId);
        if (!user) {
            throw new Error('User not found');
        }

        const userProperties = await this.userPropertiesRepository.findOneById(payload.userId);
        const patient = await this.patientRepository.findOneById(payload.userId);

        return {
            id: user.id,
            email: user.email,
            phoneNumber: user.phone,
            fcm: user.fcm ?? "",

            // UserProperties fields
            isTwoFactorEnabled: userProperties?.isTwoFactorEnabled ?? false,
            profilePhoto: userProperties?.profilePhoto ?? "",

            // Patient Details
            firstName: patient?.firstName ?? "",
            lastName: patient?.lastName ?? "",
            addresses: patient?.address ?? [],
        };
    }
}

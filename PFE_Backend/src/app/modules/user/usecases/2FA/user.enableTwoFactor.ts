import { Injectable } from '@nestjs/common';
import { UserRepository } from '../../repositories/user.repository';
import { UserPropertiesRepository } from '../../repositories/userProperties.repository';
import { UserErrors } from '../../../../../core/errors/UserErrors';
import { UserEnableTwoFactorCommand } from "../../commands/2FA/user.enableTwoFactorCommand";

@Injectable()
export class EnableTwoFactorUseCase {
    constructor(
        private readonly userRepository: UserRepository,
        private readonly userPropertiesRepository: UserPropertiesRepository
    ) {}

    async execute(command: UserEnableTwoFactorCommand): Promise<void> {
        const user = await this.userRepository.findOneById(command.userId);
        if (!user) {
            throw new UserErrors.UserNotFound();
        }

        let userProperties = await this.userPropertiesRepository.findOneById(command.userId);
        if (!userProperties) {
            throw new UserErrors.UserNotFound();
        }

        userProperties.isTwoFactorEnabled = true; // Enable 2FA
        await this.userPropertiesRepository.update(command.userId, userProperties);
    }
}

"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserOAuthLogin = void 0;
var common_1 = require("@nestjs/common");
var google_auth_library_1 = require("google-auth-library");
var UserErrors_1 = require("../../../../../core/errors/UserErrors");
var crypto = require("crypto");
var UserOAuthLogin = function () {
    var _classDecorators = [(0, common_1.Injectable)()];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var UserOAuthLogin = _classThis = /** @class */ (function () {
        function UserOAuthLogin_1(userRepository, jwtGateway, emailService) {
            this.userRepository = userRepository;
            this.jwtGateway = jwtGateway;
            this.emailService = emailService;
            this.googleClient = new google_auth_library_1.OAuth2Client(process.env.GOOGLE_CLIENT_ID, process.env.GOOGLE_CLIENT_SECRET, process.env.GOOGLE_REDIRECT_URI);
        }
        UserOAuthLogin_1.prototype.execute = function (command) {
            return __awaiter(this, void 0, void 0, function () {
                var tokenResponse, tokens, ticket, payload, email, given_name, family_name, user, twoFactorCode, twoFactorExpiresAt, jwtToken, error_1;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 10, , 11]);
                            console.log("🔍 Received OAuth Code:", command.code);
                            return [4 /*yield*/, this.googleClient.getToken(command.code)];
                        case 1:
                            tokenResponse = _a.sent();
                            tokens = tokenResponse.tokens;
                            if (!tokens.id_token) {
                                throw new Error('Google authentication failed: No ID token received');
                            }
                            console.log("✅ Google Token Response:", tokens);
                            return [4 /*yield*/, this.googleClient.verifyIdToken({
                                    idToken: tokens.id_token,
                                    audience: process.env.GOOGLE_CLIENT_ID
                                })];
                        case 2:
                            ticket = _a.sent();
                            payload = ticket.getPayload();
                            console.log("🔍 Google User Payload:", payload);
                            if (!payload) {
                                throw new UserErrors_1.UserErrors.AuthenticationFailed();
                            }
                            email = payload.email, given_name = payload.given_name, family_name = payload.family_name;
                            return [4 /*yield*/, this.userRepository.findOneByEmail(email)];
                        case 3:
                            user = _a.sent();
                            if (!!user) return [3 /*break*/, 5];
                            console.log("🚀 New User Created:", email);
                            return [4 /*yield*/, this.userRepository.save({
                                    email: email,
                                    firstName: given_name || "Unknown",
                                    lastName: family_name || "Unknown",
                                    isTwoFactorEnabled: false // Default: 2FA Disabled
                                })];
                        case 4:
                            // Create new user
                            user = _a.sent();
                            return [3 /*break*/, 6];
                        case 5:
                            console.log("🔹 Existing User Found:", email);
                            _a.label = 6;
                        case 6:
                            if (!user.isTwoFactorEnabled) return [3 /*break*/, 9];
                            console.log("🔐 2FA Enabled for User:", email);
                            twoFactorCode = crypto.randomInt(100000, 999999).toString();
                            twoFactorExpiresAt = new Date(Date.now() + 10 * 60 * 1000);
                            // Save 2FA code in DB
                            user.twoFactorCode = twoFactorCode;
                            user.twoFactorExpiresAt = twoFactorExpiresAt;
                            return [4 /*yield*/, this.userRepository.update(user.id, {
                                    twoFactorCode: twoFactorCode,
                                    twoFactorExpiresAt: twoFactorExpiresAt
                                })];
                        case 7:
                            _a.sent();
                            console.log("📧 Sending 2FA Code to:", email);
                            // Send 2FA code via email
                            return [4 /*yield*/, this.emailService.sendTwoFactorCode(email, twoFactorCode)];
                        case 8:
                            // Send 2FA code via email
                            _a.sent();
                            // Return response indicating 2FA is required
                            return [2 /*return*/, {
                                    message: "Two-Factor Authentication required",
                                    requiresTwoFactor: true,
                                    email: user.email
                                }];
                        case 9:
                            jwtToken = this.jwtGateway.generateToken({
                                payload: {
                                    email: user.email,
                                    id: user.id,
                                    role: user.role,
                                    firstName: user.firstName,
                                    lastName: user.lastName,
                                    phoneNumber: user.phoneNumber,
                                    isTwoFactorEnabled: user.isTwoFactorEnabled
                                }
                            });
                            console.log("🔐 Generated JWT Token:", jwtToken);
                            return [2 /*return*/, {
                                    token: jwtToken,
                                    message: "Successfully authenticated"
                                }];
                        case 10:
                            error_1 = _a.sent();
                            console.error("❌ Google OAuth Error:", error_1.message);
                            throw new Error("Google authentication failed: ".concat(error_1.message));
                        case 11: return [2 /*return*/];
                    }
                });
            });
        };
        return UserOAuthLogin_1;
    }());
    __setFunctionName(_classThis, "UserOAuthLogin");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        UserOAuthLogin = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return UserOAuthLogin = _classThis;
}();
exports.UserOAuthLogin = UserOAuthLogin;

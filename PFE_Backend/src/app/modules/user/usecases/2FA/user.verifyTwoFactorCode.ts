import { Injectable } from '@nestjs/common';
import { UserRepository } from '../../repositories/user.repository';
import { UserPropertiesRepository } from '../../repositories/userProperties.repository';
import { JwtGateway } from '../../../../gateways/jwt.gateway';
import { UserErrors } from '../../../../../core/errors/UserErrors';
import { UserVerifyTwoFactorCommand } from "../../commands/2FA/user.verifyTwoFactorCommand";

@Injectable()
export class VerifyTwoFactor {
    constructor(
        private readonly userRepository: UserRepository,
        private readonly userPropertiesRepository: UserPropertiesRepository,
        private readonly jwtGateway: JwtGateway
    ) {}

    async execute(command: UserVerifyTwoFactorCommand): Promise<string> {
        const user = await this.userRepository.findOneByEmail(command.email);
        if (!user) {
            throw new UserErrors.UserNotFound();
        }

        let userProperties = await this.userPropertiesRepository.findOneById(user.id);
        if (!userProperties || !userProperties.twoFactorCode || !userProperties.twoFactorExpiresAt) {
            throw new UserErrors.AuthenticationFailed();
        }

        // Check if 2FA code is expired
        if (userProperties.twoFactorExpiresAt < new Date()) {
            throw new Error("Two-Factor Authentication code expired");
        }

        // Check if 2FA code matches
        if (userProperties.twoFactorCode !== command.code) {
            throw new Error("Invalid Two-Factor Authentication code");
        }

        console.log("✅ 2FA Code Verified for:", user.email);

        // Clear 2FA code from UserProperties
        userProperties.twoFactorCode = null;
        userProperties.twoFactorExpiresAt = null;
        await this.userPropertiesRepository.update(user.id, userProperties);

        // Generate JWT token
        return this.jwtGateway.generateToken({
            payload: {
                email: user.email,
                id: user.id,
                role: user.role
            }
        });
    }
}

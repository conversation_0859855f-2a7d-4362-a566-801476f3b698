import { Injectable } from '@nestjs/common';
import { OAuth2Client } from 'google-auth-library';
import { UserRepository } from '../../repositories/user.repository';
import { UserPropertiesRepository } from '../../repositories/userProperties.repository';
import { JwtGateway } from '../../../../gateways/jwt.gateway';
import { UserErrors } from '../../../../../core/errors/UserErrors';
import { UserOauthLoginCommand } from "../../commands/2FA/user.oauthLoginCommand";
import * as crypto from 'crypto';
import { EmailService } from "../../../../Shared/Services/EmailService";

@Injectable()
export class UserOAuthLogin {
    private googleClient: OAuth2Client;

    constructor(
        private readonly userRepository: UserRepository,
        private readonly userPropertiesRepository: UserPropertiesRepository,
        private readonly jwtGateway: JwtGateway,
        private readonly emailService: EmailService
    ) {
        this.googleClient = new OAuth2Client(
            process.env.GOOGLE_CLIENT_ID,
            process.env.GOOGLE_CLIENT_SECRET,
            process.env.GOOGLE_REDIRECT_URI
        );
    }

    async execute(command: UserOauthLoginCommand): Promise<any> {
        try {
            console.log("🔍 Received OAuth Code:", command.code);

            // Exchange the code for tokens (DO NOT pass client_id & client_secret manually)
            const tokenResponse = await this.googleClient.getToken(command.code);
            const { tokens } = tokenResponse;

            if (!tokens.id_token) {
                throw new Error('Google authentication failed: No ID token received');
            }

            console.log("✅ Google Token Response:", tokens);

            // Verify the ID token
            const ticket = await this.googleClient.verifyIdToken({
                idToken: tokens.id_token,
                audience: process.env.GOOGLE_CLIENT_ID
            });

            const payload = ticket.getPayload();
            console.log("🔍 Google User Payload:", payload);

            if (!payload) {
                throw new UserErrors.AuthenticationFailed();
            }

            const { email, given_name, family_name } = payload;

            // Check if user exists in database
            let user = await this.userRepository.findOneByEmail(email);
            if (!user) {
                console.log("🚀 New User Created:", email);

                // Create new user
                user = await this.userRepository.save({
                    email
                });

                // ✅ Create associated UserProperties entry
                await this.userPropertiesRepository.save({
                    id: user.id,
                    profilePhoto: "",
                    resetCode: null,
                    resetCodeExpiresAt: null,
                    isTwoFactorEnabled: false,
                    twoFactorCode: null,
                    twoFactorExpiresAt: null
                });
            } else {
                console.log("🔹 Existing User Found:", email);
            }

            let userProperties = await this.userPropertiesRepository.findOneById(user.id);
            if (!userProperties) {
                throw new UserErrors.UserNotFound();
            }

            // 🔒 Check if Two-Factor Authentication is enabled
            if (userProperties.isTwoFactorEnabled) {
                console.log("🔐 2FA Enabled for User:", email);

                // Generate a random 2FA code
                const twoFactorCode = crypto.randomInt(100000, 999999).toString();
                const twoFactorExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // Expires in 10 min

                // Save 2FA code in UserProperties
                userProperties.twoFactorCode = twoFactorCode;
                userProperties.twoFactorExpiresAt = twoFactorExpiresAt;
                await this.userPropertiesRepository.update(user.id, userProperties);

                console.log("📧 Sending 2FA Code to:", email);

                // Send 2FA code via email
                await this.emailService.sendTwoFactorCode(email, twoFactorCode);

                // Return response indicating 2FA is required
                return {
                    message: "Two-Factor Authentication required",
                    requiresTwoFactor: true,
                    email: user.email
                };
            }

            // ✅ If 2FA is NOT enabled, proceed with login
            const jwtToken = this.jwtGateway.generateToken({
                payload: {
                    email: user.email,
                    id: user.id,
                    role: user.role,
                    phoneNumber: user.phone,
                    isTwoFactorEnabled: userProperties.isTwoFactorEnabled
                }
            });

            console.log("🔐 Generated JWT Token:", jwtToken);

            return {
                token: jwtToken,
                message: "Successfully authenticated"
            };

        } catch (error) {
            console.error("❌ Google OAuth Error:", error.message);
            throw new Error(`Google authentication failed: ${error.message}`);
        }
    }
}

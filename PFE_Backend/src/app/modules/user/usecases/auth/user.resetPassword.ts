import { Injectable } from '@nestjs/common';
import { UserRepository } from "../../repositories/user.repository";
import { UserPropertiesRepository } from "../../repositories/userProperties.repository";
import { ResetPasswordCommand } from "../../commands/auth/ResetPasswordCommand";
import { Usecase } from "../../../../../core/models/usecase";
import { UserErrors } from "../../../../../core/errors/UserErrors";
import { BcryptGateway } from "../../../../gateways/bcrypt.gateway";

@Injectable()
export class UserResetPassword implements Usecase<ResetPasswordCommand, boolean> {
    constructor(
        private userRepository: UserRepository,
        private userPropertiesRepository: UserPropertiesRepository,
        private bcryptGateway: BcryptGateway
    ) {}

    async execute(command: ResetPasswordCommand): Promise<boolean> {
        const user = await this.userRepository.findOneByEmail(command.email);
        if (!user) {
            throw new UserErrors.UserNotFound();
        }

        let userProperties = await this.userPropertiesRepository.findOneById(user.id);
        if (!userProperties) {
            throw new UserErrors.InvalidResetCode();
        }

        // **Ensure reset code was verified**
        if (userProperties.resetCode !== "VERIFIED") {
            throw new UserErrors.InvalidResetCode();
        }

        // **Check if new password and confirm new password match**
        if (command.newPassword !== command.confirmNewPassword) {
            throw new UserErrors.PasswordsDoNotMatch();
        }

        // **Encrypt new password**
        user.password = await this.bcryptGateway.encrypt(command.newPassword);

        // **Clear resetCode after successful password reset**
        userProperties.resetCode = null;
        userProperties.resetCodeExpiresAt = null;

        // **Update user and user properties in database**
        await this.userRepository.update(user.id, user);
        await this.userPropertiesRepository.update(user.id, userProperties);

        return true;
    }
}

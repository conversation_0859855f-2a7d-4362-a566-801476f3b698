import { Injectable } from '@nestjs/common';
import { UserRepository } from "../../repositories/user.repository";
import { UserPropertiesRepository } from "../../repositories/userProperties.repository";
import { ForgotPasswordCommand } from "../../commands/auth/ForgotPasswordCommand";
import { Usecase } from "../../../../../core/models/usecase";
import { UserErrors } from "../../../../../core/errors/UserErrors";
import { EmailService } from "../../../../Shared/Services/EmailService";

@Injectable()
export class UserForgotPassword implements Usecase<ForgotPasswordCommand, boolean> {
    constructor(
        private userRepository: UserRepository,
        private userPropertiesRepository: UserPropertiesRepository,
        private emailService: EmailService
    ) {}

    async execute(command: ForgotPasswordCommand): Promise<boolean> {
        const user = await this.userRepository.findOneByEmail(command.email);
        if (!user) {
            throw new UserErrors.UserNotFound();
        }

        let userProperties = await this.userPropertiesRepository.findOneById(user.id);
        if (!userProperties) {
            userProperties = await this.userPropertiesRepository.save({
                id: user.id,
                profilePhoto: "",
                resetCode: null,
                resetCodeExpiresAt: null,
                isTwoFactorEnabled: false,
                twoFactorCode: null,
                twoFactorExpiresAt: null
            });
        }

        // **Check if there's an existing reset code and if it's expired**
        if (userProperties.resetCodeExpiresAt && new Date(userProperties.resetCodeExpiresAt) < new Date()) {
            userProperties.resetCode = null;
            userProperties.resetCodeExpiresAt = null;
            await this.userPropertiesRepository.update(user.id, userProperties);
        }

        // **Generate new reset code**
        const resetCode = Math.floor(100000 + Math.random() * 900000).toString();

        // **Set expiration (10 min)**
        userProperties.resetCode = resetCode;
        userProperties.resetCodeExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

        await this.userPropertiesRepository.update(user.id, userProperties);

        // **Send email with expiration notice**
        await this.emailService.sendPasswordResetEmail(user.email, resetCode, 10);

        return true;
    }
}

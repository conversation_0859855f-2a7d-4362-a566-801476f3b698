import { Injectable } from '@nestjs/common';
import { UserLoginCommand } from "../../commands/auth/user.loginCommand";
import { UserRepository } from "../../repositories/user.repository";
import { UserPropertiesRepository } from "../../repositories/userProperties.repository";
import { BcryptGateway } from "../../../../gateways/bcrypt.gateway";
import { UserErrors } from "../../../../../core/errors/UserErrors";
import { JwtGateway } from "../../../../gateways/jwt.gateway";

@Injectable()
export class UserLogin {
  constructor(
      private readonly userRepository: UserRepository,
      private readonly userPropertiesRepository: UserPropertiesRepository,
      private readonly bcryptGateway: BcryptGateway,
      private readonly jwtGateway: JwtGateway
  ) {}

  async execute(loginCommand: UserLoginCommand): Promise<any> {
    // ✅ Validate user credentials
    const user = await this.userRepository.findOneByEmail(loginCommand.email);
    if (!user) {
      throw new UserErrors.WrongCredentials();
    }

    // ✅ Check password correctly (hashed password check)
    const isPasswordCorrect = await this.bcryptGateway.compare(loginCommand.password, user.password);
    if (!isPasswordCorrect) {
      throw new UserErrors.WrongCredentials();
    }

    // ✅ Fetch user properties (or create if missing)
    let userProperties = await this.userPropertiesRepository.findOneById(user.id);
    if (!userProperties) {
      userProperties = await this.userPropertiesRepository.save({
        id: user.id,
        profilePhoto: "",
        resetCode: null,
        resetCodeExpiresAt: null,
        isTwoFactorEnabled: false,
        twoFactorCode: null,
        twoFactorExpiresAt: null
      });
    }

    // ✅ Check if user has the default password
    const isDefaultPassword = await this.bcryptGateway.compare("password", user.password);

    // ✅ Generate token
    const token = this.jwtGateway.generateToken({
      payload: {
        email: user.email,
        id: user.id,
        role: user.role,
        phoneNumber: user.phone,
        isTwoFactorEnabled: userProperties.isTwoFactorEnabled
      }
    });

    // ✅ Return token + requiresPasswordReset flag
    return {
      token,
      requiresPasswordReset: isDefaultPassword
    };
  }
}

import { Injectable } from '@nestjs/common';
import { UserRepository } from "../../repositories/user.repository";
import { UserErrors } from "../../../../../core/errors/UserErrors";
import { UserResetLoginPasswordCommand } from "../../commands/auth/user.resetLoginPasswordCommand";
import { BcryptGateway } from "../../../../gateways/bcrypt.gateway";

@Injectable()
export class UserResetLoginPassword {
    constructor(
        private userRepository: UserRepository,
        private bcryptGateway: BcryptGateway
    ) {}

    async execute(userId: string, command: UserResetLoginPasswordCommand): Promise<boolean> {
        const user = await this.userRepository.findOneById(userId);
        if (!user) {
            throw new UserErrors.UserNotFound();
        }

        // ✅ Check if the new password matches the confirm password
        if (command.newPassword !== command.confirmPassword) {
            throw new UserErrors.PasswordsDoNotMatch();
        }

        // ✅ Encrypt the new password
        const hashedPassword = await this.bcryptGateway.encrypt(command.newPassword);

        // ✅ Update the user's password in the database
        await this.userRepository.update(user.id, { password: hashedPassword });

        return true;
    }
}

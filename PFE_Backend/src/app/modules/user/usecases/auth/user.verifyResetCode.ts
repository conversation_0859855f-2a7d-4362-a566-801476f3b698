import { Injectable } from '@nestjs/common';
import { UserRepository } from "../../repositories/user.repository";
import { UserPropertiesRepository } from "../../repositories/userProperties.repository";
import { VerifyResetCodeCommand } from "../../commands/auth/VerifyResetCodeCommand";
import { Usecase } from "../../../../../core/models/usecase";
import { UserErrors } from "../../../../../core/errors/UserErrors";

@Injectable()
export class UserVerifyResetCode implements Usecase<VerifyResetCodeCommand, boolean> {
    constructor(
        private userRepository: UserRepository,
        private userPropertiesRepository: UserPropertiesRepository
    ) {}

    async execute(command: VerifyResetCodeCommand): Promise<boolean> {
        const user = await this.userRepository.findOneByEmail(command.email);
        if (!user) {
            throw new UserErrors.UserNotFound();
        }

        let userProperties = await this.userPropertiesRepository.findOneById(user.id);
        if (!userProperties || userProperties.resetCode !== command.code) {
            throw new UserErrors.InvalidResetCode();
        }

        // **Check if code has expired**
        if (userProperties.resetCodeExpiresAt && new Date(userProperties.resetCodeExpiresAt) < new Date()) {
            throw new UserErrors.ResetCodeExpired();
        }

        // ✅ Mark the reset code as verified (Instead of deleting it)
        userProperties.resetCode = "VERIFIED";
        await this.userPropertiesRepository.update(user.id, userProperties);

        return true;
    }
}

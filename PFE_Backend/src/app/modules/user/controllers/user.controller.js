"use strict";
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
var common_1 = require("@nestjs/common");
var UserErrors_1 = require("../../../../core/errors/UserErrors");
var authenticationMiddleware_1 = require("../../../middlewares/authenticationMiddleware");
var user_enableTwoFactorCommand_1 = require("../commands/2FA/user.enableTwoFactorCommand");
var user_getUserProfileCommand_1 = require("../commands/profile/user.getUserProfileCommand");
var platform_express_1 = require("@nestjs/platform-express");
var multer_1 = require("multer");
var UserController = function () {
    var _classDecorators = [(0, common_1.Controller)('/user')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _login_decorators;
    var _register_decorators;
    var _getMe_decorators;
    var _uploadProfilePhoto_decorators;
    var _updateProfile_decorators;
    var _getProfile_decorators;
    var _forgotPassword_decorators;
    var _verifyResetCode_decorators;
    var _resetPassword_decorators;
    var _googleOAuthCallback_decorators;
    var _verifyTwoFactorAuth_decorators;
    var _enableTwoFactor_decorators;
    var _disableTwoFactor_decorators;
    var UserController = _classThis = /** @class */ (function () {
        function UserController_1(userRepository, userLogin, userRegister, userUpdateProfile, userForgotPassword, userVerifyResetCode, userResetPassword, userOAuthLogin, verifyuserTwoFactor, enableTwoFactorUseCase, disableTwoFactorUseCase, userGetProfile) {
            this.userRepository = (__runInitializers(this, _instanceExtraInitializers), userRepository);
            this.userLogin = userLogin;
            this.userRegister = userRegister;
            this.userUpdateProfile = userUpdateProfile;
            this.userForgotPassword = userForgotPassword;
            this.userVerifyResetCode = userVerifyResetCode;
            this.userResetPassword = userResetPassword;
            this.userOAuthLogin = userOAuthLogin;
            this.verifyuserTwoFactor = verifyuserTwoFactor;
            this.enableTwoFactorUseCase = enableTwoFactorUseCase;
            this.disableTwoFactorUseCase = disableTwoFactorUseCase;
            this.userGetProfile = userGetProfile;
        }
        UserController_1.prototype.login = function (res, body, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                var token, e_1;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            return [4 /*yield*/, this.userLogin.execute(body)];
                        case 1:
                            token = _a.sent();
                            return [2 /*return*/, res.status(200).json({
                                    token: token,
                                    message: i18n.translate('success.SUCCESSFULLY_LOGGED_IN'),
                                })];
                        case 2:
                            e_1 = _a.sent();
                            console.log(e_1);
                            if (e_1 instanceof UserErrors_1.UserErrors.WrongCredentials)
                                return [2 /*return*/, res.status(401).json({ message: i18n.translate('errors.WRONG_CREDENTIALS') })];
                            return [2 /*return*/, res.status(401).json({ message: i18n.translate('errors.AUTHENTICATION_FAILED') })];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        UserController_1.prototype.register = function (res, body, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                var e_2;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            return [4 /*yield*/, this.userRegister.execute(body)];
                        case 1:
                            _a.sent();
                            return [2 /*return*/, res.status(201).json({
                                    success: true,
                                    message: i18n.translate('success.SUCCESSFULLY_REGISTERED'),
                                })];
                        case 2:
                            e_2 = _a.sent();
                            if (e_2 instanceof UserErrors_1.UserErrors.EmailAlreadyUsed)
                                return [2 /*return*/, res.status(403).json({ message: i18n.translate('errors.EMAIL_ALREADY_USED') })];
                            if (e_2 instanceof UserErrors_1.UserErrors.PasswordInvalid)
                                return [2 /*return*/, res.status(403).json({ message: i18n.translate('errors.PASSWORD_INVALID') })];
                            if (e_2 instanceof UserErrors_1.UserErrors.ProfileAlreadyUsed)
                                return [2 /*return*/, res.status(403).json({ message: i18n.translate('errors.PROFILE_ALREADY_USED') })];
                            return [2 /*return*/, res.status(403).json({ message: i18n.translate('errors.AUTHENTICATION_FAILED') })];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        UserController_1.prototype.getMe = function (req, res, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    try {
                        return [2 /*return*/, res.status(200).json(req.identity)
                            // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        ];
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    }
                    catch (e) {
                        return [2 /*return*/, res.status(403).json({ message: i18n.translate('errors.AUTHENTICATION_FAILED') })];
                    }
                    return [2 /*return*/];
                });
            });
        };
        //**********************************************Profile***************************************************************
        // ✅ Upload Profile Photo
        UserController_1.prototype.uploadProfilePhoto = function (file, res) {
            return __awaiter(this, void 0, void 0, function () {
                var photoPath;
                return __generator(this, function (_a) {
                    if (!file) {
                        return [2 /*return*/, res.status(400).json({ message: 'No file uploaded' })];
                    }
                    photoPath = "/uploads/profilePhotos/".concat(file.filename);
                    return [2 /*return*/, res.status(201).json({
                            success: true,
                            message: 'Photo uploaded successfully.',
                            profilePhoto: photoPath,
                        })];
                });
            });
        };
        UserController_1.prototype.updateProfile = function (req, res, body, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                var userId, e_3;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            userId = req.identity.id;
                            if (!userId) {
                                return [2 /*return*/, res.status(401).json({
                                        message: i18n.translate('errors.UNAUTHORIZED'),
                                    })];
                            }
                            // 🔄 Execute Use Case with Photo Logic
                            return [4 /*yield*/, this.userUpdateProfile.execute({
                                    userId: userId,
                                    userUpdateProfileCommand: body
                                })];
                        case 1:
                            // 🔄 Execute Use Case with Photo Logic
                            _a.sent();
                            return [2 /*return*/, res.status(201).json({
                                    success: true,
                                    message: i18n.translate('success.SUCCESSFULLY_UPDATED'),
                                })];
                        case 2:
                            e_3 = _a.sent();
                            if (e_3 instanceof UserErrors_1.UserErrors.EmailAlreadyUsed)
                                return [2 /*return*/, res.status(403).json({ message: i18n.translate('errors.EMAIL_ALREADY_USED') })];
                            if (e_3 instanceof UserErrors_1.UserErrors.PasswordInvalid)
                                return [2 /*return*/, res.status(403).json({ message: i18n.translate('errors.PASSWORD_INVALID') })];
                            if (e_3 instanceof UserErrors_1.UserErrors.ProfileAlreadyUsed)
                                return [2 /*return*/, res.status(403).json({ message: i18n.translate('errors.PROFILE_ALREADY_USED') })];
                            console.log(e_3);
                            return [2 /*return*/, res.status(403).json({ message: i18n.translate('errors.AUTHENTICATION_FAILED') })];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        UserController_1.prototype.getProfile = function (req, res, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                var userId, command, userProfile, e_4;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            userId = req.identity.id;
                            if (!userId) {
                                return [2 /*return*/, res.status(401).json({
                                        message: i18n.translate('errors.UNAUTHORIZED'),
                                    })];
                            }
                            command = new user_getUserProfileCommand_1.UserGetProfileCommand();
                            command.userId = userId;
                            return [4 /*yield*/, this.userGetProfile.execute({ userGetProfileCommand: command, userId: userId })];
                        case 1:
                            userProfile = _a.sent();
                            return [2 /*return*/, res.status(200).json({
                                    success: true,
                                    message: i18n.translate('success.SUCCESSFULLY_FETCHED_PROFILE'),
                                    data: userProfile,
                                })];
                        case 2:
                            e_4 = _a.sent();
                            if (e_4 instanceof UserErrors_1.UserErrors.UserNotFound) {
                                return [2 /*return*/, res.status(404).json({ message: i18n.translate('errors.USER_NOT_FOUND') })];
                            }
                            console.error(e_4);
                            return [2 /*return*/, res.status(500).json({ message: i18n.translate('errors.INTERNAL_SERVER_ERROR') })];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        //**********************************************Profile***************************************************************
        UserController_1.prototype.forgotPassword = function (res, body, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.userForgotPassword.execute(body)];
                        case 1:
                            _a.sent();
                            return [2 /*return*/, res.status(200).json({ message: i18n.translate('success.EMAIL_SENT') })];
                    }
                });
            });
        };
        UserController_1.prototype.verifyResetCode = function (res, body, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.userVerifyResetCode.execute(body)];
                        case 1:
                            _a.sent();
                            return [2 /*return*/, res.status(200).json({ message: i18n.translate('success.CODE_VALID') })];
                    }
                });
            });
        };
        UserController_1.prototype.resetPassword = function (res, body, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.userResetPassword.execute(body)];
                        case 1:
                            _a.sent();
                            return [2 /*return*/, res.status(200).json({ message: i18n.translate('success.PASSWORD_UPDATED') })];
                    }
                });
            });
        };
        //*********************************************2FA**********************************************************
        UserController_1.prototype.googleOAuthCallback = function (res, req, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                var code, idToken, e_5;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            code = req.query.code;
                            if (!code) {
                                return [2 /*return*/, res.status(400).json({ message: i18n.translate('errors.INVALID_CODE') })];
                            }
                            return [4 /*yield*/, this.userOAuthLogin.execute({ code: code })];
                        case 1:
                            idToken = _a.sent();
                            return [2 /*return*/, res.status(200).json({
                                    idToken: idToken,
                                    message: i18n.translate('success.SUCCESSFULLY_AUTHENTICATED'),
                                })];
                        case 2:
                            e_5 = _a.sent();
                            console.error('OAuth Callback Error:', e_5);
                            return [2 /*return*/, res.status(401).json({ message: i18n.translate('errors.AUTHENTICATION_FAILED') })];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        UserController_1.prototype.verifyTwoFactorAuth = function (res, body, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                var token, e_6;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            return [4 /*yield*/, this.verifyuserTwoFactor.execute(body)];
                        case 1:
                            token = _a.sent();
                            return [2 /*return*/, res.status(200).json({
                                    token: token,
                                    message: i18n.translate('success.SUCCESSFULLY_LOGGED_IN'),
                                })];
                        case 2:
                            e_6 = _a.sent();
                            console.log(e_6);
                            return [2 /*return*/, res.status(401).json({ message: i18n.translate('errors.AUTHENTICATION_FAILED') })];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        UserController_1.prototype.enableTwoFactor = function (req, res, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                var command, e_7;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            command = new user_enableTwoFactorCommand_1.UserEnableTwoFactorCommand();
                            command.userId = req.identity.id;
                            return [4 /*yield*/, this.enableTwoFactorUseCase.execute(command)];
                        case 1:
                            _a.sent();
                            return [2 /*return*/, res.status(200).json({
                                    success: true,
                                    message: i18n.translate('success.TWO_FACTOR_ENABLED'),
                                })];
                        case 2:
                            e_7 = _a.sent();
                            return [2 /*return*/, res.status(500).json({ message: i18n.translate('errors.TWO_FACTOR_FAILED') })];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        UserController_1.prototype.disableTwoFactor = function (req, res, i18n) {
            return __awaiter(this, void 0, void 0, function () {
                var command, e_8;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            command = new user_enableTwoFactorCommand_1.UserEnableTwoFactorCommand();
                            command.userId = req.identity.id;
                            return [4 /*yield*/, this.disableTwoFactorUseCase.execute(command)];
                        case 1:
                            _a.sent();
                            return [2 /*return*/, res.status(200).json({
                                    success: true,
                                    message: i18n.translate('success.TWO_FACTOR_DISABLED'),
                                })];
                        case 2:
                            e_8 = _a.sent();
                            return [2 /*return*/, res.status(500).json({ message: i18n.translate('errors.TWO_FACTOR_FAILED') })];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        };
        return UserController_1;
    }());
    __setFunctionName(_classThis, "UserController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _login_decorators = [(0, common_1.Post)('/login')];
        _register_decorators = [(0, common_1.Post)('/register')];
        _getMe_decorators = [(0, common_1.Get)('me'), (0, common_1.UseGuards)(authenticationMiddleware_1.AuthenticationMiddleware)];
        _uploadProfilePhoto_decorators = [(0, common_1.Post)('/uploadProfilePhoto'), (0, common_1.UseGuards)(authenticationMiddleware_1.AuthenticationMiddleware), (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
                storage: (0, multer_1.diskStorage)({
                    destination: './uploads/profilePhotos',
                    filename: function (req, file, cb) {
                        var uniqueName = "".concat(Date.now(), "-").concat(file.originalname);
                        cb(null, uniqueName);
                    },
                }),
            }))];
        _updateProfile_decorators = [(0, common_1.Put)('/updateProfile'), (0, common_1.UseGuards)(authenticationMiddleware_1.AuthenticationMiddleware)];
        _getProfile_decorators = [(0, common_1.Get)('/getProfile'), (0, common_1.UseGuards)(authenticationMiddleware_1.AuthenticationMiddleware)];
        _forgotPassword_decorators = [(0, common_1.Post)('/forgotPassword')];
        _verifyResetCode_decorators = [(0, common_1.Post)('/verifyResetCode')];
        _resetPassword_decorators = [(0, common_1.Post)('/resetPassword')];
        _googleOAuthCallback_decorators = [(0, common_1.Get)('/oauth/callback')];
        _verifyTwoFactorAuth_decorators = [(0, common_1.Post)('/verify-2fa')];
        _enableTwoFactor_decorators = [(0, common_1.Post)('/enable-2fa'), (0, common_1.UseGuards)(authenticationMiddleware_1.AuthenticationMiddleware)];
        _disableTwoFactor_decorators = [(0, common_1.Post)('/disable-2fa'), (0, common_1.UseGuards)(authenticationMiddleware_1.AuthenticationMiddleware)];
        __esDecorate(_classThis, null, _login_decorators, { kind: "method", name: "login", static: false, private: false, access: { has: function (obj) { return "login" in obj; }, get: function (obj) { return obj.login; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _register_decorators, { kind: "method", name: "register", static: false, private: false, access: { has: function (obj) { return "register" in obj; }, get: function (obj) { return obj.register; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getMe_decorators, { kind: "method", name: "getMe", static: false, private: false, access: { has: function (obj) { return "getMe" in obj; }, get: function (obj) { return obj.getMe; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _uploadProfilePhoto_decorators, { kind: "method", name: "uploadProfilePhoto", static: false, private: false, access: { has: function (obj) { return "uploadProfilePhoto" in obj; }, get: function (obj) { return obj.uploadProfilePhoto; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _updateProfile_decorators, { kind: "method", name: "updateProfile", static: false, private: false, access: { has: function (obj) { return "updateProfile" in obj; }, get: function (obj) { return obj.updateProfile; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getProfile_decorators, { kind: "method", name: "getProfile", static: false, private: false, access: { has: function (obj) { return "getProfile" in obj; }, get: function (obj) { return obj.getProfile; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _forgotPassword_decorators, { kind: "method", name: "forgotPassword", static: false, private: false, access: { has: function (obj) { return "forgotPassword" in obj; }, get: function (obj) { return obj.forgotPassword; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _verifyResetCode_decorators, { kind: "method", name: "verifyResetCode", static: false, private: false, access: { has: function (obj) { return "verifyResetCode" in obj; }, get: function (obj) { return obj.verifyResetCode; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _resetPassword_decorators, { kind: "method", name: "resetPassword", static: false, private: false, access: { has: function (obj) { return "resetPassword" in obj; }, get: function (obj) { return obj.resetPassword; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _googleOAuthCallback_decorators, { kind: "method", name: "googleOAuthCallback", static: false, private: false, access: { has: function (obj) { return "googleOAuthCallback" in obj; }, get: function (obj) { return obj.googleOAuthCallback; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _verifyTwoFactorAuth_decorators, { kind: "method", name: "verifyTwoFactorAuth", static: false, private: false, access: { has: function (obj) { return "verifyTwoFactorAuth" in obj; }, get: function (obj) { return obj.verifyTwoFactorAuth; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _enableTwoFactor_decorators, { kind: "method", name: "enableTwoFactor", static: false, private: false, access: { has: function (obj) { return "enableTwoFactor" in obj; }, get: function (obj) { return obj.enableTwoFactor; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _disableTwoFactor_decorators, { kind: "method", name: "disableTwoFactor", static: false, private: false, access: { has: function (obj) { return "disableTwoFactor" in obj; }, get: function (obj) { return obj.disableTwoFactor; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        UserController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return UserController = _classThis;
}();
exports.UserController = UserController;

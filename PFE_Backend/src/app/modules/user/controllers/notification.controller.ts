import {
    Controller,
    Get,
    Post,
    Put,
    Body,
    Param,
    Query,
    Req,
    Res,
    UseGuards,
    HttpStatus
} from '@nestjs/common';
import { Response } from 'express';
import { AuthenticationMiddleware } from '../../../middlewares/authenticationMiddleware';
import { SendNotification } from '../usecases/notifications/sendNotification';
import { GetUserNotifications } from '../usecases/notifications/getUserNotifications';
import { MarkNotificationAsRead } from '../usecases/notifications/markNotificationAsRead';
import { UpdateFcmToken } from '../usecases/notifications/updateFcmToken';
import { SendNotificationCommand } from '../commands/notifications/sendNotification.command';
import { GetUserNotificationsCommand } from '../commands/notifications/getUserNotifications.command';
import { MarkNotificationAsReadCommand } from '../commands/notifications/markNotificationAsRead.command';
import { UpdateFcmTokenCommand } from '../commands/notifications/updateFcmToken.command';

@Controller('notifications')
export class NotificationController {
    constructor(
        private readonly sendNotificationUseCase: SendNotification,
        private readonly getUserNotificationsUseCase: GetUserNotifications,
        private readonly markNotificationAsReadUseCase: MarkNotificationAsRead,
        private readonly updateFcmTokenUseCase: UpdateFcmToken
    ) {}

    @Get()
    @UseGuards(AuthenticationMiddleware)
    async getUserNotifications(
        @Req() req: any,
        @Query() query: Omit<GetUserNotificationsCommand, 'userId'>,
        @Res() res: Response
    ) {
        try {
            const command = new GetUserNotificationsCommand();
            command.userId = req.identity.id;
            command.status = query.status;
            command.type = query.type;
            command.page = query.page || 1;
            command.limit = query.limit || 20;

            const result = await this.getUserNotificationsUseCase.execute(command);

            return res.status(HttpStatus.OK).json({
                success: true,
                message: 'Notifications retrieved successfully',
                data: result
            });
        } catch (error) {
            return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                success: false,
                message: error.message || 'Failed to retrieve notifications'
            });
        }
    }

    @Put(':notificationId/read')
    @UseGuards(AuthenticationMiddleware)
    async markAsRead(
        @Req() req: any,
        @Param('notificationId') notificationId: string,
        @Res() res: Response
    ) {
        try {
            const command = new MarkNotificationAsReadCommand();
            command.notificationId = notificationId;
            command.userId = req.identity.id;

            const notification = await this.markNotificationAsReadUseCase.execute(command);

            return res.status(HttpStatus.OK).json({
                success: true,
                message: 'Notification marked as read',
                data: notification
            });
        } catch (error) {
            const statusCode = error.status || HttpStatus.INTERNAL_SERVER_ERROR;
            return res.status(statusCode).json({
                success: false,
                message: error.message || 'Failed to mark notification as read'
            });
        }
    }

    @Post('fcm-token')
    @UseGuards(AuthenticationMiddleware)
    async updateFcmToken(
        @Req() req: any,
        @Body() body: Omit<UpdateFcmTokenCommand, 'userId'>,
        @Res() res: Response
    ) {
        try {
            const command = new UpdateFcmTokenCommand();
            command.userId = req.identity.id;
            command.fcmToken = body.fcmToken;

            const user = await this.updateFcmTokenUseCase.execute(command);

            return res.status(HttpStatus.OK).json({
                success: true,
                message: 'FCM token updated successfully',
                data: { fcmToken: user.fcm }
            });
        } catch (error) {
            const statusCode = error.status || HttpStatus.BAD_REQUEST;
            return res.status(statusCode).json({
                success: false,
                message: error.message || 'Failed to update FCM token'
            });
        }
    }

    // Admin endpoint to send notifications
    @Post('send')
    @UseGuards(AuthenticationMiddleware)
    async sendNotification(
        @Req() req: any,
        @Body() body: Omit<SendNotificationCommand, 'senderId'>,
        @Res() res: Response
    ) {
        try {
            // Only allow admins or pharmacists to send notifications
            const userRole = req.identity.role;
            if (![4, 5, 6].includes(userRole)) { // INTERNAL_PHARMACY, EXTERNAL_PHARMACY, ADMIN
                return res.status(HttpStatus.FORBIDDEN).json({
                    success: false,
                    message: 'Insufficient permissions to send notifications'
                });
            }

            const command = new SendNotificationCommand();
            command.recipientId = body.recipientId;
            command.senderId = req.identity.id;
            command.title = body.title;
            command.message = body.message;
            command.type = body.type;
            command.data = body.data;

            const notification = await this.sendNotificationUseCase.execute(command);

            return res.status(HttpStatus.CREATED).json({
                success: true,
                message: 'Notification sent successfully',
                data: notification
            });
        } catch (error) {
            return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                success: false,
                message: error.message || 'Failed to send notification'
            });
        }
    }
}

import {Body, Controller, Get, Post, Put, Req, Res, UploadedFile, UseGuards, UseInterceptors} from "@nestjs/common";
import { Request,Response } from 'express';
import { UserLogin } from "../usecases/auth/user.login";
import { UserLoginCommand } from "../commands/auth/user.loginCommand";
import { I18n, I18nContext } from "nestjs-i18n";
import { UserRegister } from "../usecases/auth/user.register";
import { UserRegisterCommand } from "../commands/auth/user.registerCommand";
import { UserErrors } from "../../../../core/errors/UserErrors";
import { AuthenticationMiddleware } from "../../../middlewares/authenticationMiddleware";
import { AuthenticatedRequest } from "../../../config/authenticatedRequest";
import {UserUpdateProfileCommand} from "../commands/profile/user.updateProfileCommand";
import {UserUpdateProfile} from "../usecases/profile/user.updateProfile";
import {VerifyResetCodeCommand} from "../commands/auth/VerifyResetCodeCommand";
import {ForgotPasswordCommand} from "../commands/auth/ForgotPasswordCommand";
import {ResetPasswordCommand} from "../commands/auth/ResetPasswordCommand";
import {UserForgotPassword} from "../usecases/auth/user.forgotPassword";
import {UserVerifyResetCode} from "../usecases/auth/user.verifyResetCode";
import {UserResetPassword} from "../usecases/auth/user.resetPassword";
import {UserOAuthLogin} from "../usecases/2FA/user.oauthLogin";
import {UserVerifyTwoFactorCommand} from "../commands/2FA/user.verifyTwoFactorCommand";
import {VerifyTwoFactor} from "../usecases/2FA/user.verifyTwoFactorCode";
import {UserEnableTwoFactorCommand} from "../commands/2FA/user.enableTwoFactorCommand";
import {EnableTwoFactorUseCase} from "../usecases/2FA/user.enableTwoFactor";
import {DisableTwoFactorUseCase} from "../usecases/2FA/user.disableTwoFactor";
import {UserGetProfileCommand} from "../commands/profile/user.getUserProfileCommand";
import {UserGetProfile} from "../usecases/profile/user.getUserProfile";
import {UserRepository} from "../repositories/user.repository";
import {FileInterceptor} from "@nestjs/platform-express";
import {storage} from "../../../config/storage.config";
import {diskStorage} from "multer";
import * as fs from "fs";
import * as path from "path";
import {PatientUpdateProfileCommand} from "../commands/profile/patient.updateProfileCommand";
import {UserPropertiesRepository} from "../repositories/userProperties.repository";
import {UserResetLoginPassword} from "../usecases/auth/user.resetLoginPassword";
import {UserResetLoginPasswordCommand} from "../commands/auth/user.resetLoginPasswordCommand";



@Controller('/user')
export class UserController {
  constructor(
      private readonly userRepository:UserRepository,
      private readonly userPropertiesRepository:UserPropertiesRepository,
      private readonly userLogin: UserLogin,
      private readonly userRegister: UserRegister,
      private readonly userUpdateProfile: UserUpdateProfile,
      private readonly userForgotPassword:UserForgotPassword,
      private readonly userVerifyResetCode:UserVerifyResetCode,
      private readonly userResetPassword:UserResetPassword,
      private readonly userOAuthLogin:UserOAuthLogin,
      private readonly verifyuserTwoFactor:VerifyTwoFactor,
      private readonly enableTwoFactorUseCase:EnableTwoFactorUseCase,
      private readonly disableTwoFactorUseCase:DisableTwoFactorUseCase,
      private readonly userGetProfile:UserGetProfile,
      private readonly userResetLoginPassword: UserResetLoginPassword,

  ) {
  }


  @Post('/login')
  async login(
      @Res() res: Response,
      @Body() body: UserLoginCommand,
      @I18n() i18n: I18nContext
  ) {
    try {
      const token: string = await this.userLogin.execute(body);
      return res.status(200).json({
        token,
        message: i18n.translate('success.SUCCESSFULLY_LOGGED_IN'),
      });
    } catch (e) {
      console.log(e);
      if (e instanceof UserErrors.WrongCredentials)
        return res.status(401).json({message: i18n.translate('errors.WRONG_CREDENTIALS')})
      return res.status(401).json({message: i18n.translate('errors.AUTHENTICATION_FAILED')})
    }
  }

  @Post('/resetLoginPassword')
  @UseGuards(AuthenticationMiddleware)  // ✅ Requires authentication
  async resetLoginPassword(
      @Req() req: AuthenticatedRequest,  // ✅ Extract user identity
      @Body() body: UserResetLoginPasswordCommand,
      @Res() res: Response,
      @I18n() i18n: I18nContext
  ) {
    try {
      const userId = req.identity.id;  // ✅ Automatically get userId from session

      await this.userResetLoginPassword.execute(userId, body);

      return res.status(200).json({
        success: true,
        message: i18n.translate('success.PASSWORD_UPDATED'),
      });
    } catch (e) {
      console.error(e);
      return res.status(400).json({ message: i18n.translate('errors.PASSWORD_RESET_FAILED') });
    }
  }

  @Post('/register')
  async register(
      @Res() res: Response,
      @Body() body: UserRegisterCommand,
      @I18n() i18n: I18nContext
  ) {
    try {
      await this.userRegister.execute(body)
      return res.status(201).json({
        success: true,
        message: i18n.translate('success.SUCCESSFULLY_REGISTERED'),
      });
    } catch (e) {
      if (e instanceof UserErrors.EmailAlreadyUsed)
        return res.status(403).json({message: i18n.translate('errors.EMAIL_ALREADY_USED')})
      if (e instanceof UserErrors.PasswordInvalid)
        return res.status(403).json({message: i18n.translate('errors.PASSWORD_INVALID')})
      if (e instanceof UserErrors.ProfileAlreadyUsed)
        return res.status(403).json({message: i18n.translate('errors.PROFILE_ALREADY_USED')})
      return res.status(403).json({message: i18n.translate('errors.AUTHENTICATION_FAILED')})
    }
  }

  @Get('me')
  @UseGuards(AuthenticationMiddleware)
  async getMe(
      @Req() req: AuthenticatedRequest,
      @Res() res: Response,
      @I18n() i18n: I18nContext
  ) {
    try {
      return res.status(200).json(req.identity)
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      return res.status(403).json({message: i18n.translate('errors.AUTHENTICATION_FAILED')})
    }
  }

//**********************************************Profile***************************************************************
  @Post('/uploadProfilePhoto')
  @UseGuards(AuthenticationMiddleware)
  @UseInterceptors(FileInterceptor('file', {
    storage: diskStorage({
      destination: './uploads/profilePhotos',
      filename: (req, file, cb) => {
        const uniqueName = `${Date.now()}-${file.originalname}`;
        cb(null, uniqueName);
      },
    }),
  }))
  async uploadProfilePhoto(
      @Req() req: AuthenticatedRequest,
      @UploadedFile() file: Express.Multer.File,
      @Res() res: Response,
  ) {
    if (!file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // 🟢 Get the current user properties
    const userId = req.identity.id;
    let userProperties = await this.userPropertiesRepository.findOneById(userId);

    if (!userProperties) {
      // If userProperties doesn't exist, create a new entry
      userProperties = await this.userPropertiesRepository.save({ id: userId, profilePhoto: "" });
    }

    // 🟢 Delete the old photo if it exists
    if (userProperties.profilePhoto) {
      const oldPhotoPath = path.join(__dirname, '../../../../../../uploads/profilePhotos', path.basename(userProperties.profilePhoto));
      if (fs.existsSync(oldPhotoPath)) {
        fs.unlinkSync(oldPhotoPath);
        console.log(`✅ Deleted old profile photo: ${oldPhotoPath}`);
      }
    }

    // ✅ Ensure the returned path is correct
    const photoPath = `/uploads/profilePhotos/${file.filename}`;
    console.log(`✅ New profile photo uploaded: ${photoPath}`);

    // ✅ Update `UserProperties` with the new profile photo
    await this.userPropertiesRepository.update(userId, { profilePhoto: photoPath });

    return res.status(201).json({
      success: true,
      message: 'Profile photo uploaded successfully.',
      profilePhoto: photoPath,
    });
  }


  @Put('/updateProfile')
  @UseGuards(AuthenticationMiddleware)
  async updateProfile(
      @Req() req: AuthenticatedRequest,
      @Res() res: Response,
      @Body() body: { user: UserUpdateProfileCommand; patient?: PatientUpdateProfileCommand },
      @I18n() i18n: I18nContext
  ) {
    try {
      const userId = req.identity.id;
      if (!userId) {
        return res.status(401).json({ message: i18n.translate('errors.UNAUTHORIZED') });
      }

      if (body.patient?.addresses) {
        for (const address of body.patient.addresses) {
          if (!address || !address.address || !address.lat || !address.long) {
            return res.status(400).json({ message: i18n.translate('errors.INVALID_ADDRESS_DATA') });
          }
        }
      }

      await this.userUpdateProfile.execute({
        userId,
        userUpdateProfileCommand: body.user,
        patientUpdateProfileCommand: {
          ...body.patient,
          addresses: body.patient?.addresses ?? [],
        },
      });

      return res.status(201).json({
        success: true,
        message: i18n.translate('success.SUCCESSFULLY_UPDATED'),
      });
    } catch (e) {
      console.error(e);
      return res.status(403).json({ message: i18n.translate('errors.AUTHENTICATION_FAILED') });
    }
  }


  @Get('/getProfile')
  @UseGuards(AuthenticationMiddleware)
  async getProfile(
      @Req() req: AuthenticatedRequest,
      @Res() res: Response,
      @I18n() i18n: I18nContext
  ) {
    try {
      const userId = req.identity.id;
      if (!userId) {
        return res.status(401).json({ message: i18n.translate('errors.UNAUTHORIZED') });
      }

      const userProfile = await this.userGetProfile.execute({ userId });

      return res.status(200).json({
        success: true,
        message: i18n.translate('success.SUCCESSFULLY_FETCHED_PROFILE'),
        data: userProfile,
      });
    } catch (e) {
      console.error(e);
      return res.status(500).json({ message: i18n.translate('errors.INTERNAL_SERVER_ERROR') });
    }
  }


  //**********************************************Profile***************************************************************


  @Post('/forgotPassword')
  async forgotPassword(
      @Res() res: Response,
      @Body() body: ForgotPasswordCommand,
      @I18n() i18n: I18nContext
  ) {
    await this.userForgotPassword.execute(body);
    return res.status(200).json({ message: i18n.translate('success.EMAIL_SENT') });
  }

  @Post('/verifyResetCode')
  async verifyResetCode(
      @Res() res: Response,
      @Body() body: VerifyResetCodeCommand,
      @I18n() i18n: I18nContext
  ) {
    await this.userVerifyResetCode.execute(body);
    return res.status(200).json({ message: i18n.translate('success.CODE_VALID') });
  }

  @Post('/resetPassword')
  async resetPassword(
      @Res() res: Response,
      @Body() body: ResetPasswordCommand,
      @I18n() i18n: I18nContext
  ) {
    await this.userResetPassword.execute(body);
    return res.status(200).json({ message: i18n.translate('success.PASSWORD_UPDATED') });
  }

  //*********************************************2FA**********************************************************
  @Get('/oauth/callback')
  async googleOAuthCallback(
      @Res() res: Response,
      @Req() req: Request,
      @I18n() i18n: I18nContext
  ) {
    try {
      const code = req.query.code as string;

      if (!code) {
        return res.status(400).json({ message: i18n.translate('errors.INVALID_CODE') });
      }

      // Execute Google login and get result
      const result = await this.userOAuthLogin.execute({ code });

      // If 2FA is required, redirect back to Flutter app
      if (result.requiresTwoFactor) {
        const appRedirectUri = `patiantapp://oauth/callback?email=${result.email}&code=${code}`;
        console.log("🔄 Redirecting to Flutter app:", appRedirectUri);
        return res.redirect(appRedirectUri);
      }

      // If no 2FA, return token
      return res.status(200).json(result);
    } catch (e) {
      console.error('OAuth Callback Error:', e);
      return res.status(401).json({ message: i18n.translate('errors.AUTHENTICATION_FAILED') });
    }
  }

  @Post('/verify-2fa')
  async verifyTwoFactorAuth(
      @Res() res: Response,
      @Body() body: UserVerifyTwoFactorCommand,
      @I18n() i18n: I18nContext
  ) {
    try {
      const token = await this.verifyuserTwoFactor.execute(body);
      return res.status(200).json({
        token,
        message: i18n.translate('success.SUCCESSFULLY_LOGGED_IN'),
      });
    } catch (e) {
      console.log(e);
      return res.status(401).json({ message: i18n.translate('errors.AUTHENTICATION_FAILED') });
    }
  }
  @Post('/enable-2fa')
  @UseGuards(AuthenticationMiddleware)
  async enableTwoFactor(
      @Req() req: AuthenticatedRequest,
      @Res() res: Response,
      @I18n() i18n: I18nContext
  ) {
    try {
      const command = new UserEnableTwoFactorCommand();
      command.userId = req.identity.id;

      await this.enableTwoFactorUseCase.execute(command);

      return res.status(200).json({
        success: true,
        message: i18n.translate('success.TWO_FACTOR_ENABLED'),
      });
    } catch (e) {
      return res.status(500).json({ message: i18n.translate('errors.TWO_FACTOR_FAILED') });
    }
  }
  @Post('/disable-2fa')
  @UseGuards(AuthenticationMiddleware)
  async disableTwoFactor(
      @Req() req: AuthenticatedRequest,
      @Res() res: Response,
      @I18n() i18n: I18nContext
  ) {
    try {
      const command = new UserEnableTwoFactorCommand();
      command.userId = req.identity.id;

      await this.disableTwoFactorUseCase.execute(command);

      return res.status(200).json({
        success: true,
        message: i18n.translate('success.TWO_FACTOR_DISABLED'),
      });
    } catch (e) {
      return res.status(500).json({ message: i18n.translate('errors.TWO_FACTOR_FAILED') });
    }
  }

  //*********************************************2FA**********************************************************





}

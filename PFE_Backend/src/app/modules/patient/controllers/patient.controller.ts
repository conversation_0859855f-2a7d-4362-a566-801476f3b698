import {Body, Controller, Get, Param, Post, Query, Req, Res, UseGuards} from '@nestjs/common';
import { Response } from 'express';
import { PatientRegisterCommand } from '../commands/auth/patient.registerCommand';
import { PatientRegister } from '../usecases/auth/patient.register';
import {AuthenticationMiddleware} from "../../../middlewares/authenticationMiddleware";
import {I18n, I18nContext} from "nestjs-i18n";
import {AuthenticatedRequest} from "../../../config/authenticatedRequest";
import {GetPatientById} from "../usecases/patient.getById";
import {FilterPatientByNameLike} from "../usecases/patient.filterByName";

@Controller('/patient')
export class PatientController {
    constructor(private readonly patientRegister: PatientRegister,
                private readonly getPatientById: GetPatientById,
                private readonly filterPatientByNameLike: FilterPatientByNameLike


    ) {}

    // ✅ Endpoint for Pharmacy to Create Patient Account

    @Post('/create')
    async createPatient(
        @Res() res: Response,
        @Body() body: PatientRegisterCommand
    ) {
        try {
            await this.patientRegister.execute(body);
            return res.status(201).json({
                success: true,
                message: 'Patient account created successfully. A default password has been sent to the patient.',
            });
        } catch (e) {
            console.error(e);
            return res.status(400).json({ message: 'Failed to create patient account.' });
        }
    }


    @Get('/getPatientInfo/:patientId')
    @UseGuards(AuthenticationMiddleware)
    async getPatientInfo(
        @Req() req: AuthenticatedRequest,
        @Res() res: Response,
        @I18n() i18n: I18nContext,
        @Param('patientId') patientId: string
    ) {
        try {
            const patient = await this.getPatientById.execute(patientId);

            if (!patient) {
                return res.status(404).json({
                    success: false,
                    message: i18n.translate('errors.PATIENT_NOT_FOUND'),
                });
            }

            return res.status(200).json({
                success: true,
                message: i18n.translate('success.PATIENT_FETCHED_SUCCESSFULLY'),
                data: patient,
            });
        } catch (error) {
            console.error(error);
            return res.status(500).json({
                success: false,
                message: i18n.translate('errors.INTERNAL_SERVER_ERROR'),
            });
        }
    }


    @Get('/search')
    async searchPatientsByName(@Query('query') query: string) {
        const patients = await this.filterPatientByNameLike.execute(query);
        return {
            success: true,
            total: patients.length,
            patients,
        };
    }

}

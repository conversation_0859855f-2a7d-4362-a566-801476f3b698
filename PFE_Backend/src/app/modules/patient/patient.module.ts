import {forwardRef, Module} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Patient, PatientSchema } from './entities/patient.entity';
import { PatientController } from './controllers/patient.controller';
import { PatientRegister } from './usecases/auth/patient.register';
import { PatientRepository } from './repositories/patient.repository';
import { UserModule } from '../user/user.module';
import {JwtModule} from "@nestjs/jwt";
import {GetPatientById} from "./usecases/patient.getById";
import {FilterPatientByNameLike} from "./usecases/patient.filterByName";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Patient", schema: PatientSchema },
        ]),
        JwtModule.register({
            secret: "reallysecurekey",
            signOptions: { expiresIn: "1y" }
        }),
        forwardRef(() => UserModule),
    ],
    controllers: [PatientController],
    providers: [PatientRegister, PatientRepository,GetPatientById,FilterPatientByNameLike],
    exports: [PatientRepository],
})
export class PatientModule {}

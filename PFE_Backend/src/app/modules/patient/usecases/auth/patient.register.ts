import { Injectable } from '@nestjs/common';
import { PatientRegisterCommand } from '../../commands/auth/patient.registerCommand';
import { PatientRepository } from '../../repositories/patient.repository';
import { UserRepository } from '../../../user/repositories/user.repository';
import { BcryptGateway } from '../../../../gateways/bcrypt.gateway';
import {v4, v4 as uuidv4} from 'uuid';
import {EmailService} from "../../../../Shared/Services/EmailService";

@Injectable()
export class PatientRegister {
    constructor(
        private readonly patientRepository: PatientRepository,
        private readonly userRepository: UserRepository,
        private readonly bcryptGateway: BcryptGateway,
        private readonly emailService: EmailService
    ) {}

    async execute(command: PatientRegisterCommand): Promise<void> {
        const userId = uuidv4();
        const defaultPassword = 'Default123!';

        // 1️⃣ Create User Instance (with same ID)
        const userSaved=await this.userRepository.save({
            id: userId,
            email:command.email,
            password:await this.bcryptGateway.encrypt(defaultPassword),
            phone:command.phoneNumber,
            fcm:null
        })
        // 2️⃣ Create Patient Instance (with same ID)
        await this.patientRepository.createPatient({
            id: userId,
            firstName: command.firstName,
            lastName: command.lastName,
            email: userSaved.email,
            pharmacyId: command.pharmacyId,
        } );


        // 3️⃣ Send Email with Default Password
        await this.emailService.sendPatientAccountEmail(command.email, command.firstName, defaultPassword);

    }
}

import {Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Matches} from 'class-validator';

export class PatientRegisterCommand {
    @IsEmail()
    @IsNotEmpty({ message: 'Email is required' })
    email: string;

    @IsString()
    @IsNotEmpty({ message: 'First name is required' })
    firstName: string;

    @IsString()
    @IsNotEmpty({ message: 'Last name is required' })
    lastName: string;

    @IsNotEmpty({ message: 'validation.PHONE_REQUIRED' })
    @Matches(/^\d{8}$/, { message: 'validation.PHONE_INVALID' }) // Ensures exactly 8 digits
    phoneNumber!: string;

    @IsString()
    @IsNotEmpty({ message: 'Pharmacy ID is required' })
    pharmacyId: string;
}

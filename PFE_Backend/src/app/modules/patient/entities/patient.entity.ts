import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { PatientPaymentType } from '../../../../core/enums/patient/PatientPaymentType';
import { SignatureType } from '../../../../core/enums/patient/SignatureType';

@Schema({ timestamps: true })
export class Patient extends Document {
    @Prop({ required: true, unique: true })
    id: string;

    @Prop({ required: true })
    pharmacyId: string;

    @Prop({ required: true, unique: true })
    email: string;

    @Prop({ required: true })
    firstName: string;

    @Prop({ required: true })
    lastName: string;

    @Prop({ required: true })
    phone: string;

    @Prop()
    post?: number;

    @Prop()
    officePhone?: string;

    @Prop({
        type: [{
            address: { type: String, required: true },
            lat: { type: Number, required: true },
            long: { type: Number, required: true },
            apartmentNumber: { type: String },
        }],
        default: []
    })
    address: Array<{ address: string; lat: number; long: number; apartmentNumber?: string }>;

    @Prop({
        type: {
            name: { type: String, required: true },
            type: { type: String, required: true },
            phone: { type: String, required: true },
        }
    })
    authorizedPerson?: {
        name: string;
        type: string;
        phone: string;
    };

    @Prop({ type: String, enum: PatientPaymentType })
    paymentType?: PatientPaymentType;

    @Prop()
    deliveryManNote?: string;

    @Prop()
    pharmacyNote?: string;

    @Prop()
    householdNote?: string;

    @Prop()
    spot?: string;

    @Prop({ type: String, enum: SignatureType })
    signatureType?: SignatureType;

    @Prop({ default: false })
    hospitalizedPatient?: boolean;

    @Prop({
        type: {
            start: { type: Date },
            end: { type: Date },
        }
    })
    vacationPeriod?: {
        start: Date;
        end: Date;
    };

    @Prop({
        type: {
            note: { type: String },
            frequency: {
                standardFrequency: { type: String },
                each: { type: Number },
                period: { type: String },
            },
            from: { type: Date },
            to: { type: Date },
        }
    })
    recurrenceOption?: {
        note: string;
        frequency: {
            standardFrequency?: string;
            each?: number;
            period?: string;
        };
        from: Date;
        to: Date;
    };

    @Prop({ type: [String], default: [] })
    deliveryType: string[];

    @Prop()
    houseHoldId?: string;

    @Prop()
    roomNumber?: number;

    @Prop()
    createdBy?: string;

    @Prop()
    deletedAt?: Date;

    @Prop()
    deletedBy?: string;
}

export const PatientSchema = SchemaFactory.createForClass(Patient);

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Patient } from '../entities/patient.entity';

@Injectable()
export class PatientRepository {
    constructor(@InjectModel('Patient') private patientModel: Model<Patient>) {}

    async createPatient(patientData: Partial<Patient>): Promise<Patient> {
        patientData.id = patientData.id || crypto.randomUUID();
        const patient = new this.patientModel(patientData);
        return patient.save();
    }

    async findOneById(id: string): Promise<Patient | null> {
        return this.patientModel.findOne({ id }).exec();
    }

    async findOneByEmail(email: string): Promise<Patient | null> {
        return this.patientModel.findOne({ email }).exec();
    }

    async findAll(): Promise<Patient[]> {
        return this.patientModel.find().exec();
    }
    async update(id: string, updateData: Partial<Patient>): Promise<Patient | null> {
        return this.patientModel.findOneAndUpdate(
            { id },
            { $set: updateData }, // ✅ Ensures nested fields like `address` update correctly
            { new: true, useFindAndModify: false }
        ).exec();
    }




    async delete(id: string): Promise<boolean> {
        const result = await this.patientModel.deleteOne({ id }).exec();
        return result.deletedCount > 0;
    }

    async existsById(id: string): Promise<boolean> {
        const count = await this.patientModel.countDocuments({ id }).exec();
        return count > 0;
    }

    async findByNameLike(name: string): Promise<Patient[]> {
        const regex = new RegExp(name.trim(), 'i'); // case-insensitive match
        return this.patientModel.find({
            $or: [
                { firstName: regex },
                { lastName: regex },
            ]
        }).exec();
    }

}

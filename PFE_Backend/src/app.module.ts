import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './app/modules/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { HeaderResolver, I18nModule } from 'nestjs-i18n';
import { join } from 'path';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {MulterModule} from "@nestjs/platform-express";
import {diskStorage} from "multer";
import {PatientModule} from "./app/modules/patient/patient.module";
import {PackageModule} from "./app/modules/package/package.module";
import {HouseHoldModule} from "./app/modules/houseHold/houseHold.module";
import {PharmacyModule} from "./app/modules/pharmacy/pharmacy.module";
import {PrescriptionModule} from "./app/modules/prescription/prescription.module";
import {EcommerceModule} from "./app/modules/ecommerce/ecommerce.module";
import {OcrModule} from "./app/modules/ocr/ocr.module";

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }), // Charge .env globalement
    UserModule,
    PatientModule,
      PackageModule,
      HouseHoldModule,
      PharmacyModule,
      PrescriptionModule,
      EcommerceModule,
      OcrModule,
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        uri: configService.get<string>('DB_URL'), // Récupère la variable DB_URL de .env
      }),
    }),
    I18nModule.forRoot({
      fallbackLanguage: 'en',
      loaderOptions: {
        path: join(__dirname, '/core/i18n/'),
        watch: true,
      },
      resolvers: [new HeaderResolver(['x-lang'])],
    }),

    MulterModule.register({
      storage: diskStorage({
        destination: './uploads/profilePhotos', // Save photos in this folder
        filename: (req, file, cb) => {
          const uniqueName = `${Date.now()}-${file.originalname}`;
          cb(null, uniqueName);
        },
      }),
    }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  constructor() {

    console.log(__dirname);
  }
}

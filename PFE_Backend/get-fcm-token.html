<!DOCTYPE html>
<html>
<head>
    <title>Get FCM Token</title>
</head>
<body>
    <h1>Get FCM Token for Testing</h1>
    <button id="getToken">Get FCM Token</button>
    <div id="token"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js';
        import { getMessaging, getToken } from 'https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging.js';

        // Your Firebase config (get this from Firebase Console)
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "med4-notifications.firebaseapp.com",
            projectId: "med4-notifications",
            storageBucket: "med4-notifications.appspot.com",
            messagingSenderId: "your-sender-id",
            appId: "your-app-id"
        };

        const app = initializeApp(firebaseConfig);
        const messaging = getMessaging(app);

        document.getElementById('getToken').addEventListener('click', async () => {
            try {
                const token = await getToken(messaging, {
                    vapidKey: 'your-vapid-key' // Get this from Firebase Console
                });
                
                if (token) {
                    document.getElementById('token').innerHTML = `
                        <h3>Your FCM Token:</h3>
                        <textarea rows="5" cols="80">${token}</textarea>
                        <p>Copy this token and use it in Postman!</p>
                    `;
                    console.log('FCM Token:', token);
                } else {
                    console.log('No registration token available.');
                }
            } catch (err) {
                console.log('An error occurred while retrieving token. ', err);
                document.getElementById('token').innerHTML = `<p>Error: ${err.message}</p>`;
            }
        });
    </script>
</body>
</html>

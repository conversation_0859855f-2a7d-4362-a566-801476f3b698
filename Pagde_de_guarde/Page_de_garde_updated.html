<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page de Garde - PFE Report</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 0;
            padding: 40px;
            background-color: white;
            line-height: 1.6;
        }
        
        .cover-page {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .header {
            margin-bottom: 40px;
        }
        
        .logo {
            margin-bottom: 30px;
        }
        
        .logo img {
            max-width: 200px;
            height: auto;
        }
        
        .university-info {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 40px 0;
        }
        
        .report-type {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .project-title {
            font-size: 24px;
            font-weight: bold;
            margin: 30px 0;
            color: #2c3e50;
            border: 3px solid #2c3e50;
            padding: 20px;
            display: inline-block;
        }
        
        .author-info {
            font-size: 16px;
            margin: 40px 0;
            line-height: 2;
        }
        
        .supervisors {
            font-size: 14px;
            margin: 30px 0;
            line-height: 2;
        }
        
        .footer {
            margin-top: 40px;
        }
        
        .academic-year {
            font-size: 14px;
            font-weight: bold;
        }
        
        .bold {
            font-weight: bold;
        }
        
        .underline {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="cover-page">
        <div class="header">
            <div class="logo">
                <img src="img/med4solutions.svg" alt="Med4Solutions Logo">
            </div>
            
            <div class="university-info">
                RÉPUBLIQUE TUNISIENNE<br>
                MINISTÈRE DE L'ENSEIGNEMENT SUPÉRIEUR<br>
                ET DE LA RECHERCHE SCIENTIFIQUE<br>
                <br>
                UNIVERSITÉ DE SFAX<br>
                ÉCOLE NATIONALE D'INGÉNIEURS DE SFAX<br>
                DÉPARTEMENT GÉNIE INFORMATIQUE
            </div>
        </div>
        
        <div class="main-content">
            <div class="report-type">
                RAPPORT DE PROJET DE FIN D'ÉTUDES
            </div>
            
            <div class="project-title">
                Developing a full stack app
            </div>
            
            <div class="author-info">
                <span class="bold">Présenté par :</span><br>
                <span class="bold underline">Hssan Ghorbel</span>
            </div>
            
            <div class="supervisors">
                <span class="bold">Encadrant Académique :</span><br>
                Mrs. Emna Charfi<br>
                <br>
                <span class="bold">Encadrant Professionnel :</span><br>
                Mr. Ziyed
            </div>
        </div>
        
        <div class="footer">
            <div class="academic-year">
                Année Universitaire : 2024-2025
            </div>
        </div>
    </div>
</body>
</html>
